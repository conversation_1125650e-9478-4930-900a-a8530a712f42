#环境
APP_ENV=dev

REDIS_HOST=redis
REDIS_PASSWORD=123456
REDIS_PORT=6379
REDIS_DB=0

MONGO_HOST=mongo
MONGO_DATABASE=momo
MONGO_USERNAME=root
MONGO_PASSWORD=123456
MONGO_PORT=27017

#缓存驱动
CACHE_DRIVER=redis
#缓存前缀
CACHE_PREFIX=MOMO

#订单前缀
ORDER_PREFIX=mm1

#用户名前缀
USER_NAME_PREFIX=mm

#jaeger配置
JAEGER_SERVICE_NAME=local_momo_kumibackend
JAEGER_HOST=*************
#推送概率
JAEGER_SAMPLER_RATE=0
##每秒最大采集数（固定采集数选项时）
#JAEGER_MAXIMUM=10
#最小采集时间
JAEGER_MIN_GATHERING_TIME=0
