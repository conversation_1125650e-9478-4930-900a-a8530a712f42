<?php

namespace app\Helper;

use Psr\Http\Message\ResponseInterface;

/**
 * app功能差异判断统一类
 */
class AppDiffHelper extends BaseHelper
{
    /** @var string  MOMO前缀的常量为对应momoApp显示的标识 */
    const TEST='test';
    const MOMO1='MOMO';
    const MOMO2='MOMO_TWO';
    const MOMO3='MOMO_THIRD';
    const MOMO4='MOMO4';
    const MOMO5='MOMO5';
    const MOMO6='MOMO6';
    const MOMO7='MOMO7';
    const MOMO8='MOMO8';
    const MOMO9='MOMO9';
    const MOMO10='MOMO10';
    const MOMO11='MOMO11';
    const MOMO12='MOMO12';
    const MOMO13='MOMO13';
    const MOMO14='MOMO14';
    const MOMO15='MOMO15';
    const MOMO16='MOMO16';
    const MOMO17='MOMO17';
    const MOMO18='MOMO18';
    const MOMO19='MOMO19';
    const MOMO20='MOMO20';
    const MOMO33='MOMO33';
    const MOMO40='MOMO40';
    const MOMO41='MOMO41';

    /** @var string FUN_前缀开头的常量为对应需要差异化的功能 */
    const FUN_PAY_WAY=['fun_pay_way',[self::MOMO4, self::MOMO16,self::MOMO17,self::MOMO18,self::MOMO19,self::MOMO20]]; //支付方式改为轮训需求功能
    const FUN_INTEGRAL=['fun_integral',[]]; //积分商城需求
    const FUN_TURNTABLE=['turntable',[self::TEST,self::MOMO3, self::MOMO33]]; //转盘抽奖规则变更需求
//    const FUN_MEMORY_MONITOR=['memory_monitor',[self::TEST, self::MOMO9]]; //异常接口内存监控
//    const FUN_AI_ALONE=['fun_ai_alone',[self::TEST, self::MOMO4, self::MOMO8]]; //ai迁移独立
//    const FUN_USER_BEHAVIOR=['memory_monitor',[self::TEST, self::MOMO4, self::MOMO8, self::MOMO33]]; //用户行为需求

    /**
     * 判断当前app是否是指定上线app范围
     * @param string $tag 功能标识
     * @param bool $is_exception 是否直接中断请求返回结果 true是 false 否
     */
    public static function checkLimitAPP(string $tag='',bool $is_exception=false)
    {

//        if(env('APP_ENV')=='dev' || in_array(env('CACHE_PREFIX'),self::getConstPluck('FUN_')[$tag])){
        if(in_array(env('CACHE_PREFIX'),self::getConstPluck('FUN_')[$tag])){
            return true;
        }
        if($is_exception){
            json_fail('该功能未上线!');
        }
        return false;
    }


}
