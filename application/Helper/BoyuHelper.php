<?php

namespace app\Helper;

use Psr\Http\Message\ResponseInterface;

class BoyuHelper
{

    //博鱼缓存前缀
    const CACHE_PREFIX="boyu:";

    public static $key='';

    public function setKey(string $key=''){
        self::$key=$key;
    }

    private static function getKey(){
        return self::$key;
    }

    //返回数据成功响应码
    const SUCCESS_CODE = 0;


    /**
     * 获取接口解密后的数据
     * @param $response_data
     * @return array
     */
    public static function getDecryptData($response_data): array
    {

        $return_data=[
            'code'=>$response_data['code']??-1,
            'data'=>[],
            'msg'=>$response_data['msg']??'',
        ];

        if(!isset($response_data['code']) || $response_data['code']!==self::SUCCESS_CODE ){
            $return_data['data']=$response_data;
        }else{
            $return_data['data'] = self::decrypt($response_data['data']);
        }

        return $return_data;

    }


    /**
     * AES 加密
     * @param mixed $content 明文
     * @param string $decrypt_key 秘钥
     * @param string $iv 向量，必须为数字
     * @param string $cipher 算法.
     * @param int $option 标记的按位或： OPENSSL_RAW_DATA=1 、 OPENSSL_ZERO_PADDING=2.
     * @return string
     */
    public static function encrypt(
        string $content,
        string $decrypt_key = '',
        string $iv = '',
        string $cipher = 'AES-128-ECB',
        int $option = 1
    ): string
    {
        if(empty($decrypt_key))$decrypt_key=self::getKey();
        $key = substr(openssl_digest(openssl_digest($decrypt_key, 'sha1', true), 'sha1', true),0,16);
        return base64_encode(@openssl_encrypt($content, $cipher, $key, $option, $iv));
    }


    /**
     * AES 解密
     * @param string $content base64密文
     * @param string $decrypt_key 秘钥
     * @param string $iv 向量，必须为数字
     * @param string $cipher 算法.
     * @param int $option 标记的按位或： OPENSSL_RAW_DATA=1 、 OPENSSL_ZERO_PADDING=2.
     * @return array
     */
    public static function decrypt(
        string $content,
        string $decrypt_key = '',
        string $iv = '',
        string $cipher = 'AES-128-ECB',
        int    $option = 1
    )
    {
        if(empty($decrypt_key))$decrypt_key=self::getKey();
        $content=base64_decode($content);
        $key = substr(openssl_digest(openssl_digest($decrypt_key, 'sha1', true), 'sha1', true),0,16);
        return json_decode(@openssl_decrypt($content, $cipher, $key, $option, $iv),true);

    }

}