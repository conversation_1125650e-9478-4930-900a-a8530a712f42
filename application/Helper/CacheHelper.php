<?php

namespace app\Helper;

use app\common\Constants\RedisConst;
use app\Service\AppPayOrderService;

class CacheHelper
{

    //机器人列表缓存
    const SHORT_VIDEO_BOT_LIST_KEY='short_video_bot_list';

    //聚划算支付限制key
    const JUHUASUAN_BUY_LIMIT_KEY='juhuasuan_buy_limit:';

    //整点秒杀支付限制key
    const WHOLE_POINT_BUY_BUY_LIMIT_KEY='whole_point_buy_buy_limit:';

    //用户购买视频记录key
    const MEMBER_BUYMOVIE_KEY = 'member_buymovie_';

    //转盘抽奖限制key
    const LUCKY_DRAW_LIMIT_KEY='lucky_draw_limit:';

    //游客接口读取用户缓存key
    const LOGIN_VISITOR_KEY='login_visitor_check_member_info:';

    //积分商城支付限制key
    const INTEGRAL_BUY_LIMIT_KEY='integral_buy_limit_key:';

    //Ai回调处理限制key
    const AI_CALL_BACK_LIMIT_KEY='ai_call_back_limit_key:';

    /**
     * 接口请求结束前清理掉的redis缓存（直接把key值丢进去即可，如['lucky_draw_limit:541']）
     * @var array
     */
    public static $end_clean_list=[];


    /**
     * 限制指定key的访问频率
     * @return bool
     */
    public static function limitRate(string $redis_key,string $msg='',int $expire_time=5,$is_exception=true){

        $redis=redis();

        if ($redis->exists($redis_key)) {
            if($is_exception){
                json_fail(empty($msg)?'请勿频繁操作':$msg);
            }else{
                return true;
            }
        }

        // 监视一个或多个键
        $redis->watch($redis_key);
        $redis->multi();
        $redis->set($redis_key, 1);
        $redis->expire($redis_key, $expire_time);
        $result = $redis->exec();

        if ($result === false) {
            if($is_exception){
            // 事务被中止，处理中止事务的情况
                json_fail(empty($msg)?'请勿频繁操作.':$msg);
            }
            return true;

        }

        return false;

    }

    /**
     * 删除支付设置的缓存
     * @return void
     */
    public static function delPayCache(int $id){
        cacheDel('all_pay');
        cacheDel('pay_'.$id);
        cacheDel(RedisConst::GET_AMOUNT_FOR_PAY_TYPE_KEY.AppPayOrderService::PAY_PARAMS_TYPE_CHANNEL[0].':'.$id);
        cacheDel(RedisConst::GET_AMOUNT_FOR_PAY_TYPE_KEY.'all_price');

        foreach (AppPayOrderService::getConstSelect('PAY_TYPE_') as $v){
            cacheDel(RedisConst::GET_AMOUNT_FOR_PAY_TYPE_KEY.AppPayOrderService::PAY_PARAMS_TYPE_PAY_TYPE[0].':'.$v['id']);
        }
    }

}
