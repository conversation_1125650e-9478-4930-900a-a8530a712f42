<?php

namespace app\Helper;

use J<PERSON>ger\Config;
use OpenTracing\GlobalTracer;

class JaegerHelper
{

//    const PORT=14268;
    const PORT=6832;

    //活跃模块名称
    const ACTIVE_SPANS = [
        "redis.connect"=>"redis.connect",
        "redis.set"=>"redis.set",
        "redis.get"=>"redis.get",
        "redis.del"=>"redis.del",
        "redis.other"=>"redis.other",
        "mysql.select"=>"mysql.select",
        "mysql.insert"=>"mysql.insert",
        "mysql.update"=>"mysql.update",
        "mysql.delete"=>"mysql.delete",
        "mysql.other"=>"mysql.other",
        "http"=>"http",
        "error"=>"error",
        "end"=>"end",
        "other"=>"other",
    ];


    //指定不集采黑名单
    const BLACK_LIST=[
        '/api/movie/m3u8raw.m3u8',
        '/admin/movie/m3u8raw.m3u8',
    ];


    public static $tracer;
    public static $init_scope;
    public static $nestedSpanScope;
    public static $jaegerHelper;
    public static $is_error=false;
    public static $start_time; //开始采集时间（单位毫秒）

    public static $config_list = [];


    /**
     * 获取实例化类
     * @return JaegerHelper
     */
    public static function getClass(): JaegerHelper
    {
        if(empty($jaegerHelper)){
            self::$jaegerHelper=(new JaegerHelper());
        }
        return self::$jaegerHelper ;
    }


    public function init(array $config_list=[]){

        self::$config_list=$config_list;

        //临时代码，灰度发布生产测试，全部上后可以去掉此代码
//        if(empty(env('JAEGER_SERVICE_NAME')))return false;

        //过滤黑名单
        foreach (self::BLACK_LIST as $v){
            if(strpos($this->getFullUrl(),$v) !== false)return false;
        }

        if(isset($config_list['jaeger_sampler_rate']) ){
            $jaeger_sampler_rate = $config_list['jaeger_sampler_rate'];
        }else{
            $jaeger_sampler_rate=env('JAEGER_SAMPLER_RATE',0.1);
            if(env("APP_ENV")=='pro'){
                //生产概率先预减
//            if(mt_rand(1,100)==1)return false;
                //生产太多项目了，直接写死代码部署更改方便点
                $jaeger_sampler_rate = 0.1;
            }
        }



        //采集率方式
        $sampler=[
            'type' => \Jaeger\SAMPLER_TYPE_PROBABILISTIC,
            'param' => (float)$jaeger_sampler_rate ,
        ];

        // 初始化全局配置(可以放到框架启动的时候执行)
        $config = new Config(
            [
                'sampler'       => $sampler,
                'logging'       => true,
                // tags记录需要记录的参数有哪些，自定义
                "tags"          => [
                    'http.url'         => $_SERVER["SERVER_NAME"]??'',
                    'http.method'      => $_SERVER['REQUEST_METHOD']??'',
//                    'http.status_code' => 'http.status_code',
                    'http.status_code' => 200,
//                    'db.query'      => 'db.query',
//                    'db.statement'  => 'db.statement',
//                    'db.query_time' => 'db.query_time',

                    'path'   => $this->getFullUrl(),
                    'method' => $_SERVER['REQUEST_METHOD']??'',

//                    'status_code' => 'response.status_code',
                ],
                // jaeger的地址，这个需要你提交搭建好jaeger服务
                "local_agent"   => [
                    "reporting_host" => env('JAEGER_HOST','*************'),
//                    "reporting_port" => 6832
                    "reporting_port" => self::PORT
                ],
                // 使用udp协议传输数据
                'dispatch_mode' => Config::JAEGER_OVER_BINARY_UDP,
//                'dispatch_mode' => Config::JAEGER_OVER_BINARY_HTTP,
            ],
            // 服务的名字，比如订单中心
            $config_list['jaeger_service_name']??env('JAEGER_SERVICE_NAME','other_service')
        );

        self::$start_time = self::getMillisecond();

        // 初始化配置
        $config->initializeTracer();
        // 全局的trancer对象，这是一个单例对象
        self::$tracer = $tracer = GlobalTracer::get();
        // 创建一个根时间分段,startActiveSpan第一个参数是自定义当前操作的名称,这里定义成
        // request，代表一个请求，注意scope的代码结构，是一个包含的关系
        // 每一个span对应一个start和一个close，它们是成对出现。

        self::$init_scope = $scope = $tracer->startActiveSpan($this->getFullUrl());

        // log记录一些大的参数,比如请求的json
        $scope->getSpan()->log([
            'header' => $this->getAllHeader(),
            'get'=>$_GET,
            'post'=>$_POST,
        ]);

    }

    /**
     * 指定活跃模块
     * @param string $active_spans
     * @return void
     */
    public function startActiveSpan(string $active_spans, $options = []){
        if(!empty(self::$tracer)){
            // 创建$nestedSpanScope1的子分段
            self::$nestedSpanScope = self::$tracer->startActiveSpan($active_spans, $options);
        }
    }

    /**
     * 手动设置span开始时间
     * @param string $operationName
     * @param array $options
     * @return void
     */
    public function startSpan(string $operationName, array $options = []){
        if(!empty(self::$tracer)){
            self::$tracer->startSpan($operationName,$options);
        }
    }


    /**
     * 设置tags，一维数组，key为tag名，value为tag内容
     * @param array $params
     * @return void
     */
    public function setTags(array $params){
        if(!empty(self::$tracer)){
            foreach ($params as $k=>$v){
                self::$nestedSpanScope->getSpan()->setTag($k, $v);
            }
        }
    }


    /**
     * 设置日志内容，一维数组，key为tag名，value为tag内容
     * @param array $params
     * @return void
     */
    public function log(array $params){
        if(!empty(self::$tracer)){
            if(empty(self::$nestedSpanScope)){
                self::$nestedSpanScope=self::$tracer->startActiveSpan(self::ACTIVE_SPANS['other']);
            }

            self::$nestedSpanScope->getSpan()->log($params);
            self::$nestedSpanScope->close();
        }

    }

    /**
     * 关闭span区间
     * @return void
     */
    public function close(string $active_spans=''){
        if(!empty(self::$tracer)){
            if(empty($active_spans)){
                self::$nestedSpanScope->close();
            }else{
                self::$tracer->startActiveSpan($active_spans)->close();
            }
        }
    }


    /**
     * 数据推送到远程
     * @return void
     */
    public function flush(){

        if(!empty(self::$tracer)){
//            self::$nestedSpanScope->close();
            if((self::$is_error || (self::getMillisecond() - self::$start_time)>=(self::$config_list['jaeger_min_gathering_time']??env('JAEGER_MIN_GATHERING_TIME',300))) ){


                if(isset(self::$config_list['is_pre_reduce']) && !self::$config_list['is_pre_reduce'] ){ //如果有设置不预减的话

                }elseif (env("APP_ENV")=='pro'){
                    //生产概率先预减
                    if(mt_rand(1,100)<99)return ;
                }

                self::$init_scope->close();
                self::$tracer->flush();
            }

        }
    }

    /**
     * 获取全部头部信息
     * @return array
     */
    public function getAllHeader()
    {
        // 忽略获取的header数据。这个函数后面会用到。主要是起过滤作用
        $ignore = array('host','accept','content-length','content-type');    $headers = array();
        //这里大家有兴趣的话，可以打印一下。会出来很多的header头信息。
        //咱们想要的部分，都是‘http_'开头的。所以下面会进行过滤输出。
        /*    var_dump($_SERVER);
       exit;*/
        foreach($_SERVER as $key=>$value){      if(substr($key, 0, 5)==='HTTP_'){
            //这里取到的都是'http_'开头的数据。
            //前去开头的前5位
            $key = substr($key, 5);        //把$key中的'_'下划线都替换为空字符串
            $key = str_replace('_', ' ', $key);        //再把$key中的空字符串替换成‘-’
            $key = str_replace(' ', '-', $key);        //把$key中的所有字符转换为小写
            $key = strtolower($key);    //这里主要是过滤上面写的$ignore数组中的数据
            if(!in_array($key, $ignore)){          $headers[$key] = $value;
            }
        }
        }//输出获取到的header
        return $headers;
    }

    /**
     * 获取完整范围url
     * @return string
     */
    private function getFullUrl(){

        $sys_protocal = isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == '443' ? 'https://' : 'http://';
        $php_self = $_SERVER['PHP_SELF'] ? $_SERVER['PHP_SELF'] : $_SERVER['SCRIPT_NAME'];
        $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
        $relate_url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : $php_self . (isset($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : $path_info);
        return $sys_protocal . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '') . $relate_url;
    }

    /**
     * 获取毫秒数
     * @return float
     */
    public static function getMillisecond(){
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
    }
}
