<?php

namespace app\Helper;

/**
 * 参数监控助手类
 */
class ParamsMonitorHelper extends BaseHelper
{
    /**
     * 参数监控
     * @return void
     */
    public static function paramsMonitor(array $params){

        if(!empty($params['get']) || !empty($params['post']) ){
            $params_monitor_strings = sysconf('params_monitor_strings');
            $string_list = explode(',',$params_monitor_strings);
            if(!empty($params_monitor_strings)){

                $route = request()->module().'/'.request()->controller() . '/' . request()->action();
                $params_json = json_encode($params,JSON_UNESCAPED_UNICODE);
                $params_base64 = base64_encode($params_json);

//                dump($params_json);die();

                $is_suspicion=false;
                foreach ($string_list as $v){

                    if($v=='script'){
                        foreach (['<','&lt;'] as $v1){
                            if(strpos(str_replace(' ','',strtolower($params_json)), $v1.$v)){
                                $is_suspicion=true;
                            }
                        }
                    }elseif(strpos($params_json, $v)){
                        $is_suspicion=true;
                    }
                }

                if($is_suspicion){
                    //发送的特殊字符处理不完美，直接改成打日志的方式
                    commonLog("疑似接口非法传参",['route'=>$route,'params'=>$params],__FUNCTION__);

//                    TelegramBot::sendMessageTextEscape(sysconf('business_note_chat_id'),TelegramBot::makePHPError([
//                        'from' =>sysconf('app_name'),
//                        'subject' => '疑似接口非法传参',
//                        'date' => date('Y-m-d H:i:s'),
//                        'content' =>
//                            <<<text
//
//                                当前嫌疑列表：$params_monitor_strings
//                                route : {$route}
//                                params_base64 : {$params_base64}
//                                params : {$params_json}
//                            text,
//                    ]));
                }
            }

        }

    }

}