<?php

namespace app\Helper;

/**
 * 静态变量文件类（临时解决项目没有类但需要静态变量存储的问题）
 */
class StaticHelper
{

    //配置表静态变量
    public static $source_config=[
        'data'=>[],
        'time'=>0, //时间戳，防止旧项目常驻任务永不更新
    ];

    //配置表2静态变量
    public static $system_config=[
        'data'=>[],
        'time'=>0, //时间戳，防止旧项目常驻任务永不更新
    ];

    //配置表3静态变量
    public static $business_config=[
        'data'=>[],
        'time'=>0, //时间戳，防止旧项目常驻任务永不更新
    ];



}
