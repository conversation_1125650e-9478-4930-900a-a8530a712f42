<?php
/******************************************************************************
 * Copyright (c) Archer 2022.                                                 *
 ******************************************************************************/

namespace app\Helper;

use Godruoy<PERSON>\Snowflake\Snowflake;
use think\helper\Str;
use think\Paginator;

final class StrHelper extends Str
{
    /**
     * 未检查此雪花ID生成器是否能在超高并发多进程下保持唯一。但我猜他们达不到这个量级
     *
     * @var null
     */
    private static $snowFlake = null;

    /**
     * @return string
     * @throws \Exception
     */
    public static function sfOrderId(): string
    {
        if (!self::$snowFlake) {
//            self::$snowFlake = new Snowflake(env('DATACENTER_ID'), env('WORKER_ID'));
            //由于目前生产环境走k8s，无法知道请求的是哪个服务器，所以这里变通边，搞个随机数来减少生成的id的可能性
            self::$snowFlake = new Snowflake(random_int(10000,99999), random_int(10000,99999));
            try {
                self::$snowFlake->setStartTimeStamp(strtotime('2022-10-10') * 1000);
            } catch (\Throwable $e) {
                // 不需要做任何事，忽略掉就可以了
            }
        }

        return self::$snowFlake->id();
    }

    /**
     * @return string
     */
    public static function snOrderId(): string
    {
        $ts = gettimeofday();

        return date('YmdHis', $ts['sec']).$ts['usec'];
    }

    /**
     * @param  string  $contents
     *
     * @return array
     */
    public static function jsonDecode(string $contents): array
    {
        return \json_decode(
            $contents,
            true,
            512,
            JSON_OBJECT_AS_ARRAY | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES,
        );
    }

    /**
     * @param  string  $timeRangeStr
     *
     * @return array|null
     */
    public static function parseDateTimeRangeStr(string $timeRangeStr): ?array
    {
        if (!strpos($timeRangeStr, ' - ')) {
            return null;
        }

        return explode(' - ', $timeRangeStr);
    }

    /**
     * 自定义分页的样式，只是处理这框架分页的临时用法
     *
     * @param  Paginator  $paginator
     *
     * @return string
     */
    public static function genPaginationHtml(Paginator $paginator): string
    {
        $html = $paginator->render();
        $html = str_ireplace('<li>', '<li class="page-item">', $html);
        $html = str_ireplace('<a href="', '<a class="page-link" href="', $html);
        $html = str_ireplace('<span', '<span class="page-link" ', $html);
        $html = str_ireplace('<li class="active">', '<li class="page-item active" aria-current="page">', $html);

        return str_ireplace('<li class="disabled">', '<li class="page-item disabled">', $html);
    }

    public static function getErrText(\Throwable $e): string
    {
        return sprintf(
            "msg : %s \r\n file : %s \r\n Line : %d \r\n Trace : %s",
            $e->getMessage(), $e->getFile(), $e->getLine(), $e->getTraceAsString(),
        );
    }

    public static function toCents(int $amount, bool $flip = false): int
    {
        return false === $flip ? $amount * 100 : $amount / 100;
    }

    public static function toSecretStr(string $str, int $offset = 0, int $length = 6): string
    {
        return substr($str, $offset, $length).str_repeat('*', strlen($str) - $length);
    }

    public static function colsArrToStr($select): string
    {
        if ($select === '*' || $select === ['*']) {
            return '*';
        }

        return '`'.implode('`,`', $select).'`';
    }
}
