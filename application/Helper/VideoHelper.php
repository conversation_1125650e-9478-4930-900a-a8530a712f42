<?php

namespace app\Helper;

class VideoHelper
{
    public function __m3u8Raw($videoId, $mapServers, $hlsInfo)
    {
        $serverFlag = $hlsInfo['server_flag'];
        $serverInfo = !empty($mapServers[$serverFlag]) ? $mapServers[$serverFlag] : [];
        if (!empty($serverInfo['url_prefix'])) {
            $m3u8Raw = (new VideoHelper())->rebuildM3u8RawLinks($videoId, $hlsInfo['m3u8_uri'], $serverInfo['url_prefix'], $hlsInfo['key']);
            header('Access-Control-Allow-Origin: *');
            header("Content-Type: video/vnd.mpegurl");

            return $m3u8Raw;
        }

    }


    /**
     * 重建m3u8内容，追加目录.
     * @param string $videoId 视频ID
     * @param string $m3u8Uri m3u8 uri连接.
     * @param string $urlPrefix 服务器URL前缀.
     * @param string $keyRaw 服务器URL前缀.
     * @return string|string[]|null
     */
    public function rebuildM3u8RawLinks($videoId, $m3u8Uri, $urlPrefix, $keyRaw = '',$url = 'movie/m3u8raw')
    {

        $cdnBaidu = new \app\extend\Cdn\Tencent();
        $tsBasePath = rtrim(dirname($m3u8Uri), '/');

        //线上由于陌陌太多，直接写死桶地址访问
        if(env('APP_ENV') == 'pro'){
            $m3u8Url = 'https://mm-pro.s3.ap-southeast-1.amazonaws.com' . $m3u8Uri;
        }else{
            $m3u8Url = $urlPrefix . $m3u8Uri;
        }

        //m3u8类型1点播视频 2ai视频
        $m3u8_type = 1;
        if(strpos($m3u8Uri, '.ai.m3u8')){
            $m3u8_type = 2;
            $querySign = $cdnBaidu->getAiSignKeyValue($m3u8Uri);
        }else{
            $querySign = $cdnBaidu->getSignKeyValue($m3u8Uri);
        }


        $cdnM3u8Url = $m3u8Url . '?'. $querySign;
        $content = \Amzn\M3u8Cache::getAndSave($cdnM3u8Url);

        if (empty($content)) {
            return $content;
        }

        $pattern = "/\#EXTINF:([0-9\.]+),\n(.+)\.ts/i";
        $content = preg_replace_callback($pattern, function ($matches) use ($urlPrefix, $cdnBaidu, $tsBasePath,$m3u8_type) {
            $relativeTsPath = $tsBasePath . '/' . $matches[2] . '.ts';
            if($m3u8_type==2){
                $querySign = $cdnBaidu->getAiSignKeyValue($relativeTsPath);
            }else{
                $querySign = $cdnBaidu->getSignKeyValue($relativeTsPath);
            }


            $tsUrl = $urlPrefix . $relativeTsPath . '?'. $querySign;
            $result = "#EXTINF:{$matches[1]},\n{$tsUrl}";
            return $result;
        }, $content);
        $replacement = "#EXTINF:$1,\n{$urlPrefix}$2.ts";
        $pattern2 = "/\#EXT-X-KEY:METHOD=AES-128,URI=\"(.+)\"/i";
//        $keyUrl = url($url,['video_id' => $videoId, 'key_raw' => $keyRaw], true, true);

        $keyUrlTemp = $tsBasePath.'/'.'key.key';
        if($m3u8_type==2){
            $keySign = $cdnBaidu->getAiSignKeyValue($keyUrlTemp);
        }else{
            $keySign = $cdnBaidu->getSignKeyValue($keyUrlTemp);
        }
        $keyUrl = $urlPrefix .$keyUrlTemp.'?'.$keySign;

        $replacement2 = "#EXT-X-KEY:METHOD=AES-128,URI=\"{$keyUrl}\"";
        $content = preg_replace([$pattern2],[$replacement2], $content, -1, $count);

        return trim($content);
    }

}
