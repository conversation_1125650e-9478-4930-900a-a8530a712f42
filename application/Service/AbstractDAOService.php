<?php

/******************************************************************************
 * Copyright (c) Archer 2022.                                                 *
 ******************************************************************************/

declare(strict_types=1);

namespace app\Service;

use app\common\BizConst;
use app\Helper\StrHelper;
use think\Db;
use think\Paginator;

abstract class AbstractDAOService
{
    public function getFullTableName(): string
    {
        return \think\facade\Config::get('database.prefix').$this->getTableName();
    }

    public function getTableName(): string
    {
        return strtolower(StrHelper::snake(str_ireplace('Service', '', class_basename(get_called_class()))));
    }

    public function getPaginator(array $conditions = null, int $page = BizConst::PAGE_DEFAULT_NUM, int $limit = BizConst::PAGE_DEFAULT_ROWS, array $fields = null,array $extra_where=[]): Paginator
    {
        $select  = $fields ?? ['*'];
        $builder = Db::name($this->getTableName())->field($select)->order('id', 'desc');
        if(!empty($extra_where))$builder=$builder->where($extra_where);

        if (!$conditions) {

            return $builder->paginate($limit, false, ['page' => $page, 'list_rows' => $limit]);
        }

        foreach ($conditions as $col => $val) {
            if ($col === 'start_time') {
                $builder = $builder->where('created_at', '>=', $val);
                continue;
            }
            if ($col === 'end_time') {
                $builder = $builder->where('created_at', '<=', $val);
                continue;
            }

            if ($col === 'channelids') {
                $channelids = explode(',',$val);
                foreach ($channelids as &$v){
                    $v=trim($v);
                }
                $builder = $builder->where('channelid', 'IN', $channelids);
                continue;
            }
            $builder = $builder->where($col, '=', $val);
        }

        return $builder->paginate($limit, false, ['page' => $page, 'list_rows' => $limit]);
    }

    public function add(array $item): int
    {
        return (int) Db::name($this->getTableName())->insert($item, false, true);
    }

    public function updateById(int $id, array $item): bool
    {
        return (int) Db::name($this->getTableName())->where('id', '=', $id)->update($item) > 0;
    }

    protected function queryStat(string $sql, array $binds, bool $onlyOne = false): ?array
    {
        $r = Db::query($sql, $binds);
        return empty($r) ? null : (false === $onlyOne ? $r : $r[0]);
    }
}
