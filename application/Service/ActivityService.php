<?php

namespace app\Service;


//活动service类
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

class ActivityService  extends BaseService {

    //记录类型 1、聚划算
    const TYPE_JUHUASUAN=[1,'聚划算'];


    /**
     * 自动组装用户购买记录字段（目前不适用整点秒杀活动，整点秒杀活动请勿调用此判断方法）
     * @param array $list 列表数据
     * @param int $type 记录类型 1、聚划算
     * @param int $member_id 用户id
     * @param string $field 活动id在$list中的字段名
     * @return void
     */
    public static function getMemberBuyRecord(array &$list ,int $type, int $member_id,string $field='id'){
        if(!empty($list)){
            $buy_record_column = Db::name('activity_buy_record')->whereIn('activity_id',array_column($list,$field))->where(['type'=>$type,'member_id'=>$member_id])->column('id','activity_id');
            foreach ($list as &$v){
                $v['is_buy']=!empty($buy_record_column[$v[$field]])?1:0;
            }
        }
    }

    /**
     * 是否购买过该活动 （目前不适用整点秒杀活动，整点秒杀活动请勿调用此判断方法）
     * @param int $type
     * @param int $activity_id
     * @param int $member_id
     * @return int
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public static function isBuy(int $type , int $activity_id , int $member_id){
        $is_buy=0;
        $where=[
            'type'=>$type,
            'activity_id'=>$activity_id,
            'member_id'=>$member_id,
        ];
        if(!empty(Db::name('activity_buy_record')->where($where)->field("id")->find())){
            $is_buy = 1;
        }

        return $is_buy;
    }


}