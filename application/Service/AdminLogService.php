<?php

namespace app\Service;


//后台操作日志service类
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

class AdminLogService  extends BaseService {

    public static function addlog($title,$content,$admin_id,$admin,$ip,$time){
        $data = [];
        $data['admin_id'] = $admin_id;
        $data['admin'] = $admin;
        $data['content'] = $content;
        $data['title'] = $title;
        $data['query_url'] = curPageURL();
        $data['time'] = $time;
        $data['ip'] = $ip;
        Db::name('admin_log')->insert($data);
    }


}
