<?php

namespace app\Service;


//ai订单支付service类
use think\Db;

class AiOrderService extends BaseService {

    /** @var array 订单状态 1:待处理 2:处理中 3:重试失败 4:失败 5:成功 */
    const STATUS_PENDING=[1,'待处理'];
    const STATUS_PROCESSING=[2,'处理中'];
    const STATUS_RETRY=[3,'重试失败'];
    const STATUS_FAILURE=[4,'失败'];
    const STATUS_SUCCESS=[5,'成功'];


    /** @var array[] 前端展示订单状态列表 1处理中 2生成成功 3生成失败 */
    const FRONT_STATUS_PENDING = [1,'处理中'];
    const FRONT_STATUS_FAILURE = [2,'生成失败'];
    const FRONT_STATUS_SUCCESS = [3,'生成成功'];
    const FRONT_SHOW_STATUS_LIST = [
        self::FRONT_STATUS_PENDING[0]=>[self::STATUS_PENDING[0],self::STATUS_PROCESSING[0],self::STATUS_RETRY[0]],
        self::FRONT_STATUS_FAILURE[0]=>[self::STATUS_FAILURE[0]],
        self::FRONT_STATUS_SUCCESS[0]=>[self::STATUS_SUCCESS[0]],
    ];


    /** @var array 消费类型  */
    const CONSUMPTION_TYPE_FREE = [1,'免费次数']; //包括除VIP卡次数外的其他免费次数
    const CONSUMPTION_TYPE_PAY = [2,'付费订单']; //仅限金币付费
    const CONSUMPTION_TYPE_VIP = [3,'VIP卡次数'];



    /** @var array 次数消耗来源类型 */
    const CONSUME_TYPE_FREE = [1,'开通会员赠送次数']; //仅限后台开通会员赠送次数
    const CONSUME_TYPE_PAY = [2,'付费订单'];
    const CONSUME_TYPE_VIP = [3,'有次数限制VIP卡次数'];
    const CONSUME_TYPE_PROMOTION = [4,'无期限限制的免费次数']; //由于历史原因，这里这个值通用为无期限限制的免费次数

    const CONSUME_TYPE_VIP_UNLIMIT = [5,'无次数限制VIP卡次数'];


    /** @var array 前端是否隐藏资源 0否 1是 */
    const IS_HIDE_FRONT_NO = [0,'否'];
    const IS_HIDE_FRONT_YES = [1,'是'];


    public function add(array $data){
        return Db::name('ai_order')->insertGetId($data);
    }

    public function del(int $id){
        return Db::name('ai_order')->where(['id'=>$id])->delete();
    }


}
