<?php

namespace app\Service;


//AI创作service类
use app\api\controller\Comm;
use app\common\Constants\LogConst;
use app\common\Constants\RedisConst;
use app\common\exception\ApiBusinessException;
use app\extend\Telegram\TelegramBot;
use app\Helper\BoyuHelper;
use app\Helper\CodeHelper;
use GuzzleHttp\Client;
use think\Db;

class AiService extends BaseService {

    /** @var array 	ai类型 1：去衣 2：换脸 3：视频 4：自定义换脸 */
    const AI_TYPE_REMOVE_CLOTHES=[1,'图片去衣'];
    const AI_TYPE_CHANGE_FACE=[2,'图片换脸'];
    const AI_TYPE_MOVIE=[3,'视频换脸'];
    const AI_TYPE_DIY_CHANGE_FACE=[4,'自定义换脸'];
    const AI_TYPE_DRAW=[5,'AI绘画'];

    const SUCCESS_CODE = 0;

    //请求java的路由列表
    const ROUTE_LIST = [
        'token'=>'/ai/authapi/member/v1/login',
        'config'=>'/ai/authapi/config/v1',
        'template'=>'/ai/authapi/template/v1/%d', //
        'clothing'=>'/ai/authapi/order/v1/clothing', //AI去衣
        'customize_face'=>'/ai/authapi/order/v1/customize_face', //AI自定义换脸
        'face'=>'/ai/authapi/order/v1/face',  //AI换脸
        'video'=>'/ai/authapi/order/v1/video',  //AI视频
        'draw'=>'/ai/authapi/order/v1/draw',  //AI绘画
        'into'=>'/ai/authapi/member/v1/trans_in',  //转入金额
        'out'=>'/ai/authapi/member/v1/trans_out_all',  //转出金额
        'sync_vip'=>'/ai/authapi/member/v1/sync_vip',  //同步VIP数据
        'domains'=>'/ai/authapi/common/v1/domains',  //获取ai前端访问域名接口
    ];

    /**
     * 第三方响应吗，改为输出友好提示的响应吗
     */
    const FRIENDLY_CODE_LIST = [4001,9008,9009,9010,9011,9012,9013,9016];

    //和ai那边的类型区别
    const AI_DEVICE_TYPE_MAPS = [
        'I'=>1,
        'A'=>2,
        'H'=>3,
        'P'=>4,
        'IH'=>5,
    ];

    private $aiOrderService;
    /**
     * @var mixed
     */
    private $user_id;
    private $userinfo;

    private $member_info_extra_detail;

    public function __construct($userinfo=[])
    {
        if(empty($userinfo)){
            $userinfo=(new Comm())->userinfo;
        }
        $this->userinfo = $userinfo;
        $this->user_id = $this->userinfo['id'];
        $this->aiOrderService = (new AiOrderService());
    }

    public static function getCallbackUrl():string
    {
        return reqHost().'/api/v2/ai/order_callback';
//        return ((int)$_SERVER['SERVER_PORT'] == 443? 'https': 'http').'://'.$_SERVER['HTTP_HOST'].'/api/v2/ai/order_callback';
    }


    /**
     * 下单前检查是否有资格下单
     * @return array
     */
    public  function checkPayBefore(string $goods_amount , int $ai_type){

//        $free_count=0;

        $this->member_info_extra_detail = $member_info_extra_detail = MemberInfoExtraService::getDetail(intval($this->userinfo['id']));
        if(isset($member_info_extra_detail['unlimit_ai_vip_time']) && $member_info_extra_detail['unlimit_ai_vip_time']>date('Y-m-d H:i:s')){
            return ['consumption_type'=>AiOrderService::CONSUMPTION_TYPE_VIP[0],'consume_type'=>AiOrderService::CONSUME_TYPE_VIP_UNLIMIT[0]];
        }

        $free_count = $this->getVipCount($ai_type,intval($this->userinfo['id']));
        $result = ['consumption_type'=>AiOrderService::CONSUMPTION_TYPE_VIP[0],'consume_type'=>AiOrderService::CONSUME_TYPE_VIP[0]];

//        if($free_count<=0 && $ai_type==AiService::AI_TYPE_REMOVE_CLOTHES[0]){
//            $free_count = $this->surplusFreeCountGiveCount();
//            $result = ['consumption_type'=>AiOrderService::CONSUMPTION_TYPE_FREE[0],'consume_type'=>AiOrderService::CONSUME_TYPE_FREE[0]];
//        }

        if($free_count<=0){
            $free_count = $this->surplusFreeCountConsumption($ai_type,intval($this->userinfo['id']));
            $result = ['consumption_type'=>AiOrderService::CONSUMPTION_TYPE_FREE[0],'consume_type'=>AiOrderService::CONSUME_TYPE_PROMOTION[0]];
        }

        if($free_count<=0){
            $amount = db("member_amount")->where(array('member_id'=>$this->userinfo['id']))->value('amount');
            if ($amount < $goods_amount*100) {
                json_success("金币余额不足",['movie_code'=>1]);
            }
            $result = ['consumption_type'=>AiOrderService::CONSUMPTION_TYPE_PAY[0],'consume_type'=>AiOrderService::CONSUME_TYPE_PAY[0]];
        }

        return $result ;

    }


    /**
     * @throws ApiBusinessException
     */
    public function addOrder(string $order_no, array $check_pay_result,array $third_result, string $price,int $ai_type,string $original_picture_url='',string $extra_data=''){

        try {
            Db::startTrans();
            $this->aiOrderService->add([
                'order_no'=>$order_no,
                'member_id'=>$this->user_id,
                'consumption_type'=>$check_pay_result['consumption_type'],
                'consume_type'=>$check_pay_result['consume_type'],
                'ai_type'=>$ai_type,
                'price'=>$price,
                'third_order_no'=>$third_result['orderNo'],
                'status'=>$third_result['orderStatus'],
                'original_picture_url'=>$original_picture_url,
                'extra_data'=>$extra_data,
                'channelid'=>$this->userinfo['channelid']??'',
            ]);
            switch ($ai_type){
                case AiService::AI_TYPE_REMOVE_CLOTHES[0]:
                    $field_name = 'ai_clothing_count';
                    $field_name_unlimit = 'ai_clothing_promotion_count';
                    break;
                case AiService::AI_TYPE_CHANGE_FACE[0]:
                    $field_name = 'change_face_count';
                    $field_name_unlimit = 'change_face_count_unlimit';
                    break;
                case AiService::AI_TYPE_MOVIE[0]:
                    $field_name = 'type_movie_count';
                    $field_name_unlimit = 'type_movie_count_unlimit';
                    break;
                case AiService::AI_TYPE_DIY_CHANGE_FACE[0]:
                    $field_name = 'ai_type_diy_change_count';
                    $field_name_unlimit = 'ai_type_diy_change_count_unlimit';
                    break;
                case AiService::AI_TYPE_DRAW[0]:
                    $field_name = 'ai_type_draw_count';
                    $field_name_unlimit = 'ai_type_draw_count_unlimit';
                    break;
            }


            switch ($check_pay_result['consume_type']){
                case AiOrderService::CONSUME_TYPE_PAY[0]:
                    //操作余额
                    (new \app\admin\model\Trad())->addTrad($this->user_id,$price*100,$order_no,TTYPE_TZ_OK,TTYPE_TZ_OK_AI);
                    break;
                case AiOrderService::CONSUME_TYPE_VIP[0]:
                    if(!empty($field_name)){
                        MemberInfoExtraService::changeValue(intval($this->user_id),$field_name,2,1);
                    }
                    break;
                case AiOrderService::CONSUME_TYPE_PROMOTION[0]:
                    if(!empty($field_name_unlimit)){
                        MemberInfoExtraService::changeValue(intval($this->user_id),$field_name_unlimit,2,1);
                    }
                    break;
            }

            Db::commit();
        }catch (\Throwable $e){
            Db::rollback();
            commonLog(LogConst::AI_LOG[1],['error'=>$e->getTraceAsString()],LogConst::AI_LOG[0]);
            throw new ApiBusinessException($e->getMessage(),$e->getCode());
//            return false;
        }
        return true;

    }

    /**
     * 获取剩余有效免费次数(仅针对后台vip和svip开通赠送的免费次数)
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
//    public function surplusFreeCountGiveCount():int
//    {
//        $equity_basic_detail = Db::name('equity_basic')->where('id','=',1)->cache(60)->field("ai_remove_clothes_vip,ai_remove_clothes_svip")->find();
//
//        $free_count = 0;
//        if (!($this->userinfo['is_super_vip']==1 || $this->userinfo['is_vip']==1)) {
//            return 0;
//        }
//        if($this->userinfo['is_super_vip']==1){
//            $free_count+=$equity_basic_detail['ai_remove_clothes_svip']??0;
//        }
//        if($this->userinfo['is_vip']==1){
//            $free_count+=$equity_basic_detail['ai_remove_clothes_vip']??0;
//        }
//
//        $success_order_count = Db::name('ai_order')->where('member_id', $this->userinfo['id'])->where('status', '<>', AiOrderService::STATUS_FAILURE[0])->where('consumption_type', AiOrderService::CONSUMPTION_TYPE_FREE[0])->count();
//
//        $surplus_free_count = $free_count-$success_order_count;
//
//        return max($surplus_free_count, 0);
//    }

    /**
     * 获取剩余有效免费次数(无时间限制)
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function surplusFreeCountConsumption(int $ai_type,int $member_id):int
    {
        $free_count=0;
        switch ($ai_type){
            case AiService::AI_TYPE_REMOVE_CLOTHES[0]:
                $free_count = MemberInfoExtraService::getDetail($member_id)['ai_clothing_promotion_count']??0;
                break;
            case AiService::AI_TYPE_CHANGE_FACE[0]:
                $free_count = MemberInfoExtraService::getDetail($member_id)['change_face_count_unlimit']??0;
                break;
            case AiService::AI_TYPE_MOVIE[0]:
                $free_count = MemberInfoExtraService::getDetail($member_id)['type_movie_count_unlimit']??0;
                break;
            case AiService::AI_TYPE_DIY_CHANGE_FACE[0]:
                $free_count = MemberInfoExtraService::getDetail($member_id)['ai_type_diy_change_count_unlimit']??0;
                break;
            case AiService::AI_TYPE_DRAW[0]:
                $free_count = MemberInfoExtraService::getDetail($member_id)['ai_type_draw_count_unlimit']??0;
                break;
        }

        return $free_count;
    }



    /**
     * 获取vip卡的ai可用次数
     */
    private function getVipCount(int $ai_type,int $member_id){

//        if (!($this->userinfo['is_super_vip']==1 || $this->userinfo['is_vip']==1)) {
//            return 0;
//        }

        if(!empty($this->member_info_extra_detail)){
            $member_info_extra_detail=$this->member_info_extra_detail;
        }else{
            $member_info_extra_detail = MemberInfoExtraService::getDetail($member_id);
        }

        //这里有个特殊情况，以前是和视频vip时间挂钩的，现在换成ai独立有效时间，需要兼容旧用户，如果以后的代码需求和这句判断冲突的话，直接注释掉这个判断逻辑2024-02-23
//        if(empty($member_info_extra_detail['ai_vip_time']) && (!empty($member_info_extra_detail['ai_clothing_count']) || empty($member_info_extra_detail['change_face_count']) || empty($member_info_extra_detail['type_movie_count']) || empty($member_info_extra_detail['ai_type_diy_change_count']))){
//            if (!($this->userinfo['is_super_vip']==1 || $this->userinfo['is_vip']==1)) {
////                    $userinfo['expire_time']
//                $member_vip_time = max($this->userinfo['expire_time'],$this->userinfo['super_vip_expire_time']);
//                MemberInfoExtraService::changeValue($member_id,'ai_vip_time',3,intval($member_vip_time));
//                $member_info_extra_detail = MemberInfoExtraService::getDetail($member_id);
//            }
//        }

        if($member_info_extra_detail['ai_vip_time']<date('Y-m-d H:i:s')){
            return 0;
        }

        switch ($ai_type){
            case AiService::AI_TYPE_REMOVE_CLOTHES[0]:
                $free_count=$member_info_extra_detail['ai_clothing_count']??0;
                break;
            case AiService::AI_TYPE_CHANGE_FACE[0]:
                $free_count=$member_info_extra_detail['change_face_count']??0;
                break;
            case AiService::AI_TYPE_MOVIE[0]:
                $free_count=$member_info_extra_detail['type_movie_count']??0;
                break;
            case AiService::AI_TYPE_DIY_CHANGE_FACE[0]:
                $free_count=$member_info_extra_detail['ai_type_diy_change_count']??0;
                break;
            case AiService::AI_TYPE_DRAW[0]:
                $free_count=$member_info_extra_detail['ai_type_draw_count']??0;
                break;
        }
        return $free_count??0;
    }

    /**
     * 获取剩余有效免费次数(除付费金币外的其他全部免费次数总累计)
     * $ai_type AI类型
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function surplusFreeCount(int $ai_type,int $member_id):int
    {
//        $surplusFreeCount = $this->surplusFreeCountGiveCount();
        $getVipCount = $this->getVipCount($ai_type,$member_id);
        $surplusFreeCountConsumption = $this->surplusFreeCountConsumption($ai_type,$member_id);

        return $getVipCount+$surplusFreeCountConsumption;
    }


    /**
     * 如果当前消耗的次数是vip卡次数的话，获取对应的卡信息
     * @return void
     */
    public function getVipData(int $member_id){
           $last_vip_detail=[];
           $vip_list = Db::name('vip')->where(['is_open_ai'=>1])->field("id,name")->select();
           if(!empty($vip_list)){
               $vip_list_column=[];
               foreach ($vip_list as $k=>$v){
                   $vip_list_column[$v['id']]=$v;
               }
               //这里有购买不同卡的情况，直接获取最后一次购买的ai卡即可
               $vip_detail = Db::name('member_vip')->whereIn('vip_id',array_column($vip_list,'id'))->where(['member_id'=>$member_id])->order("id desc")->field("vip_id")->find();
               if(!empty($vip_detail)){
                   $last_vip_detail =  $vip_list_column[$vip_detail['vip_id']]??[];
               }
           }
           return $last_vip_detail;
    }

    public function getToken(){

        $quest_data = [
            'deviceNo'=>$this->userinfo['device_id'] ?? '',
            'deviceType'=>self::AI_DEVICE_TYPE_MAPS[get_header_device_type()] ?? '',
            'ip'=>$this->userinfo['create_ip'] ?? '',
            'mobile'=>'',
            'type'=>$this->userinfo['is_visitor'] ?? '',
            'timestamp'=>getMillisecond(),
        ];
        $data = $this->commomRequest(self::ROUTE_LIST['token'] , $quest_data);
        return $data;
    }


    /**
     * 转出金额
     * @return array
     */
    public function out(string $order_no,string $amount){

        $quest_data = [
            'transNo'=>$order_no,
            'amount'=>$amount,
        ];
        return $this->commomRequest(self::ROUTE_LIST['into'] , $quest_data);
    }

    /**
     * 转入金额
     * @return array
     */
    public function into(string $order_no){

        $quest_data = [
            'transNo'=>$order_no,
        ];
        return $this->commomRequest(self::ROUTE_LIST['out'] , $quest_data);

    }


    /**
     * ai会员转移
     * @return array
     */
    public function transferVip(array $data){

        $quest_data = [
            'aiVipTime'=>$data['ai_vip_time']<"2024-06-14 00:00:00"?'':$data['ai_vip_time'],
            'clothingTimes'=>$data['ai_clothing_count'],
            'clothingUnlimitTimes'=>$data['ai_clothing_promotion_count'],
            'customizeFaceTimes'=>$data['ai_type_diy_change_count'],
            'customizeFaceUnlimitTimes'=>$data['ai_type_diy_change_count_unlimit'],
            'drawTimes'=>$data['ai_type_draw_count'],
            'drawUnlimitTimes'=>$data['ai_type_draw_count_unlimit'],
            'faceTimes'=>$data['change_face_count'],
            'faceUnlimitTimes'=>$data['change_face_count_unlimit'],
            'transNo'=>$data['order_no'],
            'unlimitAiVipTime'=>$data['unlimit_ai_vip_time']<"2024-06-14 00:00:00"?'':$data['unlimit_ai_vip_time'],
            'videoTimes'=>$data['type_movie_count'],
            'videoUnlimitTimes'=>$data['type_movie_count_unlimit'],
        ];

        if(empty($quest_data['aiVipTime']))unset($quest_data['aiVipTime']);
        if(empty($quest_data['unlimitAiVipTime']))unset($quest_data['unlimitAiVipTime']);

        return $this->commomRequest(self::ROUTE_LIST['sync_vip'] , $quest_data);

    }



    /**
     * 封装好的请求接口
     * @param string $url
     * @param array $origin_data
     * @return array
     * @throws ApiBusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \RedisException
     */
    public function commomRequest(string $url , array $origin_data){

        if(in_array($url,[self::ROUTE_LIST['into'],self::ROUTE_LIST['out']])){
            $timeout = 3;
        }else{
            $timeout = 10;
        }

        $this->client = new Client([
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            //跟域名
            'base_uri' => sysconf("ai_domain"),
            // 超时，可设置可不设置
            'timeout'  => $timeout, //由于java接口会有极少数情况5秒未响应，这里容忍度提高点10秒
        ]);

        return $this->request($url , $origin_data);
    }


    /**
     * 请求处理接口
     * @param string $url
     * @param array $origin_data
     * @return array
     * @throws ApiBusinessException|\GuzzleHttp\Exception\GuzzleException
     */
    private function request(string $url , array $origin_data)
    {

        try {

            if(empty($origin_data['sign'])){
                $origin_data['accessKey']=sysconf("ai_access_key");
                $origin_data['secretKey']=sysconf("ai_secret_key");
                $origin_data['account']=$this->userinfo['account'];
                $origin_data['channelCode']=$this->userinfo['channelid'] ?? '';
                $origin_data['platformCode']=get_order_prefix();
                $origin_data['timestamp']=getMillisecond();


                if(strpos($url,'/ai/authapi/template/v1/') !==false  || in_array($url,[ self::ROUTE_LIST['config'],self::ROUTE_LIST['domains']])
                ){
                    if(isset($origin_data['account']))unset($origin_data['account']);
                }

            }

            $sign_not_sec = self::signMsgsBefore($origin_data);
            $origin_data['sign'] = self::signMsgs($origin_data);
            if(isset($origin_data['secretKey']))unset($origin_data['secretKey']);

            $response = $this->client->request('POST', $url, [
                'json' =>$origin_data
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if($result['code']!=self::SUCCESS_CODE){
                $is_friendly=0;
//                $log_data = ['data'=>$origin_data??[],'result'=>$result??[],'sign_before'=>$sign_not_sec??''];
//                logInfo($log_data,'ai_http_request',$url, 'ai_http_request');
                if (in_array($result['code'],self::FRIENDLY_CODE_LIST)){
                    $is_friendly = 1;
                }
                $redis = redis();
                $redis_key = 'ai_error_count'; //指定时间内报错次数
                $error_count = $redis->get($redis_key);
                $tolerate_count=20;

                $not_note_list = array_merge([5004,9005,9021],self::FRIENDLY_CODE_LIST);
                if(!empty($error_count) && $error_count>$tolerate_count && !in_array($result['code'],$not_note_list)){
                    $send_content = [
                        'from' =>sysconf('app_name'),
                        'subject' => 'AI创作-java接口请求异常',
                        'date' => date('Y-m-d H:i:s'),
                        'content' =>'url:'.$url.',请求参数：'.json_encode($origin_data??[],JSON_UNESCAPED_UNICODE).'，java:返回数据：'.json_encode($result??[],JSON_UNESCAPED_UNICODE)
                    ];
                    TelegramBot::sendMessageTextMust($send_content);
                }elseif(!in_array($result['code'],$not_note_list)){
                    $redis->incr($redis_key);
                    $redis->expire($redis_key,60*60);
                }

                if($is_friendly==1){
                    throw new ApiBusinessException('功能升级中，请稍后再试',1);
                }

                throw new ApiBusinessException($result['msg'],$result['code']);
            }

        }catch (\Throwable $e) {
        } finally {

            if(!empty($e))$error_reason='error_code:'.$e->getCode().','.$e->getMessage().$e->getFile().$e->getLine();
            if(!in_array($url,[AiService::ROUTE_LIST['token'],AiService::ROUTE_LIST['out']]) || !empty($error_reason)){
                logInfo(['data'=>$origin_data??[],'result'=>$result??[],'error'=>$error_reason??'','sign_before'=>$sign_not_sec??''],'ai_http_request',$url, 'ai_http_request');
            }

            if (!empty($e) && !($e instanceof ApiBusinessException)) {

                $redis = redis();
                $redis_key = 'ai_error_count'; //指定时间内报错次数
                $error_count = $redis->get($redis_key);
                $tolerate_count=8;

                //java那边每次重启都服务都异常，搞得预警群吵死人了，这里宽容点这种情况就不报警了
                if($e->getCode()==404 && strpos($e->getMessage(),'404 Not Found')!==false){
                    $tolerate_count=30;
                }

                //超时波动允许一定次数内不报警，免得线上瞬时波动就老是报警
                if($e->getCode()==0 && strpos($e->getMessage(),'cURL error 28: Connection timed out after')!==false){
                    $tolerate_count=20;
                }

                if(!empty($error_count) && $error_count>$tolerate_count ){
                    $send_content = [
                        'from' =>sysconf('app_name'),
                        'subject' => 'AI创作-java接口请求异常',
                        'date' => date('Y-m-d H:i:s'),
                        'content' =>'url:'.$url.',error:'.$error_reason
                    ];
                    TelegramBot::sendMessageTextMust($send_content);
                }else{
                    $redis->incr($redis_key);
                    $redis->expire($redis_key,60*60);
                }
                throw new \Exception($e->getMessage(),$e->getCode());
            }elseif(!empty($e)){
                throw new ApiBusinessException($e->getMessage(),$e->getCode());
            }

        }

        return $result['data']??[];
    }


    /**
     * 接口sign签名
     * @param $data
     * @return string
     */
    static function signMsgs ($data){
        $msg = self::signMsgsBefore($data);
        return strtolower(md5( $msg ));  //签名生成
    }

    /**
     * MD5加密前签名
     * @param $data
     * @return string
     */
    static function signMsgsBefore ($data){
        $data = custom_http_build_query($data);
        ksort($data); //排序
        return urldecode(http_build_query($data));
    }






}
