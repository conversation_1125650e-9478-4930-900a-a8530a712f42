<?php

namespace app\Service;


use app\common\util\Netease;
use think\facade\Validate;

class AntiCheatingService extends BaseService {

    public function check(int $member_id,string $token,int $activity_id)
    {
        $detail_member = MemberInfoService::detail($member_id);

//        $inputs = input("post.");

//        $v = Validate::make([
//            'token' => 'require',
//            'activity_id' => 'require',
//        ], [
//            'token.require' => 'token必传',
//            'activity_id.require' => '活动的唯一标识必传',
//        ]);
//
//        if (!$v->check($inputs)) {
//            json_fail($v->getError());
//        }

        $params = [
            'token'=>$token,
            'account'=>$detail_member['account'],
            'ip'=>$detail_member['create_ip'],
            'phone'=>$detail_member['mobile'],
            'activityId'=>get_app_name().'-'.(Netease::ACTIVITY_ID_LIST[$activity_id]??''),
            'registerTime'=>$detail_member['create_time'],
            'registerIp'=>$detail_member['create_ip'],
            'nickname'=>$detail_member['nick_name'],
            'extData'=>json_encode(['registerChannel'=>$detail_member['device_type']],JSON_UNESCAPED_UNICODE),
        ];
        return (new Netease())->request($params);
    }

}