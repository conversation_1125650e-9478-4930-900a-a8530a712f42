<?php

namespace app\Service;


class AppAdvService {

    //广告链接来源
    const LINK_SOURCE_OTHER=1; //其他
    const LINK_SOURCE_BY=2;  //博鱼
    const LINK_SOURCE_TURNTABLE=3;  //转盘抽奖活动
    const LINK_SOURCE_BAISON=4;  //百胜棋牌
    const LINK_SOURCE_GAME=5;  //游戏页面（游戏大厅）
    const LINK_SOURCE_GAME_RECHARGE=6;  //游戏充值页面

//    const LIST_LINK_SOURCE = [
//        self::LINK_SOURCE_OTHER=>[0,'其他'],  //第一个值为是否需要将广告url替换成第二个值的中文，0否 1需要
//        self::LINK_SOURCE_BY=>[0,'博鱼'],
//        self::LINK_SOURCE_TURNTABLE=>[0,'转盘抽奖活动'],
//        self::LINK_SOURCE_BAISON=>[1,'百胜棋牌'],
//        self::LINK_SOURCE_GAME=>[1,'游戏大厅'],
//        self::LINK_SOURCE_GAME_RECHARGE=>[1,'游戏充值页面'],
//    ];

}
