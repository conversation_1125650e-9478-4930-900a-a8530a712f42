<?php

namespace app\Service;


//订单支付service类
use think\Db;

class AppPayOrderService extends BaseService {

    //业务类型 （默认值0是为了旧数据不乱）
    const BUSINESS_TYPE_GOLD=[1,'金币充值'];
    const BUSINESS_TYPE_VIP=[2,'开通vip'];


    //订单状态
    const STATUS_UNDONE=[1,'未支付'];
    const STATUS_SUCCESS=[0,'成功'];
    const STATUS_FAIL=[2,'失败'];


    //支付类型方式状态
    const PAY_TYPE_UNDONE=['-1','未知'];
    const PAY_TYPE_WECHAT=[1,'微信'];
    const PAY_TYPE_ALIPAY=[2,'支付宝'];
    const PAY_TYPE_BANK_CARD=[3,'银行卡'];
    const PAY_TYPE_USDT=[4,'USDT'];

    //支付传参类型
    const  PAY_PARAMS_TYPE_CHANNEL=['payid','支付方式']; //如蚂蚁支付通道
    const  PAY_PARAMS_TYPE_PAY_TYPE=['pay_type','支付类型']; //如微信，支付宝支付


    //订单溯源
    const PAGE_SOURCE_VIDEO = [1,'视频跳转'];
    const PAGE_SOURCE_VIP = [2,'视频会员入口'];
    const PAGE_SOURCE_AD_BANNER = [3,'广告banner'];
    const PAGE_SOURCE_WALLET = [4,'钱包入口'];
    const PAGE_SOURCE_AI = [5,'AI入口(现已弃用)'];
    const PAGE_SOURCE_AI_GOLD = [6,'AI金币'];
    const PAGE_SOURCE_AI_VIP = [7,'AI会员'];
    const PAGE_SOURCE_UNKNOWN = [0,'未知'];


    //订单类型
    const ORDER_TYPE_MOVIE = [1,'点播订单'];
    const ORDER_TYPE_CHESS = [2,'棋牌订单'];
    const ORDER_TYPE_AI = [3,'ai订单'];


    /**
     * 查询是否是指定订单类型的首冲，返回订单表中的is_first字段需要的值
     * @param int $order_type
     * @param int $member_id
     * @return int
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function checkFirstOrder(int $order_type,int $member_id): int
    {
        $result_first=0;
        if(in_array($order_type,array_keys(self::getConstPluck("ORDER_TYPE_")))){
            if(!Db::name("app_pay_order")->where([
                ['member_id','=',$member_id],
                ['status','=',0],
                ['order_type','=',$order_type],
            ])->field('id')->find()){
                $result_first = $order_type;
            }
        }

        return $result_first;

    }



}
