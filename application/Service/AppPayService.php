<?php

namespace app\Service;


//支付方式配置service类

use think\Db;

class AppPayService extends BaseService {

    //是否是棋牌专属
    const IS_CHESS_YES=[1,'是'];
    const IS_CHESS_NO=[0,'否'];

    /**
     * 获取点播或棋牌的支付方式列表
     * @param int $is_chess 是否棋牌专属 0否 1是
     * @return array[]
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function getPayDiffList(int $is_chess=0): array
    {
        $all = PayChannelService::spliceChannel();  //所有四方商户
        $pay_list = [];

        $app_pay_list = Db::name('app_pay')->field('id,name_code,channel,show_name,is_chess')->where([['is_chess','=',$is_chess]])->select();

        foreach ($app_pay_list as $k=>$v){
            $app_pay_colunm[$v['name_code']][]=$v;
        }

        foreach ($all as $key => $value1) {
            if(isset($app_pay_colunm[$key])){
                foreach ($app_pay_colunm[$key] as $v){
                    $pay_list[$key]['name'] = $value1['name'];
                    $pay_list[$key]['path'] = $value1['path'];
                    $pay_list[$key]['sub']=[];
                    if(isset($v['is_chess']) && $v['is_chess']==AppPayService::IS_CHESS_YES[0]){
                        $pay_list[$key]['sub'] = !empty($app_pay_colunm[$key])?$app_pay_colunm[$key]:[];
                    }elseif (isset($v['is_chess']) && $v['is_chess']==AppPayService::IS_CHESS_NO[0]){
                        $pay_list[$key]['sub'] = !empty($app_pay_colunm[$key])?$app_pay_colunm[$key]:[];
                    }
                    foreach ($pay_list[$key]['sub'] as $k1=>&$v1){
                        $v1['full_show_name'] = ($v1['show_name']??'').' - ['.($v1['channel']??'').']';
                    }


                }
            }
        }
        return $pay_list;
    }


}
