<?php

namespace app\Service;


use app\admin\controller\BaseController;
use app\common\Constants\LogConst;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\Exception;
use think\exception\DbException;
use think\exception\PDOException;

class BaisonMemberService extends BaseService {

    /** @var array 是否警示 */
    const IS_WARNING_NO = [0,'否'];
    const IS_WARNING_YES = [1,'是'];


    /**
     * 新增用户
     * @param array $params
     * @return int[]
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public static function add(array $params){

        $detail =  Db::name('baison_member')->where([['member_id','=',$params['member_id']]])->field('id')->find();
        if(empty($detail)){
            Db::name('baison_member')->insert($params);
        }

    }

    /**
     * 修改数据
     * @param int $member_id
     * @param array $params
     * @return int|string
     * @throws Exception
     * @throws PDOException
     */
    public static function update(int $member_id,array $params){
       return Db::name('baison_member')->where([['member_id','=',$member_id]])->update($params);
    }

}
