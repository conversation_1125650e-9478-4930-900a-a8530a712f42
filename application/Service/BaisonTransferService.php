<?php

/******************************************************************************
 * Copyright (c) Archer 2022.                                                 *
 ******************************************************************************/

declare(strict_types=1);

namespace app\Service;

use app\api\logic\chess\BaisonLogic;
use app\common\BizConst;
use app\Helper\StrHelper;
use think\Db;

final class BaisonTransferService extends AbstractDAOService
{
    public function getStatementsByAccount(string $account, array $cols = ['*']): ?array
    {
        $account = BaisonLogic::getLocalAccount($account);
        $s = sprintf('SELECT %s FROM `hg_baison_transfer` WHERE `account`=?', StrHelper::colsArrToStr($cols));
        return $this->queryStat($s, [$account]);
    }

    public function getDepositAmountByAccount(string $account, bool $transaction = false): ?array
    {
        $account = BaisonLogic::getLocalAccount($account);
        $s = false === $transaction ?
            'SELECT `order_id`,`amount` FROM `hg_baison_amount` WHERE `account`=:account LIMIT 1' :
            'SELECT * FROM `hg_baison_amount` WHERE `account`=:account LIMIT 1 FOR UPDATE';
        $r = $this->queryStat($s, ['account' => $account]);
        return !empty($r) ? $r[0] : null;
    }

    public function getOrderByStatus(string $account, int $statType, array $status, array $cols = ['*']): ?array
    {
        $account = BaisonLogic::getLocalAccount($account);
        $s = sprintf('SELECT %s FROM `hg_baison_transfer` WHERE `account`=? AND `type`=? AND `status` IN (%s)',
            StrHelper::colsArrToStr($cols), implode(',', $status));
        return $this->queryStat($s, [$account, $statType]);
    }

    public function moveMemberAmountToBaison(string $account): bool
    {
        $account = BaisonLogic::getLocalAccount($account);
        $s = 'UPDATE `hg_baison_amount` SET `amount`=0 WHERE `account`= ? LIMIT 1';
        return Db::execute($s, [$account]) > 0;
    }

    public function getOrderByOrderId(string $orderId, array $cols = ['*']): ?array
    {
        $s = sprintf('SELECT %s FROM `hg_baison_transfer` WHERE `order_id`=? LIMIT 1', StrHelper::colsArrToStr($cols));
        return $this->queryStat($s, [$orderId], true);
    }

    public function updateStatusByOrderId(string $orderId, int $status): bool
    {
        $data = ['status' => $status];
        if ($status !== BizConst::BAISON_DEPOSIT_WAITING && !in_array($status,
                BizConst::BAISON_WITHDRAWAL_HOLDING_STATUS, true)) {
            $data['finish_time'] = date('Y-m-d H:i:s');
        }
        return (int) Db::name('baison_transfer')->where(['order_id' => $orderId])->update($data) > 0;
    }

    public function extendVIPTime(int $memberId, int $vipSecs): void
    {
        (new \app\admin\model\Trad())->code_viptime($memberId, 5, $vipSecs / 3600);
    }

    public function plusAmount(int $memberId, int $amount): void
    {
        $this->plusMemberAmountForBonus($memberId, $amount,
            CreateDingdan($memberId, TTYPE_TZ_GIVE, TTYPE_OL_GIVE_TOOL, \random_int(0, 9)), TTYPE_TZ_GIVE,
            TTYPE_OL_GIVE_TOOL);
    }

    public function plusMemberAmountForBonus($memberId, $amount, $order, $type, $sub_type): void
    {
        //查询余额
        $memberAmountItem = Db::name('member_amount')->where('member_id', $memberId)->order('id', 'desc')->find();
        if (!$memberAmountItem) {
            Db::name('member_amount')->insert(['member_id' => $memberId]);
            $memberAmountItem = ['member_id' => $memberId, 'amount' => 0, 'recharge' => 0,];
        }

        //查询用户基本信息
        $memberInfo = Db::name('member_info')->where('id', $memberId)->field('nick_name,create_time,channelid')->find();
        $nickName   = $memberInfo["nick_name"];

        //写入交易表
        $data                  = [];
        $data['member_id']     = $memberId;
        $data['member_nick']   = $nickName;
        $data['order_id']      = $order;
        $data['amount_now']    = $memberAmountItem['amount'] + $amount;  //充值及赠送 当前余额增加
        $data['amount']        = $amount;
        $data['is_first']      = 0;
        $data['amount_before'] = $memberAmountItem['amount'];
        $data['type']          = $type;
        $data['sub_type']      = $sub_type;
        $data['addtime']       = time();
        $payDealId             = Db::table('hg_app_pay_deal')->insertGetId($data);

        //改变用户余额 如果是充值则增加总充值
        Db::name('member_amount')->where('member_id', $memberId)->setInc('amount', $amount);

        //赠送增加入款表
        Db::name('app_pay_tmp')->insert([
            'order_id' => $order, 'member_id' => $memberId, 'amount' => $amount, 'addtime' => time(), 'status' => 0,
            //状态 赠送默认成功
            'type'     => 3, //类型 赠送
        ]);
    }

    public function addPromoMember(array $data): bool
    {
        return (int) Db::name('baison_promo_member')->insert($data) > 0;
    }

    public function getMemberInfo(int $memberId, array $fields = ['id', 'account', 'nick_name',]): ?array
    {
        $member = Db::name('member_info')->where(['id' => $memberId])->field($fields)->limit(1)->find();
        if (empty($member)) {
            return null;
        }
        return $member;
    }
}
