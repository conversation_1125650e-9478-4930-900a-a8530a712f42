<?php

/******************************************************************************
 * Copyright (c) Archer 2022.                                                 *
 ******************************************************************************/

declare(strict_types=1);

namespace app\Service;

use app\common\exception\BizException;
use think\Db;

final class BankCardService
{
    /**
     * @param  string  $account
     *
     * @return array|null
     */
    public function getAllByAccount(string $account): ?array
    {
        $sql = 'SELECT `id`,`account`,`real_name`,`bank_name`,`bank_no` FROM `hg_bank_card` WHERE `account`=?';
        $r   = Db::query($sql, [$account]);

        return empty($r) ? null : $r;
    }

    /**
     * @param  string  $account
     *
     * @return int
     */
    public function getCountByAccount(string $account): int
    {
        $s = 'SELECT COUNT(`id`) AS `count` FROM `hg_bank_card` WHERE `account`=?';
        $r = Db::query($s, [$account]);

        return (int) $r[0]['count'];
    }

    /**
     * @param  array  $bankCard
     *
     * @return bool
     */
    public function addBankCard(array $bankCard): bool
    {
        try {
            $s = 'INSERT INTO `hg_bank_card` (`account`,`real_name`,`bank_name`,`bank_no`,`bank_branch`)  
                VALUES (:account,:real_name,:bank_name,:bank_no,:bank_branch)';

            return Db::execute($s, $bankCard) > 0;
        } catch (\Throwable $e) {
            if ($e->getCode() === 10501) {
                throw new BizException('请检查是否已经绑定过该卡');
            }
        }

        return false;
    }

    /**
     * @param  int  $id
     *
     * @return array|null
     */
    public function getById(int $id): ?array
    {
        $s = 'SELECT * FROM `hg_bank_card` WHERE `id`=? LIMIT 1';
        $r = Db::query($s, [$id]);

        return !empty($r) ? $r[0] : null;
    }

    /**
     * @param  int  $id
     *
     * @return bool
     */
    public function delById(int $id): bool
    {
        $s = 'DELETE FROM `hg_bank_card` WHERE `id`=? LIMIT 1';
        $r = Db::execute($s, [$id]);

        return $r > 0;
    }
}
