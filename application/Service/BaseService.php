<?php

namespace app\Service;


class BaseService {

    /**
     * 根据常量的前缀获取键值对数组（返回无指定key的集合的二维数组）
     * @param string $key 常量下标
     * @return array [[0,'待付款'],[1,'已付款']]
     */
    public static function getConstDesc(string $key): array
    {
        $oClass = new \ReflectionClass(static::class);
        $constAry = $oClass->getConstants();
        $desc = [];
        foreach ($constAry as $const => $v) {
            if (strpos($const, $key) === 0) {
                $desc[] = $v;
            }
        }
        return $desc;
    }

    /**
     * 根据常量的前缀获取键值对数组 (结果返回指定key，value字段名的二维数组)
     * @param string $key 常量下标
     * @return array  二维数组，可以指定key和value的字段名 例：[['id'=>0,'name'=>'待付款'],['id'=>1,'name'=>'已付款']]
     */
    public static function getConstSelect(string $key,string $key_field = 'id' , string $value_field = 'name'): array
    {
        $const_list = self::getConstDesc($key);
        $desc = [];
        foreach ($const_list as $const => $v) {
            $desc[] = [
                $key_field=>$v[0]??0,
                $value_field=>$v[1]??'',
            ];
        }
        return $desc;
    }

    /**
     * 根据常量的前缀获取键值对数组 (結果返回一维数组)
     * @param string $key 常量下标
     * @return array  一维数组，key为索引第一位数，value为索引第二位数。 例：['key'=>value,0=>'待付款',1=>'已付款']
     */
    public static function getConstPluck(string $key): array
    {
        $oClass = new \ReflectionClass(static::class);
        $constAry = $oClass->getConstants();
        $desc = [];
        foreach ($constAry as $const => $v) {
            if (strpos($const, $key) === 0) {
                $desc[$v[0]] = $v[1];
            }
        }
        return $desc;
    }

}