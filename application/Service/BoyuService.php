<?php

namespace app\Service;

use app\admin\controller\BaseController;
use app\Aes;
use app\api\controller\Comm;
use app\common\exception\ApiBusinessException;
use app\EasyAes;
use app\extend\Telegram\TelegramBot;
use app\Helper\BoyuHelper;
use app\Helper\CodeHelper;
use GuzzleHttp\Client;
use Psr\Http\Message\ResponseInterface;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\Model;

class BoyuService {

    //设备类型和第三方映射表(key:自己的device_type，value:第三方的device_type)
    const DEVICE_TYPE_MAP_LIST=[
        'A'=>'android',
        'lH'=>'ios',
        'H'=>'h5',
    ];

    public $config = [];

    private $client;

    private $device_id='';
    private $device_type='';
    private $token='';
    public static $iCode='';

    private $userinfo=[];

    public function __construct()
    {

        $this->userinfo = (new Comm())->userinfo;

        $this->device_id=$this->userinfo['device_id'];
        $this->device_type=$this->userinfo['device_type'];
        $this->token=(new Comm())->token;

        $this->config['appId']=sysconf("boyu_app_id");
        $this->config['appKey']=sysconf("boyu_app_key");


        (new BoyuHelper())->setKey($this->config['appKey']);

        $this->client = new Client([
            'headers' => [
                'Content-Type' => 'application/json',
                'X-FORWARDED-FOR'=>'103.65.181.208'
            ],
            //跟域名
            'base_uri' => sysconf("boyu_domain"),
            // 超时，可设置可不设置
            'timeout'  => 30,
        ]);

    }

    /**
     * 设置iCode值
     */
    public static function setICode(string $iCode){
        self::$iCode=$iCode;
    }

    /**
     * 获取iCode值
     * @return string
     */
    private static function getICode(): string
    {
        return self::$iCode;
    }


    /**
     * @param string $url
     * @return string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getPromoteUrl(string $url){
        $parse_url = parse_url($url);
        $result_url='';
        if(!empty($parse_url)){
            $query_params=[];
            parse_str($parse_url['query'],$query_params);

            if(!empty($query_params['i_code'])){
                self::setICode(strval($query_params['i_code']));
            }
//            else{
//                self::setICode(sysconf('by_code'));
//            }
            $result_url=$parse_url['scheme'].'://'.$parse_url['host'].(!empty($parse_url['port'])?":".$parse_url['port']:'')."?".$parse_url['query'].(empty($parse_url['query'])?'':'&').'token='.($this->getByMemberInfo((int)$this->userinfo['id'])['token']??'');
        }
        return $result_url;

    }

    /**
     *
     * @param int $member_id
     * @return array|string|Model|null
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public function getByMemberInfo(int $member_id){


        $detail = Db::name('member_by_info')->where(['member_id'=>$member_id])->field("by_member_id,name,token,last_login_time")->cache(10)->find();

//        //设置token有效期
//        if(strtotime($detail['last_login_time'])+60*60*3 >= time()){
//
//        }
//
//        $api = config('ob.')['login'];
//        $data = [
//            'name' => $name,
//            'password' => md5($password)
//        ];
//        $result = (new BoyuService())->request($api,$data);
//        Db::name('member_by_info')->where(['id'=>$detail])->update([ 'password' => md5($password), 'token' => $result['data']['token'],'last_login_time'=>date('Y-m-d H:i:s')]);

//        if(empty($detail)){
//            throw new ApiBusinessException(CodeHelper::CODE_NOT_REGISTER[1],CodeHelper::CODE_NOT_REGISTER[0]);
//        }

        return $detail;

    }

    /**
     * 查询用户是否已注册，且token在有效期内
     * @param int $member_id
     * @return array|Model|\PDOStatement|string|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function checkMember(int $member_id){
        $detail = Db::name('member_by_info')->where(['member_id'=>$member_id])->cache(true,5)->find();
        if(empty($detail)){
            json_fail(CodeHelper::CODE_NOT_REGISTER[1],[],CodeHelper::CODE_NOT_REGISTER[0]);
        }

        if(empty($detail['last_login_time']) || strtotime($detail['last_login_time'])+60*60*3 <= time()){
            json_fail(CodeHelper::CODE_NOT_LOGIN[1],['username'=>$detail['name']],CodeHelper::CODE_NOT_LOGIN[0]);
        }

        return $detail;
    }


    /**
     * 请求处理接口
     * @param string $url
     * @param array $origin_data
     * @return array
     * @throws ApiBusinessException|\GuzzleHttp\Exception\GuzzleException
     */
    public function request(string $url , array $origin_data,string $token='')
    {

        try {

            $data = $this->sendDataEncrypt($origin_data,$url,$token);

            $response = $this->client->request('POST', $url, [
                'json' =>$data
            ]);

            $response_data = json_decode($response->getBody()->getContents(), true);
            $result = BoyuHelper::getDecryptData($response_data);

            if($result['code']!=BoyuHelper::SUCCESS_CODE){
                //  10105 他人登录  10004 token失效   10103  账号在其他地方登录
                if ($result['code'] == 10105 || $result['code'] == 10004 || $result['code'] == 10103 || $result['code'] == 22001 || $result['code'] == 22022 || $result['code'] == 22023){
//                    throw new ApiBusinessException('TOKEN失效，请重新登录!',CodeHelper::CODE_NOT_LOGIN['0']);
                    throw new ApiBusinessException(!empty($result['msg'])?$result['msg']:'TOKEN失效，请重新登录!',CodeHelper::CODE_NOT_LOGIN['0']);
                }  else {
//                    if ($result['code'] != BoyuHelper::SUCCESS_CODE) {
                        throw new ApiBusinessException($result['msg'],$result['code']);
//                    }
                }
            }

        }catch (\Throwable $e) {
        } finally {

            if(!empty($e))$error_reason='error_code:'.$e->getCode().','.$e->getMessage().$e->getFile().$e->getLine();
            $url_name = array_flip(config('ob.'))[$url]??'by_other_request';
            logInfo(['data'=>$origin_data??[],'encrypt_data'=>$data??'','result'=>$result??[],'error'=>$error_reason??''],$url_name ,$url_name.(!empty($e)?'_error':''), 'boyu_http_request');

            if(!empty($e) && !in_array($e->getCode(),explode(',',sysconf('boyu_note_whitelist_codes'))) ){
                $redis = redis();
                $redis_key = 'boyu_error_count'; //博鱼指定时间内报错次数
                $error_count = $redis->get($redis_key);

                if(!empty($error_count) && $error_count>1 ){
                    TelegramBot::sendMessageTextEscape(sysconf('business_note_chat_id'),TelegramBot::makePHPError([
                        'from' =>sysconf('app_name'),
                        'subject' => '博鱼接口请求异常',
                        'date' => date('Y-m-d H:i:s'),
                        'content' => $error_reason
                    ]));
                }else{
                    $redis->incr($redis_key);
                    $redis->expire($redis_key,60*60);
                }

            }

           if(!empty($e)) throw new ApiBusinessException($e->getMessage(),$e->getCode());
        }

        return $result??[];
    }



    /**
     * 请求数据加密
     * @param array $params
     * @return array
     */
    private function sendDataEncrypt(array $params,string $url,string $token): array
    {
        $ts = time();
        $data=[
            'reqId'=>uniqid().rand(100,999),
            'thUuid'=>empty($this->device_id)?uuid():$this->device_id,
            'thClientType'=>self::DEVICE_TYPE_MAP_LIST[$this->device_type]??self::DEVICE_TYPE_MAP_LIST['H'],
            'appId'=>$this->config['appId'],
            'ts'=>$ts,
            'userIp'=>get_client_ip(),
            'data'=>BoyuHelper::encrypt(json_encode($params,JSON_UNESCAPED_UNICODE)),
        ];
        if(!in_array($url,[config('ob.')['reg'],config('ob.')['login']])){
            $data['token']=$token;
        }

        $data['sign']=self::getSign($data);

        return $data;
    }

    /**
     * 生成sign参数
     * @param array $params
     * @return string
     */
    private static function getSign(array $params){
        return md5($params['reqId'].'|'.$params['appId'].'|'.$params['ts'].'|'.(empty($params['token'])?'':$params['token']).'|'.$params['data']);
    }

}