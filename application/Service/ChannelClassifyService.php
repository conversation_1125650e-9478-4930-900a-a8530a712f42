<?php

namespace app\Service;


//渠道视频分类统计service类
use app\common\Constants\RedisConst;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

class ChannelClassifyService  extends BaseService {


    const STATISTICS_TYPE_PAGE=[1,'页面统计'];
    const STATISTICS_TYPE_PLAY=[2,'播放统计'];


    /**
     * 统计方法
     * @return void
     */
    public static function classify(int $statistics_type , array $data)
    {

        $date = date("Y-m-d");
        $channelid=$data['channelid'];
        $member_id=$data['member_id'];
        $category_id=$data['category_id']??0;
        $data['movie_type']=$data['movie_type']??-1;
        $expire = 86410;
        $redis = redis();
        $update_data =[];

        $redis_suffix = $date.':'.$channelid.':'.$category_id;
        if($statistics_type==self::STATISTICS_TYPE_PAGE[0]){
            $redis_click_uv_key = RedisConst::CHANNEL_CLASSIFY_CLICK_KEY.'uv:'.$redis_suffix;
            $redis_click_pv_key = RedisConst::CHANNEL_CLASSIFY_CLICK_KEY.'pv:'.$redis_suffix;

            $redis->sadd($redis_click_uv_key,$member_id);
            $redis->expire($redis_click_uv_key,$expire);
            $update_data['category_click_uv'] =  $redis->scard($redis_click_uv_key);
            $update_data['category_click_pv'] = $redis->incr($redis_click_pv_key);
        }elseif($statistics_type==self::STATISTICS_TYPE_PLAY[0]){
            $redis_free_uv_key = RedisConst::CHANNEL_CLASSIFY_FREE_KEY.'uv:'.$redis_suffix;
            $redis_free_pv_key = RedisConst::CHANNEL_CLASSIFY_FREE_KEY.'pv:'.$redis_suffix;
            $redis_preview_uv_key = RedisConst::CHANNEL_CLASSIFY_PREVIEW_KEY.'uv:'.$redis_suffix;
            $redis_preview_pv_key = RedisConst::CHANNEL_CLASSIFY_PREVIEW_KEY.'pv:'.$redis_suffix;
            $redis_play_uv_key = RedisConst::CHANNEL_CLASSIFY_PLAY_KEY.'uv:'.$redis_suffix;
            $redis_play_pv_key = RedisConst::CHANNEL_CLASSIFY_PLAY_KEY.'pv:'.$redis_suffix;

            $redis->sadd($redis_play_uv_key,$member_id);
            $redis->expire($redis_play_uv_key,$expire);
            $update_data['play_uv'] = $redis->scard($redis_play_uv_key);
            $update_data['play_pv'] = $redis->incr($redis_play_pv_key);

            if(isset($data['is_preview']) && $data['is_preview']==1){
                $redis->sadd($redis_preview_uv_key,$member_id);
                $redis->expire($redis_preview_uv_key,$expire);
                $update_data['preview_uv'] = $redis->scard($redis_preview_uv_key);
                $update_data['preview_pv'] = $redis->incr($redis_preview_pv_key);
            }elseif($data['movie_type']==AppMovieService::TYPE_FREE[0]){
                $redis->sadd($redis_free_uv_key,$member_id);
                $redis->expire($redis_free_uv_key,$expire);
                $update_data['free_play_uv']  = $redis->scard($redis_free_uv_key);
                $update_data['free_play_pv']  = $redis->incr($redis_free_pv_key);
            }
        }


        $detail = Db::name('channel_classify')->where(['date'=>$date,'channelid'=>$data['channelid'],'category_id'=>$category_id])->cache(true,86410)->field('id')->find();
        if(empty($detail)){
            Db::name('channel_classify')->insert([
                'date'=>$date,
                'channelid'=>$data['channelid'],
                'category_id'=>$category_id,
            ]);
        }

        if(!empty($update_data)){
            $sql = db('channel_classify')->where([
                'date'=>$date,
                'channelid'=>$data['channelid'],
                'category_id'=>$category_id,
            ])->fetchSql()->update($update_data);
            pushSql($sql);
        }

    }


}
