<?php

namespace app\Service;

use Elasticsearch\ClientBuilder;

class ElasticsearchService
{
    const  INDEX_NAME = 'userbehavior';

    protected $client;

    public function __construct()
    {
        $hosts = config('extra.elasticsearch.hosts');
        $this->client = ClientBuilder::create()->setHosts($hosts)->build();
    }



    public function scroll($scroll,$scroll_id)
    {
        $params = [
            'scroll_id'  => $scroll_id,
            'scroll' => $scroll,
//            'body'  => $query,
        ];

        return $this->client->scroll($params);
    }


    public function search($index, $scroll, $query)
    {
        $params = [
            'index' => $index,
//            'type'  => $type,
            'body'  => $query,
        ];

        if(!empty($scroll))$params['scroll'] = $scroll;

        return $this->client->search($params);
    }

    public function insertData($index, $data, $id=1)
    {
        $params = [
            'index' => $index,
//            'id'    => $id,
            'body'  => $data,
        ];

        return $this->client->index($params);
    }
}
