<?php

namespace app\Service;


use app\common\exception\ApiBusinessException;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

class GameAmountRecordService extends BaseService {

    //转账类型
    const TYPE_ENTER = [1,'转入'];
    const TYPE_OUT   = [2,'转出'];

    //细分子类型
    const SUB_TYPE_GOLD_GIVE_AWAY=[1,'充值金币赠送'];
    const SUB_TYPE_VIP_GIVE_AWAY=[2,'开通vip赠送'];
    const SUB_TYPE_ENTER_BY=[3,'划转博鱼账号'];

    //类型总列表
    const TYPES_LIST=[
        [
            'type' => self::TYPE_ENTER[0],
            'name' => self::TYPE_ENTER[1],
            'sub_type' => [
                [
                    'type' => self::SUB_TYPE_GOLD_GIVE_AWAY[0],
                    'name' => self::SUB_TYPE_GOLD_GIVE_AWAY[1],
                ],
                [
                    'type' => self::SUB_TYPE_VIP_GIVE_AWAY[0],
                    'name' => self::SUB_TYPE_VIP_GIVE_AWAY[1],
                ]
            ]
        ],
        [
            'type' => self::TYPE_OUT[0],
            'name' => self::TYPE_OUT[1],
            'sub_type' => [
                [
                    'type' => self::SUB_TYPE_ENTER_BY[0],
                    'name' => self::SUB_TYPE_ENTER_BY[1],
                ],
            ]
        ],
    ];


    /**
     * 增加游戏钱包余额
     * @param int $member_id
     * @param int $amount
     * @param int $sub_type
     * @throws ApiBusinessException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public static function balanceChange(int $member_id , int $amount , int $sub_type )
    {

        $type = self::getTypeBysubType($sub_type);

        if($type==self::TYPE_OUT[0])$amount=-$amount;

        $member_info = Db::name('member_info')->where(['id'=>$member_id])->field("id,channelid")->find();
        Db::startTrans();
        try {

            $game_amount_id=self::getGameAmountId($member_id);

            Db::name('game_amount')->where(['id'=>$game_amount_id])->inc('amount',$amount)->update();
            Db::name('game_amount_record')->insert([
                'order_id'=>CreateGameDingdan($member_id,$type,$sub_type,rand(0, 9)),
                'member_id'=>$member_id,
                'channelid'=>$member_info['channelid']??'',
                'amount'=>$amount,
                'type'=>$type,
                'sub_type'=>$sub_type,
            ]);

            Db::commit();

        }catch (\Throwable $e) {
            Db::rollback();
            throw new ApiBusinessException('钱包数据插入失败'.$e->getMessage().$e->getFile().$e->getLine());
        }

    }

    public static function getGameAmountId(int $member_id)
    {
        $detail = Db::name('game_amount')->where(['member_id'=>$member_id])->find();
        if(empty($detail['id'])){
            $id = Db::name('game_amount')->insertGetId([
                'member_id'=>$member_id,
                'amount'=>0,
                'freeze_amount'=>0,
            ]);
        }else{
            $id= $detail['id'];
        }

        return $id;

    }

    /**
     * 根据子类型获取对应主类型
     * @param int $sub_type
     * @return mixed
     * @throws ApiBusinessException
     */
    public static function getTypeBysubType(int $sub_type){

        foreach (self::TYPES_LIST as $k=>$v){
            foreach ($v['sub_type'] as $k1=>$v1){
                if($v1['type']==$sub_type){
                    return $v['type'];
                }
            }
        }

        throw new ApiBusinessException('找不到对应主类型');

    }


    /**
     * 获取个人游戏钱包信息
     * @param int $member_id
     * @return array|int[]|\PDOStatement|string|\think\Model
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getGameAmountDetail(int $member_id){
        $detail = Db::name('game_amount')->where('member_id', $member_id)->cache(true, 3)->find();
        if (empty($detail)) {
            $install_data=[
                'member_id' => $member_id,
                'amount' => 0,
                'freeze_amount' => 0
            ];
            $install_data['id']=Db::name('game_amount')->insertGetId($install_data);
            return $install_data;
        }

        return $detail;
    }



}