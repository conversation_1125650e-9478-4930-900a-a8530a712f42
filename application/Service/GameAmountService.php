<?php
/******************************************************************************
 * Copyright (c) Archer 2022.                                                 *
 ******************************************************************************/

declare(strict_types=1);

namespace app\Service;

use think\Db;

/**
 * 游戏钱包金额表
 */
final class GameAmountService
{
    /**
     * @param  int  $memberId
     * @param  bool  $forUpdate
     *
     * @return array|null
     */
    public function getGameAmountArr(int $memberId, bool $forUpdate = false): ?array
    {
        $sql = 'SELECT `amount`,`freeze_amount` FROM `hg_game_amount` WHERE `member_id`=? LIMIT 1';
        $sql = false === $forUpdate ? $sql : $sql.' FOR UPDATE';

        /* 华为云强制读主库语法 !mycat:db_type=master*/
        $r = Db::query($sql, [$memberId],);

        return empty($r) ? null : current($r);
    }

    /**
     * 更新用户游戏钱包金额
     *
     * @param  int  $memberId
     * @param  int  $amount
     * @param  int  $freezeAmount
     *
     * @return bool
     */
    public function updateGameAmount(int $memberId, int $amount, int $freezeAmount = 0): bool
    {
        $sql = 'UPDATE `hg_game_amount` SET `amount`=?,`freeze_amount`=? WHERE `member_id`=? LIMIT 1';

        return Db::execute($sql, [$amount, $freezeAmount, $memberId]) > 0;
    }

    /**
     * 添加1条游戏钱包转移流水记录
     *
     * @param  array  $data
     *
     * @return bool
     */
    public function addGameAmountRecord(array $data): bool
    {
        $sql = 'INSERT INTO `hg_game_amount_record` (`order_id`,`member_id`,`channelid`,`amount`,`type`,
                `sub_type`,`status`,`success_time`,`third_member_id`) VALUES (:orderId,:memberId,:channelId,
                :amount,:type,:subType,:status,:successTime,:thirdMemberId)';

        return Db::execute($sql, $data) > 0;
    }

    /**
     * 为用户初始化0元游戏钱包记录
     *
     * @param  int  $memberId
     *
     * @return int 返回初始化钱包金额
     * @throws \ErrorException
     */
    public function initializeGameAmount(int $memberId): int
    {
        $initAmount = 0;
        $executed   = Db::execute(
            'INSERT INTO `hg_game_amount` (`member_id`,`amount`,`freeze_amount`) VALUES (?,?,?)',
            [$memberId, $initAmount, 0],
        );

        if ($executed < 1) {
            throw new \ErrorException(sprintf('插入用户钱包0数据失败:memberId : %d', $memberId));
        }

        return $initAmount;
    }
}
