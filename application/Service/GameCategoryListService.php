<?php

namespace app\Service;


//场馆service类
use app\api\controller\Comm;
use app\common\Constants\LogConst;
use app\common\Constants\RedisConst;
use app\common\exception\ApiBusinessException;
use app\extend\Telegram\TelegramBot;
use app\Helper\BoyuHelper;
use app\Helper\CodeHelper;
use GuzzleHttp\Client;
use think\Db;

class GameCategoryListService extends BaseService {

    public function __construct()
    {
    }

    /**
     * 获取下拉选择表
     * @return array
     */
    public static function getListColumn(): array
    {
        return Db::name('game_category_list')->column('title','id');
    }


}
