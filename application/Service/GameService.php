<?php

namespace app\Service;


//游戏service类
use app\api\controller\Comm;
use app\common\BizConst;
use app\common\Constants\LogConst;
use app\common\Constants\RedisConst;
use app\common\exception\ApiBusinessException;
use app\extend\Telegram\TelegramBot;
use app\Helper\BoyuHelper;
use app\Helper\CodeHelper;
use GuzzleHttp\Client;
use think\Db;

class GameService extends BaseService {


    /** @var array 	游戏或场馆所属第三方 */
    const BELONG_THIRD_JYOU = [1,'九游'];

    private $userinfo;
    private $user_id;

    public function __construct()
    {

        $this->userinfo = (new Comm())->userinfo;
        $this->user_id = $this->userinfo['id']??'';
    }

    /**
     * 注册接口
     * @return array
     * @throws ApiBusinessException
     */
    public function register(){
        if (BizConst::IS_REGISTER !== $this->userinfo['is_visitor']) {
            throw new ApiBusinessException('请先注册账号登录再玩游戏',2);
        }

        $data = (new JYouGameService())->register();
        return [
            'username'=>$data['username'],
        ];
    }

    /**
     * 获取钱包信息
     */
    public function wallet(){
        (new JYouGameService())->oneClickRecovery();
        return [
            'availableMoney'=>(new JYouGameService())->wallet(),
        ];
    }

    /**
     * 一键回收
     */
    public function oneClickRecovery(){
        return (new JYouGameService())->oneClickRecovery();
    }

    /**
     * 获取登录游戏场馆url
     */
    public function getGameUrl(string $apiName , string $gameCode=''){
        $wallet = (new GameService())->wallet();
        (new JYouGameService())->venueEnName($apiName,strval($wallet['availableMoney']));
        $result = (new JYouGameService())->getGameUrl( $apiName , $gameCode);
        return [
            'url'=>$result['url'],
        ];
    }






}
