<?php

namespace app\Service;


//积分商城商品service类
use think\Db;

class IntegralGoodsService extends BaseService {


    /** @var array 商品类型/分类  */
    const TYPE_VIP_MOVIE_TICKET=[1,'vip观影券'];
    const TYPE_SUPER_MOVIE_TICKET=[2,'超级观影券'];
    const TYPE_VIP=[3,'vip'];
    const TYPE_GOLD=[4,'金币'];


    /** @var array 商品兑换类型 */
    const CHANGE_TYPE_INTEGRAL=[1,'积分'];
    const CHANGE_TYPE_GOLD=[2,'金币'];
    const CHANGE_TYPE_INTEGRAL_ADD_GOLD=[3,'积分+金币'];


    /** @var array 商品展示状态 */
    const GOOD_SHOW_STATUS_NORMAL = [1,'正常'];
    const GOOD_SHOW_STATUS_SOLD_OUT = [2,'已兜售'];
    const GOOD_SHOW_STATUS_EXPIRED = [3,'已过期'];


    /**
     * 获取积分类型
     * @return array
     */
    public static function getChangeType(int $integral,int $gold){
        if(!empty($integral) && !empty($gold) ){
            return IntegralGoodsService::CHANGE_TYPE_INTEGRAL_ADD_GOLD;
        }elseif (!empty($integral) ){
            return IntegralGoodsService::CHANGE_TYPE_INTEGRAL;
        }else{
            return IntegralGoodsService::CHANGE_TYPE_GOLD;
        }
    }



}
