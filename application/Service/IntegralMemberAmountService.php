<?php

namespace app\Service;


//用户积分钱包service类
use app\common\Constants\LogConst;
use app\common\Constants\RedisConst;
use app\common\exception\ApiBusinessException;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\Model;

class IntegralMemberAmountService extends BaseService {


    /** @var int 变更类型 */
    const CHANGE_TYPE_ADD=1;
    const CHANGE_TYPE_REDUCE=2;

    /**
     * 获取指定用户积分钱包的数据
     * @param int $member_id
     * @return array|\PDOStatement|string|Model|null
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public static function getDetail(int $member_id){
        return Db::name('integral_member_amount')->where(['member_id'=>$member_id])->cache(RedisConst::INTEGRAL_MEMBER_AMOUNT_DETAIL_KEY.$member_id,86400)->field("amount,consume_amount,consume_gold_amount")->find();
    }

    /**
     * 修改指定用户的数据
     * @param int $where_member_id
     * @param array $update_data
     * @return void
     * @throws \think\Exception
     * @throws \think\exception\PDOException
     */
    public static function updateWhereMemberId(int $where_member_id,array $update_data){
        Db::name('integral_member_amount')->cache(RedisConst::INTEGRAL_MEMBER_AMOUNT_DETAIL_KEY.$where_member_id)->where(['member_id'=>$where_member_id])->update($update_data);
    }


    /**
     * 变更积分
     * @param int $member_id
     * @param int $type 类型 1增加 2减少
     * @param int $integral_num 变更积分数
     * @param int $source 获取积分来源 （一般$type=1的情况下有值）
     * @param int $consume_gold_amount 消耗金币数（一般$type=2的情况下有值）
     * @param int $integral_goods_id 兑换的商品id （一般$type=2的情况下有值）
     * @param int $goods_num 兑换的商品数量（一般$type=2的情况下有值）
     * @return void
     * @throws ApiBusinessException
     */
    public function changeIntegral(int $member_id , int $type , int $integral_num,int $source=0,int $consume_gold_amount=0, int $integral_goods_id=0,int $goods_num=0){

        if(empty($integral_num) && empty($consume_gold_amount) )return true;

        Db::startTrans();
        try {
            $init_amount = $integral_num=$integral_num*100;
            $consume_gold_amount=$consume_gold_amount*100;
            $integral_member_amount_detail =Db::name('integral_member_amount')->where(['member_id'=>$member_id])->field('id,amount')->find();

            $member_info_detail=[];
            if(empty($integral_member_amount_detail)){
                $member_info_detail = Db::name('member_info')->where(['id'=>$member_id])->field('channelid')->find();
            }

            switch ($type){
                case self::CHANGE_TYPE_ADD:

                    $integral_member_amount_data=[
                        'amount'=>Db::raw('amount+'.$integral_num),
                        'last_get_integral_time'=>date('Y-m-d H:i:s'),
                    ];

                    Db::name('integral_source_record')->insert([
                        'source'=>$source,
                        'member_id'=>$member_id,
                        'integral'=>$integral_num,
                    ]);
                    break;
                case self::CHANGE_TYPE_REDUCE:

                    if(!empty($integral_num)){
                        if(empty($integral_member_amount_detail['amount']) || $integral_member_amount_detail['amount']< $integral_num){
                            throw new ApiBusinessException('账号积分余额不足',500);
                        }
                    }

                    if(!empty($consume_gold_amount)){
                        $member_amount_detail = Db::name('member_amount')->where('member_id',$member_id)->field('member_id,amount')->find();
                        if(empty($member_amount_detail['amount']) || $member_amount_detail['amount']< $consume_gold_amount){
                            throw new ApiBusinessException('账号金币余额不足',500);
                        }

                        $orderno =  CreateDingdan($member_id, TTYPE_TZ_OK, TTYPE_TZ_OK_INTEGRAL, rand(0, 9));
                        (new \app\admin\model\Trad())->addTrad($member_id,$consume_gold_amount,$orderno,TTYPE_TZ_OK,TTYPE_TZ_OK_INTEGRAL);
                    }

                    $init_amount=0;
                    $integral_member_amount_data=[
                        'amount'=>Db::raw('amount-'.$integral_num),
                        'consume_amount'=>Db::raw('consume_amount+'.$integral_num),
                        'consume_gold_amount'=>Db::raw('consume_gold_amount+'.$consume_gold_amount),
                        'last_consume_time'=>date('Y-m-d H:i:s'),
                    ];

                    Db::name('integral_consume_record')->insert([
                        'order_no'=>getOrderNo(),
                        'member_id'=>$member_id,
                        'integral_goods_id'=>$integral_goods_id,
                        'num'=>$goods_num,
                        'consume_integral'=>$integral_num,
                        'consume_gold'=>$consume_gold_amount,
                        'channelid'=>$member_info_detail['channelid']??'',
                    ]);

                    if($integral_goods_id){
                        $integral_goods = Db::name('integral_goods')->where(['id'=>$integral_goods_id])->find();
                        if(!empty($integral_goods)){
                            Db::name('integral_goods')->where(['id'=>$integral_goods_id])->update([
                                'real_sales'=>Db::raw('real_sales+'.$goods_num),
                                'inventory_num'=>Db::raw('inventory_num-'.$goods_num),
                            ]);

                            switch ($integral_goods['type']){
                                case IntegralGoodsService::TYPE_VIP_MOVIE_TICKET[0]:
                                    (new MemberInfoExtraService())->changeValue($member_id,'vip_movie_ticket',1,$integral_goods['number']*$goods_num);
                                    break;
                                case IntegralGoodsService::TYPE_SUPER_MOVIE_TICKET[0]:
                                    (new MemberInfoExtraService())->changeValue($member_id,'super_movie_ticket',1,$integral_goods['number']*$goods_num);
                                    break;
                                case IntegralGoodsService::TYPE_VIP[0]:
                                    (new \app\admin\model\Trad())->viptime($member_id,VipExpireLogService::TYPE_INTEGRAL[0],intval($integral_goods['number']*$goods_num),VipExpireLogService::TYPE_INTEGRAL[1]);//增加vip时间并增加记录
                                    break;
                                case IntegralGoodsService::TYPE_GOLD[0]:
                                    $order_no = CreateDingdan($member_id, OTHER_INCOME, OTHER_INCOME_INTEGRAL, rand(0, 9));
                                    //操作余额
                                    (new \app\admin\model\Trad())->addTrad($member_id,$integral_goods['number']*$goods_num*100,$order_no,OTHER_INCOME,OTHER_INCOME_INTEGRAL);

                                    break;
                            }
                        }
                    }
                    break;
                default:return;
            }
            if(empty($integral_member_amount_detail)){
                $integral_member_amount_data['amount']=$init_amount;
                Db::name('integral_member_amount')->cache(RedisConst::INTEGRAL_MEMBER_AMOUNT_DETAIL_KEY.$member_id)->insert(array_merge(['member_id'=>$member_id,'channelid'=>$member_info_detail['channelid']??''],$integral_member_amount_data));
            }else{
                self::updateWhereMemberId($member_id,$integral_member_amount_data);
            }

            Db::commit();
        }catch (\Throwable $e){
            DB::rollback();
            if (!($e instanceof ApiBusinessException)) {
                commonLog(LogConst::INTEGAL_CHANGE_LOG[1],['param'=>func_get_args(),'error'=>$e->getMessage().$e->getFile().$e->getLine()],LogConst::INTEGAL_CHANGE_LOG[0]);
            }
            throw new ApiBusinessException($e->getMessage(),$e->getCode());
        }

    }





}
