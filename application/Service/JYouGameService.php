<?php

namespace app\Service;


//九游游戏service类
use app\api\controller\Comm;
use app\common\Constants\LogConst;
use app\common\Constants\RedisConst;
use app\common\exception\ApiBusinessException;
use app\extend\Telegram\TelegramBot;
use app\Helper\BoyuHelper;
use app\Helper\CodeHelper;
use GuzzleHttp\Client;
use think\Db;

class JYouGameService extends BaseService {

    const XOR_KEY='hKnNSNAmGBWDB6aQ4NPoDiVYt';

    const SUCCESS_CODE = 6000;

    //请求九游的路由列表
    const ROUTE_LIST = [
        'register'=>'/lsp/api/v1/member/v1/register',
        'login'=>'/lsp/api/v1/member/v1/login',
        'checkUserName'=>'/lsp/api/v1/member/v1/checkUserName',
        'launchCache'=>'/lsp/api/v1/memberVenue/v2/launchCache', //获取登录游戏场馆url
        'queryGameByType'=>'/lsp/api/v1/memberVenue/v2/queryGameByType', //获取场馆列表
        'getElectronicGameList'=>'/lsp/api/v1/memberVenue/v2/getElectronicGameList', //获取电子游戏场馆游戏列表
        'wallet'=>'/lsp/api/v1/member/v1/wallet',  //查询会员中心钱包余额
        'getActualTotalAssets'=>'/lsp/api/v1/memberVenue/v1/getActualTotalAssets',  //查询帐户总资产接口
        'oneClickRecovery'=>'/lsp/api/v1/transfer/v1/oneClickRecovery',  //一键回收接口
        'transaciton'=>'/record/transaciton',  //交易记录
        'transferInto'=>'/lsp/api/v1/transfer/v1/transferInto',  //中心钱包转场馆钱包接口
        'myDeposit'=>'/mine/finance/myDeposit',  //存款(h5路由)
        'withDraw'=>'/mine/finance/withDraw',  //取款(h5路由)
        'bet'=>'/record/bet',  //投注记录(h5路由)
        'privilege'=>'/mine/vip/privilege',  //vip特权(h5路由)
    ];

    //和9游那边的类型区别
    const DEVICE_TYPE_MAPS = [
        'A'=>'android',
        'I'=>'ios',
        'H'=>'h5',
        'P'=>'web',
        'IH'=>'h5',
    ];

    private $aiOrderService;
    /**
     * @var mixed
     */
    private $user_id;
    private $userinfo;

    private $header = [];

    public function __construct(array $userinfo=[])
    {

        $this->userinfo = empty($userinfo)?(new Comm())->userinfo:$userinfo;
        $this->user_id = $this->userinfo['id'];
    }



    /**
     * 一键回收
     */
    public function oneClickRecovery(){
        $quest_data = [];
        $result = $this->commomRequest(self::ROUTE_LIST['oneClickRecovery'] , $quest_data);
        return $result??0;
    }


    /**
     * 获取钱包信息
     */
    public function wallet(){
        $quest_data = [];
        $result = $this->commomRequest(self::ROUTE_LIST['wallet'] , $quest_data);
        return $result??0;
    }



    /**
     * 中心钱包转场馆钱包
     */
    public function venueEnName(string $apiName , string $money){
        if($money<=0)return [];
        $quest_data = [
            'VenueEnName'=>$apiName,
            'money'=>$money,
        ];
        if(!empty($gameCode))$quest_data['gameCode']=$gameCode;
        $result = $this->commomRequest(self::ROUTE_LIST['transferInto'] , $quest_data);
        return $result??[];
    }

    /**
     * 获取登录游戏场馆url
     */
    public function getGameUrl(string $apiName , string $gameCode=''){
//        dump($gameCode);die();
        $quest_data = [
            'apiName'=>$apiName,
            'https'=>true,
            'isApp'=>true,
        ];
        if(!empty($gameCode))$quest_data['gameCode']=$gameCode;
        $result = $this->commomRequest(self::ROUTE_LIST['launchCache'] , $quest_data);
        return $result??[];
    }



    /**
     * 获取用户token
     * @return false|mixed|\Redis|string|null
     * @throws ApiBusinessException
     * @throws \RedisException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function getUserToken(){
        $redis_key = RedisConst::JYOU_USER_TOKEN_KEY.$this->user_id;
        $user_token = cacheStringGet($redis_key);
        if(!$user_token){
            $detail = Db::name('game_users')->where(['member_id'=>$this->user_id])->field('id,username,password')->find();
            if(!empty($detail)){
                try {
                    $quest_data = [
                        'name'=>$detail['username'],
                        'password'=>xor_crypt(base64_decode($detail['password']),self::XOR_KEY),
                    ];

                    $result = $this->commomRequest(self::ROUTE_LIST['login'] , $quest_data);

                    if(!empty($result['token'])){
                        cacheStringSet($redis_key, $result['token'],60*60*6);
                        return $result['token'];
                    }
                    throw new ApiBusinessException('系统异常-2',1);
                }catch (\Throwable $e){
                    if(!($e instanceof ApiBusinessException)){
                        commonLog(LogConst::GAME_LOG[1],['error'=>$e->getTrace()],LogConst::GAME_LOG[0]);
                    }
                    throw new ApiBusinessException($e->getMessage(),1);
                }

            }

        }

        return $user_token;
    }

    /**
     * 用户注册接口
     * @return array|string[]
     */
    public  function register()
    {

        if(empty(sysconf('jy_code')))throw new ApiBusinessException('请先配置推广码',1);

        $detail = Db::name('game_users')->where(['member_id'=>$this->user_id])->field('id,username')->cache(true,86400*15)->find();
//        $detail = '';

        if(empty($detail)){

            try {
                $password = random(1,'lower').random(10,'lowernumber').random(1,'number');
//                $password = md5(uniqid());
                $install_data = [
                    'member_id'=>$this->user_id,
                    'username'=>strtolower(substr(env('USER_NAME_PREFIX'), 0, 2).$this->user_id),
//                    'username'=>'jyofficial10',
//                    'username'=>'bd3504134',
                    'password'=>base64_encode(xor_crypt($password,self::XOR_KEY)),
                ];

                Db::startTrans();

                Db::name('game_users')->insert($install_data);

                $quest_data = [
                    'name'=>$install_data['username'],
                    'password'=>$password,
                    'secondPassword'=>$password,
                    'icode'=>sysconf('jy_code'),
                ];

                $result = $this->commomRequest(self::ROUTE_LIST['register'] , $quest_data);
                if(!empty($result['token'])){
                    cacheStringSet(RedisConst::JYOU_USER_TOKEN_KEY.$this->user_id, $result['token'],60*60*5);
                }else{
                    throw new ApiBusinessException('注册失败-2',1);
                }


                Db::commit();
            }catch (\Throwable $e){
                Db::rollback();
                if(!($e instanceof ApiBusinessException)){
                    commonLog(LogConst::GAME_LOG[1],['error'=>$e->getTrace()],LogConst::GAME_LOG[0]);
                }
                throw new ApiBusinessException($e->getMessage(),1);
            }

        }

        return [
            'username'=>!empty($detail['username'])?$detail['username']:($install_data['username']??''),
        ];

    }

    /**
     * 封装好的请求接口
     * @param string $url
     * @param array $origin_data
     * @return array
     * @throws ApiBusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \RedisException
     */
    public function commomRequest(string $url , array $origin_data){

        $header = [
            'Content-Type' => 'application/json;charset=utf-8',
            'X-Request-Id' => uniqid(),
            'X-API-CLIENT' => self::DEVICE_TYPE_MAPS[$this->userinfo['device_type']] ?? 'android',
            'X-API-UUID' => md5($this->user_id),
            'X-API-VERSION'=>1,
            'X-User-Ip'=>php_sapi_name()=='cli'?'**************':get_client_ip(),
        ];

        if (!in_array($url,[self::ROUTE_LIST['login'],self::ROUTE_LIST['register']])){
            $header['X-API-TOKEN']=$this->getUserToken();
        }
        $this->header=$header;
        $this->client = new Client([
            'headers' => $header,
            //跟域名
            'base_uri' => sysconf("jy_api_url"),
            // 超时，可设置可不设置
            'timeout'  => 10,
        ]);

        return $this->request($url , $origin_data);
    }

    /**
    * 请求处理接口
    * @param string $url
    * @param array $origin_data
    * @return array
    * @throws ApiBusinessException|\GuzzleHttp\Exception\GuzzleException
    */
    private function request(string $url , array $origin_data)
    {

        try {
            $response = $this->client->request('POST', $url, [
                'json' =>$origin_data
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if($result['status_code']!=self::SUCCESS_CODE){

                if($result['message']=='注册失败' && $result['status_code']==6005){
                    $result['message']='游戏暂时关闭1';
                }

                throw new ApiBusinessException($result['message'],-1);
            }

        }catch (\Throwable $e) {
        } finally {

            if(!empty($e)){
                $error_reason='error_code:'.$e->getCode().','.$e->getMessage().$e->getFile().$e->getLine();
                logInfo(['data'=>$origin_data??[],'header'=>$this->header,'result'=>$result??[],'is_error'=>empty($error_reason)?:'error_request','error'=>empty($error_reason)?:''],'jyou_http_request',$url, 'jyou_http_request');
            }


            if (!empty($e) && !($e instanceof ApiBusinessException)) {

                $redis = redis();
                $redis_key = 'jyou_error_count'; //指定时间内报错次数
                $error_count = $redis->get($redis_key);
                $tolerate_count=10;

                if(!empty($error_count) && $error_count>$tolerate_count ){
                    $send_content = [
                        'from' =>sysconf('app_name'),
                        'subject' => '9游游戏-接口请求异常',
                        'date' => date('Y-m-d H:i:s'),
                        'content' =>'url:'.$url.',error:'.$error_reason
                    ];
                    TelegramBot::sendMessageTextMust($send_content);
                }else{
                    $redis->incr($redis_key);
                    $redis->expire($redis_key,60*60);
                }
                throw new \Exception($e->getMessage(),$e->getCode());
            }elseif(!empty($e)){
                throw new ApiBusinessException($e->getMessage(),$e->getCode());
            }

        }

        return $result['data']??[];
    }



}
