<?php

namespace app\Service;


//用户钱包service类
use app\api\controller\Comm;
use app\common\Constants\LogConst;
use app\common\Constants\RedisConst;
use app\common\exception\ApiBusinessException;
use app\extend\Telegram\TelegramBot;
use app\Helper\BoyuHelper;
use app\Helper\CacheHelper;
use app\Helper\CodeHelper;
use app\Helper\StrHelper;
use GuzzleHttp\Client;
use think\Db;

class MemberAmountService extends BaseService {

    private $user_id;
    private $userinfo;

    private $member_info_extra_detail;

    public function __construct($userinfo=[])
    {
        if(empty($userinfo)){
            $userinfo=(new Comm())->userinfo;
        }
        $this->userinfo = $userinfo;
        $this->user_id = $this->userinfo['id'];
        $this->aiOrderService = (new AiOrderService());
    }


    const TYPE_INTO=[1,'转入'];
    const TYPE_OUT=[2,'转出'];


    const OUT_WHERE_AI = [1,'AI'];

    const SUB_TYPE_INC_AI=[1,'从AI转入'];
    const SUB_TYPE_REDUCE_AI=[100 ,'转出到AI'];

    const STATUS_SUCCESS=[0,'处理成功'];
    const STATUS_WAITING=[1,'待查询'];
    const STATUS_FAIL=[2,'处理失败'];

    /**
     * 用户余额（单纯查询自有钱包余额，不做任何第三方自动转入的骚操作）
     */
    public function amount(){
        $re = Db::name('member_amount')->where('member_id', $this->user_id)->find();
        if (!$re) {
            Db::name('member_amount')->insert([
                'member_id' => $this->user_id,
                'amount' => 0,
                'recharge' => 0
            ]);
            $amount = 0;
        } else {
            $amount = $re['amount'];
        }

        return $amount;
    }

    /**
     *
     * 封装具体业务逻辑的金币转入方法
     * 转入(目前就一个ai钱包互通，暂时直接在需要金币消耗的地方调用就行了，以后多个模块的话要取消掉金币自动调用，改为中心钱包手动回收页面，并且消耗金币的地方需要做版本号判断，兼容以前的旧包)
     */
    public function intoBusiness(int $need_amount=***********){
        $amount = $this->amount();
        if($need_amount>$amount && checkVersion("1.8.2") ){
            try { //这里不抛出任何异常，失败就当熔断服务降级
                $into_amount = $this->into();
                $amount+=$into_amount;
            } catch (\Throwable $e) {
                if(!($e instanceof ApiBusinessException)){
                    commonLog(LogConst::CENTRAL_WALLET_LOG[1],['error'=>$e->getTrace()],LogConst::CENTRAL_WALLET_LOG[0]);
                }
                throw new ApiBusinessException($e->getMessage().'-'.$e->getCode(),1);
            }

        }
        return $amount;
    }

    /**
     * 转出
     * @return void
     */
    public function out(int $out_where, string $amount)
    {

        try {
            switch ($out_where){
                case self::OUT_WHERE_AI[0]:
                    $order_no = $this->updateBalance(intval($this->user_id),-intval($amount*100),self::SUB_TYPE_REDUCE_AI[0]);
                    $result = (new AiService())->out($order_no,$amount);
                    if(isset($result['status']) && $result['status']===0){
                        Db::name("central_wallet_record")->where(["order_no"=>$order_no])->update([
                            'status'=>self::STATUS_SUCCESS[0],
                        ]);
                    }

                    break;
            }
        }catch (\Throwable $e){

            if(!($e instanceof ApiBusinessException)){
                commonLog(LogConst::CENTRAL_WALLET_LOG[1],['error'=>$e->getTrace()],LogConst::CENTRAL_WALLET_LOG[0]);
            }
            throw new ApiBusinessException($e->getMessage().'-'.$e->getCode(),1);
        }


    }

    /**
     * 转入(目前就一个ai钱包互通，暂时直接在需要金币消耗的地方调用就行了，以后多个模块的话要取消掉金币自动调用，改为中心钱包手动回收页面，并且消耗金币的地方需要做版本号判断，兼容以前的旧包)
     * @return float|int
     */
    public function into()
    {
        try {
            CacheHelper::$end_clean_list[] = $limit_rate_key = RedisConst::MEMBER_AMOUNT_INTO_KEY.$this->user_id;
            if(!CacheHelper::limitRate($limit_rate_key,'',10,false)){
                $order_no = self::getOrderNo();
                $result = (new AiService())->into($order_no);

                if(!(isset($result['status']) && $result['status']===0)){
                    $status = self::STATUS_WAITING[0];
                }elseif (!empty($result['amount']) && $result['amount']>0){
                    $status = self::STATUS_SUCCESS[0];
                }
                if(isset($status)){
                    $this->updateBalance(intval($this->user_id),intval($result['amount']*100),self::SUB_TYPE_INC_AI[0],$order_no,$status);
                }
            }

        }catch (\Throwable $e){

            //以后上面有多个不同钱包的话，需要判断这个$order_no属于哪个第三方钱包，目前只有一个钱包，直接赋值$order_no就行了
            if(!empty($order_no)){
                if(empty(Db::name('central_wallet_record')->where(['order_no'=>$order_no])->find())){
                    $this->updateBalance(intval($this->user_id),0,self::SUB_TYPE_INC_AI[0],$order_no);
                }
            }

            if(!($e instanceof ApiBusinessException)){
                commonLog(LogConst::CENTRAL_WALLET_LOG[1],['error'=>$e->getTrace()],LogConst::CENTRAL_WALLET_LOG[0]);
            }
            throw new ApiBusinessException($e->getMessage().'-'.$e->getCode(),1);
        }
        return ($result['amount']??0)*100;
    }


    /**
     * 转入转出余额变动（这个接口只提供给对外钱包转入转出用，内部钱包消费🙅‍不可调用此接口）
     * @param int $member_id
     * @param int $amount 变动金币数（单位分）(减的话直接-，入减500，传入-500)
     * @return string
     * @throws \Exception
     */
    public function updateBalance(int $member_id , int $amount,int $sub_type,string $order_no="",int $status=1,int $is_insert_record=1)
    {

        if(empty($order_no)){
            $order_no = self::getOrderNo();
        }

        try {

            if($amount>0){
                $type=self::TYPE_INTO[0];
            }else{
                $type=self::TYPE_OUT[0];
            }

            $amount_raw = Db::raw("amount+".$amount);

            $this->amount();

            Db::startTrans();

            Db::name('member_amount')->where(['member_id'=>$member_id])->update(
                [
                    'amount'=>$amount_raw,
                ]
            );

            $central_wallet_record_data = [
                'status'=>$status,
            ];
            if($is_insert_record==1){
                $central_wallet_record_data = array_merge($central_wallet_record_data, [
                    'order_no'=>$order_no,
                    'member_id'=>$member_id,
                    'amount'=>$amount,
                    'type'=>$type,
                    'sub_type'=>$sub_type,
                ]);
                Db::name('central_wallet_record')->insert($central_wallet_record_data);
            }elseif($is_insert_record==2){
                Db::name('central_wallet_record')->where(['order_no'=>$order_no])->update($central_wallet_record_data);
            }

            Db::commit();

        }catch (\Throwable $e){
            Db::rollback();

            if(!($e instanceof ApiBusinessException)){
                commonLog(LogConst::CENTRAL_WALLET_LOG[1],['error'=>$e->getTrace()],LogConst::CENTRAL_WALLET_LOG[0]);
            }
            throw new ApiBusinessException($e->getMessage().'-'.$e->getCode(),1);
        }

        return $order_no;

    }

    /**
     * 获取订单号
     * @return string
     */
    public static function getOrderNo(){
        return env('ORDER_PREFIX','').StrHelper::sfOrderId();
    }




}
