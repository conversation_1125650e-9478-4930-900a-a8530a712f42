<?php

namespace app\Service;


//自动站内信service类
use app\common\Constants\CommonConst;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

class MemberAutoMsgService extends BaseService {

    //消息类型
    const TYPE_TODAY_EXPIRED_VIP=[1,'今日过期会员'];
    const TYPE_TODAY_NOT_VIP =[2,'未开会员用户'];
    const TYPE_TODAY_ADD_NOT_BUY =[3,'今日新增用户但未购买会员用户'];
    const TYPE_ACTIVITY =[4,'活动'];

    /**
     * 自动发送站内信
     * @param int $member_id
     * @return void
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public static function autoSendMsg(int $member_id){

        $list = Db('auto_msg')->where(['status'=>CommonConst::ENABLED[0]])->cache(60)->select();
        if(!empty($list)){
            $detail = MemberInfoService::detail($member_id);
            if(!empty($detail)){

                $member_message_column = Db('member_message')->where(['member_id'=>$member_id])->whereIn('auto_msg_id',array_column($list,'id'))->field('id,auto_msg_id')->column('id','auto_msg_id');
                foreach ($list as $v){

                    if(empty($member_message_column[$v['id']])){
                        $bath_insert_data=[];
                        $base_insert_data=[
                            'message_type'=>1,
                            'member_id'=>$member_id,
                            'title'=>$v['title']??'',
                            'content'=>$v['content']??'',
                            'is_read'=>1,
                            'auto_msg_id'=>$v['id'],
                            'create_time'=>time(),
                            'create_user'=>1,
                        ];
                        switch ($v['id']){
                            case self::TYPE_TODAY_EXPIRED_VIP[0]:
                                if(
                                    ($detail['expire_time'] >= strtotime(date('Y-m-d'))  && $detail['expire_time']<= strtotime(date('Y-m-d'))+86399) ||
                                    ($detail['super_vip_expire_time'] >= strtotime(date('Y-m-d'))  && $detail['super_vip_expire_time']<= strtotime(date('Y-m-d'))+86399)
                                ){
                                    $bath_insert_data[]=$base_insert_data;
                                }
                                break;
                            case self::TYPE_TODAY_NOT_VIP[0]:
                                if(empty($detail['expire_time']) && empty($detail['super_vip_expire_time'])){
                                    $bath_insert_data[]=$base_insert_data;
                                }
                                break;
                            case self::TYPE_TODAY_ADD_NOT_BUY[0]:
                                if(date('Y-m-d',$detail['create_time'])==date('Y-m-d')  &&  empty($detail['expire_time']) && empty($detail['super_vip_expire_time']))  {
                                    $bath_insert_data[]=$base_insert_data;
                                }
                                break;
                            case self::TYPE_ACTIVITY[0]:
                                $bath_insert_data[]=$base_insert_data;
                                break;
                        }

                    }

                }

                if(!empty($bath_insert_data))Db('member_message')->insertAll($bath_insert_data);

            }

        }

    }

}