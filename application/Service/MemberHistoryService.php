<?php

namespace app\Service;


//播放记录service类
use app\common\Constants\CommonConst;
use app\common\Constants\RedisConst;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

class MemberHistoryService extends BaseService {


    private static function getCacheKey(int $member_id){
        return config("cache.prefix").RedisConst::MEMBER_PLAY_COUNT_KEY.date('d').':'.$member_id;
    }


    /**
     * 用户播放次数缓存次数增加
     * @param int $member_id
     * @return void
     * @throws \RedisException
     */
    public static function updateCache(int $member_id){
        redis()->incrBy(self::getCacheKey($member_id),1);
    }


    /**
     * 检测指定用户是否有播放测试
     * @param int $member_id
     * @return int
     */
    public static function checkIsAllow(int $member_id){

        $today_history_count=self::getTodayHistoryCount($member_id);
        $times = get_visitor_long_view_times($member_id); //观影次数
        if ($today_history_count>=$times) {
            $is_allow = 0;
        }else{
            $is_allow = 1;
        }

        return $is_allow;

    }

    /**
     * 获取用户今日播放次数
     * @param int $member_id
     * @return false|float|int|mixed|\Redis|string
     * @throws \RedisException
     */
    public static function getTodayHistoryCount(int $member_id){
        $redis = redis();
        $redis_key = self::getCacheKey($member_id);
        $today_history_count = $redis->get($redis_key);
        if(!$today_history_count){
            $today_history_count = db('member_history')->where([
                ['member_id','=',$member_id],
                ['create_time','>',strtotime(date('Y-m-d'))]
            ])->count();
            $redis->setex($redis_key,86402,$today_history_count);
        }

        return $today_history_count;
    }

}