<?php

namespace app\Service;


//用户信息拓展service类
use app\common\Constants\RedisConst;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\Model;

class MemberInfoExtraService extends BaseService {


    /**
     * 获取指定用户详细信息
     * @param int $member_id
     * @param bool $is_add 数据库不存在是否新增
     * @return array|\PDOStatement|string|Model
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function getDetail(int $member_id,bool $is_add=false)
    {

        $detail = Db::name('member_info_extra')->where('member_id',$member_id)->cache(RedisConst::MEMBER_INFO_EXTRA_DETAIL_KEY.$member_id,86400)->find();
        if(empty($detail) && $is_add){
            $id = Db::name('member_info_extra')->insertGetId([
                'member_id'=>$member_id,
            ]);
            return  ['id'=>$id];
        }

        return $detail;
    }

    /**
     * 修改指定字段的值
     * @param int $member_id
     * @param string $field_name
     * @param int $type 增减类型 1新增 2减少 3直接覆盖
     * @param  $num
     * @return void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * @throws \think\Exception
     * @throws \think\exception\PDOException
     */
    public static function changeValue(int $member_id,string $field_name,int $type, $num){

        $detail=self::getDetail($member_id,true);
        if($type==3){
            $value = $num;
        }else{
            $value = Db::raw("{$field_name}".($type==1?'+':'-').$num);
        }
        Db::name('member_info_extra')->where('id',$detail['id'])->cache(RedisConst::MEMBER_INFO_EXTRA_DETAIL_KEY.$member_id)->update(
            [
                $field_name=>$value,
            ]
        );
    }


    /**
     * 指定用户是否对指定影片有通过观影券兑换获得的观看权限
     * @param int $member_id
     * @param int $movie_id
     * @return bool
     * @throws \RedisException
     * @throws \app\common\exception\InsideBusinessException
     */
    public static function isPlayFromMovieTicketExist(int $member_id,int $movie_id){
        $redis=redis();
        $redis_key=RedisConst::EXCHANGE_PLAY_FROM_TICKET_KEY.$member_id.':'.$movie_id;
        if($redis->exists($redis_key)){
            return true;
        }
        return false;
    }


}
