<?php

namespace app\Service;


//用户service类
use app\common\Constants\CommonConst;
use app\common\Constants\RedisConst;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\Model;

class MemberInfoService extends BaseService {

    /**
     * 获取指定用户详细信息
     * @param int $member_id
     * @return array|\PDOStatement|string|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function detail(int $member_id){

        $redis_key = RedisConst::MEMBER_INFO_NEW_KEY.$member_id;
        $detail = cacheGet($redis_key);
        if(!$detail)$detail = db("member_info")->field('id,token,is_visitor,account,is_nick_name,nick_name,expire_time,super_vip_expire_time,global_area_code,is_avatar,avatar,is_sex,sex,is_comment,level_id,promo_code,reg_promo_code,promo_qrcode,today_view_times,re_today_view_times,today_cache_times,re_today_cache_times,is_save_qrcode,is_today_click_adv,status,channelid,is_deleted,device_id,device_type,create_time,mobile,create_ip')->cache(true,30)->find($member_id);

        return $detail;
    }

    /**
     * 更新指定字段为指定值
     * @param int $member_id
     * @param array $data
     * @return array|Model|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function updateFieldDetail(int $member_id, array $data=[]){

        $detail = self::detail($member_id);
        foreach ($data as $k=>$v){
            $detail[$k]=$v;
        }
        $redis_key = RedisConst::MEMBER_INFO_NEW_KEY.$member_id;
        cacheSet($redis_key,$detail,300);
        return $detail;
    }


}
