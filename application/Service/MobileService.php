<?php

/******************************************************************************
 * Copyright (c) Archer 2022.                                                 *
 ******************************************************************************/

declare(strict_types=1);

namespace app\Service;

use think\Db;

final class MobileService
{
    public function getBoundMobileNo(string $account): ?array
    {
        $sql = 'SELECT `id`,`account`,`mobile_no` FROM `hg_mobile` WHERE `account`=? LIMIT 1';
        $r   = Db::query($sql, [$account]);
        return empty($r) ? null : current($r);
    }

    public function addMobileBind(string $account, string $mobileNo): bool
    {
        $sql = 'INSERT INTO `hg_mobile` (`account`,`mobile_no`,`bound_time`) VALUES (?,?,?)';
        $r   = Db::execute($sql, [$account, $mobileNo, date('Y-m-d H:i:s')]);
        return $r > 0;
    }

    public function syncToMemberInfo(string $account, string $mobileNo): bool
    {
        $i = Db::name('member_info')->where('account', '=', $account)->limit(1)
            ->update(['global_area_code' => '86', 'mobile' => $mobileNo,]);
        return (int) $i > 0;
    }
}
