<?php

namespace app\Service;


use think\Db;

class NewReportService extends BaseService {

    public  function newReportData(){
        $date = (date('Y')-1).'-'.date('m'); //
        $all = [];
        for ($i = 0; $i < 12; $i++) {

            $begin_time = strtotime(get_next_month_days($date)[0] . ' 00:00:00');
            $end_time = strtotime(get_next_month_days($date)[1] . ' 23:59:59');
            $mon = date('Ym', $begin_time);


            $data = Db::name('site_mon')->where('date', $mon)->find();
            if (!$data) {
                if($mon==date('Ym')){
                    $is_del_redis=false;
                }else{
                    $is_del_redis=true;
                }
                $data = $this->getdata($mon, $begin_time, $end_time,$is_del_redis);
                Db::name('site_mon')->insert($data);
            }

            if ($mon == date('Ym')) {
                Db::name('site_mon')->where(['date'=>$mon])->update($data);
            }


            $all[$mon] = $data;

            if ($end_time > time()) {
                break;
            }
            $date = get_next_month_days($date)[0];
        }
        return $all;
    }

    public function getdata($mon, $begin_time, $end_time,bool $is_del_redis=false)
    {
        $data = [];
        $create_time = date('Y-m-d H:i:s', $begin_time);
        $end_times = date('Y-m-d H:i:s', $end_time);
        //短视频分享记录
        $video_share = Db::connect('short_video')->table('xg_share_video_list')->where('create_time', '>', $create_time)->where('create_time', '<', $end_times)->count();
        $data['video_share'] = $video_share + Db::name('video_clip_share')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->count();
        $video_share_member = Db::connect('short_video')->table('xg_share_video_list')->where('create_time', '>', $create_time)->where('create_time', '<', $end_times)->group('user_id')->count();
        $data['video_share_member'] = $video_share_member + Db::name('video_clip_share')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->group('user_id')->count();

        //短视频评论记录
        $video_comment = Db::connect('short_video')->table('xg_video_comment')->where('create_time', '>', $create_time)->where('create_time', '<', $end_times)->count();
        $data['video_comment'] = $video_comment + Db::name('video_clip_comment')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->count();
        $video_comment_member = Db::connect('short_video')->table('xg_video_comment')->where('create_time', '>', $create_time)->where('create_time', '<', $end_times)->group('user_id')->count();
        $data['video_comment_member'] = $video_comment_member + Db::name('video_clip_comment')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->group('user_id')->count();

        //短视频点赞记录
        $video_like = Db::connect('short_video')->table('xg_video_like')->where('create_time', '>', $create_time)->where('create_time', '<', $end_times)->count();
        $data['video_like'] = $video_like + Db::name('video_clip_like')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->where('status', 1)->where('cancel_time', 100)->count();
        $video_like_member = Db::connect('short_video')->table('xg_video_like')->where('create_time', '>', $create_time)->where('create_time', '<', $end_times)->group('user_id')->count();
        $data['video_like_member'] = $video_like_member + Db::name('video_clip_like')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->where('status', 1)->where('cancel_time', 100)->group('user_id')->count();


        //长视频点赞
        $data['movie_like'] = Db::name('member_movie_up_down')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->count();
        $data['movie_like_member'] = Db::name('member_movie_up_down')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->group('member_id')->count();

        //长视频评论
        $data['movie_comment'] = Db::name('member_comment')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->count();
        $data['movie_comment_member'] = Db::name('member_comment')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->group('member_id')->count();


        //推广记录
        $data['promo'] = Db::name('member_promo')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->count();
        $data['promo_member'] = Db::name('member_promo')->where('create_time', '>', $begin_time)->where('create_time', '<', $end_time)->group('member_id')->count();

        //月首冲人数
        $data['pay_first'] = Db::query("SELECT COUNT(*) as count from hg_app_pay_deal WHERE addtime >= {$begin_time} AND addtime <= {$end_time} AND type = 1 AND is_first = 1")[0]['count'];
        //月充值人数
        $data['pay_member'] = Db::query("SELECT COUNT(DISTINCT member_id) as count from hg_app_pay_deal WHERE addtime >= {$begin_time} AND addtime <= {$end_time} AND type = 1")[0]['count'];

        //月充值总额
        $data['pay_amount'] = Db::query("SELECT sum(amount) as count from hg_app_pay_deal WHERE addtime >= {$begin_time} AND addtime <= {$end_time} AND type = 1")[0]['count'] / 100;
        //月购买vip人数与总额
        $data['pay_vip'] = Db::query("SELECT COUNT(DISTINCT member_id) as count from hg_app_pay_deal WHERE addtime >= {$begin_time} AND addtime <= {$end_time} AND type = 3 AND sub_type = 1")[0]['count'];
        $data['pay_vip_amount'] = -Db::query("SELECT sum(amount) as count from hg_app_pay_deal WHERE addtime >= {$begin_time} AND addtime <= {$end_time} AND type = 3 AND sub_type = 1")[0]['count'] / 100;
        //月购买视频人数与总额
        $data['pay_movie'] = Db::query("SELECT COUNT(DISTINCT member_id) as count from hg_app_pay_deal WHERE addtime >= {$begin_time} AND addtime <= {$end_time} AND type = 3 AND sub_type = 2")[0]['count'];
        $data['pay_movie_amount'] = -Db::query("SELECT sum(amount) as count from hg_app_pay_deal WHERE addtime >= {$begin_time} AND addtime <= {$end_time} AND type = 3 AND sub_type = 2")[0]['count'] / 100;


        $data['active3'] = redis()->pfcount("huoyue-" . $mon);  //日活跃三天以上

        if($is_del_redis){
            $redis = redis();
            $redis->del("huoyue-" . $mon . '-array');
            $redis->del("huoyue-" . $mon);
        }

        $data['date'] = $mon;
        return $data;
    }

}
