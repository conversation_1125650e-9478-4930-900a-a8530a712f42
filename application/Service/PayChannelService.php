<?php

namespace app\Service;



use app\common\Constants\RedisConst;
use think\Db;

class PayChannelService extends BaseService {

    const VERSION = 6;

    public function getList(){
        $list = Db::name('pay_channel')->where(['status'=>1])->select();
        return $list;
    }

    /**
     * 历史遗留代码结构，这里给它封装组装
     * $is_all 是否返回全部渠道（包含已禁用的）0否 1是
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public static function spliceChannel(int $is_all=1){

        $data = config('pay.');
        $redie_key = RedisConst::GET_PAY_CHANNEL_KEY.':'.$is_all.':'.self::VERSION;
        $list = cacheGet($redie_key);
        if(empty($list)){
            $where=[];
            if($is_all!=1){
                $where=['status'=>1];
            }
            $list_temp = Db::name('pay_channel')->field('name_code,channel,title')->where($where)->select();
            $list = [];
            foreach ($list_temp as $k=>$v){
                $list[$v['name_code']][$v['channel']]=$v['title'];
            }
            cacheSet($redie_key,$list,86400*30);
        }

        foreach ($data as $k=>$v){
            $data[$k]['channels']=$list[$k]??[];
        }

        return $data;
    }

    public static function delCache(){
        cacheDel(RedisConst::GET_PAY_CHANNEL_KEY.':0'.':'.self::VERSION);
        cacheDel(RedisConst::GET_PAY_CHANNEL_KEY.':1'.':'.self::VERSION);
    }


}
