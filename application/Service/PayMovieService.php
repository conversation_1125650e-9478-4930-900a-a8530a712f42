<?php

namespace app\Service;


//视频购买记录service类
use app\Helper\CacheHelper;
use think\Db;

class PayMovieService  extends BaseService {

    /**
     * 自动组装用户购买记录字段
     * @param array $list 列表数据
     * @param int $member_id 用户id
     * @param string $field 视频id在$list中的字段名
     * @return void
     */
    public static function getMemberBuyRecord(array &$list , int $member_id,string $field='movie_id'){
        if(!empty($list)){
            $bought_list_column = self::boughtList($member_id);
            foreach ($list as &$v){
                $v['is_buy']=in_array($v[$field]??'',$bought_list_column)?1:0;
            }
        }
    }


    /**
     * 用户已购买列表
     * @param int $member_id
     * @param int $movie_id
     * @return array|mixed
     */
    public static function boughtList(int $member_id , int $movie_id =0){
        //已经购买的视频
        $key = CacheHelper::MEMBER_BUYMOVIE_KEY . $member_id;
        $member_buymovie = cacheGet($key);
        if (!$member_buymovie) {
            $member_buymovie = Db::name('app_pay_movie')->where([
                'member_id'=>$member_id,
            ])->column('movie_id');  //获取已购买

            cacheSet($key,$member_buymovie,86400);
        }

        if(!empty($movie_id)){
            foreach ($member_buymovie as $k=>$v){
                if($v!=$movie_id)unset($member_buymovie[$k]);
            }
            if(!empty($member_buymovie))$member_buymovie = array_values($member_buymovie);
        }

        return $member_buymovie;
    }

    /**
     * 是否购买过该视频
     * @param int $member_id
     * @param int $movie_id
     * @return int
     */
    public static function isBuy(int $member_id , int $movie_id){
        return in_array($movie_id,self::boughtList($member_id,$movie_id))?1:0;
    }


}