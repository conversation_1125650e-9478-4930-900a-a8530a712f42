<?php

namespace app\Service;

use think\Db;

class PayToolsService extends BaseService {

    public function job(string $order_id){
        try {
            // 先校验订单
            $order = Db::name('app_pay_order')->where(['order_id' => $order_id, 'status' => 0])
                ->field('order_id,member_id,pay_amount,successtime')->find();
            if (empty($order)) {
                return;
            }

            $tools = Db::name('app_pay_tools')->where(['status' => 1])->where('start_time','<=',$order['successtime'])
                ->where('end_time','>=',$order['successtime'])->select();
            $rewards = [];
            foreach ($tools as $item) {
                // 比对单次
                if ($item['type'] == 1 && $item['amount']<=$order['pay_amount']) {
                    $rewards[] = [
                        'member_id' => $order['member_id'],
                        'tools_id' => $item['id'],
                        'addtime' => $order['successtime'],
                        'need_amount' => $item['amount'],
                        'amount' => $order['pay_amount'],
                        'amount_give' => $item['amount_give'],
                        'vip_give' => $item['vip_give'],
                        'give_time' => ''
                    ];
                    continue;
                }

                // 比对累计
                if ($item['type'] == 2) {
                    // 充值了多少
                    $total =  redis()->zIncrBy('PAY|TOOLS|TOTAL|' . $order['member_id'] ,$order['pay_amount'],$item['id']);
                    if ($total >= $item['amount']) {
                        $log = Db::name('app_pay_tools_log')->where(['member_id' => $order['member_id'], 'tools_id' => $item['id']])->field('id')->find();
                        if (!empty($log)) {
                            // 已入库
                            continue;
                        }
                    }

                    $rewards[] = [
                        'member_id' => $order['member_id'],
                        'tools_id' => $item['id'],
                        'addtime' => $order['successtime'],
                        'need_amount' => $item['amount'],
                        'amount' => $total,
                        'amount_give' => $item['amount_give'],
                        'vip_give' => $item['vip_give'],
                        'give_time' => $item['give_time']
                    ];
                }
            }
            if (!empty($rewards)) {
                Db::name('app_pay_tools_log')->insertAll($rewards);
            }

        }catch (\Throwable $e) {
            logInfo(['data' => $order_id, 'error' => $e->getMessage().$e->getFile().$e->getLine()],'Job' ,'新增运营工具：充值奖励统计失败', 'PayTools');
        }
    }
}
