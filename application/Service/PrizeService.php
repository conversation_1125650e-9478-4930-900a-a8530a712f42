<?php

namespace app\Service;


//转盘奖品service类
use think\Db;

class PrizeService extends BaseService {


    const TYPE_GOLD=[1,'金币'];
    const TYPE_VIP=[2,'普通vip'];
    const TYPE_SUPER_VIP=[3,'超级vip'];
    const TYPE_THANKS=[4,'谢谢惠顾'];
    const TYPE_INTEGAL=[5,'积分'];
    const TYPE_AI_CLOTHING=[6,'AI图片脱衣'];
    const TYPE_AI_CUSTOMIZE_FACE=[7,'AI图片换脸'];
    const TYPE_AI_VIDEO=[8,'AI视频换脸'];
    const TYPE_AI_FACE=[9,'AI自定义换脸'];

}
