<?php

namespace app\Service;


use app\admin\controller\BaseController;
use app\common\Constants\LogConst;
use think\Db;

class SliceDataService extends BaseService {

    /** @var array 视频状态 */
    const STATUS_EDITED = [0,'已编辑'];
    const STATUS_UNEDITED = [1,'未编辑'];
    const STATUS_EDITED_WAITING = [3,'已编辑待传片'];

    /**
     * 待编辑的视频转为正式上架视频
     * @return int[]
     */
    public function changeMoive(array $post,array $data,array $slice_data_detail){

        Db::startTrans();
        try {

            $tag_str = $post['tag_str'];
            $tag_ids = [];
            if ($tag_str) {
                $tag = array_filter(explode(',', $tag_str));

                foreach ($tag as $key => $value) {
                    $id = Db::name('app_tag')->where('title',trim($value))->value('id');
                    if (!$id) {
                        $id = Db::name('app_tag')->insertGetId(['title'=>trim($value),'status'=>0]);
                    }
                    $tag_ids[] = $id;
                }
            }
            $data['tag_ids'] = empty($tag_ids) ? '' : implode(',', $tag_ids);


            $star_str = $post['star_str'];
            $star_ids = [];
            if ($star_str) {
                $star = array_filter(explode(',', $star_str));

                foreach ($star as $key => $value) {
                    $id = Db::name('app_star')->where('uname',trim($value))->value('id');
                    if (!$id) {
                        $id = Db::name('app_star')->insertGetId(['uname'=>trim($value),'status'=>0]);
                    }
                    $star_ids[] = $id;
                }
            }

            $post['star_ids'] = $star_ids;

            if (empty($post['star_ids'])) {

                $data['star_ids'] = '';
            } else {
                $data['star_ids'] = implode(',', $post['star_ids']);
                Db::name('app_star')->where('id', 'in', $data['star_ids'])->setInc('movie_num');
                $nations = Db::name("app_star")->where('id', 'in', $data['star_ids'])->column('nation_id');
                Db::name('app_nation')->where('id', 'in', $nations)->setInc('movie_num');

                $jobs = Db::name("app_star")->where('id', 'in', $data['star_ids'])->column('job_id');
                Db::name('app_star_job')->where('id', 'in', $jobs)->setInc('movie_num');

            }

            $data['duration'] = gmstrftime('%H:%M:%S',$post['times']);
            Db::name('app_products')->where('id', 'in', $data['product_id'])->setInc('videos');
            $data['category_ids'] = empty($post['category_ids']) ? '' : implode(',', $post['category_ids']);
            $data['theme_ids'] = empty($post['theme_ids']) ? '' : implode(',', $post['theme_ids']);


            // 增加清晰度
            $movieUrl = '';
            $data['sp_reso1'] = $slice_data_detail['sp_reso1'] ? $slice_data_detail['sp_reso1'] : '';
            $data['sp_url1'] = $slice_data_detail['sp_url1'] ? $movieUrl . $slice_data_detail['sp_url1'] : '';
            $data['sp_reso2'] = $slice_data_detail['sp_reso2'] ? $slice_data_detail['sp_reso2'] : '';
            $data['sp_url2'] = $slice_data_detail['sp_url2'] ? $movieUrl . $slice_data_detail['sp_url2'] : '';
            $data['sp_reso3'] = $slice_data_detail['sp_reso3'] ? $slice_data_detail['sp_reso3'] : '';
            $data['sp_url3'] = $slice_data_detail['sp_url3'] ? $movieUrl . $slice_data_detail['sp_url3'] : '';

            $data['url'] = $data['sp_url1'] ?$data['sp_url1'] : '';

            $reid = Db::name('app_movie')->insertGetId($data);
            //        if (!$reid) {
            //            json_fail_admin();
            //        }

            if ($post['is_new'] == 1 || $post['is_hot'] == 1  || $post['is_rec'] == 1) {  //有一个说明有添加首页
                $add = []; //数据
                $add['new'] = $post['is_new'];
                $add['hot'] = $post['is_hot'];
                $add['rec'] = $post['is_rec'];
                $add['title'] = $post['title']; //title
                $add['movie_id'] = $reid; //电影id
                $add['create_time'] = time(); //时间
                Db::name('home_movie')->insert($add);
            }

            $slice_data_find = Db::name('slice_data')->where('id',$post['id'])->find();
            if($slice_data_find['status']==SliceDataService::STATUS_EDITED_WAITING[0]){
                $cron_edited_data = json_decode($slice_data_find['cron_edited_data']??'[]',true);
//                if(!empty($cron_edited_data)){
                    AdminLogService::addlog('添加新视频','定时传片：视频ID:'.$reid,$cron_edited_data['log_data']['admin_id'],$cron_edited_data['log_data']['admin'],$cron_edited_data['log_data']['ip'],$cron_edited_data['log_data']['time']);
//                }
            }

            Db::name('slice_data')->where('id',$post['id'])->update(['status'=>SliceDataService::STATUS_EDITED[0]]);

            Db::commit();
        }catch (\Throwable $e){
            Db::rollback();
            commonLog(LogConst::CHANGE_MOIVE_LOG[1] , ['post'=>$post,'slice_data_detail'=>$slice_data_detail,'error'=>$e->getMessage().$e->getFile().$e->getLine()], LogConst::CHANGE_MOIVE_LOG[0]);
        }

        return ['app_movie_id'=>$reid??0];
    }

}
