<?php

namespace app\Service;


//转盘活动service类
use app\admin\model\Trad;
use app\Helper\CommonConstHelper;
use LuckyBox\Card\IdCard;
use LuckyBox\LuckyBox;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;

class TurntableService extends BaseService {


    /** @var int 抽奖每次消耗积分数（部分指定陌陌的功能） */
    const TURN_CONSUME_INTEGRAL = 100;


    /**
     * 获取指定时间开启可参加的转盘活动
     * $datetime 指定时间（不传默认当前时间），转入格式必须是 Y-m-d H:i:s
     * @param string $datetime
     * @return array|string
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     * @throws DbException
     */
    public static function getOpenDetail(string $datetime=''){
        if(empty($datetime))$datetime=date('Y-m-d H:i:s');

        $condition = [
            ['status', '=', CommonConstHelper::STATUS_OPEN],
            ['start_time', '<=', $datetime],
            ['end_time', '>=', $datetime],
        ];

        return db('turntable')->where($condition)->whereNull('delete_time')->field('id,start_time,end_time,prizes,explan_rule')->find();

    }

    /**
     * 抽奖逻辑
     * @param array $prizes 奖品列表，结构字段参数必须和turntable表中的prizes一模一样！
     * @return int 返回抽中的id
     */
    public static function luckyDraw(array $prizes){

        $luckyBox = new LuckyBox();

        foreach ($prizes as $k=>$v){
            if(!is_numeric($v['prize_id']) || !is_numeric($v['probability']) ){
                json_fail("抽奖参数有误");
            }

            $card = new IdCard();
            $card->setId($k)
                ->setRate($v['probability']);
            $luckyBox->add($card);
        }

        $card = $luckyBox->draw();

        return intval($prizes[$card->getId()]['prize_id']);

    }

    /**
     * 派发指定奖品到指定账户
     * @param int $turntable_id 转盘活动id
     * @param int $prize_id  奖品id
     * @param int $user_id  用户id
     * @param string $nick_name 用户昵称
     * @return true
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public static function sendPrize(int $turntable_id , int $prize_id,int $user_id,string $nick_name,string $channelid='')
    {

        $prize_detail = Db::name("prize")->whereNull('delete_time')->where(['id'=>$prize_id])->find();

        if(empty($prize_detail)) return true;

        Db::startTrans();
        try {

            //这里开始写抽奖奖品的处理流程
            switch ($prize_detail['type']){
                case PrizeService::TYPE_GOLD[0]:
                    $order_no = CreateDingdan($user_id, TTYPE_TZ_GIVE, TTYPE_OL_GIVE_TURNTABLE, rand(0, 9));
                    //操作余额
                    (new \app\admin\model\Trad())->addTrad($user_id,$prize_detail['number']*100,$order_no,TTYPE_TZ_GIVE,TTYPE_OL_GIVE_TURNTABLE);
                    break;
                case PrizeService::TYPE_VIP[0]:
                    (new \app\admin\model\Trad())->viptime($user_id,VipExpireLogService::TYPE_DOWN_TURNTABLE[0],intval($prize_detail['number']),VipExpireLogService::TYPE_DOWN_TURNTABLE[1]);//增加vip时间并增加记录
                    break;
                case PrizeService::TYPE_SUPER_VIP[0]:
                    (new \app\admin\model\Trad())->viptime($user_id,VipExpireLogService::TYPE_DOWN_TURNTABLE[0],intval($prize_detail['number']),VipExpireLogService::TYPE_DOWN_TURNTABLE[1],true);//增加vip时间并增加记录
                    break;
                case PrizeService::TYPE_THANKS[0]:
                    break;
                case PrizeService::TYPE_INTEGAL[0]:
                    (new IntegralMemberAmountService())->changeIntegral($user_id,IntegralMemberAmountService::CHANGE_TYPE_ADD,$prize_detail['number'],IntegralSourceRecordService::SOURCE_TURNTABLE[0]);
                    break;
                case PrizeService::TYPE_AI_CLOTHING[0]:
                    MemberInfoExtraService::changeValue($user_id,'ai_clothing_promotion_count',1,1);
                    break;
                case PrizeService::TYPE_AI_CUSTOMIZE_FACE[0]:
                    MemberInfoExtraService::changeValue($user_id,'change_face_count_unlimit',1,1);
                    break;
                case PrizeService::TYPE_AI_VIDEO[0]:
                    MemberInfoExtraService::changeValue($user_id,'type_movie_count_unlimit',1,1);
                    break;
                case PrizeService::TYPE_AI_FACE[0]:
                    MemberInfoExtraService::changeValue($user_id,'ai_type_diy_change_count_unlimit',1,1);
                    break;

            }

            //中奖表记录
            if($prize_detail['type']!=PrizeService::TYPE_THANKS[0]){

                $last_recharge_time = Db::name("app_pay_order")->where(['member_id'=>$user_id,'status'=>AppPayOrderService::STATUS_SUCCESS[0],'business_type'=>AppPayOrderService::BUSINESS_TYPE_GOLD[0]])->order('successtime','desc')->value('successtime');
                $last_vip_time = Db::name("app_pay_order")->where(['member_id'=>$user_id,'status'=>AppPayOrderService::STATUS_SUCCESS[0],'business_type'=>AppPayOrderService::BUSINESS_TYPE_VIP[0]])->order('successtime','desc')->value('successtime');

                Db::name("turntable_winning_record")->insert([
                    'member_id'=>$user_id,
                    'nick_name'=>$nick_name,
                    'turntable_id'=>$turntable_id,
                    'prize_id'=>$prize_id,
                    'prize_title'=>$prize_detail['title'],
                    'prize_type'=>$prize_detail['type'],
                    'prize_number'=>$prize_detail['number'],
                    'channelid'=>$channelid,
                    'last_recharge_time'=>!empty($last_recharge_time)?date('Y-m-d H:i:s',$last_recharge_time):null,
                    'last_vip_time'=>!empty($last_vip_time)?date('Y-m-d H:i:s',$last_vip_time):null,
                ]);

            }

            Db::name("lucky_draw_record")->insert([
                'member_id'=>$user_id,
                'prize_id'=>$prize_id,
                'draw_time'=>date('Y-m-d H:i:s'),
            ]);

            Db::commit();
        }catch (\Exception $e) {
            Db::rollback();
            commonLog("派发奖品失败:user_id:".$user_id.",".json_encode(['error'=>$e->getMessage().$e->getFile().$e->getLine()]),'send_prize');
            json_fail("抽奖失败:".$e->getMessage().$e->getFile().$e->getLine());
        }

        return true;
    }

}
