<?php

namespace app\Service;

use app\api\controller\Comm;
use app\common\Constants\RedisConst;
use app\common\exception\ApiBusinessException;
use app\extend\Telegram\TelegramBot;
use GuzzleHttp\Client;
use think\Db;

class UserBehaviorService extends BaseService {


    /**
     * @var mixed
     */
    private $user_id;
    private $userinfo;

    public function __construct(array $userinfo=[])
    {
        if(empty($userinfo)){
            $this->userinfo = (new Comm())->userinfo;
            $this->user_id = $this->userinfo['id'];
        }
    }

    //用户行为映射表
    const USER_BEHAVIOR_LIST = [
        'open_app'=>['打开APP','用户首次打开APP',[
            1=>'登录页面',
        ]],
        'open_popup'=>['弹出弹窗','用户自动弹出弹窗',[
            1=>'首页',
        ]],
        'close_popup'=>['关闭窗口','用户关闭弹窗',[
            1=>'首页',
        ]],
        'contact_support'=>['联系客服','用户点击联系客服',[
            1=>'首页',
            2=>'我的',
            3=>'AI',
            100=>'其他',
        ]],
        'click_bottom_nav'=>['点击底部导航','',[
            1=>'首页',
            2=>'短视频',
            3=>'游戏',
            4=>'活动',
            5=>'AI创作',
            6=>'我的',
        ]],
        'click_more_videos'=>['点击更多影片',''],
        'click_change'=>['点击换一换',''],
        'click_video_cover'=>['点击视频封面','视频ID:{extraField1}'],
        'enter_video_page'=>['进入视频详情页','视频ID:{extraField1}'],
        'click_ad'=>['点击广告','跳转链接：{extraField2}'],
        'click_top_category'=>['点击顶部分类',''],
        'click_subcategory'=>['点击二级分类','',[
            1=>'最近更新',
            2=>'最多播放',
            3=>'好评榜',
        ]],
        'click_search'=>['点击搜索',''],
        'search_preset_tag'=>['搜索预置标签','搜索内容：{extraField1}'],
        'search_manual_tag'=>['手动搜索标签','搜索内容：{extraField1}'],
        'navigate_payment_page'=>['跳转支付页面','',[
            1=>'金币充值',
            2=>'棋牌充值',
            8=>'vip卡',
        ]],
        'invoke_cashier'=>['唤醒收银台',''],
        'create_order'=>['产生拉单','用户产生拉单，类型：{extraField2}，金额：{extraField1}'],
        'activate_vip_success'=>['开通VIP成功','用户成功开通VIP，卡名：{card_name}，金额：{extraField1}'],
        'coin_recharge_success'=>['金币充值成功','金额：{extraField1}'],
        'chess_recharge_success'=>['棋牌充值成功','金额：{extraField1}'],
        'close_app'=>['关闭APP',''],
        'switch_app'=>['切换应用',''],
        'play_video'=>['播放视频','用户开始播放视频，类型：{video_type_name}，视频ID: {extraField1}'],
        'pause_video'=>['暂停视频','视频id：{extraField1}，用户在第{extraField2}时暂停视频'],
        'fail_play_video'=>['播放失败','视频id：{extraField1}，用户播放视频失败'],
        'continue_play_video'=>['继续播放视频','视频id：{extraField1}，用户恢复播放视频'],
        'like_video'=>['点赞视频','视频id：{extraField1}，用户点赞视频'],
        'fast_forward_video'=>['快进视频','用户将视频从{extraField1}快进到{extraField2}'],
        'comment_video'=>['评论视频','视频id：{extraField1}，用户评论：{extraField2}'],
        'favorite_video'=>['收藏视频','视频id：{extraField1}，用户收藏视频'],
        'download_video'=>['下载视频','视频id：{extraField1}，用户下载视频'],
        'exit_video'=>['退出视频','视频id：{extraField1}，用户在观看到第{extraField2}时退出视'],
        'try_video'=>['试看视频','视频id：{extraField1}，用户开始播放试看视频'],
    ];

    const ROUTE_LIST=[
        'token'=>'/dh-api/diyauth/gettoken',
    ];

    const SUCCESS_CODE=0;

    public function getToken(){

        $redis_key = RedisConst::USER_BEHAVIOR_TOKENS_KEY.$this->user_id;

        $data = cacheGet($redis_key);
        if(!$data){
            $quest_data = [
                'userId'=>strval($this->user_id ?? ''),
                'account'=>strval($this->userinfo['account'] ?? ''),
                'platformCode'=>get_order_prefix(),
            ];
            $data = $this->commomRequest(self::ROUTE_LIST['token'] , $quest_data);

            if(isset($data) && !empty($data['token']) ){
                cacheSet($redis_key,$data,7200+mt_rand(1,300));
            }
        }

        return $data;
    }

    /**
     * 封装好的请求接口
     * @param string $url
     * @param array $origin_data
     * @return array
     * @throws ApiBusinessException
     * @throws \GuzzleHttp\Exception\GuzzleException
     * @throws \RedisException
     */
    public function commomRequest(string $url , array $origin_data){

        $this->client = new Client([
            'headers' => [
                'Content-Type' => 'application/json',
            ],
            //跟域名
            'base_uri' => sysconf("user_behavior_inter_url"),
            // 超时，可设置可不设置
            'timeout'  => 10,
        ]);

        return $this->request($url , $origin_data);
    }


    /**
     * 请求处理接口
     * @param string $url
     * @param array $origin_data
     * @return array
     * @throws ApiBusinessException|\GuzzleHttp\Exception\GuzzleException
     */
    private function request(string $url , array $origin_data)
    {

        try {

            if(empty($origin_data['sign'])){
                $origin_data['accessKey']=sysconf("uh_access_key");
                $origin_data['secretKey']=sysconf("uh_secret_key");
            }


            $response = $this->client->request('POST', $url, [
                'json' =>$origin_data
            ]);

            $result = json_decode($response->getBody()->getContents(), true);

            if($result['code']!=self::SUCCESS_CODE){

                if (in_array($result['code'],[1001])){
                    $log_data = ['data'=>$origin_data??[],'result'=>$result??[]];
                    logInfo($log_data,'ub_http_request',$url.(!empty($e)?'_error':''), 'ub_http_request');
                    $send_content = [
                        'from' =>sysconf('app_name'),
                        'subject' => '用户行为上报-java接口请求异常',
                        'date' => date('Y-m-d H:i:s'),
                        'content' =>'url:'.$url.',java:返回数据：'.json_encode($result??[],JSON_UNESCAPED_UNICODE)
                    ];
                    TelegramBot::sendMessageTextMust($send_content);
                    throw new ApiBusinessException('功能升级中，请稍后再试',1);
                }

                throw new ApiBusinessException($result['msg'],$result['code']);
            }


        }catch (\Throwable $e) {
        } finally {

            if(!empty($e)){
                $error_reason='error_code:'.$e->getCode().','.$e->getMessage().$e->getFile().$e->getLine();
                logInfo(['data'=>$origin_data??[],'result'=>$result??[],'error'=>$error_reason??''],'hb_http_request',$url, 'ub_http_request');
            }

            if (!empty($e) && !($e instanceof ApiBusinessException)) {

                $redis = redis();
                $redis_key = 'ub_error_count'; //指定时间内报错次数
                $error_count = $redis->get($redis_key);
                $tolerate_count=50;

                if($e->getCode()==404 && strpos($e->getMessage(),'404 Not Found')!==false){
                    $tolerate_count=120;
                }

                //超时波动允许一定次数内不报警，免得线上瞬时波动就老是报警
                if($e->getCode()==0 && strpos($e->getMessage(),'cURL error 28: Connection timed out after')!==false){
                    $tolerate_count=120;
                }

                if(!empty($error_count) && $error_count>$tolerate_count && $error_count<$tolerate_count+5 ){
                    $send_content = [
                        'from' =>sysconf('app_name'),
                        'subject' => '用户行为上报-java接口请求异常',
                        'date' => date('Y-m-d H:i:s'),
                        'content' =>'url:'.$url.',error:'.$error_reason
                    ];
//                    TelegramBot::sendMessageTextMust($send_content);
                }

                $redis->incr($redis_key);
                $redis->expire($redis_key,60*60);

                throw new \Exception($e->getMessage(),$e->getCode());
            }elseif(!empty($e)){
                throw new ApiBusinessException($e->getMessage(),$e->getCode());
            }

        }

        return $result['data']??[];
    }

    public function job(string $order_id){

        $order_detail = Db::name('app_pay_order')->where(['order_id'=>$order_id,'status'=>0])->find();
        if(!empty($order_detail)){
            $member_info_detail =  MemberInfoService::detail($order_detail['member_id']);
            if(!empty($member_info_detail)){

                if(!empty($order_detail['vip_id'])){
                    $extraField2 = $order_detail['vip_id'];
                    $uid = 'activate_vip_success';
                }elseif(in_array($order_detail['order_type'],[AppPayOrderService::ORDER_TYPE_MOVIE[0],AppPayOrderService::ORDER_TYPE_AI[0]])){
                    $uid = 'coin_recharge_success';
                }elseif ($order_detail['order_type']==AppPayOrderService::ORDER_TYPE_CHESS[0]){
                    $uid = 'chess_recharge_success';
                }

                if(!empty($uid)){
                    $time = time();
                    $install_data=[
                        'category'=>1,
                        'userId'=>strval($member_info_detail['id']),
                        'account'=>strval($member_info_detail['account']),
                        'platformCode'=>get_order_prefix(),
                        'uid'=>$uid,
                        'firstLevelPage'=>'',
                        'secondLevelPage'=>'',
                        'extraField1'=>strval($order_detail['pay_amount']/100),
                        'extraField2'=>$extraField2??'',
                        'createTime'=>date('Y-m-d H:i:s',$time),
                        'time'=>$time*1000,
                    ];


                    (new ElasticsearchService())->insertData(ElasticsearchService::INDEX_NAME, $install_data);

                }

            }
        }

    }


    /**
     * 数据处理公共封装方法
     * @param array $results
     * @return array
     */
    public static function handleData(array $results)
    {
        $results = array_column($results,'_source');
        $list=[];
        if(!empty($results)){
            foreach ($results as $v){
//                $v=$v['_source']??[];
                $user_behavior = UserBehaviorService::USER_BEHAVIOR_LIST[$v['uid']??'']??['','',[]];

                $extraField1 = $v['extraField1']??'';
                $extraField2 = $v['extraField2']??'';

                if(!empty($user_behavior[2]))$v['firstLevelPage'] = $user_behavior[2][$v['firstLevelPage']]??'';

                switch ($user_behavior[0]){
                    case UserBehaviorService::USER_BEHAVIOR_LIST['create_order'][0]:
                        if(!empty($extraField2)){
                            $card_name = Db::name('vip')->where(['id'=>$extraField2])->cache(600)->value('name');
                            $replace_string='VIP卡，'.'卡名：'.$card_name.'，';
                        }else{
                            $replace_string="充值";
                        }
                        $user_behavior[1] = str_replace('{extraField2}',$replace_string,$user_behavior[1]);

                        break;
                    case UserBehaviorService::USER_BEHAVIOR_LIST['activate_vip_success'][0]:
                        $card_name = Db::name('vip')->where(['id'=>$extraField2])->cache(600)->value('name');
                        $user_behavior[1] = str_replace('{card_name}',$card_name,$user_behavior[1]);

                        break;
                    case UserBehaviorService::USER_BEHAVIOR_LIST['play_video'][0]:
                        $type = Db::name('app_movie')->where(['id'=>$extraField1])->cache(600)->value('type');
                        $user_behavior[1] = str_replace('{video_type_name}',AppMovieService::getConstPluck('TYPE_')[$type]??'',$user_behavior[1]);
                        break;
                }

                $describe = str_replace('{extraField2}',$extraField2,str_replace('{extraField1}',$extraField1,$user_behavior[1]));
                $describe_short = mb_substr($describe,0,100).(strlen($describe)>100?'.....':'');

                $list[]=[
                    'userId'=>$v['userId']??'',
                    'account'=>$v['account']??'',
                    'platformCode'=>$v['platformCode']??'',
                    'uid_name'=>$user_behavior[0]??'',
                    'page'=>($v['firstLevelPage']??'').(empty($v['secondLevelPage']??'')?'':" --> ".($v['secondLevelPage']??'')),
                    'describe'=>$describe,
                    'createTime'=>$v['createTime']??'',
                    'describe_short'=>$describe_short??'',
                ];
            }
        }

        return $list;
    }






}
