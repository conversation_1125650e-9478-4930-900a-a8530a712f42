<?php

namespace app\Service;


//用户登录记录service类
use app\common\Constants\LogConst;
use app\common\Constants\RedisConst;
use app\Helper\CacheHelper;
use think\Db;
use think\db\exception\DataNotFoundException;
use think\db\exception\ModelNotFoundException;
use think\exception\DbException;
use think\Model;

class UserLoginService extends BaseService {


    /**
     * 更新用户最后登录时间
     * @param int $member_id
     * @param string $app_version
     * @return void
     * @throws \think\Exception
     * @throws \think\exception\PDOException
     */
    public static function updateLogin(int $member_id,string $app_version)
    {
        try{
            $limit_rate_key = RedisConst::USER_UPDATE_LOGIN_KEY.$member_id;
            if(!CacheHelper::limitRate($limit_rate_key,'',10,false)){
                $detail =  Db::name('user_login')->where(['member_id'=>$member_id])->field('id','last_login_time')->cache(true,86400)->find();
                if(!empty($detail['id'])){
                    $sql = Db::name('user_login')->where(['id'=>$detail['id']])->fetchSql()->update([
                        'last_app_version'=>$app_version,
                        'last_login_time'=>time(),
                    ]);
                }else{
                    $sql = Db::name('user_login')->fetchSql()->insert([
                        'member_id'=>$member_id,
                        'last_app_version'=>$app_version,
                        'last_login_time'=>time(),
                    ]);
                }
                pushSql($sql);
            }
        }catch (\Throwable $e){
            commonLog(LogConst::USER_LOGIN_LOG[1],[
                'member_id'=>$member_id,
                'app_version'=>$app_version,
                'error'=>$e->getMessage().$e->getFile().$e->getLine()],LogConst::USER_LOGIN_LOG[0]);
        }



    }







}
