<?php

namespace app\Service;


//vip时间变动记录service类
use think\Db;

class VipExpireLogService extends BaseService {


    const TYPE_BUY=[1,'购买'];
    const TYPE_OLINE_RECHARGE=[2,'单次充值奖励'];
    const TYPE_TOTAL_RECHARGE=[3,'累计充值奖励'];
    const TYPE_DOWN_ORDER=[4,'掉单补充'];
    const TYPE_DOWN_OTHER=[5,'其他'];
    const TYPE_DOWN_NEW_USER=[6,'新用户'];
    const TYPE_DOWN_TURNTABLE=[7,'转盘活动中奖'];
    const TYPE_INTEGRAL=[8,'积分兑换'];
    const TYPE_RECHARGEL=[9,'充值赠送红包'];

}
