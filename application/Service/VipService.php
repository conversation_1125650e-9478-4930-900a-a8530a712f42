<?php

namespace app\Service;


//vip卡service类
use think\Db;

class VipService extends BaseService {


    /** @var array VIP卡显示条件 */
    const SHOW_CONDITION_NOTHING = [0,'无'];
    const SHOW_CONDITION_OPENED_VIP = [1,'已支付开通过普通vip的用户'];
    const SHOW_CONDITION_OPENED_SVIP = [2,'已支付过开通超级vip的用户'];

    /** @var array VIP卡隐藏条件 */
    const HIDE_CONDITION_NOTHING = [0,'无'];
    const HIDE_CONDITION_BUY_ONE = [1,'用户购买过一次后'];

    /** @var array VIP卡类型 */
    const CARD_TYPE_MOVIE=[1,'视频卡'];
    const CARD_TYPE_AI=[2,'ai卡'];


    /** @var array ai会员卡显示条件 */
    const AI_VIP_SHOW_CONDITION_ALL = [1,'全部用户（展示）'];
    const AI_VIP_SHOW_CONDITION_NEVER = [2,'全部用户（不展示）'];
    const AI_VIP_SHOW_CONDITION_ORDER_COUNT = [3,'AI任务订单数≥2的全部用户'];
    const AI_VIP_SHOW_CONDITION_REGIST_TIME = [4,'注册时长≥N天'];

    /**
     * 指定用户是否展示ai卡信息
     * @param int $member_id
     * @param int $member_create_time
     * @return bool
     */
    public static function isShowAiCard(int $member_id,int $member_create_time): bool
    {
        $result = true;
        $ai_vip_show_condition = Db::name('equity_basic')->where('id', '=', 1)->cache(60)->value('ai_vip_show_condition');

        if(!empty($ai_vip_show_condition)){
            $ai_vip_show_condition=json_decode($ai_vip_show_condition,true);

            switch ($ai_vip_show_condition['ai_vip_show_condition']){
                case self::AI_VIP_SHOW_CONDITION_ALL[0];break;
                case self::AI_VIP_SHOW_CONDITION_NEVER[0];
                    $result=false;
                    break;
                case self::AI_VIP_SHOW_CONDITION_ORDER_COUNT[0];
                    if(Db::name('ai_order')->where(['member_id'=>$member_id,'status'=>AiOrderService::STATUS_SUCCESS[0]])->count()<2){
                        $result=false;
                    }
                    break;
                case self::AI_VIP_SHOW_CONDITION_REGIST_TIME[0];
                    if(!empty($ai_vip_show_condition['ai_vip_show_regist_time']) &&
                        is_numeric($ai_vip_show_condition['ai_vip_show_regist_time']) &&
                        time()<($member_create_time+(86400*$ai_vip_show_condition['ai_vip_show_regist_time']))
                    ){
                        $result=false;
                    }
                    break;
            }
        }

        return $result;

    }
}
