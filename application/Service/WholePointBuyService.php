<?php

namespace app\Service;


//活动service类
use think\Db;

class WholePointBuyService {

    //记录类型 1、已结束  2、开枪中 3、即将开始
    const ACTIVITY_STATUS_PAST=1;
    const ACTIVITY_STATUS_ING=2;
    const ACTIVITY_STATUS_FUTURE=3;


    //商品状态 1、已结束  2、开枪中 3、即将开始 4、已兜售 5、已购买
    const GOODS_STATUS_PAST=1;
    const GOODS_STATUS_ING=2;
    const GOODS_STATUS_FUTURE=3;
    const GOODS_STATUS_SOLD_OUT=4;
    const GOODS_STATUS_BOUGHT=5;


    /*
     * 获取指定秒杀商品的活动状态
     */
    public static function getActivityStatus(string $start_time)
    {
        if(strtotime($start_time)>time()){
            return self::ACTIVITY_STATUS_FUTURE;
        }

        $where = [
            ['status', '=', 1],
            ['start_time', '<', date('Y-m-d H:i:s')],
        ];
         $detail = Db::name('whole_point_buy')->where($where)->whereNull('delete_time')->order("start_time desc")->find();

         if(empty($detail)){
             return self::ACTIVITY_STATUS_ING;
         }

         if($detail['start_time']==$start_time){
             return self::ACTIVITY_STATUS_ING;
         }else{
             return self::ACTIVITY_STATUS_PAST;
         }

    }

}