<?php
namespace app\admin\controller;

use think\Db;
class SystemTj extends BaseController
{
    public $title, $current_url;
    public function initialize()
    {
        parent::initialize();
        $this->title = "系统统计";
        $this->current_url = "/admin/system_tj";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
		
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        
        $res['all_recharge'] = Db::name('member_charge')->where(array("status"=>2))->sum("money");
        $res['all_user'] = Db::name('member_info')->count();

        $todayWhere = [];
        $todayWhere[] = ['create_time', '>=', strtotime(date('Y-m-d 00:00:00', time() - 3600 * 24 * 0))];
        $todayWhere[] = ['create_time', '<=', strtotime(date('Y-m-d 23:59:59', time() - 3600 * 24 * 0))];

        $res['today_user'] = Db::name('member_info')->where($todayWhere)->count();

        $res['today_money'] =  Db::name('member_charge')->where($todayWhere)->where(array("status"=>2))->sum("money");
        $res['today_vip'] =  Db::name('member_charge')->where($todayWhere)->where(array("status"=>2,"charge_type"=>1))->sum("money");
        $res['today_yb'] =  Db::name('member_charge')->where($todayWhere)->where(array("status"=>2,"charge_type"=>2))->sum("money");

        $channel = Db::name("app_channel")->where("status","=","1")->select();


        $whereTime = [];
        if (!empty($condition['begin_time'])) $whereTime[] = ['create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $whereTime[] = ['create_time', '<', strtotime($condition['end_time'])+24*3600-1];
        $list = [];
        foreach ($channel as $key => $one) {
            $list[$key]['channel_name'] = $one['title'];
            $list[$key]['channel_no'] = $one['no'];
            $list[$key]['users'] = Db::name('member_info')->where("channel_id","=",$one['id'])->where($whereTime)->count();
            $list[$key]['charge_vip'] = Db::name('member_charge')->where(array("channel_id"=>$one['id'],"status"=>2,"charge_type"=>1))->where($whereTime)->sum("money");
            $list[$key]['charge_yb'] = Db::name('member_charge')->where(array("channel_id"=>$one['id'],"status"=>2,"charge_type"=>2))->where($whereTime)->sum("money");
            $list[$key]['charge_money'] =  Db::name('member_charge')->where(array("channel_id"=>$one['id'],"status"=>2))->where($whereTime)->sum("money");
    
            if($list[$key]['users']==0){
                 $list[$key]['ap'] = 0;
            }else{
                 $list[$key]['ap'] = $list[$key]['charge_money']/$list[$key]['users'];
            }
           
        }
        $this->assign([
            'res' => $res,
            'list' => $list,
        ]);
        $this->_assign();
        return view();
    }
}