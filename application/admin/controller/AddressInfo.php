<?php
namespace app\admin\controller;

use think\Db;
class AddressInfo extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "address_info",
            'title' => "通讯录",
            'current_url' => "/admin/address_info",
        ];
        $this->where = [
            'username' => 'like',
            'app_id' => '',
        ];

        $this->class_list = Db::name('app_info')->select();
        parent::__construct($conf);
    }
}