<?php
namespace app\admin\controller;

use think\Db;
class Admin extends BaseController
{
    public function initialize()
    {
        parent::initialize();
        $this->name = 'admin';
        $this->title = '管理员列表';
        $this->current_url = '/admin/'.$this->name;
    }
    public function _assign(){
        $this->assign(['current_url'=>$this->current_url]);
        $this->assign(['title'=>$this->title]);
    }
    public function index()
    {
        $where = [];
        $keyword = input('request.keyword');
        if(!empty($keyword)) $where[] = ['username','like',"%".$keyword."%"];

        $list = Db::name($this->name)->order('id desc')->where($where)->paginate(20);
        $this->assign([
            'list'=>$list,
            'keyword'=>$keyword,
        ]);
        $this->_assign();

        return view();
    }
    public function add()
    {
        if(request()->isPost()){
            $post = input('post.');
            if(empty($post['username'])) json_fail_admin('用户名不能为空!');
            if(empty($post['password'])) json_fail_admin('密码不能为空!');
            if($post['password'] != $post['password2']) json_fail_admin('两次输入密码不一致!');
            $count = Db::name($this->name)->where(['username'=>$post['username']])->count();
            if($count>0) json_fail_admin('用户已存在!');
            $data = [
                    'username'=>$post['username'],
                    'password'=>encode($post['password']),
                    'remark'=>$post['remark'],
                    'create_user'=>$this->user['id'],
                    'create_time'=>time(),
                ];
            $res = Db::name($this->name)->insert($data);
            if (!$res) {
                json_fail_admin();
            }
            json_success_admin();
        }else{
            return view();
        }
    }
    public function edit($id)
    {
        if($id<2) json_fail_admin('访问出错!');
        $res = Db::name($this->name)->where(['id'=>$id])->find();
        if(empty($res)) json_fail_admin('管理员不存在');
        if(request()->isPost()){
            $post = input('post.');
            if(empty($post['username'])) json_fail_admin('用户名不能为空!');
            $where[] = ['id','<>',$id];
            $where[] = ['username','=',$post['username']];
            $count = Db::name($this->name)->where($where)->count();
            if($count>0) json_fail_admin('用户名重复!');
            $data = [
                    'username'=>$post['username'],
                    'remark'=>$post['remark'],
                    'update_user'=>$this->user['id'],
                    'update_time'=>time(),
                ];
            if(!empty($post['password'])) {
                if($post['password'] != $post['password2']) json_fail_admin('两次输入密码不一致!');
                $data['password'] = encode($post['password']);
            }
            $res ? json_success_admin() : json_fail_admin();
        }else{
            $this->assign(['res'=>$res]);
            return view();
        }
    }
    public function del($id)
    {
        if($id==1) json_fail_admin('默认管理员不能删!');
        $res = Db::name($this->name)->delete($id);
        $res ? json_success_admin() : json_fail_admin();
    }
}
