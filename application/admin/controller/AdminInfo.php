<?php
namespace app\admin\controller;

use think\Db;

class AdminInfo extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "admin_info";
        $this->title = "管理员列表";
        $this->current_url = "/admin/admin_info";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $keyword = input('request.keyword');
        $role_id = input('request.role_id');
        if (!empty($keyword)) $where[] = ['a.username', 'like', '%' . $keyword . '%'];
        if (!empty($role_id)) $where[] = ['a.role_id', '=', $role_id];

        $list = Db::name($this->name)->alias('a')
            ->join('admin_role b', 'a.role_id=b.id', 'left')
            ->field('a.*,b.name role')->order('a.id desc')
            ->where($where)->paginate(20);
        $role_list = Db::name('admin_role')->order('id desc')->select();
        $this->assign([
            'list' => $list,
            'role_list' => $role_list,
            'keyword' => $keyword,
            'role_id' => $role_id,
            'my_role_id' => $this->user['role_id']
        ]);

        $this->_assign();
        return view();
    }
    public function add()
    {
        if (!request()->isPost()) {
            $list = Db::name('admin_role')->order('id desc')->select();
            $this->assign(['list' => $list]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['username'])) json_fail_admin('用户名不能为空!');
            if (empty($post['password'])) json_fail_admin('密码不能为空!');
            if ($post['password'] != $post['password2']) json_fail_admin('两次输入密码不一致!');
            if (empty($post['role_id'])) json_fail_admin('请选择角色!');
            $salt = getRandStr();
            $data = [
                'username' => $post['username'],
                'password' => encrypt_password($post['password'],$salt),
                'salt' => $salt,
                'role_id' => $post['role_id'],
                'create_time' => time(),
                'create_user' => $this->user['id'],
            ];
            $count = Db::name($this->name)->where('username', '=', $data['username'])->count();
            if ($count > 0) json_fail_admin('用户名重复!');

            if($this->user['role_id']!=\app\admin\model\AdminRole::ROLE_SUPER_ADMIN && in_array($post['role_id'],[\app\admin\model\AdminRole::ROLE_SUPER_ADMIN,\app\admin\model\AdminRole::ROLE_ADMIN] )){
                json_fail_admin('只有超级管理员才能创建管理员或超管账号!');
            }

            $res = Db::name($this->name)->insert($data);
            if (!$res) {
               json_fail_admin();
            }
            $this->addlog('添加管理','添加管理: '.$data['username']);
            json_success_admin();
        }
    }

    public function edit($id)
    {
        if ($id < 2) json_fail_admin('参数错误');
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if(empty($res)) json_fail_admin('管理员不存在');
        if (!request()->isPost()) {
            $list = Db::name('admin_role')->order('id desc')->select();
            $this->assign([
                'list' => $list,
                'res' => $res,
            ]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['username'])) json_fail_admin('用户名不能为空!');
            if ($post['password'] && $post['password'] != $post['password2']) {
               json_fail_admin('两次输入密码不一致!');
            }
            
            if (empty($post['role_id'])) json_fail_admin('请选择角色!');
            $count = Db::name($this->name)->where([['username', '=', $post['username']], ['id', '<>', $id]])->count();
            if ($count > 0) json_fail_admin('用户名重复!');
            $data = [
                'username' => $post['username'],
                'role_id' => $post['role_id'],
                'update_time' => time(),
                'update_user' => $this->user['id'],
                'channels' => $post['channels']
            ];
            if($post['password']){
                $data['password'] = encrypt_password($post['password'],$res['salt']);
            }

            if($this->user['role_id']!=\app\admin\model\AdminRole::ROLE_SUPER_ADMIN && $res['role_id'] != $post['role_id']  && in_array($post['role_id'],[\app\admin\model\AdminRole::ROLE_SUPER_ADMIN,\app\admin\model\AdminRole::ROLE_ADMIN] )){
                json_fail_admin('只有超级管理员才能创建管理员或超管账号!');
            }

            $res = Db::name($this->name)->where($where)->update($data);
            $this->addlog('修改管理信息','修改管理信息: '.$data['username']);

            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function del($id)
    {
        if ($id < 2) json_fail_admin('参数错误');
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        $name = $res['username'];
        if(empty($res)) json_fail_admin('管理员不存在');
        $res = Db::name($this->name)->where($where)->delete();
        if (!$res) {
           json_fail_admin();
        }
        $this->addlog('删除管理','删除管理: '.$name);
        json_success_admin();
    }

    public function untie($id)
    {
//        if ($id < 2) json_fail_admin('参数错误');
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        $name = $res['username'];
        if(empty($res)) json_fail_admin('管理员不存在');
        $res = Db::name($this->name)->where($where)->update(['is_bind_google_check'=>0,'google_secret'=>'']);
//        if (!$res) {
//            json_fail_admin();
//        }
        $this->addlog('解绑谷歌登录验证','解绑谷歌登录验证：: '.$name);
        json_success_admin();
    }
    

    public function status($id)
    {
        if ($id < 2) json_fail_admin('参数错误');
        $where[] = ['id', '=', $id];
        $info = Db::name($this->name)->where($where)->find();     
        if(empty($info)) json_fail_admin('管理员不存在');
        $res = Db::name($this->name)->where($where)->exp('status', 'if(status=1,2,1)')->update();
        if (!$res) {
           json_fail_admin();
        }
        if ($info['status'] == 1) {
            $this->addlog('禁用管理','禁用管理: '.$info['username']);
        }else{
            $this->addlog('启用管理','启用管理: '.$info['username']);
        }
        
        json_success_admin();
    }
}