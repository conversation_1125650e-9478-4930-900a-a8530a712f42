<?php
namespace app\admin\controller;

use think\Db;

class AdminNode extends BaseController
{
    public $title, $current_url, $model_name;
    public function initialize()
    {
        parent::initialize();
        $this->model_name = "admin_node";
        $this->title = "节点管理";
        $this->current_url = "/admin/admin_node";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $list = self::get_menu();
        $this->assign(['list' => $list]);
        $this->_assign();
        return view();
    }
    public function add()
    {
        $id = input('param.id/d');
        $pid = input('param.pid/d');
        $level = input('param.level/d');
        if (($id < 0) || ($pid < 0) || ($level < 0)) json_fail_admin('访问错误!');
        if (!request()->isPost()) {
            $list = parent::get_menu();
            $this->assign(['list' => $list]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['title']) || empty($post['path'])) json_fail_admin('权限名称和地址不能为空!');
            $data = [
                'title' => $post['title'],
                'path' => $post['path'],
                'sort' => $post['sort'],
                'pid' => $pid,
                'level' => $level,
                'create_time' => time(),
                'create_user' => $this->user['id'],
            ];
            $res = Db::name($this->model_name)->insert($data);
            if (!$res) {
               json_fail_admin();
            }
            $this->addlog('添加权限','添加权限: '.$data['title']);
            json_success_admin();
        }
    }

    public function edit($id)
    {
        $id = input('param.id/d');
        $pid = input('param.pid/d');
        $level = input('param.level/d');
        if (($id < 0) || ($pid < 0) || ($level < 0)) json_fail_admin('访问错误!');
        $where[] = ['id', '=', $id];
        $info = Db::name($this->model_name)->where($where)->find();
        if (empty($info)) json_fail_admin('节点不存在');

        if (!request()->isPost()) {
            $this->assign(['res' => $info]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['title']) || empty($post['path'])) json_fail_admin('权限名称和地址不能为空!');
            $data = [
                'title' => $post['title'],
                'path' => $post['path'],
                'sort' => $post['sort'],
                'pid' => $pid,
                'level' => $level,
                'update_time' => time(),
                'update_user' => $this->user['id'],
            ];
            $res = Db::name($this->model_name)->where($where)->update($data);
            if (!$res) {
               json_fail_admin();
            }
            $this->addlog('修改权限','修改权限: '.$info['title'].' 为 '.$data['title']);
            json_success_admin();
        }
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $info = Db::name($this->model_name)->where($where)->find();
        if (empty($info)) json_fail_admin('节点不存在');

        if (Db::name($this->model_name)->where('pid', '=', $id)->count() > 0) json_fail_admin('该节点下有子节点,不能删除!');
        $res = Db::name($this->model_name)->where($where)->delete();
        if (!$res) {
           json_fail_admin();
        }
        $this->addlog('删除权限','删除权限: '.$info['title']);
        json_success_admin();
    }
}