<?php
namespace app\admin\controller;

use think\Db;

class AdminRole extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "admin_role";
        $this->title = "角色管理";
        $this->current_url = "/admin/admin_role";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $list = Db::name($this->name)->alias('a')
            ->field('a.id,a.name,a.remark,a.default,(select count(1) from hg_admin_info b where b.role_id = a.id)count')
            ->order('a.id desc')->paginate(20);
        $this->assign(['list' => $list]);
        $this->_assign();
        return view();
    }
    public function add()
    {
        if (!request()->isPost()) {
            $list = parent::get_menu();
            $this->assign(['list' => $list]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['name'])) json_fail_admin('角色名不能为空');
            $data = [
                'name' => $post['name'],
                'remark' => $post['remark'],
                'create_time' => time(),
                'create_user' => $this->user['id'],
            ];
            if (Db::name($this->name)->where('name', '=', $data['name'])->count() > 0) json_fail_admin('角色名重复!');

            $id = Db::name($this->name)->insertGetId($data);
            if (!$id) json_fail_admin();
            $ids = $post['menu_ids'];
            if (!empty($ids)) {
                $menu = explode(',', $ids);
                foreach ($menu as $k => $v) {
                    $asd[] = [
                        "node_id" => $v,
                        "role_id" => $id,
                        "create_time" => time(),
                        'create_user' => $this->user['id'],
                    ];
                }
                $res = Db::name('admin_role_node')->insertAll($asd);
            }
            if (!$res) {
               json_fail_admin();
            }
            $this->addlog('添加角色','添加角色: '.$data['name']);
            json_success_admin();
        }
    }

    public function edit($id)
    {
        $where[] = ['id', '=', $id];
        $info = Db::name($this->name)->where($where)->find();
        if (empty($info)) json_fail_admin('角色不存在');
        
        if (!request()->isPost()) {
            $list = parent::get_menu();
            $check = Db::name('admin_role_node')->where('role_id', '=', $id)->column('node_id');
            $this->assign([
                'res' => $info,
                'list' => $list,
                'check' => $check,
            ]);
            return view();
        } else {
            $post = input('post.');
            $data = [
                'name' => $post['name'],
                'remark' => $post['remark'],
                'update_time' => time(),
                'update_user' => $this->user['id'],
            ];
            if (empty($data['name'])) json_fail_admin('角色名不能为空');

            $count = Db::name($this->name)->where([['id', '<>', $id], ['name', '=', $data['name']]])->count();
            if ($count > 0) json_fail_admin('角色名重复!');
            $res = Db::name($this->name)->where('id', '=', $id)->update($data);
            if (!$res) json_fail_admin();
            Db::name('admin_role_node')->where('role_id', '=', $id)->delete();
            $ids = $post['menu_ids'];
            if (!empty($ids)) {
                $menu = explode(',', $ids);
                foreach ($menu as $k => $v) {
                    $asd[] = [
                        "node_id" => $v,
                        "role_id" => $id,
                        "create_time" => time(),
                        "create_user" => $this->user['id'],
                    ];
                }
                $res = Db::name('admin_role_node')->insertAll($asd);
            }
            if (!$res) {
               json_fail_admin();
            }
            $this->addlog('修改角色','修改角色: '.$info['name'].' 为 '.$data['name']);
            json_success_admin();
        }
    }

    public function del($id)
    {
        if ($id < 2) json_fail_admin('参数错误');
        $info = Db::name($this->name)->where('id', '=', $id)->find();
        if (empty($info)) json_fail_admin('角色不存在');

        $where[] = ['role_id', '=', $id];
        if (Db::name('admin_info')->where($where)->count() > 0) json_fail_admin('该角色下有管理员,不能删除!');
        $res = Db::name($this->name)->where('id', '=', $id)->delete();
        if (!$res) json_fail_admin();
        Db::name('admin_role_node')->where($where)->delete();
        $this->addlog('删除角色','删除角色: '.$info['name']);
        $res ? json_success_admin() : json_fail_admin();
    }
}