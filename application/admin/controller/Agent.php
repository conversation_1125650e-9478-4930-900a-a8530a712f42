<?php
namespace app\admin\controller;

use think\Db;

/**
 * 代理管理
 *
 */
class Agent extends BaseController
{
    private $baseUrl = '/admin/agent';
    private static $redis = null;
    const AGENT_CREDIT_KEY_PRE = 'agent_credit_';
    const AGENT_CREDIT_STATE_KEY_PRE = 'agent_credit_state_';

    /**
     * 代理列表
     */
    public function user(){
        $search = input('request.search', '', 'trim');
        $where[] = ['status', '=', 1];
        if ($search) {
            $where[] = ['id|user_name|nick_name|weixin|qq', 'like', '%' . $search . '%'];
        }
        $list = Db::name('agent_info')->where($where)->order('id desc')->paginate(10);
        $this->assign([
            'baseUrl' => $this->baseUrl,
            'title' => '代理列表',
            'search' => $search,
            'list' => $list,
        ]);
        return view();
    }

    /**
     * 添加代理
     */
    public function user_add() {
        if (request()->isPost()) {
            // 数据提交
            $data = $this->getAgentCheckedData();
            $data['create_time'] = time();
            $data['create_user'] = $this->user['id'];
            if (! Db::name('agent_info')->insert($data)) json_fail_admin('添加失败！');
            $this->addlog('添加代理','添加代理: '. $data['user_name']);
            json_success_admin();
        }
        return view();
    }

    /**
     * 代理授信
     * @param number $id
     */
    public function user_credit($id) {
        $data = $this->getAgentInfo($id);
        if (request()->isPost()) {
            // 接收数据
            if (! key_exists('credit', input('post.'))) json_fail_admin('本次授信金额不能为空！');
            $credit = input('post.credit');
            if (! is_numeric($credit) || $credit == 0) json_fail_admin('本次授信金额错误！');
            // 金额只允许保留两位小数
            $pattern = '/^\-?[0-9]{1,14}(\.[0-9]{1,2})?$/';
            if (! preg_match($pattern, $credit)) json_fail_admin('授信金额只支持两位小数！');
            // 更新
            $opName = $this->getUserCreditState($id);
            if ($opName !== null) json_fail_admin('当前代理正在被管理员【' . $opName . '】进行授信，请等待操作结束！',[], 2);
            $this->setUserCreditStateLock($id);
            $this->setUserCreditLock($id);
            $creditOri = Db::name('agent_info')->where('id', $id)->value('credit_limit');
            $creditNew = $creditOri + $credit;
            $update = [
                'credit_limit' => $creditNew,
                'update_time' => time(),
                'update_user' => $this->user['id']
            ];
            Db::startTrans();
            try {
                $result = Db::name('agent_info')->where('id', $id)->update($update);
                if ($result < 0) {
                    Db::rollback();
                    $this->setUserCreditLock($id, 0);
                    $this->setUserCreditStateLock($id, 0);
                    json_fail_admin('授信失败！');
                }
                // 添加记录
                $log = [
                    'agent_id' => $id,
                    'agent_user' => $data['user_name'],
                    'credit_before' => $creditOri,
                    'credit' => $credit,
                    'credit_after' => $creditNew,
                    'credit_date' => date('Y-m-d H:i:s'),
                    'create_time' => time(),
                    'create_user' => $this->user['id']
                ];
                $i = 0;
                do {
                    if (Db::name('agent_credit')->insert($log)) {
                        $logState = false;
                        $result = true;
                    } else {
                        $result = false;
                        if ($i > 3) {
                            $logState = false;
                        } else {
                            $logState = true;
                            usleep(100);
                        }
                    }
                    $i ++;
                } while ($logState);
                if ($result) {
                    Db::commit();
                } else {
                    Db::rollback();
                }
                $this->setUserCreditLock($id, 0);
                $this->setUserCreditStateLock($id, 0);
                if (! $result) json_fail_admin('授信失败，数据已回滚！');

                $this->addlog('代理增加额度','代理: '. $data['user_name'].' 增加额度:'.$credit);

                json_success_admin();
            } catch (\Exception $e) {
                Db::rollback();
                $this->setUserCreditLock($id, 0);
                $this->setUserCreditStateLock($id, 0);
                json_fail_admin($e->getMessage());
            }
        }
        $this->assign('data', $data);
        return view();
    }

    /**
     * 修改代理
     */
    public function user_edit($id) {
        // 查询数据是否存在
        $data = $this->getAgentInfo($id);
        if (request()->isPost()) {
            // 数据提交
            $post = $this->getAgentCheckedData($id);
            unset($post['user_name']);
            foreach ($post as $key => $item) {
                if ($item == $data[$key]) {
                    unset($post[$key]);
                }
            }
            if (! empty($post)) {
                $post['update_time'] = time();
                $post['update_user'] = $this->user['id'];
                if (Db::name('agent_info')->where('id', $id)->update($post) < 0) json_fail_admin('修改失败！');
            }
            $this->addlog('修改代理信息','代理: '. $data['user_name']);
            json_success_admin();
        }
        // 显示页面
        $this->assign([
            'data' => $data
        ]);
        return view('user_add');
    }

    /**
     * 代理启用、禁用
     * @param number $id
     */
    public function user_state($id) {
        // 查询数据是否存在
        $data = $this->getAgentInfo($id);
        $status['state'] = $data['state'] == 1 ? 0 : 1;
        $status['update_time'] = time();
        $status['update_user'] = $this->user['id'];
        if (! Db::name('agent_info')->where('id', $id)->update($status)) json_fail_admin();
        if ($status['state'] == 0) {
            $this->addlog('禁用代理','代理: '. $data['user_name']);
        }else{
            $this->addlog('启用代理','代理: '. $data['user_name']);
        }


        json_success_admin();
    }

    /**
     * 代理删除
     * @param number $id
     */
    public function user_del($id) {
        $data = $this->getAgentInfo($id);

        $status['status'] = 0;
        $status['update_time'] = time();
        $status['update_user'] = $this->user['id'];
        if (! Db::name('agent_info')->where('id', $id)->update($status)) json_fail_admin();

        $this->addlog('删除代理','代理: '. $data['user_name']);

        json_success_admin();
    }

    /**
     * 代理授信记录
     */
    public function credit() {
        $temp = $this->getCreditLogCondition();
        $where = $temp['where'];
        $search = $temp['search'];
        $list = Db::name('agent_credit')->alias('a')->leftJoin('admin_info b', 'a.create_user=b.id')->where($where)
                    ->field('a.agent_user,a.credit,a.create_time,a.credit_after,b.username admin_name')->order('a.id desc')->paginate(10);
        $this->assign([
            'list' => $list,
            'search' => $search,
            'baseUrl' => $this->baseUrl
        ]);
        return view();
    }

    /**
     * 授信记录导出
     */
    public function credit_export() {
        $where = $this->getCreditLogCondition('where');
        $list = Db::name('agent_credit')->alias('a')->leftJoin('admin_info b', 'a.create_user=b.id')->where($where)
                ->field('a.agent_user,a.credit,a.credit_date,a.credit_after,b.username')->order('a.id desc')->select();
        if (empty($list)) json_fail_admin('没有数据可供导出');
        $title = ['代理账号', '授信金额', '授信时间', '操作后授信余额', '操作人'];
        $this->addlog('代理授信记录导出','');

        $this->exportExcel($list, $title, '代理授信记录');
    }

    /**
     * 代理充值记录
     */
    public function recharge() {
        $temp = $this->getRechargeLogCondition();
        $where = $temp['where'];
        $search = $temp['search'];
        $list = Db::name('agent_recharge')->alias('a')->leftJoin('member_info b', 'a.user_id=b.id')->where($where)
            ->field('a.agent_user,b.mobile,a.user_id,a.order,a.recharge,a.recharge_after,a.recharge_date')->order('a.id desc')->paginate(10);
        $sum = Db::name('agent_recharge')->alias('a')->leftJoin('member_info b', 'a.user_id=b.id')->where($where)->sum('a.recharge');
        $this->assign([
            'list' => $list,
            'search' => $search,
            'baseUrl' => $this->baseUrl,
            'sum' => $sum,
        ]);
        return view();
    }

    /**
     * 代理充值记录导出
     */
    public function recharge_export() {
        $where = $this->getRechargeLogCondition('where');
        $list = Db::name('agent_recharge')->alias('a')->leftJoin('member_info b', 'a.user_id=b.id')->where($where)
                ->field('a.agent_user,b.mobile user_name,a.recharge,a.recharge_after,a.recharge_date')->order('a.id desc')->select();
        if (empty($list)) json_fail_admin('没有数据可代导出');
        $title = ['代理账号', '用户账号', '用户充值金额', '代理授信余额', '充值时间'];
        $this->addlog('代理充值记录导出','');

        $this->exportExcel($list, $title, '代理充值记录');
    }

    /**
     * 获取授信记录的返回条件
     * @param string $getType
     * @return array
     */
    private function getCreditLogCondition($getType = 'all') {
        $post = input('request.');
        $search = [
            'begin_time' => key_exists('begin_time', $post) ? $post['begin_time'] : '',
            'end_time' => key_exists('end_time', $post) ? $post['end_time'] : '',
            'agent_user' => key_exists('agent_user', $post) ? $post['agent_user'] : '',
            'credit' => key_exists('credit', $post) ? $post['credit'] : ''
        ];
        $pattern = '/^20[0-9]{2}\-[0|1][0-9]\-[0|1|2|3][0-9]$/';
        $where = [];
        if (preg_match($pattern, $search['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($search['begin_time'])];
        if (preg_match($pattern, $search['end_time'])) $where[] = ['a.create_time', '<=', strtotime($search['end_time'] . ' 23:59:59')];
        if (! empty($search['agent_user'])) $where[] = ['a.agent_user', 'like', '%'. $search['agent_user'] .'%'];
        if (! empty($search['credit']) && is_numeric($search['credit'])) $where[] = ['a.credit', '=', $search['credit']];
        if ($getType == 'all') {
            return [
                'search' => $search,
                'where' => $where
            ];
        } elseif ($getType == 'where') {
            return $where;
        } else {
            return $search;
        }
    }

    /**
     * 获取代理充值记录查询条件
     * @param string $getType
     * @return array
     */
    private function getRechargeLogCondition($getType = 'all') {
        $post = input('request.');
        $search = [
            'begin_time' => key_exists('begin_time', $post) ? $post['begin_time'] : '',
            'end_time' => key_exists('end_time', $post) ? $post['end_time'] : '',
            'agent_user' => key_exists('agent_user', $post) ? $post['agent_user'] : '',
            'user_name' => key_exists('user_name', $post) ? $post['user_name'] : '',
            'recharge' => key_exists('recharge', $post) ? $post['recharge'] : ''
        ];
        $pattern = '/^20[0-9]{2}\-[0|1][0-9]\-[0|1|2|3][0-9]$/';
        $where = [];
        if (preg_match($pattern, $search['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($search['begin_time'])];
        if (preg_match($pattern, $search['end_time'])) $where[] = ['a.create_time', '<=', strtotime($search['end_time'] . ' 23:59:59')];
        if (! empty($search['agent_user'])) $where[] = ['a.agent_user', 'like', '%'. $search['agent_user'] .'%'];
        if (! empty($search['user_name'])) $where[] = ['b.mobile', 'like', '%'. $search['user_name'] .'%'];
        if (! empty($search['recharge']) && is_numeric($search['recharge'])) $where[] = ['a.recharge', '=', $search['recharge']];
        if ($getType == 'all') {
            return [
                'search' => $search,
                'where' => $where
            ];
        } elseif ($getType == 'where') {
            return $where;
        } else {
            return $search;
        }
    }

    /**
     * 获取代理检验后的数据
     * @param number $agentId
     * @return array
     */
    private function getAgentCheckedData($agentId = null) {
        // 数据检验
        $post = input('post.');
        $validate = [
            'user_name|代理账号' => 'require|/^[a-zA-Z]{1}[a-zA-Z0-9_]{2,15}$/',
            'nick_name|代理昵称' => 'require|length:2,12',
            'weixin|微信号' => 'require|/^[a-zA-Z]{1}[a-zA-Z\d_-]{5,19}$/',
            'weixin_qrcode|微信二维码图片' => 'length:10,200',
            'qq|QQ号' => 'require|/^[1-9]{1}[0-9]{4,11}$/',
            'qq_qrcode|QQ二维码图片' => 'length:5,200',
            'state|状态' => 'require|between:0,1',
        ];
        if ($agentId === null) {
            // 新增
            $validate['login_pwd|登录密码'] = 'require|length:6,20';
            $validate['recharge_pwd|充值密码'] = 'require|length:6,20';
        } else {
            // 是否修改密码
            if (! empty($post['login_pwd'])) $validate['login_pwd|登录密码'] = 'require|length:6,20';
            if (! empty($post['recharge_pwd'])) $validate['recharge_pwd|充值密码'] = 'require|length:6,20';
        }
        $role = new \think\Validate($validate);
        if (! $role->check($post)) json_fail_admin($role->getError());
        // 加密密码
        if (! empty($post['login_pwd'])) {
            $post['login_pwd'] = encode($post['login_pwd']);
        } else {
            unset($post['login_pwd']);
        }
        if (! empty($post['recharge_pwd'])) {
            $post['recharge_pwd'] = encode($post['recharge_pwd']);
        } else {
            unset($post['recharge_pwd']);
        }
        // 查询账号和昵称是否可用
        $where1 = [
            //['status', '=', 1],
            ['user_name', '=', $post['user_name']]
        ];
        $where2 = [
            ['status', '=', 1],
            ['nick_name', '=', $post['nick_name']]
        ];
        if ($agentId !== null) {
            // 修改
            $where1[] = ['id', '<>', $agentId];
            $where2[] = ['id', '<>', $agentId];
        }
        if (Db::name('agent_info')->where($where1)->find()) json_fail_admin('该账号已存在，请更换！');
        if (Db::name('agent_info')->where($where2)->find()) json_fail_admin('该昵称已存在，请更换！');
        return $post;
    }

    /**
     * 获取代理数据
     * @param number $id
     * @return array
     */
    private function getAgentInfo($id) {
        if (! is_numeric($id) || ! $id) json_fail_admin('未找到该代理信息！');
        // 查询数据是否存在
        $where = [
            ['status', '=', 1],
            ['id', '=', $id]
        ];
        $data = Db::name('agent_info')->where($where)->find();
        if (empty($data)) json_fail_admin('该代理不存在');
        return $data;
    }

    /**
     * 获取代理授信操作状态（以此判断是否可作授信操作）
     * @param number $agentId
     * @return string
     */
    private function getUserCreditState($agentId) {
        $key = self::AGENT_CREDIT_STATE_KEY_PRE . $agentId;
        if ($this->getRedis()->exists($key)) {
            return $this->getRedis()->get($key);
        }
        return null;
    }

    /**
     * 代理授信操作加锁解锁
     * @param number $agentId
     * @param number $type 1:锁定；0，解锁
     */
    private function setUserCreditStateLock($agentId, $type = 1) {
        $key = self::AGENT_CREDIT_STATE_KEY_PRE . $agentId;
        if ($type == 1) {
            $this->getRedis()->set($key, $this->user['username']);
            $this->getRedis()->expire($key, 20);
        } else {
            if ($this->getRedis()->exists($key)) {
                $this->getRedis()->del($key);
            }
        }
    }

    /**
     * 代理充值加锁解锁
     * @param number $agentId
     * @param number $type
     */
    private function setUserCreditLock($agentId, $type = 1) {
        $key = 'lock:' . self::AGENT_CREDIT_KEY_PRE . $agentId;
        if ($type == 1) {
            // 加锁
            do {
                if ($this->getRedis()->exists($key)) {
                    $isLock = true;
                    usleep(500);
                } else {
                    $this->getRedis()->set($key, $this->user['id']);
                    $this->getRedis()->expire($key, 10);
                    $isLock = false;
                }
            } while ($isLock);
        } else {
            // 解锁
            if ($this->getRedis()->exists($key)) {
                $this->getRedis()->del($key);
            }
        }
    }

    /**
     * 导出excel文件
     * @param array $data  需导出的数据
     * @param array $title 文件头，与数据对应
     * @param string $fileName 导出文件名
     */
    private function exportExcel($data, $title, $fileName = null) {
        require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // 表头
        foreach ($title as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
        }
        // 写入内容
        $row = 2;
        foreach ($data as $item) {
            $column = 1;
            foreach ($item as $value) {
                $sheet->setCellValueByColumnAndRow($column, $row, $value);
                $column ++;
            }
            $row ++;
        }
        // 输出 下载内容
        $filename = $fileName ? $fileName : time();
        ob_end_clean();
        ob_start();
        header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');
        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 获取 redis
     * @return Redis
     */
    private function getRedis() {
        if (! self::$redis) {
            self::$redis = redis();
        }
        return self::$redis;
    }
}

