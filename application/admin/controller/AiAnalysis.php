<?php
namespace app\admin\controller;

use think\Db;
/**
 * 数据分析
 *
 */
class AiAnalysis extends BaseController
{
    private $url = "/admin/ai_analysis";

    public function index()
    {
        $where = [];
        $condition['start'] = input('request.start');
        $condition['end'] = input('request.end');
        $condition['channel'] = $channel = input("request.channel", null, "trim");

        if(input('request.start_enter')!=1){
            if(empty($condition['start']))$condition['start']=date('Y-m-d');
            if(empty($condition['end']))$condition['end']=date('Y-m-d');
        }

        if(!empty($condition['channel']))$where[] = ['channelid', 'IN', explode(',',$condition['channel'])];
        if (!empty($condition['start'])) $where[] = ['date', '>=', date('Ymd',strtotime($condition['start']))];
        if (!empty($condition['end'])) $where[] = ['date', '<=', date('Ymd',strtotime($condition['end']))];

        $limit=20;
        if(!empty($_GET['is_export']) && $_GET['is_export']==1){
            $limit = 999999;
        }

        $list = Db::name("ai_analysis")->field('*')->where($where)->order("id desc")
            ->paginate($limit)->each(function($item, $key){
                $item['first_recharge'] = $item['first_recharge']/100;
                $item['recharge'] = $item['recharge']/100;
                $item['gold_recharge'] = $item['gold_recharge']/100;
                $item['vip_recharge'] = $item['vip_recharge']/100;
                $item['gold_consume_draw'] = $item['gold_consume_draw']/100;
                $item['gold_consume_remove_clother'] = $item['gold_consume_remove_clother']/100;
                $item['gold_consume_change_face'] = $item['gold_consume_change_face']/100;
                return $item;
            });;

        if(!empty($_GET['is_export']) && $_GET['is_export']==1){
            if(!empty($list->toArray()['data'])){
                // 日志记录
                $this->addlog('导出ai数据分析',json_encode(['before_data'=>[],'after_data'=>$where]));
                $this->export_excel($list->toArray()['data']);
            }
            exit;
        }

        $total = Db::name("ai_analysis")->where($where)->fieldRaw("sum(first_pay_count) first_pay_count, sum(first_recharge) first_recharge, sum(all_pay_count) all_pay_count , sum(recharge) recharge, sum(gold_pay_count) gold_pay_count , sum(gold_recharge) gold_recharge , sum(vip_pay_count) vip_pay_count , sum(vip_recharge) vip_recharge")->find();

        $this->assign([
            "baseUrl" => $this->url,
            'list' => $list,
            'condition' => $condition,
            'total' => $total,
            "channel" => $channel,
            'channelAll'=> Db::name('channel')->distinct(true)->field('channelid')->select(),
        ]);

        return view();
    }

    public function export_excel($data){

        $export_data = [];
        foreach ($data as $k=>$v){
            $export_data[]=[
                '日期'=>[
                    '日期'=>$v['date'],
                ],
                '渠道名称'=>[
                    '渠道名称'=>$v['channelid'],
                ],
                '下单人数'=>[
                    '下单人数'=>$v['order_count'],
                ],
                'AI邀请'=>[
                    'AI邀请'=>$v['invite_count'],
                ],
                'AI首冲'=>[
                    '首冲人数'=>$v['first_pay_count'],
                    '首冲金额'=>$v['first_recharge'],
                ],
                'AI充值'=>[
                    '充值人数'=>$v['all_pay_count'],
                    '充值金额'=>$v['recharge'],
                ],
                'AI金币'=>[
                    '充值人数'=>$v['gold_pay_count'],
                    '充值金额'=>$v['gold_recharge'],
                ],
                'AI会员'=>[
                    '充值人数'=>$v['vip_pay_count'],
                    '充值金额'=>$v['vip_recharge'],
                ],
                '金币消耗'=>[
                    '绘画'=>$v['gold_consume_draw'],
                    '脱衣'=>$v['gold_consume_remove_clother'],
                    '换脸'=>$v['gold_consume_change_face'],
                ],
                'AI创作/部'=>[
                    '绘画'=>$v['draw_count'],
                    '脱衣'=>$v['remove_clother_count'],
                    '换脸'=>$v['change_face_count'],
                ],
            ];
        }

        $title=[];
        foreach ($export_data['0']??[] as $k=>$v){
            foreach ($v as $k1=>$v1){
                $title[]=$k1;
            }
        }

        $filename = 'ai数据分析';
        $this->ai_excel_data($filename,$title,$export_data);exit;
    }

    /**
     * ai统计特殊表头
     * @param $filename
     * @param $data
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function ai_excel_data($filename,$title,$data){

        require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $spreadsheet->getDefaultStyle()->getFont()->setName('Arial');
        $spreadsheet->getDefaultStyle()->getFont()->setSize(12);
        $sheet = $spreadsheet->getActiveSheet();

        $spreadsheet->getActiveSheet()->mergeCells('A1:A2');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('A1','日期');
        $spreadsheet->getActiveSheet()->mergeCells('B1:B2');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('B1','渠道名称');
        $spreadsheet->getActiveSheet()->mergeCells('C1:C2');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('C1','下单人数');
        $spreadsheet->getActiveSheet()->mergeCells('D1:D2');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('D1','AI邀请');
        $spreadsheet->getActiveSheet()->mergeCells('E1:F1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('E1','AI首冲');
        $spreadsheet->getActiveSheet()->mergeCells('G1:H1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('G1','AI充值');
        $spreadsheet->getActiveSheet()->mergeCells('I1:J1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('I1','AI金币');
        $spreadsheet->getActiveSheet()->mergeCells('K1:L1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('K1','AI会员');
        $spreadsheet->getActiveSheet()->mergeCells('l1:N1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('l1','金币消耗');
        $spreadsheet->getActiveSheet()->mergeCells('O1:Q1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('O1','AI创作/部');

        // 表头
        foreach ($title as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 2, $value);
        }

        $sheet->getDefaultColumnDimension()->setWidth(20);

        // 写入内容
        $row = 3;
        foreach ($data as $item_one) {

//            dump($data);die();
            $column = 1;

            foreach ($item_one as $item){
                if (!is_array($item)) {
                    continue;
                }
                foreach ($item as $value) {
                    $sheet->setCellValueByColumnAndRow($column, $row, $value);
                    $column ++;
                }
            }
            $row ++;
        }
        // 输出 下载内容
        ob_end_clean();
        ob_start();
        header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }


}

