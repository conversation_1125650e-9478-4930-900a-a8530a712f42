<?php
namespace app\admin\controller;
use think\Db;
class AppAdv extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_adv",
            'title' => "广告管理",
            'current_url' => "/admin/app_adv",
        ];
        $this->where = [
            'title' => 'like',
            'belong_area' => '',
        ];
        $this->verify = [
            'url'=>[
                'rule'=>'/^(http|https|kumimi):/i',
                'message'=>'链接地址不正确'
            ],
            'pic' => '广告图不能为空!'
        ];
        $this->order = 'sort desc,id desc';
        $this->files = ['pic','subscript'];

        $this->edit_default = ['subscript']; //编辑时添加默认值

        $class_list = config('api.belong_area');
        $belong_area = input('belong_area');
        foreach ($class_list as $k=>$v) {
            if ($k == $belong_area) $this->class_list[] = [
                'id' => $k,
                'title' => $v,
            ];
        }
        parent::__construct($conf);
    }
    public function report(){

        $where=[];
        $adv_id = input('param.adv_id');
//        $time = input('param.begin_time');
        $start_time = input('param.start_time');
        $end_time = input('param.end_time');
        $export_excel = input('param.export_excel');

        if($adv_id == null) {
            $where['belong_area'] = 'start_page';
        }elseif ($adv_id!='-1'){
            $where['belong_area'] = $adv_id;
        }

        if((empty($start_time) && !empty($end_time)) || (!empty($start_time) && empty($end_time)) ){
            echo "<script>alert('开始和结束日期必选'); window.history.back(-1);  </script>";exit();
        }

        if(empty($start_time))$start_time=date('Y-m-d');
        if(empty($end_time))$end_time=date('Y-m-d');

        $start_day = intval(date('d',strtotime($start_time)));
        $end_day = intval(date('d',strtotime($end_time)));
        $mon = intval(date('m',strtotime($start_time)));
        $end_mon = intval(date('m',strtotime($end_time)));

        $start_time =mktime(0,0,0,$mon,$start_day,date('Y',strtotime($start_time)));
        $end_time = mktime(0,0,0,$end_mon,$end_day,date('Y',strtotime($end_time))) + 24 * 3600 -1;



        if(date('m',$start_time)!=date('m',$end_time)){
            echo "<script>alert('时间区间禁止跨月!'); window.history.back(-1);  </script>";exit();
        }

        $condition['mon_begin'] = mktime(0,0,0,$mon,1,date('Y'));
        $condition['mon_end'] = mktime(0,0,0,$mon+1,0,date('Y')) + 24 * 3600 -1;

        $class_list = config('api.belong_area');
        $key_list = array_keys($class_list);
        $list = [];
        foreach ($key_list as $k=>$v) {
            $list[$k]['belong_area'] = $v;
            $list[$k]['title'] = $class_list[$v];
        }


        $adv_data = Db::name('app_adv')->where($where)->field('id,title,belong_area')->order('id desc')->select();

        $app_adv_ids = array_column($adv_data,'id');

//        $adv_date_list =  db('member_adv_total')->whereIn('adv_id',$app_adv_ids)->whereTime('create_time',[$start_time, $end_time])->field("count(id) count , FROM_UNIXTIME(create_time,'%Y-%m-%d') date , adv_id")->group("date , adv_id")->select();

        $adv_date_list =  db('adv_click_date')->whereIn('adv_id',$app_adv_ids)->whereTime('date',[date('Y-m-d',$start_time), date('Y-m-d',$end_time)])->field("sum(click_count) count , DATE_FORMAT(date,'%Y-%m-%d') date , adv_id")->group("date , adv_id")->select();


        $adv_date_list_column=[];
        foreach ($adv_date_list as $k=>$v){
            $adv_date_list_column[$v['adv_id']][$v['date']]=$v;
        }

//        $adv_month_list =  db('member_adv_total')->whereIn('adv_id',$app_adv_ids)->whereTime('create_time',[$condition['mon_begin'], $condition['mon_end']])->field("count(id) count , FROM_UNIXTIME(create_time,'%Y-%m') month , adv_id")->group("month , adv_id")->select();

//        dump($sql->getLastSql());
        $adv_month_list =  db('adv_click_date')->whereIn('adv_id',$app_adv_ids)->whereTime('date',[date('Y-m-d',$condition['mon_begin']), date('Y-m-d',$condition['mon_end'])])->field("sum(click_count) count , DATE_FORMAT(date,'%Y-%m') month , adv_id")->group("month , adv_id")->select();

        $adv_month_list_column=[];
        foreach ($adv_month_list as $k=>$v){
            $adv_month_list_column[$v['adv_id']][$v['month']]=$v;
        }

//        $adv_all_list =  db('member_adv_total')->whereIn('adv_id',$app_adv_ids)->field("count(id) count , adv_id")->group("adv_id")->select();
        $adv_all_list =  db('adv_click_date')->whereIn('adv_id',$app_adv_ids)->field("sum(click_count) count , adv_id")->group("adv_id")->select();

        $adv_all_list_column=[];
        foreach ($adv_all_list as $k=>$v){
            $adv_all_list_column[$v['adv_id']]=$v;
        }

        $date_list = date_segmentation(date('Y-m-d H:i:s',$start_time),date('Y-m-d H:i:s',$end_time));
        $days_list = array_reverse($date_list['days_list']);
        $count = [];

        foreach ($days_list as $v1){
            foreach($adv_data  as  $v){
                $daysdata=[
                    'date'=>$v1,
                    'belong_area_name'=>$class_list[$v['belong_area']]??'',
                    'title'=>$v['title'],
                    'count_day'=> $adv_date_list_column[$v['id']][$v1]['count']??0,
                    'count_mon'=>$adv_month_list_column[$v['id']][date('Y-m',strtotime($v1))]['count']??0,
                    'count'=>$adv_all_list_column[$v['id']]['count']??0,
                ];
//                if($adv_id=='home_pop'){
//                    $daysdata['show_day']=$daysdata['show_month']=0;
//
//                    $show_day_num=redis()->hget('adv_home_pop_show',$v1.'_'.$v['id']);
//                    $show_month_num=redis()->hget('adv_home_pop_show',date('Y-m',strtotime($v1)).'_'.$v['id']);
//
//                    if($show_day_num)
//                        $daysdata['show_day']=$show_day_num;
//
//                    if($show_month_num)
//                        $daysdata['show_month']=$show_month_num;
//                }
                $count[]=$daysdata;
            }
        }

        if($export_excel==1)$this->export_excel($count);

        $this->assign([
            'title' => '广告统计',
            'current_url' => '/admin/app_adv/report',
            'count' => $count,
            'list' => $list,
            'adv_id'=>$adv_id,
            'start_time' => date('Y-m-d',$start_time),
            'end_time' => date('Y-m-d',$end_time),
        ]);
        return view();

    }

    public function export_excel($data){
        foreach ($data as $v){
            $export_data[]=[
                'date'=>$v['date'],
                'belong_area_name'=>$v['belong_area_name'],
                'title'=>$v['title'],
                'count_day'=>$v['count_day'],
                'count_mon'=>$v['count_mon'],
                'count'=>$v['count'],
            ];
        }

        $title = ['日期','广告区位','广告标题','日点击','月点击','累计点击'];
        $filename = '广告统计列表';
        $this->excel_data($filename,$title,$export_data);exit;

    }

    public function video_report(){
        $adv_id = input('param.adv_id');
        $time = input('param.begin_time');
        if($adv_id == null) $adv_id = 1;
        if($time == null) $time = date('Y-m-d',time()); $times = strtotime($time);
        $day = intval(date('d',$times));
        $mon = intval(date('m',$times));

        $condition['day_begin'] =mktime(0,0,0,$mon,$day,date('Y'));
        $condition['day_end'] = mktime(0,0,0,$mon,$day,date('Y')) + 24 * 3600 -1;
        $condition['mon_begin'] = mktime(0,0,0,$mon,1,date('Y'));
        $condition['mon_end'] = mktime(0,0,0,$mon+1,0,date('Y')) + 24 * 3600 -1;

        $list = [
            0=>[
                'type'=>1,
                'title'=>'长视频跑马灯'
            ],
            1=>[
                'type'=>2,
                'title'=>'短视频跑马灯'
            ],
            2=>[
                'type'=>3,
                'title'=>'短视频广告'
            ],
            3=>[
                'type'=>9,
                'title'=>'首页跑马灯广告'
            ]
        ];

        if($adv_id ==3 ){
            $adv_data = Db::connect('short_video')->table('xg_video_ad')->field('id,advertising,place')->order('id desc')->select();
        }else{
            $adv_data = Db::connect('short_video')->table('xg_marquee')->where('type',$adv_id)->field('id,msg,type')->order('type desc')->select();
        }
        $count = [];
        foreach($adv_data  as $k => $v){
            if($adv_id ==3 ){
                $place = '上边';
                if($v['place'] == 1)$place = '下方1';
                if($v['place'] == 2)$place = '下方2';
                if($v['place'] == 1)$place = '上方';
                if($v['place'] == 1)$place = '侧边';
                $v['type'] = 3;
                $count[$k]['title'] = $v['advertising'].'('.$place.')';
            }else{
                $count[$k]['title'] = $v['msg'];
            }
            $count[$k]['count_day'] = Db::connect('short_video')->table('xg_marquee_log')
                ->where(['marquee_id'=>$v['id'],'type'=>$v['type']])
                ->whereTime('create_time',[$condition['day_begin'], $condition['day_end']])
                ->order('type desc')->count('id');

            $count[$k]['count_mon'] = Db::connect('short_video')->table('xg_marquee_log')
                ->where(['marquee_id'=>$v['id'],'type'=>$v['type']])
                ->whereTime('create_time',[$condition['mon_begin'], $condition['mon_end']])
                ->order('type desc')->count('id');

            $count[$k]['count'] = Db::connect('short_video')->table('xg_marquee_log')
                ->where(['marquee_id'=>$v['id'],'type'=>$v['type']])
                ->order('type desc')->count('id');
        }

        $this->assign([
            'title' => '视频广告统计',
            'current_url' => '/admin/app_adv/video_report',
            'count' => $count,
            'list' => $list,
            'adv_id'=>$adv_id,
            'time' => $time,
        ]);
        return view();

    }

}
