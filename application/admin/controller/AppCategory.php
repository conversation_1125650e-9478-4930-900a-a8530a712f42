<?php
namespace app\admin\controller;
use think\Db;
class AppCategory extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_category",
            'title' => "分类",
            'current_url' => "/admin/app_category",
        ];
        $this->where = [
            'title' => 'like',
            'is_pc_top' => '',
            'is_pc_middle' => '',
            'category_type' => '',
        ];

        $this->order = 'sort desc';
        $this->files = ['icon'];
        $class_list = config('api.category_type');
        $category_type = input('category_type');
        foreach ($class_list as $v) {
            if($v['id'] == $category_type) $this->class_list = [$v];
        }
        $this->verify =
            [
                'title'=>[
                    'rule'=>'/^[\x{4e00}-\x{9fa5}A-Za-z0-9 ]{2,20}$/u',
                    'message'=>"标题只能是中文字符，数字，字母，空格，长度2-20位",
                ]
            ];

        parent::__construct($conf);
    }
}
