<?php
namespace app\admin\controller;

use think\Db;
class AppCdn extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_cdn",
            'title' => "CDN列表",
            'current_url' => "/admin/app_cdn",
        ];
        $this->where = [
            'host' => 'like',
            'status' => '',
        ];
        $this->verify = [
            'host'=>[
                'rule'=>'/[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/',
                'message'=>'主机信息不正确',
            ]
        ];
        $this->class_list = Db::name('app_config')->where('id','=','1')->value('cdn_type');
        $this->status_one = ($this->class_list==1) ? 1 : 0;
        parent::__construct($conf);
    }
    public function cdn_type()
    {
        if (!$this->status_one) {
            Db::name($this->name)->where('id', '>', 0)->update(['status' => 2]);
        }
        $res = Db::name('app_config')->where('id', '=', '1')->exp('cdn_type', 'if(cdn_type=1,2,1)')->update();
        $res ? json_success_admin() : json_fail_admin();
    }
}