<?php
namespace app\admin\controller;

use think\Db;
class AppCdn2 extends CommonController
{
    public function __construct()
    {
        
        $conf = [
            'name' => "app_cdn2",
            'title' => "CDN列表",
            'current_url' => "/admin/app_cdn2",
        ];
        $this->where = [
            'host' => 'like',
            'cdn_type' => '',
            'status' => '',
        ];
        $this->verify = [
            'host'=>'主机不能为空',
        ];
        
        $class_list = config('api.cdn_type_arr');
        $cdn_type = input('cdn_type');
        // if(empty($cdn_type) || !in_array($cdn_type, $class_list)) json_fail_admin('参数错误');
        foreach ($class_list as $v) {
            if($v['id'] == $cdn_type) $this->class_list = [$v];
        }
        parent::__construct($conf);
    }
}