<?php
namespace app\admin\controller;
use think\Db;
class AppConfig extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_config",
            'title' => "APP配置",
            'current_url' => "/admin/app_config",
        ];
        $this->id = 1;
        $this->is_parent = 0;
        parent::__construct($conf);
    }
    public function edit()
    {
        $id = input('param.id');
        if (empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        if (empty($res)) json_fail_admin($this->title . '不存在');

        if (request()->isPost()) {
            $post = input('post.');
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if (empty($post[$k])) json_fail_admin($v);
                }
            }

			$cache_time = $post['cache_time'];
            $auto_comment_time = $post['auto_comment_time'];
			$post['cache_time'] = ($cache_time[0]?:0)*3600*24+($cache_time[1]?:0)*3600+($cache_time[2]?:0)*60+($cache_time[3]?:0);
            $post['auto_comment_time'] = ($auto_comment_time[0]?:0)*3600*24+($auto_comment_time[1]?:0)*3600+($auto_comment_time[2]?:0)*60+($auto_comment_time[3]?:0);
            $post['update_time'] = time();
            $post['update_user'] = $this->user['id'];
            $res = Db::name($this->name)->where('id', '=', $id)->update($post);
            $this->addlog('修改APP配置','');
            reload_cache_app_status();
            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') unset($role['icon']);
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if (isset($v['type']) && $v['type'] == 'class_list') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => '分类',
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'custom') {
						$cache_time = $res[$k];
						$cache_time_list = $cache_time>=86400?explode("-",date('d-H-i-s',$cache_time-3600*32)):explode("-",'00-'.date('H-i-s',$cache_time-3600*8));
                        $arr[$k] = [
                            'value' => $cache_time_list,
                            'role' => $v,
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => $v,
                        ];
                    }
                }
            }
//            print_r($arr);die;
            $this->assign([
                'res' => $arr,
                'is_parent' => $this->is_parent,
            ]);
            return view('/common/edit');
        }
    }
}
