<?php
namespace app\admin\controller;

use think\Db;
class AppExchange extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_exchange",
            'title' => "等级兑换码",
            'current_url' => "/admin/app_exchange",
        ];
        $this->where = [
            'title' => 'like',
            'level_id' => '',
            'status' => '',
        ];
        $this->class_list = Db::name('app_level')->field('id,icon,concat(code,grade,title) title')->where('status', '=', 1)->order('id desc')->select();
        parent::__construct($conf);
    }
    public function add()
    {
        if (request()->isPost()) {
            $post = input('post.');
            $num = input('post.num/d');
            $data = [
                'level_id' => $post['level_id'],
                'title' => $post['title'],
                'create_time' => time(),
                'create_user' => $this->user['id'],
            ];
            for ($i=0; $i < $num; $i++) {
                $data['code'] = random(12, 'upper_number');
                Db::startTrans();
                for ($j = 0; $j < 3; $j++) { //最多三次重试
                    try {
                        $res = Db::name($this->name)->insert($data);
                        if ($res) {
                            Db::commit();
                            break;
                        }
                    } catch (\Exception $e) {
                        $data['code'] = random(12, 'upper_number');
                        Db::rollback();
                    }
                }
                if (!$res) json_fail_admin('第' . ($i + 1) . '次插入失败,成功' . $i . '次');
            }
            json_success_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            $role['num'] = [
                'title' => '生成数量',
                'type' => 'number',
            ];
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if (isset($v['type']) && $v['type'] == 'class_list') {
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => '分类',
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => '',
                            'role' => $v,
                        ];
                    }
                }
            }
            $this->assign([
                'res' => $arr,
            ]);
            return view('/common/add');
        }
    }
}