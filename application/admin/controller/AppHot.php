<?php
namespace app\admin\controller;

use think\Db;

class AppHot extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_hot",
            'title' => "热门搜索",
            'create_user'=>'',
            'current_url' => "/admin/app_hot",
        ];
        parent::__construct($conf);

    }

    public function index(){

        $view_type = 1;
        $where = [];
        $whereOr = [];
        $reach_count = 0;
        $condition['title'] = input('request.title');
        $condition['start_time'] = input('request.start_time');
        $condition['end_time'] = input('request.end_time');
        $condition['create_user'] = input('request.create_user');

        if(!empty($condition['start_time']) or !empty($condition['end_time'])){
            $view_type = 2;
            if (!empty($condition['title'])) $where[] = ['title', '=', $condition['title']];
            if (!empty($condition['start_time'])) $where[] = ['create_time', '>=', strtotime($condition['start_time'])];
            if (!empty($condition['end_time'])) $where[] = ['create_time', '<=',  strtotime($condition['end_time'])];
            if (!empty($condition['create_user']) && $condition['create_user'] == 1) $where[] = ['create_user', '=', '后台'];
            if (!empty($condition['create_user']) && $condition['create_user'] == 2) $where[] = ['create_user', '=', '用户'];

            //导出
            if(input('request.type') == "explode"){
                $list = Db::name("app_hot_info")
                    ->field('hot_id as id,title,create_user,sum(num) as num')
                    ->where($where)->whereOr($whereOr)->group('title')->order('num desc')->select();
                //echo Db::name("app_hot_info")->getLastSql();exit;
                if (empty($list)) json_fail_admin('没有数据可代导出');
                $title = ['id', '标题', '来源','搜索次数'];
                $this->addlog('热门搜索记录导出','');

                $this->exportExcel($list, $title, '热门搜索记录');
                return ;
            }

            $list = Db::name("app_hot_info")
                ->field('hot_id as id,title,create_time,create_user,sum(num) as num')
                ->where($where)->whereOr($whereOr)->group('title')->order('num desc')->paginate(20);
            $reach_count = Db::name("app_hot_info")
                ->field('sum(num) as num')
                ->where($where)->whereOr($whereOr)->find();

            $page = $list->render();
        }else{
            if (!empty($condition['title'])) $where[] = ['title', '=', $condition['title']];
            if (!empty($condition['create_user']) && $condition['create_user'] == 1) $where[] = ['create_user', '=', 1];
            if (!empty($condition['create_user']) && $condition['create_user'] == 2){
                $list = Db::name("app_hot")->where($where)->where('create_user!=1 or create_user is null')->order('sort desc')->paginate(20);
            }else{
                $list = Db::name("app_hot")->where($where)->whereOr($whereOr)->order('sort desc')->paginate(20);
            }

            $page = $list->render();
        }


        $this->assign(['current_url' => $this->current_url]);
        $this->assign(['export_current_url' => $this->export_current_url]);
        $this->assign(['title' => $this->title]);
        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'view_type' => $view_type,
            'reach_count' => $reach_count,
        ]);
        return view();
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];

        $res = Db::name('app_hot')->where($where)->delete();
        $res = Db::name('app_hot_info')->where("hot_id",$id)->delete();
        json_success_admin();
    }

    private function exportExcel($data, $title, $fileName = null) {
        require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // 表头
        foreach ($title as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
        }
        // 写入内容
        $row = 2;
        foreach ($data as $item) {
            $column = 1;
            foreach ($item as $value) {
                $sheet->setCellValueByColumnAndRow($column, $row, $value);
                $column ++;
            }
            $row ++;
        }
        // 输出 下载内容
        $filename = $fileName ? $fileName : time();
        ob_end_clean();
        ob_start();
        header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');
        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

}
