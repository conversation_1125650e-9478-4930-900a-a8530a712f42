<?php
namespace app\admin\controller;

use think\Db;
class AppInfo extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_info",
            'title' => "APP列表",
            'current_url' => "/admin/app_info",
        ];
        parent::__construct($conf);
    }
    public function status($id)
    {
    	$str = input('post.str');
    	if(!$str) json_fail_admin('参数错误');
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name($this->name)->where($where)->exp($str, "if({$str}=1,2,1)")->update();
        $res ? json_success_admin() : json_fail_admin();
    }
}