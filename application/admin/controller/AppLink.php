<?php
namespace app\admin\controller;

use think\Db;
class AppLink extends CommonController
{
    public function __construct()
    {
        
        $conf = [
            'name' => "app_link",
            'title' => "链接列表",
            'current_url' => "/admin/app_link",
        ];
        $this->where = [
            'title' => 'like',
            'host' => 'like',
            'status' => '',
        ];
        $this->verify = [
            'host'=>'链接不能为空',
        ];
		$this->order = 'sort asc';
        parent::__construct($conf);
    }
}