<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class AppModule extends BaseController
{
    protected $module_type = [
        1 => 'Banner',
        2 => '横幅广告',
        3 => '长视频 - 1+4格',
        4 => '长视频 - 4格',
        5 => '长视频 - 6格',
        6 => '明星模块',
        7 => '游戏模块',
        8 => '最新消息公告',
        9 => '5格广告',
        10 => '无文字2格广告',
        11 => '4格广告',
    ];

	//页面管理
    public function page()
    {
    	$list = Db::name('app_module_page')->order('sort desc')->field('id,title,sort,status')->paginate(20);     
    	$this->assign([
    		'list' => $list,
    		'current_url' => '/admin/app_module'
    	]);
    	return view("/app_module/page");
	}
    //设置页面
    public function set_module()
    {
        $id = input('param.id');
        $pages = Db::name('app_module_page')->where('id',$id)->find();
        if (request()->isPost()) {
            $post = input("post.");
            $update = [
                'id' => $post['id'],
                'modules' => implode(',',$post['module']),

            ];
            $res = Db::name('app_module_page')->update($update);
            redis()->del('app_module_page');
            $this->addlog('配置页面',' 页面标题: '.$pages['title']);

            redis()->del('module_page_'.$id);

            $res ? json_success_admin() : json_fail_admin();

        }else{
            
            $pages['modules'] = array_filter(explode(',',$pages['modules']));
            //所有模块
            $all = Db::name('app_module')->where('status',1)->select();
            $this->assign([
                'pages' => $pages,
                'all' => $all,
                'module_type' => $this->module_type,
            ]);
            return view("/app_module/set_module");
        }
        
    }
    //预览页面
    public function view_module()
    {
         $module = input("param.module");

         //模块对应数据
         $list = Db::name('app_module')->where('id','in',$module)->column('*','id');
         $data = [];
         foreach ($list as $key => $value) {
             if ($value['type'] == 1 || $value['type'] == 2) {
                $tmp = explode(',',$value['pics']);
                $adv = Db::name('app_adv')->where('id','in',$tmp)->field('id,title,url,pic')->cache(true,3)->select();
                foreach ($adv as $k => &$v) {
                    $v['pic'] = readBase($v['pic']);
                }
                $data[$key]['data'] = $adv;
                
             }else if($value['type'] == 3 || $value['type'] == 4 || $value['type'] == 5){
                $db =  Db::name('app_movie');
                $where = [];

                //画质处理
                if ($value['movie_qua']) {
                    $qua = explode(',',$value['movie_qua']);
                    foreach ($qua as $q) {
                       if ($q == 2) {
                           $where[] = ['sp_url2', 'eq', 'not null'];
                       }
                       if ($q == 3) {
                           $where[] = ['sp_url3', 'eq', 'not null'];
                       }
                    }
                   
                }
                //出品方
                if ($value['movie_pros']) {
                    $p = array_unique(explode(',',$value['movie_pros']));
                    $where[] = ['product_id', 'in', $p];
                }
                if ($where) {
                    $db->where(function ($query) use ($where) {
                        $query->where($where);
                    });
                }
                

                //女星
                $whereOr1 = [];
                if ($value['movie_stars']) {
                    $stars = array_unique(explode(',',$value['movie_stars']));
                    foreach ($stars as $star) {
                        $whereOr1[] = ['', 'exp', Db::raw("FIND_IN_SET('{$star}',star_ids)")];
                    }
                }
                if ($whereOr1) {
                    $db->where(function ($query) use ($whereOr1) {
                        $query->whereOr($whereOr1);
                    });
                }
                
                //电影类型
                $whereOr2 = [];
                if ($value['movie_type']) {
                    $mtypes = array_unique(explode(',',$value['movie_type']));
                    foreach ($mtypes as $m) {
                        $whereOr2[] = ['', 'exp', Db::raw("FIND_IN_SET('{$m}',category_ids)")];
                    }
                }
                if ($whereOr2) {
                    $db->where(function ($query) use ($whereOr2) {
                        $query->whereOr($whereOr2);
                    });
                }

                //电影标签
                $whereOr3 = [];
                if ($value['movie_tags']) {
                    $mtags = array_unique(explode(',',$value['movie_tags']));
                    foreach ($mtags as $tag) {
                        $whereOr3[] = ['', 'exp', Db::raw("FIND_IN_SET('{$tag}',tag_ids)")];
                    }
                }
                if ($whereOr3) {
                    $db->where(function ($query) use ($whereOr3) {
                        $query->whereOr($whereOr3);
                    });
                }

                $limit = 5;
                if ($value['type'] == 3 && $value['pic_type'] == 2) {
                    $limit = 7;
                }
                if ($value['type'] == 4) {
                    $limit = 4;
                    if ($value['pic_type'] == 2) {
                       $limit = 6;
                    }
                }
                if ($value['type'] == 5) {
                    $limit = 6;
                    if ($value['pic_type'] == 2) {
                       $limit = 9;
                    }
                }
                //获取前五个电影
                $data[$key]['data'] = $db->field('id,title,cover,gif,cover_vertical,url,price,type')->order($value['movie_order'])->limit(0,$limit)->select();
             }else if ($value['type'] == 6) {
                 $ids = array_unique(explode(",",$value['mingxing']));
                 $stars = Db::name('app_star')->where('id','in',$ids)->field('uname,avatar,id')->select();
                 foreach ($stars as $y => &$ve) {
                     $ve['avatar'] = readBase($ve['avatar']);
                 }
                 $data[$key]['mingxing'] =  $stars;     
                 $data[$key]['mx_showmore'] = $value['mx_showmore'];
             }else{
                 $games = Db::name('ob_category')->field('img_index,id,name')->order('sort desc')->select();
                 foreach ($games as $y => &$e) {
                     $e['img_index'] = readBase($e['img_index']);
                 }
                 $data[$key]['games'] = $games;
             }
             $data[$key]['type'] = $value['type'];
             $data[$key]['title'] = $value['title'];
             $data[$key]['pic_type'] = $value['pic_type'];

         }
         
         foreach ($module as $key => &$value) {
             $value = $data[$value];
             if ($value['type'] == 3 || $value['type'] == 4 || $value['type'] == 5) {
                foreach ($value['data'] as $k => &$v) {
                    $v['cover'] = readBase($v['cover']);
                    $v['cover_vertical'] = readBase($v['cover_vertical']);
                    $v['key'] = $k;
                }
             }
         }


         $this->assign('module',$module);
         return view('/app_module/view_module');
    }

	//添加页面
	public function add_page()
    {
        if (request()->isPost()) {
            $post = input("post.");
            if (!$post['title']) {
            	json_fail_admin('页面名称不能为空');
            }
            $insert = [
            	'title' => $post['title'],
            	'sort' => $post['sort'],
            	'status' => $post['status'],
            ];
            $res = Db::name('app_module_page')->insertGetId($insert);
            $this->addlog('添加页面',' 页面名称: '. $post['title']);
            redis()->del('app_module_page');
            
            $res ? json_success_admin() : json_fail_admin();
        } else {
            
            return view('/app_module/add_page');
        }
    }
    //修改页面
    public function edit_page()
    {
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name('app_module_page')->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin('数据不存在');

        if (request()->isPost()) {
            $post = input('post.');
            $data = [];
            $data['title'] = $post['title'];
            $data['sort'] = $post['sort'];
            $data['status'] = $post['status'];
                    
            $res = Db::name('app_module_page')->where('id', '=', $id)->update($post);
            $this->addlog('修改页面',' 页面名称: '.$post['title']);
            redis()->del('app_module_page');
            
            $res ? json_success_admin() : json_fail_admin();
        } else {
            
            $this->assign([
                'info' => $res,
            ]);
            return view('/app_module/edit_page');
        }
    }
    //禁用页面
    public function status($id)
    {
        $res = Db::name('app_module_page')->where('id',$id)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        
        $res = Db::name('app_module_page')->where('id',$id)->exp('status', 'if(status=1,0,1)')->update();
        if (!$res) {
            json_fail_admin();
        }
        $this->addlog('修改页面状态','表名: app_module_page 数据ID: '.$id);
        redis()->del(config("cache.prefix").'app_module_page');
        
        json_success_admin();
    }
    //删除页面
    public function del($id)
    {
        json_fail_admin('该功能暂时关闭');
        $res =  Db::name('app_module_page')->where('id',$id)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        
        $res = Db::name('app_module_page')->delete($id);
        $this->addlog('删除app_module_page数据',' 数据ID: '.$id);
        redis()->del('app_module_page');
        
        $res ? json_success_admin() : json_fail_admin();
    }

    //模块管理
    public function module(){
        $where = [];
        $condition = [];
        $condition['title'] = input('request.title');
        if (!empty($condition['title'])) $where[] = ['title', 'like', '%' . $condition['title'] . '%'];

        $list = Db::name('app_module')->where($where)->order('id desc')->field('id,title,type,sort,status,remark')->paginate(20);     
        $this->assign([
            'list' => $list,
            'current_url' => '/admin/app_module',
            'condition' => $condition,
            'module_type' => $this->module_type
        ]);
        return view("module");
    }

    //添加模块
    public function add_module()
    {
        if (request()->isPost()) {
            $post = input("post.");

            if (!$post['title']) {
                json_fail_admin('页面名称不能为空');
            }
            
            $insert = [
                'title' => $post['title'],
                'type' => $post['type'],
                'status' => $post['status'],
                'remark' => $post['remark'],
            ];
            if ($post['type'] == 1 or $post['type']==9 or $post['type']==10 ) {
                $insert['pics'] = implode(',',$post['banner']);
            }elseif ($post['type'] == 2) {
                $insert['pics'] = implode(',',$post['adv']);
            }elseif($post['type'] == 3 || $post['type'] == 4 || $post['type'] == 5){
                 $ruleid =  $post['ruleid'];
                 $rule =  $post['ruletype'];
                 $insert['movie_tags'] = [];
                 $insert['movie_type'] = [];
                 $insert['movie_stars'] = [];
                 $insert['movie_pros'] = [];
                 $insert['movie_qua'] = [];
        
                foreach ($rule as $key => $value) {

                    $tmp = $ruleid[$key];
                    if ($value == 'tags') {
                        $insert['movie_tags'][] = $tmp;
                        
                    }elseif($value == 'mtype'){
                        $insert['movie_type'][] = $tmp;
                        
                    }elseif($value == 'stars'){
                        $insert['movie_stars'][] = $tmp;

                    }elseif($value == 'products'){
                        $insert['movie_pros'][] = $tmp;
                    }else{
                        $insert['movie_qua'][] = $tmp;
                        
                    }
                } 
                 $insert['movie_tags'] = implode(',', array_unique($insert['movie_tags']));
                 $insert['movie_type'] = implode(',', array_unique($insert['movie_type']));
                 $insert['movie_stars'] = implode(',',array_unique( $insert['movie_stars']));
                 $insert['movie_pros'] = implode(',', array_unique($insert['movie_pros']));
                 $insert['movie_qua'] = implode(',', array_unique($insert['movie_qua']));
                 $insert['movie_order'] = $post['sort_type'];
                 // $insert['movie_num'] = $post['movie_num'];
                 $insert['pic_type'] = $post['pic_type'];
            }elseif ($post['type'] == 6) {
                 $insert['mx_showmore'] = $post['mx_showmore'];
                 $insert['mingxing'] = implode(',', array_unique($post['mingxing']));
            }elseif ($post['type'] == 8){
                $insert['pics'] = implode(',',$post['maequee']);
            }
            
            $res = Db::name('app_module')->insertGetId($insert);
            $this->addlog('添加模块',' 模块名称: '. $post['title']);
            $res ? json_success_admin() : json_fail_admin();
        } else {
            //所有广告图
            $advs = Db::name('app_adv')->where("belong_area","new_adv")->where('status',1)->field('title,url,pic,id')->select();
            foreach ($advs as $key => &$value) {
                $value['img'] = readBase($value['pic']);
            }

            //所有视频分类
            $mtype = Db::name('app_category')->where("category_type","movie")->field('title,sort,id')->order('sort desc')->select();
            //所有标签
            $tags = Db::name('app_tag')->where('status',1)->field('title,sort,id')->order('sort desc')->select();
            //主演
            $stars = Db::name('app_star')->where('status',1)->field('uname as title,sort,id')->order('sort desc')->select();
            //出品方
            $products = Db::name('app_products')->where('status',1)->field('title,sort,id')->order('sort desc')->select();
            //跑马灯
            $maequee = Db::connect('short_video')->table("xg_marquee")->where('type',9)->select();
            //清晰度
            $qua = [
                [
                    'id' => 1,
                    'title' => '标清'
                ],
                [
                    'id' => 2,
                    'title' => '高清'
                ],
                [
                    'id' => 3,
                    'title' => '超清'
                ]
            ];
            $this->assign([
                'current_url' => '/admin/app_module',
                'module_type' => $this->module_type,
                'advs' => $advs,
                'mtype' => $mtype,
                'tags' => $tags,
                'stars' => $stars,
                'products' => $products,
                'maequee' => $maequee,
                'qua' => $qua,
            ]);
            return view('/app_module/add_module');
        }
    }

    //实时获取模块条件视频数量
    public function movieNum(){

        $rule = input('param.ruletype',[]);
        if (!$rule) {
            json_success_admin('ok',['count'=>0]);
        }
        $ruleid = input('param.ruleid',[]);

        $where = [];
        $whereOr1 = [];
        $whereOr2 = [];
        $whereOr3 = [];

        $products = [];
        $qua = [];

        foreach ($rule as $key => $value) {
            if(!isset($ruleid[$key]))continue;
            $tmp = $ruleid[$key];
            if ($value == 'tags') {
                $whereOr1[] = ['', 'exp', Db::raw("FIND_IN_SET('{$tmp}',tag_ids)")];
            }elseif($value == 'mtype'){
                $whereOr2[] = ['', 'exp', Db::raw("FIND_IN_SET('{$tmp}',category_ids)")];
            }elseif($value == 'stars'){
                $whereOr3[] = ['', 'exp', Db::raw("FIND_IN_SET('{$tmp}',star_ids)")];
            }elseif($value == 'products'){
                $products[] = $tmp;
            }else{
    
                if ($tmp == 2) {
                   $where[] = ['sp_url2', 'eq', 'not null'];
                }
                if ($tmp == 3) {
                   $where[] = ['sp_url3', 'eq', 'not null'];
                }
            }
        }

        if ($products) {
            array_unique($products);

            $where[] = ['product_id', 'in', $products];
        }
        
        $count =  Db::name('app_movie')->where(function ($query) use ($where) {
            $query->where($where);
        })->where(function ($query) use ($whereOr1) {
            $query->whereOr($whereOr1);
        })->where(function ($query) use ($whereOr2) {
            $query->whereOr($whereOr2);
        })->where(function ($query) use ($whereOr3) {
            $query->whereOr($whereOr3);
        })->count();

        json_success_admin('ok',['count'=>$count]);

    }


    //禁用模块
    public function status_module($id)
    {
        $res = Db::name('app_module')->where('id',$id)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        
        $res = Db::name('app_module')->where('id',$id)->exp('status', 'if(status=1,0,1)')->update();
        if (!$res) {
            json_fail_admin();
        }
        $this->addlog('修改模块状态','表名: app_module 数据ID: '.$id);

        $keys = redis()->keys('module_info_'.$id.'*');
        redis()->del($keys);

        json_success_admin();
    }
    //删除模块
    public function del_module($id)
    {
        $res =  Db::name('app_module')->where('id',$id)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        
        $res = Db::name('app_module')->delete($id);
        $this->addlog('删除模块数据',' 数据ID: '.$id);
        
        $res ? json_success_admin() : json_fail_admin();
    }


    //修改模块
    public function edit_module()
    {
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name('app_module')->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin($res['title'].'不存在');

        if (request()->isPost()) {
            $post = input("post.");

            if (!$post['title']) {
                json_fail_admin('页面名称不能为空');
            }
            
            $update = [
                'title' => $post['title'],
                'type' => $post['type'],
                'status' => $post['status'],
                'remark' => $post['remark'],
                'pic_type' => $post['pic_type'],
            ];
            if ($post['type'] == 1 or $post['type']==9 or $post['type']==10 or $post['type']==11) {
                $update['pics'] = implode(',',$post['banner']);
            }elseif ($post['type'] == 2) {
                $update['pics'] = implode(',',$post['adv']);
            }elseif ($post['type'] == 3 || $post['type'] == 4 || $post['type'] == 5) {
                 $ruleid =  $post['ruleid'];
                 $rule =  $post['ruletype'];
                 $update['movie_tags'] = [];
                 $update['movie_type'] = [];
                 $update['movie_stars'] = [];
                 $update['movie_pros'] = [];
                 $update['movie_qua'] = [];
        
                foreach ($rule as $key => $value) {

                    $tmp = $ruleid[$key];
                    if ($value == 'tags') {
                        $update['movie_tags'][] = $tmp;
                        
                    }elseif($value == 'mtype'){
                        $update['movie_type'][] = $tmp;
                        
                    }elseif($value == 'stars'){
                        $update['movie_stars'][] = $tmp;

                    }elseif($value == 'products'){
                        $update['movie_pros'][] = $tmp;
                    }else{
                        $update['movie_qua'][] = $tmp;
                        
                    }
                } 
                 $update['movie_tags'] = implode(',', array_unique($update['movie_tags']));
                 $update['movie_type'] = implode(',', array_unique($update['movie_type']));
                 $update['movie_stars'] = implode(',', array_unique($update['movie_stars']));
                 $update['movie_pros'] = implode(',', array_unique($update['movie_pros']));
                 $update['movie_qua'] = implode(',', array_unique($update['movie_qua']));
                 $update['movie_order'] = $post['sort_type'];
                 // $update['movie_num'] = $post['movie_num'];
            }else if($post['type'] == 6){
                $update['mingxing'] = implode(',',array_unique($post['mingxing']));
                $update['mx_showmore'] = $post['mx_showmore'];
            }else if($post['type'] == 8){
                $update['pics'] = implode(',',array_unique($post['maequee']));
            }
            $update['id'] = $post['id'];

            $res = Db::name('app_module')->update($update);
            $this->addlog('修改模块',' 模块名称: '. $post['title']);

            $keys = redis()->keys('module_info_'.$post['id'].'*');
            redis()->del($keys);

            $res ? json_success_admin() : json_fail_admin();
        } else {
           //所有广告图
            $advs = Db::name('app_adv')->where("belong_area","new_adv")->where('status',1)->field('title,url,pic,id')->select();
            foreach ($advs as $key => &$value) {
                $value['img'] = readBase($value['pic']);
            }
            //所有视频分类
            $mtype = Db::name('app_category')->where("category_type","movie")->field('title,sort,id')->order('sort desc')->select();
            //所有标签
            $tags = Db::name('app_tag')->where('status',1)->field('title,sort,id')->order('sort desc')->select();
            //主演
            $stars = Db::name('app_star')->where('status',1)->field('uname as title,sort,id')->order('sort desc')->select();
            //出品方
            $products = Db::name('app_products')->where('status',1)->field('title,sort,id')->order('sort desc')->select();
            //跑马灯
            $maequee = Db::connect('short_video')->table("xg_marquee")->where('type',9)->select();
            //清晰度
            $qua = [
                [
                    'id' => 1,
                    'title' => '标清'
                ],
                [
                    'id' => 2,
                    'title' => '高清'
                ],
                [
                    'id' => 3,
                    'title' => '超清'
                ]
            ];
            $pics = explode(',',$res['pics']);
            $res['pics'] = [];
            foreach ($pics as $key => &$value) {
                $tmp =  Db::name('app_adv')->where("belong_area","new_adv")->where('id',$value)->value('pic');
                $res['pics'][$key]['url'] = readBase($tmp);
                $res['pics'][$key]['id'] = $value;
            }
            if($res['type'] == 8 ){
                $res['marquee'] = Db::connect('short_video')->table("xg_marquee")->field('msg,id')->whereIn('id',$pics)->select();
            }
            $res['movie_type'] = array_filter(explode(',',$res['movie_type']));
            $res['movie_tags'] = array_filter(explode(',',$res['movie_tags']));
            $res['movie_stars'] = array_filter(explode(',',$res['movie_stars']));
            $res['movie_pros'] = array_filter(explode(',',$res['movie_pros']));
            $res['movie_qua'] = array_filter(explode(',',$res['movie_qua']));
            $res['mingxing'] = array_filter(explode(',',$res['mingxing']));
            $this->assign([
                'current_url' => '/admin/app_module',
                'module_type' => $this->module_type,
                'advs' => $advs,
                'mtype' => $mtype,
                'tags' => $tags,
                'stars' => $stars,
                'products' => $products,
                'qua' => $qua,
                'res' => $res,
                'maequee' => $maequee,
            ]);
            return view('/app_module/edit_module');
        }
    }
}