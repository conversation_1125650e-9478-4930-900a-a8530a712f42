<?php
namespace app\admin\controller;

use app\common\Constants\RedisConst;
use app\Service\SliceDataService;
use think\Db;
use think\exception\DbException;
use think\facade\Env;
use think\response\View;

class AppMovie extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "app_movie";
        $this->title = "视频列表";
        $this->current_url = "/admin/app_movie";
        $this->price_type = [
            3 => 'VIP',
            1 => '免费',
            2 => '收费',
            5 => '活动',
        ];
    }
    public function _assign()
    {
        $movie_type_list_fun = function (){
            $return_list=[];
            foreach ($this->price_type as $k=>$v){
                $return_list[]=[
                    'id'=>$k,
                    'title'=>$v,
                ];
            }
            return $return_list;
        };
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
        $this->assign("price_type", $this->price_type);
        $this->assign("movie_type_list", $movie_type_list_fun());
    }

    function play() {
        $m3u8 = input('request.addr');
        $jpg  = input('request.jpg');

        if (empty($m3u8)) {
            json_fail_admin("url地址错误！");
        }

        $this->assign("m3u8", $m3u8);
        $this->assign("jpg", $jpg);
        return view();
    }

    public function index()
    {
        $where = [];
        $condition['number'] = input('request.number');
        $condition['title'] = input('request.title');
        $condition['category_ids'] = input('request.category_ids');
        $condition['tag_ids'] = input('request.tag_ids');
        $condition['star_ids'] = input('request.star_ids');
        $condition['product_id'] = input('request.product_id')?input('request.product_id'):input('product_id');
        $condition['theme_ids'] = input('request.theme_ids');
        $condition['cj'] = input('request.cj');
        $condition['status'] = input('request.status');
        $condition['id'] = input('request.id');
        $condition['duration'] = input('request.duration');
        $condition['movie_type'] = input('request.movie_type');
        $condition['start_duration'] = input('request.start_duration');
        $condition['end_duration'] = input('request.end_duration');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

//        if(input('request.start_enter')!=1){
//            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
//            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
//        }

        if (!empty($condition['number'])) $where[] = ['a.number', '=', $condition['number']];
        if (!empty($condition['title'])) $where[] = ['a.title', 'like', '%' . $condition['title'] . '%'];
        if (!empty($condition['category_ids'])) {
            if($condition['category_ids']=='-1'){
                $where[] = ['a.category_ids', 'IN', ['',null,0]];
            }else{
                $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$condition['category_ids']}',a.category_ids)")];
            }
        }

        if (!empty($condition['tag_ids'])) {
            if($condition['tag_ids']=='-1'){
                $where[] = ['a.tag_ids', 'IN', ['',null,0]];
            }else{
                $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$condition['tag_ids']}',a.tag_ids)")];
            }
        }

        if (!empty($condition['star_ids'])) {
            if($condition['star_ids']=='-1'){
                $where[] = ['a.star_ids', 'IN', ['',null,0]];
            }else{
                $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$condition['star_ids']}',a.star_ids)")];
            }
        }

        if (!empty($condition['product_id'])) {
            if($condition['product_id']=='-1'){
                $where[] = ['a.product_id', 'IN', ['',null,0]];
            }else{
                $where[] = ['a.product_id', '=', $condition['product_id']];
            }
        }

        if (!empty($condition['theme_ids'])) {
            if($condition['theme_ids']=='-1'){
                $where[] = ['a.theme_ids', 'IN', ['',null,0]];
            }else{
                $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$condition['theme_ids']}',a.theme_ids)")];
            }
        }

        if (!empty($condition['cj'])) $where[] = ['a.cj', '=', $condition['cj']];
        if (!empty($condition['id'])) $where[] = ['a.id', '=', $condition['id']];
        if (!empty($condition['status']) || $condition['status']==='0')  $where[] = ['a.status', '=', $condition['status']];
        if (!empty($condition['start_duration'])) $where[] = ['a.duration', '>=', $condition['start_duration']];
        if (!empty($condition['end_duration'])) $where[] = ['a.duration', '<=', $condition['end_duration']];
        if (!empty($condition['movie_type'])) $where[] = ['a.type', '=', $condition['movie_type']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+60*60*24];


        $list = Db::name($this->name)->alias('a')->where($where)->order('id desc');

        $app_category = Db::name('app_category')->field('id,title,pinyin')->where('category_type', '=', 'movie')->order('pinyin asc')->select();
        $app_tag = Db::name('app_tag')->field('id,title')->order('pinyin asc')->select();
        $app_star = Db::name('app_star')->field('id,uname title')->order('pinyin asc')->select();
        $app_theme = Db::name('app_theme')->field('id,title')->order('pinyin asc')->select();
        $app_products = Db::name("app_products")->where('status',1)->field('id,title')->order('id ASC')->select();
        $app_products_column = Db::name("app_products")->column('title','id');




        if(!empty($_GET['is_export']) && $_GET['is_export']==1){

            $list=$list->select();

            $pay_movie_date_column=[];
            if(!empty($list)){
                $pay_movie_date_list =  Db::name('pay_movie_date')->group("movie_id")->field('movie_id,count,amount')->select();
                $pay_movie_date_column = array_column($pay_movie_date_list,null,'movie_id');
            }

            foreach ($list as $k=>$v){
                $list[$k]['url'] = replaceCdnUrl($v['url']);
                $list[$k]['sale_count'] = $pay_movie_date_column[$v['id']]['count']??0;
                $list[$k]['sale_amount'] = $pay_movie_date_column[$v['id']]['amount']??0;
                if(!empty($list[$k]['sale_amount']))$list[$k]['sale_amount']=$list[$k]['sale_amount']/100;
                $list[$k]['app_products_name']=$app_products_column[$v['product_id']]??'';
            }
            $export_data=[];
            foreach ($list as $k=>$v){
                $app_category_names = '';
                $app_theme_names = '';

                foreach (explode(',',$v['category_ids']) as $v1){
                    foreach ($app_category as $v2){
                        if($v1==$v2['id']){
                            $app_category_names.=(empty($app_category_names)?'':',').$v2['title'];
                        }
                    }
                }
                foreach (explode(',',$v['theme_ids']) as $v1){
                    foreach ($app_theme as $v2){
                        if($v1==$v2['id']){
                            $app_theme_names.= (empty($app_theme_names)?'':',').$v2['title'];
                        }
                    }
                }


                $export_data[]=[
                    'id'=>$v['id'],
                    'title'=>$v['title'],
                    'number'=>$v['number'],
                    'app_category_names'=>$app_category_names,
                    'app_theme_names'=>$app_theme_names,
                    'price'=>$v['price'],
                    'sale_count'=>$v['sale_count'],
                    'sale_amount'=>$v['sale_amount'],
                    'create_time'=>date('Y-m-d H:i:s',$v['create_time']),
                    'app_products_name'=>$v['app_products_name'],
                    'real_click_num'=>$v['real_click_num'],
                    'up_num'=>$v['up_num'],
                    'real_folder_num'=>$v['real_folder_num'],
                    'status_name'=>$v['status']==1?'启用':'禁用',
                ];

            }

            $title = ['ID','标题','番号','类型','主题','售价','成交数量','总价','新增时间','出品方','点击数(真实)','点赞数(虚拟的)','收藏数(真实)','状态'];
            $filename = '视频列表';
            $this->excel_data($filename,$title,$export_data);exit;
        }else{
            $list=$list->paginate(10)->each(function($item, $key){
                $item['url'] = replaceCdnUrl($item['url']);
                $item['url_base64'] = base64_encode($item['url']);
                return $item;
            });
//            dump(Db::name('app_movie')->getLastSql());die();
        }

//dump($list);die();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'app_category' => $app_category,
            'app_tag' => $app_tag,
            'app_star' => $app_star,
            'app_theme' => $app_theme,
            'app_products'=>$app_products,
            'url' => config('file.url')

        ]);
        $this->_assign();
        return view();
    }
    public function add()
    {
        if (!request()->isPost()) {
            $app_products = Db::name("app_products")->where('status',1)->field('id,title')->order('id ASC')->select();
            $this->assign([
                'app_products'=>$app_products
            ]);
            return view();
        } else {
            $post = input('post.');

            // if(empty($post['category_ids'])) json_fail_admin('请选择分类');
            if (empty($post['title'])) json_fail_admin('标题不能为空');
            if (empty($post['url'])) json_fail_admin('请选择上传视频');
            if (empty($post['cover'])) json_fail_admin('请选择上传水平封面');
            if (empty($post['cover_vertical'])) json_fail_admin('请选择上传垂直封面');
            if ($post['number']) {
               $re = Db::name('app_movie')->where('number',$post['number'])->find();
               if ($re) {
                   json_fail_admin('番号重复!');
               }
            }
            $data = [
                'title' => $post['title'],
                'product_id' => $post['product_id'],
                'number' => $post['number'],
                'url2' => $post['url'],
                'url' => $post['m3u8'],
                'cover' => $post['cover'],
                'cover_vertical' => $post['cover_vertical'],
                'duration' => $post['duration'],
                'detail' => $post['detail'],
                'click_num' => $post['click_num'],
                'folder_num' => $post['folder_num'],
                'up_num' => $post['up_num'],
                'down_num' => $post['down_num'],
                'score' => $post['score'],
                'sort' => $post['sort'],
                'status' => $post['status'],
                'create_time' => time(),
                'create_date' => date('Y-m-d'),
                'create_user' => $this->user['id'],
            ];
            if (empty($post['star_ids'])) {
                $data['star_ids'] = '';
            } else {
                $data['star_ids'] = implode(',', $post['star_ids']);
                if($post['status'] == 1){
                    Db::name('app_star')->where('id', 'in', $data['star_ids'])->setInc('movie_num');
                    $nations = Db::name("app_star")->where('id', 'in', $data['star_ids'])->column('nation_id');
                    $jobs = Db::name("app_star")->where('id', 'in', $data['star_ids'])->column('job_id');
                    Db::name('app_nation')->where('id', 'in', $nations)->setInc('movie_num');
                    Db::name('app_star_job')->where('id', 'in', $jobs)->setInc('movie_num');
                }
            }
            if($post['status'] == 1){
                Db::name('app_products')->where('id', 'in', $data['product_id'])->setInc('videos');
            }

            if (empty($post['theme_ids'])) {
                $data['theme_ids'] = '';
            } else {
                $data['theme_ids'] = implode(',', $post['theme_ids']);
                Db::name('app_theme')->where('id', 'in', $data['theme_ids'])->update(['last_time' => $data['create_time']]);

            }
            $data['category_ids'] = empty($post['category_ids']) ? '' : implode(',', $post['category_ids']);
            $data['tag_ids'] = empty($post['tag_ids']) ? '' : implode(',', $post['tag_ids']);


            // $file['cover'] = request()->file("file_cover");
            // $file['cover_vertical'] = request()->file("file_cover_vertical");

            // $file['cover'] = request()->file("file_cover");
            // $file['cover_vertical'] = request()->file("file_cover_vertical");
            $file['hot_pic'] = request()->file("file_hot_pic");


            // if (!$file['cover']) json_fail_admin("请选择上传水平封面");
            // if (!$file['cover_vertical']) json_fail_admin("请选择上传垂直封面");
            // if (empty($post['url'])) json_fail_admin('请选择上传视频');
            // if (empty($post['cover'])) json_fail_admin('请选择上传水平封面');
            // if (empty($post['cover_vertical'])) json_fail_admin('请选择上传垂直封面');

            // $info['cover'] = $file['cover']->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/cover');
            // if (!$info['cover']) json_fail_admin('水平封面', $file['cover']->getError());

            // $info['cover_vertical'] = $file['cover_vertical']->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/cover_vertical');
            // if (!$info['cover_vertical']) json_fail_admin('垂直封面', $file['cover_vertical']->getError());

            // $data['cover'] = UPLOAD_NAME . '/cover/' . $info['cover']->getSaveName();
            // $data['cover_vertical'] = UPLOAD_NAME . '/cover_vertical/' . $info['cover_vertical']->getSaveName();

            // $data['url'] = mp4_m3u8($post['url']);
            // $data['cover'] = $post['cover'];
            // $data['cover_vertical'] = $post['cover_vertical'];
            $res = Db::name($this->name)->insertGetId($data);
            if(!empty($post['bt'])){
                foreach ($post['bt'] as $key=>$value){
                    if($value){
                        $add['movie_id'] = $res;
                        $add['bt_url'] = $value;
                        $add['create_time'] = time();
                        Db::name("app_movie_bt")->insertGetId($add);
                    }
                }
            }
            if(!empty($post['hot_pic'])){
                $padd = [];
                foreach ($post['hot_pic'] as $key=>$value){
                    if($value){
                        $info['hot_pic'] = $file['hot_pic'][$key]->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/hot_pic');
                        if (!$info['hot_pic']) continue;
                        $padd[]= [
                            'hot_pic'=>UPLOAD_NAME . '/hot_pic/' . $info['hot_pic']->getSaveName(),
                            'movie_id'=>$res,
                            'hot_time'=>str_replace("：",":",$post['hot_time'][$key]),
                            'create_time'=>time(),
                        ];
                    }
                }
                if($padd)
                    Db::name("app_movie_hotpoint")->insertAll($padd);
            }

            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function edit($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');
        if (!request()->isPost()) {
            $class_where[] = ['status', '=', 1];
            $all_tag = Db::name('app_tag')->field('id,title')->order('id desc')->select();
            $app_tag = Db::name('app_tag')->field('id,title')->where('id', 'in', trim($res['tag_ids'], "'"))->order('id desc')->select();
            $app_star = Db::name('app_star')->field('id,uname title')->order('id desc')->select();
            $app_theme = Db::name('app_theme')->field('id,title')->where($class_where)->order('id desc')->select();
            $class_where = [];
            $class_where[] = ['category_type', '=', 'movie'];
            $app_category = Db::name('app_category')->field('id,title')->where($class_where)->order('id desc')->select();
            $app_movie_bt = Db::name("app_movie_bt")->field('id,bt_url')->where('movie_id',$id)->select();
            $app_products = Db::name("app_products")/*->where('status',1)*/->field('id,title')->order('id ASC')->select();
            $app_movie_hotpoint = Db::name("app_movie_hotpoint")->where('movie_id',$id)->field('id,hot_time,hot_pic')->order('id ASC')->select();
            $app_tag1 = addslashes(json_encode($app_tag));
            $app_star1 = addslashes(json_encode($app_star));
            $res['url'] = replaceCdnUrl($res['url']);
            $this->assign([
                'res' => $res,
                'app_category' => $app_category,
                'all_tag' => $all_tag,
                'app_tag' => $app_tag,
                'app_tag1' => $app_tag1,
                'app_star' => $app_star,
                'app_star1' => $app_star1,
                'app_theme' => $app_theme,
                'app_products'=>$app_products,
                'app_movie_bt'=>$app_movie_bt,
                'app_movie_hotpoint'=>$app_movie_hotpoint,
            ]);
            $this->_assign();
            return view();
        } else {
            $post = input('post.');

            // if (empty($post['category_ids'])) json_fail_admin('请选择分类');
            if (empty($post['title'])) json_fail_admin('标题不能为空');
            // if (empty($post['url'])) json_fail_admin('请选择上传视频');
            if ($post['number']) {
               $re = Db::name('app_movie')->where('id','<>',$id)->where('number',$post['number'])->find();
               if ($re) {
                   json_fail_admin('番号重复!');
               }
            }

             $money_reg = '/^[0-9]+(.[0-9]{1,2})?$/';
             if($post['price'] != '0.00' && $post['price'] && !preg_match($money_reg, $post['price'])){
               json_fail('金额不合法!');

             }

            $data = [
                'title' => $post['title'],
                'product_id' => $post['product_id'],
                'number' => $post['number'],
                'duration' => $post['duration'],
                'cover' => $post['cover'],
                'gif' => $post['gif'],
                'cover_vertical' => $post['cover_vertical'],
                'detail' => $post['detail'],
                'click_num' => $post['click_num'],
                'folder_num' => $post['folder_num'],
                'up_num' => $post['up_num'],
                'down_num' => $post['down_num'],
                'score' => $post['score'],
                'sort' => $post['sort'],
                'search_sort' => $post['search_sort'],
                'price' => $post['price'],
                'type' => $post['type'],
                'status' => $post['status'],
                'update_time' => time(),
                'update_date' => date('Y-m-d'),
                'update_user' => $this->user['id'],
            ];

            // $data['tag_ids'] = empty($post['tag_ids']) ? '' : implode(',', $post['tag_ids']);
            $tag_str = $post['tag_str'];
            $tag_ids = [];
            if ($tag_str) {
                $tag = array_filter(explode(',', $tag_str));
                foreach ($tag as $key => $value) {
                    $id = Db::name('app_tag')->where('title',trim($value))->value('id');
                    if (!$id) {
                       $id = Db::name('app_tag')->insertGetId(['title'=>trim($value),'status'=>0]);
                    }
                    $tag_ids[] = $id;
                }
            }
            $data['tag_ids'] = empty($tag_ids) ? '' : implode(',', $tag_ids);

            $star_str = $post['star_str'];
            $star_ids = [];
            if ($star_str) {
                $star = array_filter(explode(',', $star_str));

                foreach ($star as $key => $value) {
                    $id = Db::name('app_star')->where('uname',trim($value))->value('id');
                    if (!$id) {
                       $id = Db::name('app_star')->insertGetId(['uname'=>trim($value),'status'=>0]);
                    }
                    $star_ids[] = $id;
                }
            }

            $post['star_ids'] = $star_ids;


            if($res['status'] == 1 && $post['status'] == 1){
                if (empty($post['star_ids'])) {
                    Db::name('app_star')->where('id', 'in', trim($res['star_ids'], "'"))->setDec('movie_num');
                    $data['star_ids'] = '';
                } else {

                    $data['star_ids'] = implode(',', $post['star_ids']);
                    Db::name('app_star')->where('id', 'in', trim($res['star_ids'], "'"))->setDec('movie_num');
                    Db::name('app_star')->where('id', 'in', $data['star_ids'])->setInc('movie_num');

                    $nations = Db::name("app_star")->where('id', 'in', $data['star_ids'])->column('nation_id');
                    Db::name('app_nation')->where('id', 'in', $nations)->setInc('movie_num');
                    $nations = Db::name("app_star")->where('id', 'in', trim($res['star_ids'], "'"))->column('nation_id');
                    Db::name('app_nation')->where('id', 'in', $nations)->setDec('movie_num');

                    $jobs = Db::name("app_star")->where('id', 'in', $data['star_ids'])->column('job_id');
                    Db::name('app_star_job')->where('id', 'in', $jobs)->setInc('movie_num');
                    $jobs = Db::name("app_star")->where('id', 'in', trim($res['star_ids'], "'"))->column('job_id');
                    Db::name('app_star_job')->where('id', 'in', $jobs)->setDec('movie_num');
                }
                Db::name('app_products')->where('id', 'in', $data['product_id'])->setInc('videos');
                if($res['product_id']){
                    Db::name('app_products')->where('id', 'in', $res['product_id'])->setDec('videos');
                }
            }elseif($res['status'] == 0 && $post['status'] == 0){
                if (empty($post['star_ids'])) {
                    $data['star_ids'] = '';
                } else {
                    $data['star_ids'] = implode(',', $post['star_ids']);
                }
            }elseif($res['status'] == 0 && $post['status'] == 1){
                if (empty($post['star_ids'])) {
                    $data['star_ids'] = '';
                } else {
                    $data['star_ids'] = implode(',', $post['star_ids']);
                    Db::name('app_star')->where('id', 'in', $data['star_ids'])->setInc('movie_num');

                    $nations = Db::name("app_star")->where('id', 'in', $data['star_ids'])->column('nation_id');
                    Db::name('app_nation')->where('id', 'in', $nations)->setInc('movie_num');
                    $jobs = Db::name("app_star")->where('id', 'in', $data['star_ids'])->column('job_id');
                    Db::name('app_star_job')->where('id', 'in', $jobs)->setInc('movie_num');
                }
                Db::name('app_products')->where('id', 'in', $data['product_id'])->setInc('videos');
            }elseif($res['status'] == 1 && $post['status'] == 0){
                if (empty($post['star_ids'])) {
                    Db::name('app_star')->where('id', 'in', trim($res['star_ids'], "'"))->setDec('movie_num');
                    $data['star_ids'] = '';
                } else {
                    $data['star_ids'] = implode(',', $post['star_ids']);
                    Db::name('app_star')->where('id', 'in', trim($res['star_ids'], "'"))->setDec('movie_num');
                    $nations = Db::name("app_star")->where('id', 'in', trim($res['star_ids'], "'"))->column('nation_id');
                    Db::name('app_nation')->where('id', 'in', $nations)->setDec('movie_num');
                    $jobs = Db::name("app_star")->where('id', 'in', trim($res['star_ids'], "'"))->column('job_id');
                    Db::name('app_star_job')->where('id', 'in', $jobs)->setDec('movie_num');
                }
                if($res['product_id']){
                    Db::name('app_products')->where('id', 'in', $res['product_id'])->setDec('videos');
                }
            }

            $data['category_ids'] = empty($post['category_ids']) ? '' : implode(',', $post['category_ids']);
            $data['theme_ids'] = empty($post['theme_ids']) ? '' : implode(',', $post['theme_ids']);

            $file['hot_pic'] = request()->file("file_hot_pic");

            $res = Db::name($this->name)->where($where)->update($data);


            if(!empty($post['bt'])){
                Db::name("app_movie_bt")->where('movie_id',$id)->delete();
                foreach ($post['bt'] as $key=>$value){
                    if($value){
                        $add['movie_id'] = $id;
                        $add['bt_url'] = $value;
                        $add['create_time'] = time();
                        Db::name("app_movie_bt")->insertGetId($add);
                    }
                }
            }


            if(!empty($post['hot_time'])){
                $padd = [];
                Db::name("app_movie_hotpoint")->where('movie_id',$id)->delete();
                $fk = 0;
                foreach ($post['hot_time'] as $key=>$value){
                    if($value){
                        if(strpos($post['hot_pic'][$key],'hot_pic') !== false){
                            $padd []= [
                                'hot_pic'=>$post['hot_pic'][$key],
                                'movie_id'=>$id,
                                'hot_time'=>str_replace("：",":",$post['hot_time'][$key]),
                                'create_time'=>time(),
                            ];
                        }else{
                            $info['hot_pic'] = $file['hot_pic'][$fk]->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/hot_pic');
                            if (!$info['hot_pic']) continue;
                            $padd []= [
                              'hot_pic'=>UPLOAD_NAME . '/hot_pic/' . $info['hot_pic']->getSaveName(),
                                'movie_id'=>$id,
                                'hot_time'=>str_replace("：",":",$post['hot_time'][$key]),
                                'create_time'=>time(),
                            ];
                            $fk++;
                        }
                    }
                }
                if($padd)
                    Db::name("app_movie_hotpoint")->insertAll($padd);

            }
            $this->addlog('修改视频信息','视频ID:'.$id);

            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function del($id)
    {
        json_fail_admin('该功能暂时关闭');
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        $m = $res;
        if (empty($res)) json_fail_admin('视频不存在');
        $del = [$res['url'],$res['url2'],$res['cover'],$res['cover_vertical']];

        $star_ids = trim($res['star_ids'], "'");
        $res = Db::name($this->name)->where($where)->delete();
        if ($res) {
            if($m['status'] == 1){
                $a = Db::name('app_star')->where([['id', 'in', $star_ids], ['movie_num', '>', 0]])->setDec('movie_num');
                $jobs = Db::name("app_star")->where('id', 'in', $star_ids)->column('job_id');
                $nations = Db::name("app_star")->where('id', 'in', $star_ids)->column('nation_id');
                Db::name('app_nation')->where('id', 'in', $nations)->setDec('movie_num');
                Db::name('app_star_job')->where('id', 'in', $jobs)->setDec('movie_num');
                Db::name('member_cache')->where('movie_id', '=', $id)->delete();
                Db::name('app_products')->where('id', '=', $m['product_id'])->setDec('videos');
                Db::name('member_comment')->where('movie_id', '=', $id)->delete();
                Db::name('member_comment_up')->where('movie_id', '=', $id)->delete();
                Db::name('member_history')->where('movie_id', '=', $id)->delete();
                Db::name('member_like')->where('movie_id', '=', $id)->delete();
                Db::name('member_movie_up_down')->where('movie_id', '=', $id)->delete();
            }
            $this->addlog('删除视频信息','视频ID:'.$id);

            delPathList($del);
            json_success_admin();
        } else {
            json_fail_admin();
        }
    }
    public function status($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');

        try {
            Db::startTrans();
            if($res['status'] == 1){
                $star_ids = trim($res['star_ids'], "'");
                $a = Db::name('app_star')->where([['id', 'in', $star_ids], ['movie_num', '>', 0]])->setDec('movie_num');
                $jobs = Db::name("app_star")->where('id', 'in', $star_ids)->column('job_id');
                $nations = Db::name("app_star")->where('id', 'in', $star_ids)->column('nation_id');
                Db::name('app_nation')->where('id', 'in', $nations)->setDec('movie_num');
                Db::name('app_star_job')->where('id', 'in', $jobs)->setDec('movie_num');
                Db::name('member_cache')->where('movie_id', '=', $id)->delete();
                Db::name('app_products')->where('id', '=', $res['product_id'])->setDec('videos',1);
                Db::name('member_comment')->where('movie_id', '=', $id)->delete();
                Db::name('member_comment_up')->where('movie_id', '=', $id)->delete();
                Db::name('member_history')->where('movie_id', '=', $id)->delete();
                Db::name('member_like')->where('movie_id', '=', $id)->delete();
                Db::name('member_movie_up_down')->where('movie_id', '=', $id)->delete();

                $this->addlog('禁用视频','视频ID:'.$id);
            }else{
                $star_ids = trim($res['star_ids'], "'");
                $a = Db::name('app_star')->where([['id', 'in', $star_ids], ['movie_num', '>', 0]])->setInc('movie_num');
                $jobs = Db::name("app_star")->where('id', 'in', $star_ids)->column('job_id');
                $nations = Db::name("app_star")->where('id', 'in', $star_ids)->column('nation_id');
                Db::name('app_nation')->where('id', 'in', $nations)->setInc('movie_num');
                Db::name('app_star_job')->where('id', 'in', $jobs)->setInc('movie_num');
                Db::name('app_products')->where('id', '=', $res['product_id'])->setInc('videos');
                $this->addlog('启用视频','视频ID:'.$id);

            }
            $res = Db::name($this->name)->where($where)->exp('status', 'if(status=1,0,1)')->update();
            Db::commit();
        }catch (\Throwable $e){
            Db::rollback();
            json_fail_admin("操作失败：".$e->getMessage().$e->getFile().$e->getFile());
        }

        $res ? json_success_admin() : json_fail_admin();
    }
    public function re_index()
    {
        Db::query("UPDATE hg_app_movie a JOIN (SELECT @rownum := @rownum +1 AS rownum,e.* FROM (SELECT  @rownum := 0) r,hg_app_movie e ) b on a.id=b.id set a.index = b.rownum");
        json_success_admin();
    }
    public function details($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');

        $class_where[] = ['status', '=', 1];
        $app_tag = Db::name('app_tag')->field('id,title')->where('id', 'in', trim($res['tag_ids'], "'"))->order('sort desc')->select();
        // $app_tag = Db::name('app_tag')->field('id,title')->where($class_where)->order('sort desc')->select();
        $app_star = Db::name('app_star')->field('id,uname title')->where($class_where)->order('sort desc')->select();
        $app_theme = Db::name('app_theme')->field('id,title')->where($class_where)->order('sort desc')->select();
        $class_where[] = ['category_type', '=', 'movie'];
        $app_category = Db::name('app_category')->field('id,title')->where($class_where)->order('sort desc')->select();
        $app_products = Db::name("app_products")->where('status',1)->field('id,title')->order('id ASC')->select();

        $this->assign([
            'res' => $res,
            'app_category' => $app_category,
            'app_tag' => $app_tag,
            'app_star' => $app_star,
            'app_theme' => $app_theme,
            'app_products' => $app_products,
        ]);
        return view();
    }
    public function choose()
    {
        $this->public_choose(__FUNCTION__);
        return view();
    }

    /**
     * 批量设置的选择列表
     * @return View
     * @throws DbException
     */
    public function batch_update_choose(){
        $this->public_choose(__FUNCTION__);
        return view();
    }


    /**
     * 通用选择列表代码（内部方法）
     * @return void
     * @throws DbException
     */
    private function public_choose(string $function_name){
        $arr = ['app_category', 'app_tag', 'app_star', 'app_theme','app_products','app_movie'];
        $name = input('param.name');
        $star_id = input('param.star_id');
        $ids = input('param.ids');
        $movie_type = input('param.movie_type');
        $movie_ids = input('param.movie_ids');
        $is_add_selected_ids = input('param.is_add_selected_ids');
        if (!in_array($name, $arr)) json_fail_admin('参数错误!');
        $where = [];
        $field = 'id,title,pinyin';
        $order = "pinyin asc";
        if ($name == 'app_star') {
            $order = "id asc";
            // $where []= ['status','=',1];
            $field = 'id,uname title,pinyin';
        }
        if ($name == 'app_tag') {
            $order = "id asc";
            $field = 'id, title,pinyin';
        }
        if ($name == 'app_category') {
            $where[] = ['category_type', '=', 'movie'];
            $order = "id asc";
        }
        if($name == "app_products"){
            $field = "id,title";
            $order = "id asc";
            $where []= ['status','=',1];
        }

        if($name == "app_movie"){
            $field = "id,title,cover_vertical";
            $order = "id desc";
            if(!empty($star_id)){
                $where[] = ['','exp', Db::raw("FIND_IN_SET('{$star_id}',star_ids)")];
            }
            if(!empty($movie_type)){
                $where []= ['type','=',$movie_type];
            }
            $where []= ['status','=',1];

            if(!empty($movie_ids)){
                $where []= ['id','IN',explode(',',$movie_ids)];
            }
        }
        $condition['title'] = input('request.title');
        if (!empty($condition['title'])) {
            if ($name == 'app_star') {
                $where[] = ['uname', 'like', '%' . $condition['title'] . '%'];
            } else {
                $where[] = ['title', 'like', '%' . $condition['title'] . '%'];
            }
        }
        $condition['movie_ids'] = $movie_ids;
        $list = Db::name($name)->field($field)->where($where)->order($order)->paginate(99999)->each(function ($item,$key)use ($name){
            $item['title_base64']=$item['title']??'';
            if(!empty($item['title'])){
                $item['title_base64']=base64_encode($item['title']);
            }
            return $item;
        });

        $page = $list->render();
        $list = $list->toArray();
        foreach ($list['data'] as $k=>&$v){
            $v['selected']=0;
        }

        $is_show_overlay_data = 0;
        if(in_array($name,['app_category','app_tag','app_star'])){
            $is_show_overlay_data=1;
            if($is_add_selected_ids==1){
                $memory_ids = cacheGet(RedisConst::GET_BATCH_MEMORY_KEY.$name.':'.$this->user['id']);
                if(!empty($memory_ids) && !empty($list['data'])){
                    foreach ($list['data'] as $k=>&$v){
                        if(in_array($v['id'],$memory_ids)){
                            $v['selected']=1;
                        }
                    }
                    $list_selecteds = array_column($list['data'],'selected');
                    array_multisort($list_selecteds,SORT_DESC,$list['data']);
                }
            }
        }

        $this->assign([
            'name' => $name,
            'list' => $list,
            'page' => $page,
            'condition' => $condition,
            'ids' => $ids,
            'is_show_overlay_data' => $is_show_overlay_data,
            'selected_ids' => !empty($memory_ids)?implode(',',$memory_ids):'',
            'current_url' => '/admin/app_movie/'.$function_name.'/name/' . $name."?ids=".$ids.(!empty($movie_type)?'&movie_type='.$movie_type:''),
        ]);
    }

    public function is_new($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');
        $res = Db::name($this->name)->where($where)->update(['create_time'=>time()]);
        $res ? json_success_admin('已设置视频为最新') : json_fail_admin();
    }
    public function is_play($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');
        $res = Db::name($this->name)->where($where)->setInc('click_num',100000);
        $res ? json_success_admin('已添加100000点击数量') : json_fail_admin();
    }
    public function is_like($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');
        $res = Db::name($this->name)->where($where)->setInc('like_num',1000);
        $res ? json_success_admin('已添加1000喜欢数量') : json_fail_admin();
    }

    public function wait()
    {
        $condition['id'] = input('request.id');
        $condition['title'] = input('request.title');
        if (!empty($condition['title'])) $where[] = ['name', 'like', '%' . $condition['title'] . '%'];

        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

//        if(input('request.start_enter')!=1){
//            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
//            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
//        }

         if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'])+60*60*24];


        $condition['status'] = input('request.status',1);

        if ($condition['status'] != '-1') {
            $where[] = ['status', '=', $condition['status']];
        }

        $where[] = ['type', '=', 1];

        $list = Db::name('slice_data')->where($where)->order('id desc')->paginate(10);
        $page = $list->render();
        $list = $list->toArray();

        $imgs_cdn = source_config()['imgs_cdn'];


        foreach($list['data'] as $k=>&$v){

            if(!empty($v['url']))$v['url'] = replaceCdnUrl($v['url']);
            $v['cover']='';
            if(!empty($v['index']) && !empty(explode(',',$v['index'])['0']) )$v['cover'] = readBase(rtrim($imgs_cdn,'/').explode(',',$v['index'])['0']);
            $v['status_name']=SliceDataService::getConstPluck('STATUS_')[$v['status']]??'未知';
            $v['url_base64']=base64_encode($v['url']);
        }

        $this->assign([
            'list' => $list,
            'url' => source_config()['movie_url'],
            'page' => $page,
            'condition' => $condition
        ]);
        $this->_assign();

        return view();

    }

    public function edit_wait($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name('slice_data')->where($where)->find();

        $imgs_cdn = source_config()['imgs_cdn'];
        $res['cover'] = $res['cover_vertical'] = [];
        if ($res['index']) {
            $res['index'] = explode(',',$res['index']);
            foreach ($res['index'] as $key => $value) {
                $res['cover'][$value] = readBase(rtrim($imgs_cdn,'/').$value);
            }
        }

        if ($res['index3']) {
            $res['index3'] = explode(',',$res['index3']);
            foreach ($res['index3'] as $key => $value) {
                $res['cover_vertical'][$value] = readBase(rtrim($imgs_cdn,'/').$value);
            }
        }
        $res['cover'] = array_filter($res['cover']); //资源路径不存在时返回的空前段依然有选项
        $res['number'] = '';
        if ($res['path']) {
            $res['number'] = explode('/',$res['path']);
            $res['number'] = $res['number'][count($res['number'])-2];
        }
        $res['theme_ids']=$res['tag_ids']=$res['star_ids']=$res['category_ids']='';
        if(!empty($res['cron_edited_data'])){
            $cron_edited_data = json_decode($res['cron_edited_data'],true);
            $res['category_ids']=implode(',',$cron_edited_data['post']['category_ids']??[]);
            $res['theme_ids']=implode(',',$cron_edited_data['post']['theme_ids']??[]);
            $res['tag_ids']=implode(',',$cron_edited_data['post']['tag_ids']??[]);
            $res['star_ids']=implode(',',$cron_edited_data['post']['star_ids']??[]);
        }

        if(!empty($res['url']))$res['url'] = replaceSourceUrl($res['url']);

        if (empty($res)) json_fail_admin('视频不存在');
        if (!request()->isPost()) {
            $class_where[] = ['status', '=', 1];
            $app_tag = Db::name('app_tag')->field('id,title')->order('id desc')->select();
            $app_star = Db::name('app_star')->field('id,uname title')->order('id desc')->select();
            $app_theme = Db::name('app_theme')->field('id,title')->where($class_where)->order('id desc')->select();
            $class_where[] = ['category_type', '=', 'movie'];
            $app_category = Db::name('app_category')->field('id,title')->where($class_where)->order('id desc')->select();
            $app_movie_bt = Db::name("app_movie_bt")->field('id,bt_url')->where('movie_id',$id)->select();
            $app_products = Db::name("app_products")->where('status',1)->field('id,title')->order('id ASC')->select();
            $app_movie_hotpoint = Db::name("app_movie_hotpoint")->where('movie_id',$id)->field('id,hot_time,hot_pic')->order('id ASC')->select();


            $app_tags = Db::name('app_tag')->field('id,title')->where('id', 'in', trim($res['tag_ids'], "'"))->order('id desc')->select();

            $app_stars=$app_star;
            $app_tag = addslashes(json_encode($app_tag));
            $app_star = addslashes(json_encode($app_star));

            if(!empty($res['cron_edited_data'])){
                $res['cron_edited_data'] = json_decode($res['cron_edited_data'],true);
            }
//dump($app_tag);die();
            $this->assign([
                'res' => $res,
                'app_category' => $app_category,
                'app_tag' => $app_tag,
                'app_tags' => $app_tags,
                'app_stars' => $app_stars,
                'app_star' => $app_star,
                'app_theme' => $app_theme,
                'app_products'=>$app_products,
                'app_movie_bt'=>$app_movie_bt,
                'app_movie_hotpoint'=>$app_movie_hotpoint,
                'url' => source_config()['movie_url']

            ]);
            $this->_assign();
            return view();
        } else {
            $post = input('post.');

            if (empty($post['title'])) json_fail_admin('标题不能为空');
            // if (empty($post['cover'])) json_fail_admin('水平封面不能为空');
            // if (empty($post['cover_vertical'])) json_fail_admin('垂直封面不能为空');
            if (empty($post['product_id'])) json_fail_admin('出品方不能为空!');
            // if (empty($post['gif'])) json_fail_admin('GIF不能为空!');
            if ($post['number']) {
                $re = Db::name('app_movie')->where('number',$post['number'])->find();
                if ($re) {
                    json_fail_admin('番号重复!');
                }
            }
            $money_reg = '/^[0-9]+(.[0-9]{1,2})?$/';
            if($post['price'] != '0.00' && $post['price'] && !preg_match($money_reg, $post['price'])){
                json_fail('金额不合法!');

            }

            $data = [
                'title' => $post['title'],
                'product_id' => $post['product_id'],
                'number' => $post['number'],
                'status' => $post['status'],
                'detail' => $post['detail'],
                'click_num' => 0,
                'up_num' => 0,
                'down_num' => 0,
                'price' => $post['price'],
                'type' => $post['type'],
                'create_time' => time(),
                'create_date' => date('Y-m-d'),
                'create_user' => $this->user['id'],
            ];

            $slice_data_base=$slice_data_update=[];
            //初始观影次数设置和初始点赞率设定
            $data['click_num'] = 0;
            if (sysconf('init_num_status')) {
                $data['click_num'] = rand(sysconf('init_num_start'),sysconf('init_num_end'));
            }
            if (sysconf('init_rate_status')) {
                $percent = rand(sysconf('init_rate_start'),sysconf('init_rate_end'));
                $data['up_num'] = ceil(( $percent / 100 ) * $data['click_num']);
            }

            if (!empty($post['cover_hidden']) ) {
                $data['cover'] = $post['cover_hidden'];
                if(!empty(parse_url($post['cover_hidden'])['path']))$post['cover_hidden']=parse_url($post['cover_hidden'])['path'];
                if(!empty($res['cover'][$post['cover_hidden']])){
                    unset($res['cover'][$post['cover_hidden']]);
                }

                $slice_data_base['index'] = implode(',',array_merge([$post['cover_hidden']],array_keys($res['cover'])));
            }elseif (!empty($post['cover'])) {
//                dump([$post['cover'],parse_url($post['cover'])]);die();
//                if(!empty(parse_url($post['cover'])['path']))$post['cover']=parse_url($post['cover'])['path'];
                $data['cover'] = rtrim($imgs_cdn,'/').$post['cover'];
                if(!empty($res['cover'][$post['cover']])){
                    unset($res['cover'][$post['cover']]);
                }
                $slice_data_base['index'] = implode(',',array_merge([$post['cover']],array_keys($res['cover'])));
            }

            if (!empty($post['cover_vertical_hidden'])) {
                $data['cover_vertical'] = $post['cover_vertical_hidden'];
                if(!empty(parse_url($post['cover_vertical_hidden'])['path']))$post['cover_vertical_hidden']=parse_url($post['cover_vertical_hidden'])['path'];
                $post['cover_vertical'] = rtrim($imgs_cdn,'/').$post['cover_vertical_hidden'];
                if(!empty($res['cover_vertical'][$post['cover_vertical_hidden']])){
                    unset($res['cover_vertical'][$post['cover_vertical_hidden']]);
                }
                $slice_data_base['index3'] = implode(',',array_merge([$post['cover_vertical_hidden']],array_keys($res['cover_vertical'])));
            }elseif (!empty($post['cover_vertical']) ) {
//                if(!empty(parse_url($post['cover_vertical'])['path']))$post['cover_vertical']=parse_url($post['cover_vertical'])['path'];
                $data['cover_vertical'] = rtrim($imgs_cdn,'/').$post['cover_vertical'];
                if(!empty($res['cover_vertical'][$post['cover_vertical']])){
                    unset($res['cover_vertical'][$post['cover_vertical']]);
                }

                $slice_data_base['index3'] = implode(',',array_merge([$post['cover_vertical']],array_keys($res['cover_vertical'])));
            }

            if (isset($post['gif']) && $post['gif']) {
                $data['gif'] = rtrim($imgs_cdn,'/').$post['gif'];
            }
            // $data['tag_ids'] = empty($post['tag_ids']) ? '' : implode(',', $post['tag_ids']);

            //这里判断如果是定时传片的话
            if(!empty($post['cron_edited_time']) || $res==SliceDataService::STATUS_EDITED_WAITING[0]) {

                if(strtotime($post['cron_edited_time']) < time()){
                    json_fail_admin('定时传片时间必须大于当前时间');
                }

                $slice_data_update = [
                    'status'=>SliceDataService::STATUS_EDITED_WAITING[0],
                    'cron_edited_time'=>$post['cron_edited_time'],
                    'name'=>$post['title'],
                    'cron_edited_data'=>json_encode(['post'=>$post,'data'=>$data,'res'=>$res,'log_data'=>['admin_id'=>$this->user['id'],'admin'=>$this->user['username'],'ip'=>request()->ip(),'time'=>time()]],JSON_UNESCAPED_UNICODE),
                ];
            }else{

                $change_moive_return = (new SliceDataService())->changeMoive($post,$data,$res);
                if(!empty($change_moive_return['app_movie_id'])){
                    $this->addlog('添加新视频','视频ID:'.$change_moive_return['app_movie_id']);
                }else{
                    json_fail_admin();
                }

            }

            Db::name('slice_data')->where('id',$post['id'])->update(array_merge($slice_data_update,$slice_data_base));

            json_success_admin();
        }
    }

    public function del_wait($id)
    {
        $res = Db::name('slice_data')->whereIn('id',$id)->update(['status' => 2]);
        $res ? json_success_admin() : json_fail_admin();
    }



    //设置售价
    public function set_price()
    {
        if (request()->isPost()) {
            $ids = input('param.ids');
            $price = input('param.price');
            if (!$ids) {
                json_fail_admin('视频不能为空!');
            }

            $money_reg = '/^[0-9]+(.[0-9]{1,2})?$/';
             if(!preg_match($money_reg, $price) && !($price==='0')){
               json_fail('金额不合法!');
             }

            Db::name('app_movie')->whereIn('id',$ids)->update(['price' => $price,'type' => 2]);
            json_success_admin('设置成功!');
        }else{
            $ids = input('param.ids');
            $res = Db::name('app_movie')->whereIn('id',$ids)->field("id,title,price")->order('id desc')->select();
            $this->assign('ids',$ids);
            $this->assign('res',$res);
            $this->assign('current_url',$this->current_url);
            return view('set_price');
        }

    }
    //设置权限
    public function set_type()
    {
        $this->_assign();

        if (request()->isPost()) {
            $ids = input('param.ids');
            $type = input('param.type');
            if (!$ids) {
                json_fail_admin('视频不能为空!');
            }
            if (!$type || !is_numeric($type)) {
                 json_fail_admin('视频类型有误!');
            }
            Db::name('app_movie')->whereIn('id',$ids)->update(['type' => $type]);
            json_success_admin('设置成功!');
        }else{
            $ids = input('param.ids');
            $res = Db::name('app_movie')->whereIn('id',$ids)->field("id,title,type")->order('id desc')->select();
            $this->assign('ids',$ids);
            $this->assign('res',$res);
            $this->assign('current_url',$this->current_url);

            return view('set_type');
        }
    }

    //批量编辑视频
    public function batch_video_info()
    {

        if (request()->isPost()) {
            $ids = input('param.ids');
            $type_name = input('param.type_name');
            $update_ids = input('param.update_ids');
            $submit_type = input('param.submit_type');
            if (!$ids) {
                json_fail_admin('视频不能为空!');
            }
            if(empty($update_ids))$update_ids=[];

            $online_movie_lists = Db::name('app_movie')->whereIn('id',$ids)->where(['status'=>1])->select();

            try{
                Db::startTrans();
                $field_name='';
                switch ($type_name){
                    case 'app_category':$field_name='category_ids';break;
                    case 'app_tag':$field_name='tag_ids';break;
                    case 'app_star':

                        $field_name='star_ids';

                        $star_ids_chunk = array_count_values(array_filter(array_column($online_movie_lists,'star_ids')));

                        foreach ($star_ids_chunk as $k=>$v){
                            $k=trim($k,',');
                            $count = Db::name('app_star')->where('id', $k)->value('movie_num');
                            if($count<$v)$v = $count;
                            Db::name('app_star')->where('id', $k)->setDec('movie_num',$v);
                        }
                        Db::name('app_star')->whereIn('id', $update_ids)->setInc('movie_num',count($online_movie_lists));

                        $star_ids_temp = array_column($online_movie_lists,'star_ids');
                        $star_ids=[];
                        foreach ($star_ids_temp as $k=>$v){
                            if(!empty($v))$star_ids=array_merge($star_ids,explode(',',$v));
                        }

                        $nation_id_chunk=[];
                        $job_id_chunk=[];
                        foreach ($star_ids as $v){
                            $nation_id = Db::name("app_star")->where('id',$v)->cache(60)->value('nation_id');
                            $job_id = Db::name("app_star")->where('id',$v)->cache(60)->value('job_id');
                            if(empty($nation_id_chunk[$nation_id]))$nation_id_chunk[$nation_id]=0;
                            if(empty($job_id_chunk[$job_id]))$job_id_chunk[$job_id]=0;
                            $nation_id_chunk[$nation_id]+=1;
                            $job_id_chunk[$nation_id]+=1;
                        }


                        $app_star_add_list = Db::name('app_star')->whereIn('id',$update_ids)->select();
                        $nation_id_add_chunk = array_count_values(array_column($app_star_add_list,'nation_id'));

                        foreach ($nation_id_chunk as $k=>$v){
                            $k=trim($k,',');
                            $count = Db::name('app_nation')->where('id', $k)->value('movie_num');
                            if($count<$v)$v = $count;
                            Db::name('app_nation')->where('id', $k)->setDec('movie_num',$v);
                        }
                        foreach ($nation_id_add_chunk as $k=>$v){
                            if(!empty($v)) {
                                Db::name('app_nation')->whereIn('id', $k)->setInc('movie_num', $v);
                            }
                        }

                        $job_id_add_chunk = array_count_values(array_column($app_star_add_list,'job_id'));
                        foreach ($job_id_chunk as $k=>$v){
                            $count = Db::name('app_star_job')->where('id', $k)->value('movie_num');
                            if($count<$v)$v = $count;
                            Db::name('app_star_job')->where('id', $k)->setDec('movie_num',$v);
                        }
                        foreach ($job_id_add_chunk as $k=>$v){
                            if(!empty($v)){
                                Db::name('app_star_job')->where('id', $k)->setInc('movie_num',$v);
                            }
                        }

                        break;
                    case 'app_products':

                        $field_name='product_id';
                        $product_ids_chunk = array_count_values(array_column($online_movie_lists,'product_id'));
                        foreach ($product_ids_chunk as $k=>$v){
                            $k=trim($k,',');
                            $count = Db::name('app_products')->where('id', $k)->value('videos');
                            if($count<$v)$v = $count;

                            Db::name('app_products')->where('id', $k)->setDec('videos',$v);
                        }
                        Db::name('app_products')->whereIn('id', $update_ids)->setInc('videos',count($online_movie_lists));

                        break;
                    default:
                        Db::rollback();
                        json_fail_admin("name值不对");
                }

                if(!empty($field_name)){
                    if(in_array($type_name,['app_category','app_tag','app_star']) && $submit_type=='overlay_data' ){
                        foreach (explode(',',$ids) as $id){
                            $update_ids_temp = $update_ids;
                            $detail = Db::name('app_movie')->where(['id'=>$id])->field(['id',$field_name])->find();
                            if(!empty($detail[$field_name])){
                                $update_ids = array_unique(array_merge($update_ids,explode(',',$detail[$field_name])));
                            }
                            Db::name('app_movie')->where(['id'=>$id])->update([$field_name =>implode(',',$update_ids) ]);
                            $update_ids=$update_ids_temp;
                        }

                    }else{
                        Db::name('app_movie')->whereIn('id',$ids)->update([$field_name => implode(',',$update_ids)]);
                    }

                }

                Db::commit();

                if(in_array($type_name,['app_category','app_tag','app_star'])){
                    cacheSet(RedisConst::GET_BATCH_MEMORY_KEY.$type_name.':'.$this->user['id'],$update_ids,86400*90);
                }

            }catch (\Throwable $e){
                Db::rollback();
                json_fail_admin("设置失败，error:".$e->getMessage().$e->getFile().$e->getLine());
            }


            json_success_admin('设置成功!');
        }
    }


   public function set_show()
   {
        if ($this->request->isGet()) {
            $this->assign([
                'movie_show_bottom_img' => sysconf('movie_show_bottom_img'),
            ]);
            return view('set_show');
        }else{

            foreach ($this->request->post() as $key => $vo) {
                sysconf($key, $vo);
            }
            $this->addlog('视频角标设置', json_encode($this->request->post()));
            json_success_admin('视频角标设置成功!');
        }

   }


   public function movie_preview(){
       $url =  input('param.url');
       $this->assign([
           'url' => base64_decode($url),
       ]);
       return view();
   }

    public function set_prefix_suffix()
    {
        if (request()->isPost()) {
            $ids = input('param.ids');
            $prefix = ltrim($_POST['prefix']);
            $suffix = rtrim($_POST['suffix']);

            $del_prefix = ltrim($_POST['del_prefix']);
            $del_suffix = rtrim($_POST['del_suffix']);

            if (!$ids) {
                json_fail_admin('视频不能为空!');
            }
            if(empty($prefix) && empty($suffix) && empty($del_prefix) && empty($del_suffix)){
                json_fail('前缀或后缀至少设置一个!');
            }

            foreach (explode(',',$ids) as  $id){
                $app_movie_detail = Db::name('app_movie')->where(['id'=>$id])->find();
                if(!empty($app_movie_detail)){
                    $end_title = $app_movie_detail['title'];
                    // 去除前缀
                    if (!empty($del_prefix) && strpos($end_title, $del_prefix) === 0) {
                        $end_title = substr($end_title, strlen($del_prefix));
                    }

                    // 去除后缀
                    if (!empty($del_suffix) && substr($end_title, -strlen($del_suffix)) === $del_suffix) {
                        $end_title = substr($end_title, 0, -strlen($del_suffix));
                    }

                    Db::name('app_movie')->where(['id'=>$id])->update([
                        'title' => $prefix.$end_title.$suffix
                        ]
                    );
                }
            }

            json_success_admin('设置成功!');
        }else{
            $ids = input('param.ids');
            $res = Db::name('app_movie')->whereIn('id',$ids)->field("id,title,price")->order('id desc')->select();
            $this->assign('ids',$ids);
            $this->assign('res',$res);
            $this->assign('current_url',$this->current_url);
            return view();
        }

    }
}
