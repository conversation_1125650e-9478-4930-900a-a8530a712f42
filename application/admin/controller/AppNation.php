<?php
namespace app\admin\controller;

use think\Db;
class AppNation extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_nation",
            'title' => "地区管理",
            'current_url' => "/admin/app_nation",
        ];
        $this->where = [
            'title' => 'like',
            'status' => '',
        ];
        $this->verify = [
            'title'=>'标题不能为空',
        ];
        $this->order = 'sort desc';
        parent::__construct($conf);
    }
}