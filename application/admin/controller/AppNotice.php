<?php
namespace app\admin\controller;

use think\Db;
class AppNotice extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_notice",
            'title' => "公告列表",
            'current_url' => "/admin/app_notice",
        ];
        $this->where = [
            'title' => 'like',
            'content' => 'like',
            'status' => '',
        ];
        $this->order = 'sort desc';
        parent::__construct($conf);
    }
    public function mass($id)
    {
        $res = Db::name('app_notice')->where('id','=',$id)->find();
        if(!$res) json_fail_admin('公告不存在');
        $UserIds = Db::name('member_info')->field('id')->select();
        $data = [];
        foreach($UserIds as $k=>$v)
        {
            $data[$k]['member_id'] = $v['id'];
            $data[$k]['message_type'] = 2;
            $data[$k]['title'] = $res['title'];
            $data[$k]['content'] = $res['content'];
            $data[$k]['is_read'] = 1;
            $data[$k]['create_time'] = time();
        }
                
        $count=ceil(count($data)/100);

        for ($i=1;$i<=$count;$i++){

            $offset=($i-1)*(100);

            $ids=array_slice($data,$offset,100);

            Db::name('member_message')->insertAll($data);
            ob_flush();
        }
        json_success_admin('ok');
    }
}