<?php
namespace app\admin\controller;

use think\Db;
class AppProducts extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_products",
            'title' => "出品方管理",
            'current_url' => "/admin/app_products",
        ];
        $this->where = [
            'title' => 'like',
            'nation_id' => '',
            'category_id' => '',
            'status' => '',
        ];
        $this->files = ['icon','bg_icon1','bg_icon2','bg_icon3','image'];
        $this->verify =
            [
                'icon'=>'ICON图不能为空',
                'description'=>[
                    'rule'=>'/^.{0,500}$/',
                    'message'=>'简介不超过500字符',
                ],
                'image' => '首页推荐图不能为空'
            ];
        $this->class_list = Db::name('app_nation')->field('id,title')->where([ ['status', '=', '1']])->where('type',2)->order('sort desc')->select();
        $this->order = 'id desc';
        parent::__construct($conf);
    }

    public function index()
    {
        $where = $condition = [];
        if(!empty($this->where)){
            foreach ($this->where as $key => $value) {
                $$key = input("{$key}");
                if (!empty($$key)) {
                    switch ($value) {
                        case 'like':
                            $where[] = [$key, $value, '%' . $$key . '%'];
                            break;

                        default:
                            $where[] = [$key, '=', $$key];
                            break;
                    }
                }
                $condition[$key] = $$key;
            }
        }
        $list = Db::name($this->name)->where($where)->order($this->order)->paginate(20)->each(function ($item, $key) {
            $item['bg_icon'] = Db::name('app_product_pics')->where('product_id',$item['id'])->field('id,pic_url')->order('id asc')->select();
            return $item;
        });
        $this->_assign();
        $this->assign([
            'list' => $list,
            'class_list' => $this->class_list,
            'class_list2' => $this->class_list2,
            'condition' => $condition,
        ]);
        return view("/{$this->name}/index");
    }

    public function add(){
        if (request()->isPost()) {
            $post = input("post.");
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        $res = preg_match($v['rule'],$post[$k]);
                        if(!$res) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }

            $post['create_time'] = time();
            $post['create_user'] = $this->user['id'];
            $pics = [];
            if(!empty($post['bg_icon1'])){
                $pics []= $post['bg_icon1'];
            }
            if(!empty($post['bg_icon2'])){
                $pics []= $post['bg_icon2'];
            }
            if(!empty($post['bg_icon3'])){
                $pics []= $post['bg_icon3'];
            }
            unset($post['bg_icon1']);
            unset($post['bg_icon2']);
            unset($post['bg_icon3']);


            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            foreach ($pics as $key=>$value){
                $add['product_id'] = $res;
                $add['pic_url'] = $value;
                Db::name("app_product_pics")->insert($add);
            }
            Db::name('app_nation')->where('id', 'in', $post['nation_id'])->setInc('product_num');
            $this->addlog('添加出品方','出品方ID:'.$res);
            $res ? json_success_admin() : json_fail_admin();

        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if(isset($v['type']) && $v['type'] == 'class_list'){
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => '',
                            'role' => $v,
                        ];
                    }
                }
            }
            $this->assign([
                'res' => $arr,
            ]);
            return view('/common/add');
        }
    }




    public function edit()
    {
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        $res2 = Db::name("app_product_pics")->where('product_id',$id)->select();
        for ($i=1;$i<=3;$i++){
            $res['bg_icon'.$i] = empty($res2[$i-1]['pic_url'])?"":$res2[$i-1]['pic_url'];
        }
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input('post.');
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        $res1 = preg_match($v['rule'],$post[$k]);
                        if(!$res1) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }
//             if (!empty($this->files)) {
//                 $file = $info = [];
//                 foreach ($this->files as $v) {
//                     $file[$v] = request()->file("file_{$v}");
//                 }
//                 $role = config("role.{$this->name}");

//                 foreach ($this->files as $v) {
//                     if (!empty($file[$v])){
//                         $info[$v] = $file[$v]->validate(['size' => 10 * 1024 * 1024, 'ext' => 'jpg,jpeg,png,gif'])->move(UPLOAD_PATH . UPLOAD_NAME . '/' . $this->name);
//                         if (!$info[$v]) json_fail_admin($file[$v]->getError());
//                         delPathList([$res[$v]]);
//                         $post[$v] = UPLOAD_NAME . '/' . $this->name . '/' . $info[$v]->getSaveName();
// //                        $size = getimagesize(get_host($post[$v]));
// //
// //                        if($role[$v]['size']){
// //                            $s = explode(":",$role[$v]['size']);
// //                            if (($size[0] / $size[1]) != ($s[0] / $s[1])) {
// //                                json_fail_admin("文件尺寸不符");
// //                            }
// //                            // if($size[0] != $s[0] || $size[1] != $s[1]){
// //                            //     json_fail_admin("文件尺寸不符");
// //                            // }
// //                        }
//                     }
//                     unset($post["file_{$v}"]);
//                 }
//             }
            $post['update_time'] = time();
            $post['update_user'] = $this->user['id'];
            if(!empty($post['bg_icon1'])){
                $pics []= $post['bg_icon1'];
            }
            if(!empty($post['bg_icon2'])){
                $pics []= $post['bg_icon2'];
            }
            if(!empty($post['bg_icon3'])){
                $pics []= $post['bg_icon3'];
            }
            Db::name("app_product_pics")->where('product_id',$id)->delete();
            unset($post['bg_icon1']);
            unset($post['bg_icon2']);
            unset($post['bg_icon3']);
            if(!empty($pics)){
                foreach ($pics as $key=>$value){
                    $add['product_id'] = $id;
                    $add['pic_url'] = $value;
                    Db::name("app_product_pics")->insert($add);
                }
            }
            Db::name('app_nation')->where('id', 'in', $post['nation_id'])->setInc('product_num');
            Db::name('app_nation')->where('id', 'in', $res['nation_id'])->setDec('product_num');
            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);
            $this->addlog('修改出品方','出品方ID:'.$id);

            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if (isset($v['type']) && $v['type'] == 'class_list') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => '分类',
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => $v,
                        ];
                    }
                }
            }
            $this->assign([
                'res' => $arr,
                'is_parent' => $this->is_parent,
            ]);
            return view('/common/edit');
        }
    }



    public function del($id){
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        Db::name('app_nation')->where('id', 'in', trim($res['nation_id'], "'"))->setDec('product_num');
        $res = Db::name($this->name)->delete($id);
        $this->addlog('删除出品方','出品方ID:'.$id);

        $res ? json_success_admin() : json_fail_admin();
    }

}