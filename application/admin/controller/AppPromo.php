<?php
namespace app\admin\controller;

use think\Db;
class AppPromo extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_promo",
            'title' => "推广列表",
            'current_url' => "/admin/app_promo",
        ];
        parent::__construct($conf);
    }
    public function index()
    {
        $where = $condition = [];
        $condition['qq'] = input('param.qq');
        $condition['url'] = input('param.url');
        $condition['promo_code'] = input('param.promo_code');
        if(!empty($condition['qq'])) $where[] = ['a.qq','=',$condition['qq']];
        if(!empty($condition['url'])) $where[] = ['a.url','like','%' . $condition['url'] . '%'];
        if(!empty($condition['promo_code'])) $where[] = ['a.promo_code','like','%' . $condition['promo_code'] . '%'];


        $list = Db::name($this->name)->alias('a')
        ->join('member_info b','b.reg_promo_code=a.promo_code','left')
        ->join('member_open c','c.member_id=b.id','left')
        ->join('member_open d','d.member_id=b.id and d.create_time>='.strtotime(date('Y-m-d 00:00:00',time()-24*3600*3)).' and d.create_time<'.time().' ','left')
        ->join('member_open e','e.member_id=b.id and e.create_time>='.strtotime(date('Y-m-d 00:00:00',time()-24*3600*7)).' and e.create_time<'.time().' ','left')
        ->field('a.*,
            count(DISTINCT b.id) reg_count,
            count(DISTINCT c.member_id) login_count,
            count(DISTINCT d.member_id) life3_count,
            count(DISTINCT e.member_id) life7_count,
            if(count(DISTINCT b.id)<=0,99999,a.mon_pay*a.renew_num/count(DISTINCT b.id)) reg_one,
            if(count(DISTINCT c.member_id)<=0,99999,a.mon_pay*a.renew_num/count(DISTINCT c.member_id)) login_one
            ')
        ->where($where)
        ->order('reg_one asc')
        ->group('a.promo_code')
        ->paginate(20);

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
        ]);
        return view();
    }

    public function add()
    {
        if (request()->isPost()) {
            $post = input("post.");
            if(strlen($post['promo_code']) < 6) json_fail_admin('推广码最少6位');

            $url_arr = explode('/', $post['url']);
            foreach ($url_arr as $v) {
                if(!empty($v)) {
                    $one = Db::name($this->name)->where('url','like','%'.$v.'%')->find();
                    if($one) json_fail_admin($v.'网址已存在');
                }
            }

            $one = Db::name($this->name)->where('promo_code','=',$post['promo_code'])->find();
            if($one) json_fail_admin('推广码已存在');
            $overdue_time = strtotime($post['overdue_time']);
            if($overdue_time < time())
            {
                json_fail_admin('到期时间不能小于当前时间');
            }

            $data = [
                'qq'=>$post['qq'],
                'url'=>$post['url'],
                'promo_code'=>$post['promo_code'],
                'overdue_time'=>strtotime($post['overdue_time']),
                'mon_pay'=>$post['mon_pay'],
                'renew_num'=>$post['renew_num'],
                'create_time'=>time(),
                'create_user'=>$this->user['id'],
                'remark'=>$post['remark'],
            ];

            $one = Db::name('member_info')->where('promo_code','=',$post['promo_code'])->find();
            if(!$one) {
                try {
                    Db::name('member_info')->insert([
                        'account'=>$post['promo_code'],
                        'password'=>encode($post['promo_code']),
                        'promo_code'=>$post['promo_code'],
                        'create_time'=>time(),
                        'create_user'=>$this->user['id'],
                    ]);
                } catch (\Exception $e) {
                    json_fail_admin('注册失败');
                }
            }
            $one = Db::name($this->name)->where('qq','=',$post['qq'])->find();

            $res = Db::name($this->name)->insert($data);
            if($res) {
                if($one) json_success_admin('qq已存在');
                json_success_admin();
            }else{
                json_fail_admin();
            }
        } else {
            return view();
        }
    }
    public function edit()
    {
        $id = input('param.id');
        $res = Db::name($this->name)->where('id','=',$id)->find();
        if(!$res) json_fail_admin('数据不存在');
        if (request()->isPost()) {
            $post = input("post.");
            $url_arr = explode('/', $post['url']);
            foreach ($url_arr as $v) {
                if(!empty($v)) {
                    $one = Db::name($this->name)->where([['url','like','%'.$v.'%'],['id','<>',$id]])->find();
                    if($one) json_fail_admin($v.'网址已存在');
                }
            }

            $data = [
                'qq'=>$post['qq'],
                'url'=>$post['url'],
                'overdue_time'=>strtotime($post['overdue_time']),
                'mon_pay'=>$post['mon_pay'],
                'renew_num'=>$post['renew_num'],
                'update_time'=>time(),
                'update_user'=>$this->user['id'],
                'remark'=>$post['remark'],
            ];

            $one = Db::name($this->name)->where([['qq','=',$post['qq']],['id','<>',$id]])->find();
            $res = Db::name($this->name)->where('id','=',$id)->update($data);
            if($res) {
                if($one) json_success_admin('编辑成功(qq重复)');
                json_success_admin();
            }else{
                json_fail_admin();
            }
        } else {
            $res['overdue_time'] = date('Y-m-d H:i:s',$res['overdue_time']);
            $this->assign(['res'=>$res]);
            return view();
        }
    }
    public function config()
    {
        $id = 1;
        $res = Db::name('app_promo_config')->where('id','=',$id)->find();
        if(request()->isPost()) {
            $app_pro_head = input('post.app_pro_head');
            if(empty($res)) {
                $res = Db::name('app_promo_config')->insert([
                    'app_pro_head'=>$app_pro_head,
                    'create_time'=>time(),
                    'create_user'=>$this->user['id'],
                ]);
            }else{
                $res = Db::name('app_promo_config')->where('id','=',$id)->update([
                    'app_pro_head'=>$app_pro_head,
                    'update_time'=>time(),
                    'update_user'=>$this->user['id'],
                ]);
            }
            $res ? json_success_admin() : json_fail_admin();
        }else{
            $this->assign(['res'=>$res]);
            return view();
        }
    }
    public function report()
    {
        $where = '';
        $condition['promo_code'] = input('param.promo_code');
        $condition['begin_time'] = input('param.begin_time');
        $condition['end_time'] = input('param.end_time');
        if($condition['promo_code']) $where = "WHERE a.promo_code = '".$condition['promo_code']."'";
        if(!$condition['begin_time']) $condition['begin_time'] = date('Y-m-d',time()-24*3600*9);
        if(!$condition['end_time']) $condition['end_time'] = date('Y-m-d');
        $start = strtotime($condition['begin_time']);
        $end = strtotime($condition['end_time']);
        $today['user'] = Db::query("SELECT count(*) user FROM hg_app_promo a INNER JOIN hg_member_info b ON b.reg_promo_code=a.promo_code AND b.create_time >= '".strtotime(date('Y-m-d 00:00:00'))."' AND b.create_time<'".strtotime(date('Y-m-d 23:59:59',time()+3600*24))."' ".$where)[0]['user'];
        $today['open'] = Db::query("SELECT count(*) open FROM hg_member_open 
                                    WHERE member_id IN (SELECT b.id FROM hg_app_promo a INNER JOIN hg_member_info b ON b.reg_promo_code = a.promo_code AND b.create_time >= '".strtotime(date('Y-m-d 00:00:00'))."' AND b.create_time<'".strtotime(date('Y-m-d 23:59:59',time()+3600*24))."' ".$where.") AND create_time >= '".strtotime(date('Y-m-d 00:00:00'))."' AND create_time<'".strtotime(date('Y-m-d 23:59:59',time()+3600*24))."'")[0]['open'];

        $list = [];
        for ($i=$end; $i >= $start; $i-=24*3600) { 
            $data = [
                'create_date' => date('Y-m-d',$i),
                'user' => Db::query("SELECT count(*) user FROM hg_app_promo a INNER JOIN hg_member_info b ON b.reg_promo_code=a.promo_code AND b.create_time >= '".$i."' AND b.create_time<'".($i+3600*24)."' ".$where)[0]['user'],
                'open' => Db::query("SELECT count(*) open FROM hg_member_open 
                                    WHERE member_id IN (SELECT b.id FROM hg_app_promo a INNER JOIN hg_member_info b ON b.reg_promo_code = a.promo_code AND b.create_time >= '".$i."' AND b.create_time<'".($i+3600*24)."' ".$where.") AND create_time >= '".$i."' AND create_time<'".($i+3600*24)."'")[0]['open'],
            ];
            $list[] = $data;
        }

        $this->assign([
            'title' => '推广统计',
            'current_url' => '/admin/app_promo/report',
            'today' => $today,
            'list' => $list,
            'condition' => $condition,
        ]);
        return view();
    }
}