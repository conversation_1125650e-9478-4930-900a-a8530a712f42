<?php
namespace app\admin\controller;

use think\Db;
class AppQuestions extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_questions",
            'title' => "常见问题",
            'current_url' => "/admin/app_questions",
        ];
        $this->where = [
            'title' => 'like',
            'status' => '',
        ];
        $this->order = 'sort desc';
        $this->class_list = Db::name('app_level')->field('id,title')->order('id desc')->select();
        parent::__construct($conf);
    }
}