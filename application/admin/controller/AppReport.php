<?php

namespace app\admin\controller;

use think\Db;
use think\Request;

class AppReport extends BaseController
{
    public $title, $current_url;

    public function initialize()
    {
        parent::initialize();
        $this->title = "报表";
        $this->current_url = "/admin/app_report";
    }

    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index(Request $request)
    {
        $request = $_REQUEST;
        $export_excel = $request['is_export']??'';
        $status = $request['status']??1;
//        if ($request->isPost()) {
//            $status = $request->status;
//        }
        $res = [];
        $list = [];
        $pid = Db::name('sys_config')->where('id', '=', '1')->find()['show_number'] ?: 1;
        if ($status == 2) {
            $create_time = $request['create_time'];
            $update_time = $request['update_time'];
            if ($create_time && $update_time) {
                $t = floor((strtotime($update_time) - strtotime($create_time)) / 86400);
                if ($t > 30) {
                    echo '<h4 data-filtered="red"><a href="">报表显示筛选时间段内的所有相关数据，最少1天，最多30天；</a></h4>';
                    exit;
                }
                $list = Db::query("  SELECT  create_date,
                movie_num movie,
                user_num `user`,
                visitor_num visitor,
                history_num history,
                cache_num `cache`,
                up_num up,
                like_num `like`,
                comment_num `comment`,
                adv_num adv,
                promo_num promo,
                login_num,
                open_num `open`,
                qrcode_num qrcode,
                feedback_num feedback,
                ip,pv,uv,pay_first,pay_member,pay_amount,pay_vip,pay_vip_amount,pay_movie,pay_movie_amount,video,video_share,video_like,video_comment,video_play,type_num1,type_num2,type_num3   FROM  hg_site_day
  WHERE create_date >= '{$create_time}'  AND create_date  <=  '{$update_time}' ORDER BY create_date DESC LIMIT 30");
//                print_r(db::getlastsql());
            } else {
                echo '<h4 data-filtered="red"><a href="">请选择查询时间</a></h4>';
                exit;
                $list = Db::query("  SELECT  create_date,
                movie_num movie,
                user_num `user`,
                visitor_num visitor,
                history_num history,
                cache_num `cache`,
                up_num up,
                like_num `like`,
                comment_num `comment`,
                adv_num adv,
                promo_num promo,
                login_num,
                open_num `open`,
                qrcode_num qrcode,
                feedback_num feedback,
                ip,pv,uv,pay_first,pay_member,pay_amount,pay_vip,pay_vip_amount,pay_movie,pay_movie_amount,video,video_share,video_like,video_comment,video_play,type_num1,type_num2,type_num3   FROM  hg_site_day  ORDER BY create_date DESC LIMIT 30");
            }
            $res['all_mov'] = Db::name('app_movie')->count();   //电影总数

            $member_info_all = Db::query("SELECT count('id') as `count` ,is_visitor  FROM hg_member_info group by is_visitor");
            $res['all_user']=$res['all_visitor']=0;
            foreach ($member_info_all as $k=>$v){
                if($v['is_visitor']==1)$res['all_visitor']=$v['count'];
                if($v['is_visitor']==2)$res['all_user']=$v['count'];
            }

            $res['all_history'] = Db::name('member_history')->count()+sysconf('member_history_all'); //用户播放记录总数

            $star = strtotime(date('Y-m-d 00:00:00', time())); //今日开始时间
            $end = strtotime(date('Y-m-d 23:59:59', time())); //今日结束时间

            $list1 = [];

            $list1['create_date'] = date('Y-m-d'); //今日年月日

            $list1['movie'] = Db::query("SELECT COUNT('id') as movie FROM hg_app_movie WHERE create_time>={$star} AND create_time<={$end}")['0']['movie']; //今日新增影片

            $list1['user']=$list1['visitor']=0;
            $member_info_detail = Db::query("SELECT count('id') as `count` ,is_visitor  FROM hg_member_info WHERE create_time>={$star} AND create_time<={$end} group by is_visitor");
            foreach ($member_info_detail as $k=>$v){
                if($v['is_visitor']==1)$list1['visitor']=$v['count'];
                if($v['is_visitor']==2)$list1['user']=$v['count'];
            }

            $list1['history'] = Db::query("SELECT count('id') as `history` FROM hg_member_history WHERE create_time>={$star} AND create_time<={$end}")['0']['history']; //今日用户播放记录数


            $list1['cache'] = Db::query("SELECT count('id') as `cache` FROM hg_member_cache WHERE create_time>={$star} AND create_time<={$end}")['0']['cache']; //今日用户缓存数

            $list1['up'] = Db::query("SELECT count('id') as `up` FROM hg_member_movie_up_down WHERE create_time>={$star} AND create_time<={$end}")['0']['up']; //用户点赞数
            $list1['like'] = Db::query("SELECT count('id') as `like` FROM hg_member_like WHERE create_time>={$star} AND create_time<={$end}")['0']['like']; //用户喜欢数

            $list1['comment'] = Db::query("SELECT count('id') `comment` FROM hg_member_comment WHERE create_time>={$star} AND create_time<={$end}")['0']['comment']; //用户评论数
            $list1['adv'] = Db::query("SELECT count('id') as `adv` FROM hg_member_click_adv WHERE create_time>={$star} AND create_time<={$end}")['0']['adv']; //

            $list1['promo'] = Db::query("SELECT count('id') `promo` FROM hg_member_promo WHERE create_time>={$star} AND create_time<={$end}")['0']['promo'];
            $list1['open'] = Db::query("SELECT count('id') `open` FROM hg_member_open WHERE create_time>={$star} AND create_time<={$end}")['0']['open'];
            // $list1['login_num'] = Db::query("SELECT count(distinct member_id) as total FROM hg_member_open WHERE create_time>={$star} AND create_time<={$end}")['0']['total']; //今日用户
            $list1['login_num'] = 0;
            // $list1['device_num'] = Db::query("SELECT count(distinct device_id) as total FROM hg_member_open WHERE create_time>={$star} AND create_time<={$end}")['0']['total']; //今日设备


            $list1['type_num1']=$list1['type_num2']=$list1['type_num3']=0;
            $hg_site_log_one_detail = Db::query("SELECT count(DISTINCT member_id) as total ,type FROM hg_site_log_one WHERE create_time>={$star} AND create_time<={$end}  GROUP BY type"); //今日游客
            foreach ($hg_site_log_one_detail as $v){
                if($v['type']==1)$list1['type_num1']=$v['total'];
                if($v['type']==2)$list1['type_num2']=$v['total'];
                if($v['type']==3)$list1['type_num3']=$v['total'];
            }

            $list1['qrcode'] = Db::query("SELECT count('id') `qrcode` FROM hg_member_info WHERE save_qrcode_time>={$star} AND save_qrcode_time<={$end}")['0']['qrcode'];
            $list1['feedback'] = Db::query("SELECT count('id') `feedback` FROM hg_member_feedback WHERE create_time>={$star} AND create_time<={$end}")['0']['feedback'];

            // $list1['pv'] = Db::query("SELECT count('id') as `pv` FROM hg_site_log WHERE create_time>={$star} AND create_time<={$end}")['0']['pv'];
            $list1['pv'] = redis()->hGet('site_log_pv', date('Y-m-d'));
            if (date('Y-m-d') == '2021-01-21') {
                $list1['pv'] += 6170000;
            }

            $site_log_one_count_detail = Db::query("SELECT count(DISTINCT create_ip) as `ip` ,  count(DISTINCT device_id) as `uv`  FROM hg_site_log_one WHERE create_time >= {$star} AND create_time <= {$end}")[0]??[];
            $list1['ip']=$site_log_one_count_detail['ip']??0;
            $list1['uv']=$site_log_one_count_detail['uv']??0;


            $app_pay_deal_count_detail = Db::query("SELECT COUNT(DISTINCT member_id) as `member_id_count` , sum(amount) as `amount_count` , sum(if(is_first=1,1,0)) as `first_count` from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} AND type = 1")[0];
            $list1['pay_first']=$app_pay_deal_count_detail['first_count']??0; //今日首冲人数
            $list1['pay_member']=$app_pay_deal_count_detail['member_id_count']??0; //今日充值人数
            $list1['pay_amount']=!empty($app_pay_deal_count_detail['amount_count'])?$app_pay_deal_count_detail['amount_count']/100:0; //今日充值总额


            $list1['pay_vip']=$list1['pay_vip_amount']=$list1['pay_movie']=$list1['pay_movie_amount']=0;
            $app_pay_deal_detail = Db::query("SELECT COUNT(DISTINCT member_id) as `member_id_count` ,   sum(amount) as `amount_count` ,`type` , `sub_type` from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} GROUP BY `type` , `sub_type` ");
            foreach ($app_pay_deal_detail as $v){
                //今日购买vip人数与总额
                if($v['type']==3 and $v['sub_type']==1){
                    $list1['pay_vip'] = $v['member_id_count'];
                    $list1['pay_vip_amount'] = -($v['amount_count']??0)/100;
                }

                //今日购买视频与总额
                if($v['type']==3 and $v['sub_type']==2){
                    $list1['pay_movie'] = $v['member_id_count'];
                    $list1['pay_movie_amount'] = -($v['amount_count']??0)/100;
                }

            }


            //今日新增短视频
            $newvideo = Db::connect('short_video')->query("SELECT COUNT('id') as video FROM xg_video_pool WHERE to_days(create_time) >= to_days(curdate())")[0]['video'];
            $list1['video'] = $newvideo + Db::query("SELECT COUNT('id') as video FROM hg_video_clip WHERE create_time>={$star} AND create_time<={$end}")['0']['video']; //今日新增短视频

            //短视频分享
            $newvideo_share = Db::connect('short_video')->query("SELECT COUNT('id') as video_share FROM xg_share_video_list WHERE to_days(create_time) >= to_days(curdate())")[0]['video_share'];
            $list1['video_share'] = $newvideo_share + Db::query("SELECT COUNT('id') as video_share FROM hg_video_clip_share WHERE create_time>={$star} AND create_time<={$end}")['0']['video_share'];

            //短视频点赞
            $newvideo_like = Db::connect('short_video')->query("SELECT COUNT('id') as video_like FROM xg_video_like WHERE to_days(create_time) >= to_days(curdate())")[0]['video_like'];
            $list1['video_like'] = $newvideo_like + Db::query("SELECT COUNT('id') as video_like FROM hg_video_clip_like WHERE create_time>={$star} AND create_time<={$end} AND like_type = 1 AND cancel_time = 100")['0']['video_like'];

            //短视频评论
            $newvideo_comment = Db::connect('short_video')->query("SELECT COUNT('id') as video_comment FROM xg_video_comment WHERE to_days(create_time) >= to_days(curdate())")[0]['video_comment'];
            $list1['video_comment'] = $newvideo_comment + Db::query("SELECT COUNT('id') as video_comment FROM hg_video_clip_comment WHERE create_time>={$star} AND create_time<={$end} ")['0']['video_comment'];
            //短视频播放
            $newvideo_play = Db::connect('short_video')->query("SELECT COUNT('id') as video_play FROM xg_video_record WHERE to_days(create_time) >= to_days(curdate())")[0]['video_play'];
            $list1['video_play'] = $newvideo_play + Db::query("SELECT COUNT('id') as video_play FROM hg_video_clip_play WHERE create_time>={$star} AND create_time<={$end} ")['0']['video_play'];

            $list[] = $list1;
            array_unshift($list, $list1);
            $list = array_unique($list, SORT_REGULAR);

            if($export_excel==1){
                $this->export_excel($list);
            }

        }
        foreach ($list as $key => &$value) {
            if ($value['type_num1'] == 0) {
                $value['type_num1'] = $value['login_num'];
            }
        }
        $this->assign([
            'res' => $res,
            'list' => $list,
            'status' => $status,
            'condition' => $request,
            'current_url' => '/admin/app_report'
        ]);
        $this->_assign();
        return view();
    }


    public function export_excel($data){

        $export_data = [];
        foreach ($data as $k=>$v){
            $export_data[]=[
                '日期'=>[
                    '日期'=>$v['create_date'],
                ],
                '新增电影'=>[
                    '长'=>$v['movie'],
                    '短'=>$v['video'],
                ],
                '用户'=>[
                    '新游客'=>$v['visitor'],
                    '新注册'=>$v['user'],
                    '活跃游客'=>$v['type_num1'],
                    '活跃普通用户'=>$v['type_num2'],
                    '活跃会员'=>$v['type_num3'],
                ],
                '长视频'=>[
                    '播放'=>$v['history'],
                    '缓存'=>$v['cache'],
                    '点赞'=>$v['up'],
                    '评论'=>$v['comment'],
                ],
                '短视频'=>[
                    '播放'=>$v['video_play'],
                    '分享'=>$v['video_share'],
                    '点赞'=>$v['video_like'],
                    '评论'=>$v['video_comment'],
                ],
                '充值'=>[
                    '首冲人数'=>$v['pay_first'],
                    '充值人数'=>$v['pay_member'],
                    '充值总额'=>$v['pay_amount'],
                    'VIP购买人数'=>$v['pay_vip'],
                    'VIP购买总额'=>$v['pay_vip_amount'],
                    '视频购买人数'=>$v['pay_movie'],
                    '视频购买总额'=>$v['pay_movie_amount'],
                    '反馈'=>$v['feedback'],
                    '推广'=>$v['promo'],
                    '打开'=>$v['open'],
                ],
                '其他'=>[
                    '保存二维码'=>$v['qrcode'],
                    'IP'=>$v['ip'],
                    'PV'=>$v['pv'],
                    'UV'=>$v['uv'],
                ],

            ];
        }

        $title=[];
        foreach ($export_data['0']??[] as $k=>$v){

            foreach ($v as $k1=>$v1){

                $title[]=$k1;
            }

        }

        $filename = '统计报表';
        $this->excel_data_index($filename,$title,$export_data);exit;
    }

    //点击广告月份数据
    public function click_month()
    {
        $type = [
            'banner' => '首页BANNER',
            'movie_middle' => '视频详情页',
            'ob_game_index' => '欧宝首页游戏',
            'ob_game_h5' => '欧宝H5点击',
        ];
        // $type = config('api.belong_area');
        // $type['ob_game_index'] => '欧宝首页游戏';
        $start = input("request.start", null, "trim");

        if (!$start) {
            $start = date('Y-m');
        }
        $nstart = date('Ym', strtotime($start));

        $data = Db::name('app_adv_click')->where('month', $nstart)->select();

        $this->assign([
            'data' => $data,
            "start" => $start,
            'current_url' => '/admin/app_report',
            'type' => $type,

        ]);

        $this->_assign();
        return view();
    }

    //视频分类
    public function category_month()
    {
        $cates = Db::name('app_category')->where('category_type', 'movie')->column('title', 'id');
        $start = input("request.start", null, "trim");

        if (!$start) {
            $start = date('Y-m');
        }
        $nstart = date('Ym', strtotime($start));

        $data = Db::name('app_category_click')->where('month', $nstart)->select();

        $this->assign([
            'data' => $data,
            "start" => $start,
            'current_url' => '/admin/app_report',
            'type' => $cates,

        ]);

        $this->_assign();
        return view();
    }
}

