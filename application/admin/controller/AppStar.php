<?php
namespace app\admin\controller;

use think\Db;
class AppStar extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_star",
            'title' => "女优管理",
            'current_url' => "/admin/app_star",
        ];
        $this->where = [
            'uname' => 'like',
            'category_id' => '',
            'status' => '',
            'product_ids' => 'exp',
        ];
        $this->class_list = Db::name('app_category')->field('id,title')->where([['category_type', '=', 'star'], ['status', '=', '1']])->order('sort desc')->select();
        $this->class_list2 = Db::name('app_nation')->field('id,title')->where('type',1)->where([['status', '=', '1']])->order('sort desc')->select();
        $this->order = 'id desc';
        $this->files = ['avatar'];
        parent::__construct($conf);
    }

    public function index()
    {
        $app_products = Db::name("app_products")->where('status',1)->field('id,title')->select();
        $this->assign('app_products',$app_products);
        $app_star_job = Db::name("app_star_job")->where('status',1)->field('id,title')->select();
        $this->assign('app_star_job',$app_star_job);
        return parent::index(); // TODO: Change the autogenerated stub
    }


    public function add(){
        if (!request()->isPost()) {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if(isset($v['type']) && $v['type'] == 'class_list'){
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => '',
                            'role' => $v,
                        ];
                    }
                }
            }
            $app_star_job = Db::name("app_star_job")->where('status',1)->select();
            $this->assign([
                'res' => $arr,
                'app_star_job'=>$app_star_job,
                'app_category'=>$this->class_list,
                'app_nation'=>$this->class_list2,
            ]);
            return view();
        } else {
            $post = input("post.");
                    
            $post['uname'] = trim($post['uname']);
            if (!$post['uname']) {
                json_fail_admin("女优名称不能为空");
            }
            $restar = Db::name("app_star")->where('uname',$post['uname'])->find();
            if($restar){
                json_fail_admin("女优名称不能重复");
            }
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        $res = preg_match($v['rule'],$post[$k]);
                        if(!$res) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }
//             if (!empty($this->files)) {
//                 $file = $info = [];
//                 foreach ($this->files as $v) {
//                     $file[$v] = request()->file("file_{$v}");
//                 }
//                 foreach ($this->files as $v) {
//                     if (!empty($file[$v])) {
//                         $fname = $file[$v]->getPathName();
//                         if(!empty($fname)){
//                             $imagewh = getimagesize($fname);
//                             if($imagewh[0]/$imagewh[1]==1){
//                                 $info[$v] = $file[$v]->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png,gif'])->move(UPLOAD_PATH . UPLOAD_NAME . '/' . $this->name);
//                                 if (!$info[$v]) json_fail_admin($file[$v]->getError());
//                                 $post[$v] = UPLOAD_NAME . '/' . $this->name . '/' . $info[$v]->getSaveName();
//                             }else{
//                                 json_fail_admin("请选择上传宽高比一样的图片");
//                             }
// //                            $post[$v] = '';
//                         }
//                     }
//                     // if (!$file[$v]) json_fail_admin("请选择上传{$v}的文件");
//                     unset($post["file_{$v}"]);
//                 }
//             }

            $post['create_time'] = $post['create_time']?strtotime($post['create_time']):strtotime(date("Y-m-d"));
            $post['create_user'] = $this->user['id'];
            if (empty($post['products_ids'])) {
                $post['product_ids'] = '';
            } else {
                $post['product_ids'] = implode(',', $post['products_ids']);
                Db::name('app_products')->where('id', 'in', $post['product_ids'])->setInc('actors');
            }
            unset($post['products_ids']);
                    
            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            Db::name('app_nation')->where('id', 'in', $post['nation_id'])->setInc('star_num');
            Db::name('app_star_job')->where('id', 'in', $post['job_id'])->setInc('star_num');

            $this->addlog('添加女星','女星ID:'.$res);

            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input('post.');
            $post['uname'] = trim($post['uname']);
            $restar = Db::name("app_star")->where('id','<>',$id)->where('uname',$post['uname'])->find();
            if($restar){
                json_fail_admin("女优名称不能重复");
            }
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if (empty($post[$k])) json_fail_admin($v);
                }
            }

            $post['create_time'] = $post['create_time']?strtotime($post['create_time']):strtotime(date("Y-m-d"));

            $post['update_time'] = time();
            $post['update_user'] = $this->user['id'];
            Db::name('app_nation')->where('id', 'in', trim($res['nation_id'], "'"))->setDec('star_num');
            Db::name('app_nation')->where('id', 'in', $post['nation_id'])->setInc('star_num');
            Db::name('app_star_job')->where('id', 'in', trim($res['job_id'], "'"))->setDec('star_num');
            Db::name('app_star_job')->where('id', 'in', $post['job_id'])->setInc('star_num');
            if (empty($post['products_ids'])) {
                $post['product_ids'] = '';
            } else {
                $post['product_ids'] = implode(',', $post['products_ids']);
                Db::name('app_products')->where('id', 'in', trim($res['product_ids'], "'"))->setDec('actors');
                Db::name('app_products')->where('id', 'in', $post['product_ids'])->setInc('actors');
            }
            unset($post['products_ids']);


            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);
            $this->addlog('修改女星','女星ID:'.$id);

            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if (isset($v['type']) && $v['type'] == 'class_list') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => '分类',
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => $v,
                        ];
                    }
                }
            }
            $app_products = Db::name("app_products")->where('status',1)->field('id,title')->select();
            $app_star_job = Db::name("app_star_job")->where('status',1)->select();

            $this->assign([
                'res' => $res,
                'is_parent' => $this->is_parent,
                'app_category'=>$this->class_list,
                'app_nation'=>$this->class_list2,
                'app_products'=>$app_products,
                'app_star_job'=>$app_star_job,
            ]);
            return view();
        }
    }


    public function pics(){
        $star_id = input('param.star_id');
        if (request()->isPost()) {
            $post = input('post.');
            if(empty($post['hot_pic'])){
                json_fail_admin("至少1张图片");
            }
            $post['hot_pic'] = array_filter($post['hot_pic']);
            if(count($post['hot_pic']) < 1){
                json_fail_admin("至少1张图片");
            }
            if(!empty($post['hot_pic'])){
                
                        
                Db::name("app_star_pics")->where('star_id',$star_id)->delete();
                $padd = [];
                foreach ($post['hot_pic'] as $key=>$value){
                    $padd []= [
                        'pic_url' => $value,
                        'star_id'=>$star_id,
                        'create_time'=>date("Y-m-d H:i"),
                    ];
                }
                Db::name("app_star_pics")->insertAll($padd);

            }
            json_success_admin() ;

        }else{
            $app_star_pics = Db::name("app_star_pics")->where('star_id',$star_id)->field('star_id,pic_url')->select();
            for ($i=0;$i<8;$i++){
                $app_star_pics[$i] = empty($app_star_pics[$i])?[]:$app_star_pics[$i];
            
            }

            $this->assign([
                'app_star_pics'=>$app_star_pics,

            ]);
            return view();
        }
    }


    public function movies(){
        $star_id = input('param.star_id');
        if (request()->isPost()) {
            $post = input('post.');
            $master = $post['master'];
            if($master == 1|| $master == 3){
                Db::name("app_star_movies")->where('star_id',$star_id)->delete();
                unset($post['movie_ids']);
                unset($post['app_movie']);
                $res = Db::name($this->name)->where('id', '=', $star_id)->update($post);
                json_success_admin();
            }

            if(!empty($post['movie_ids'])){
                Db::name("app_star_movies")->where('star_id',$star_id)->delete();
                $count = count($post['movie_ids']);
                if($count > 5){
                    json_fail_admin("代表作不超过五部");
                }
                foreach ($post['movie_ids'] as $key=>$value){
                    if($value){
                        $padd['movie_id'] = $value;
                        $padd['star_id'] = $star_id;
                        Db::name("app_star_movies")->insertGetId($padd);
                    }
                }
                unset($post['movie_ids']);
                $res = Db::name($this->name)->where('id', '=', $star_id)->update($post);
                json_success_admin();
            }else{
                Db::name("app_star_movies")->where('star_id',$star_id)->delete();

                $res = Db::name($this->name)->where('id', '=', $star_id)->update($post);
                json_success_admin() ;
            }

        }else{
            $app_star = Db::name("app_star")->where('id',$star_id)->field('id,master')->find();
            $app_star_movies = Db::name("app_star_movies")->alias('sm')
                ->join('app_movie m','sm.movie_id = m.id')
                ->where('star_id',$star_id)
                ->field('star_id,movie_id,cover_vertical')
                ->select();
            foreach ($app_star_movies as $key=>$value){
                $app_star_movies[$key]['cover_vertical'] = get_host($value['cover_vertical']);
            }
            $condition[] = ['','exp', Db::raw("FIND_IN_SET('{$star_id}',star_ids)")];
            $app_movie = Db::name("app_movie")->where('status',1)->field('id,title')->select();
            for ($i=1;$i<5;$i++){
                $app_star_movies[$i] = empty($app_star_movies[$i])?[]:$app_star_movies[$i];
            }
            $this->assign([
                'app_star_movies'=>$app_star_movies,
                'app_star'=>$app_star,
                'app_movie'=>$app_movie,
            ]);
            return view();
        }
    }




    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        if (!empty($this->files)) {
            $files = [];
            foreach ($this->files as $v) {
                $files[] = $res[$v];
            }
            delPathList($files);
        }
        Db::name('app_nation')->where('id', 'in', trim($res['nation_id'], "'"))->setDec('star_num');
        Db::name('app_star_job')->where('id', 'in', trim($res['job_id'], "'"))->setDec('star_num');
        $res = Db::name($this->name)->delete($id);

        $this->addlog('删除女星','女星ID:'.$id);

        $res ? json_success_admin() : json_fail_admin();
    }

}