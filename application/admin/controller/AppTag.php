<?php
namespace app\admin\controller;

use think\Db;
class AppTag extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_tag",
            'title' => "标签管理",
            'current_url' => "/admin/app_tag",
        ];
        $this->where = [
            'title' => 'like',
            'category_id' => '',
            'is_hot' => '',
            'status' => '',
        ];
        $this->verify =
            [
                'title'=>[
                    'rule'=>'/^[\x{4e00}-\x{9fa5}A-Za-z0-9]{2,20}$/u',
                    'message'=>"标题只能是中文字符，数字，字母，长度2-20位",
                ]
            ];
        $this->class_list = Db::name('app_category')->field('id,title')->where([['category_type', '=', 'tag'], ['status', '=', '1']])->order('sort desc')->select();
        $this->order = 'id desc';
        parent::__construct($conf);
    }


    public function add()
    {
        if (request()->isPost()) {
            $post = input("post.");
            $post['title'] = trim($post['title']);
            $restar = Db::name("app_tag")->where('title',$post['title'])->find();
            if($restar){
                json_fail_admin("标签名称不能重复");
            }
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        $res = preg_match($v['rule'],$post[$k]);
                        if(!$res) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }
            if (!empty($this->files)) {
                $file = $info = [];
                foreach ($this->files as $v) {
                    $file[$v] = request()->file("file_{$v}");
                }
                foreach ($this->files as $v) {
                    if (!empty($file[$v])) {
                        $info[$v] = $file[$v]->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png,gif'])->move(UPLOAD_PATH . UPLOAD_NAME . '/' . $this->name);
                        if (!$info[$v]) json_fail_admin($file[$v]->getError());
                        $post[$v] = UPLOAD_NAME . '/' . $this->name . '/' . $info[$v]->getSaveName();
                    }
                    // if (!$file[$v]) json_fail_admin("请选择上传{$v}的文件");
                    unset($post["file_{$v}"]);
                }
            }
            $post['create_time'] = time();
            $post['create_user'] = $this->user['id'];
            $res = Db::name($this->name)->insert($post);
            $this->addlog('添加标签','标签:'.$post['title']);

            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if(isset($v['type']) && $v['type'] == 'class_list'){
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => '',
                            'role' => $v,
                        ];
                    }
                }
            }
            $this->assign([
                'res' => $arr,
            ]);
            return view('/common/add');
        }
    }
    public function edit()
    {
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input('post.');
            $post['title'] = trim($post['title']);
            $restar = Db::name("app_tag")->where('id','<>',$id)->where('title',$post['title'])->find();
            if($restar){
                json_fail_admin("标签名称不能重复");
            }
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if (empty($post[$k])) json_fail_admin($v);
                }
            }
            if (!empty($this->files)) {
                $file = $info = [];
                foreach ($this->files as $v) {
                    $file[$v] = request()->file("file_{$v}");
                }
                foreach ($this->files as $v) {
                    if (!empty($file[$v])){
                        $info[$v] = $file[$v]->validate(['size' => 10 * 1024 * 1024, 'ext' => 'jpg,jpeg,png,gif'])->move(UPLOAD_PATH . UPLOAD_NAME . '/' . $this->name);
                        if (!$info[$v]) json_fail_admin($file[$v]->getError());
                        delPathList([$res[$v]]);
                        $post[$v] = UPLOAD_NAME . '/' . $this->name . '/' . $info[$v]->getSaveName();
                    }
                    unset($post["file_{$v}"]);
                }
            }
            $post['update_time'] = time();
            $post['update_user'] = $this->user['id'];
            $res = Db::name($this->name)->where('id', '=', $id)->update($post);
            $this->addlog('修改标签','标签:'.$post['title']);
            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if (isset($v['type']) && $v['type'] == 'class_list') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => '分类',
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => $v,
                        ];
                    }
                }
            }
            $this->assign([
                'res' => $arr,
                'is_parent' => $this->is_parent,
            ]);
            return view('/common/edit');
        }
    }
}