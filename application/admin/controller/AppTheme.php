<?php
namespace app\admin\controller;

use think\Db;
class AppTheme extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_theme",
            'title' => "专题管理",
            'current_url' => "/admin/app_theme",
        ];
        $this->where = [
            'title' => 'like',
            'is_home' => '',
            'is_hot' => '',
            'is_top' => '',
            'status' => '',
        ];
        $this->order = 'sort desc';
        $this->files = ['icon','pic'];
        $this->verify = [
            'title' => '标题不能为空',
        ];
        parent::__construct($conf);
    }
}