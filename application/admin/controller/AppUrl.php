<?php
namespace app\admin\controller;

use think\Db;
use app\EasyAes;
class AppUrl extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "app_url",
            'title' => "动态域名管理",
            'current_url' => "/admin/app_url",
        ];
        $this->where = [
            'url'=>'like',
            // 'status'=>'',
            'type'=>'',
        ];
        $this->verify = [
          'url'=>[
              'rule' => "/^http(s)?:\\/\\/.+/",
              'message'=>'域名地址不正确'
          ],

        ];
        $this->class_list = config('api.app_url');

        parent::__construct($conf);
    }

    public function aesurl()
    {
        json_fail_admin('关闭此功能!');
//        $content = input('param.content');
//        $type = input('param.type');
//        if (!$content) {
//            json_fail_admin('内容为空!');
//        }
//        $aes = new EasyAes("eDWowITDNUKgND6PfQfA3OMTJ0nm640L", 128, "################");
//        $content = trim($content);
//        if ($type == 1) {
//            $newdata = $aes->encrypt($content);
//            json_success_admin('加密成功',['content'=>$newdata]);
//        }else{
//            $newdata = $aes->decrypt($content);
//            json_success_admin('解密成功',['content'=>$newdata]);
//        }

    }
}
