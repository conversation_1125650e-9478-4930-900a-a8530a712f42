<?php
namespace app\admin\controller;

use app\api\logic\chess\BaisonLogic;
use app\common\BizConst;
use app\Helper\StrHelper;
use app\Service\BaisonTransferService;
use think\Db;
use think\facade\Validate;

class BaisonBalanceApply extends CommonController
{
    /**
     * @var BaisonLogic $baisonLogic
     */
    protected $baisonLogic;

    public function __construct()
    {
        $conf=[
            'title' => '人工增减',
            'name'=>'baison_balance_apply',
            'current_url'=>'/admin/baison_balance_apply',
        ];
        parent::__construct($conf);
        $this->baisonLogic = \app(BaisonLogic::class);
    }

    public function index()
    {

        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['account'] = input('request.account');
        $condition['type'] = input('request.type');
        $condition['status'] = input('request.status');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['begin_review_time'] = input('request.begin_review_time');
        $condition['end_review_time'] = input('request.end_review_time');
        $condition['create_user_name'] = input('request.create_user_name');
        $condition['review_user_name'] = input('request.review_user_name');
        $condition['order_id'] = input('request.order_id');

        if (!empty($condition['member_id'])) $where[] = ['o.member_id','=', $condition['member_id']];
        if (!empty($condition['type'])) $where[] = ['o.type','=', $condition['type']];
        if (!empty($condition['order_id'])) $where[] = ['o.order_id','=', $condition['order_id']];
        if (!empty($condition['account'])) $where[] = ['o.account','LIKE', '%' .$condition['account'].'%' ];
        if (!empty($condition['begin_time'])) $where[] = ['o.create_time', '>=', $condition['begin_time'] . ' 00:00:00'];
        if (!empty($condition['end_time'])) $where[] = ['o.create_time', '<', $condition['end_time'] . ' 23:59:59'];
        if (!empty($condition['begin_review_time'])) $where[] = ['o.review_time', '>=', $condition['begin_review_time'] . ' 00:00:00'];
        if (!empty($condition['end_review_time'])) $where[] = ['o.review_time', '<', $condition['end_review_time'] . ' 23:59:59'];
        if (!empty($condition['status'])) {
            $where[] = ['o.status', '=',$condition['status']];
        }
        if (!empty($condition['create_user_name'])) $where[] = ['o.create_user_name','LIKE', '%' .$condition['create_user_name'].'%' ];
        if (!empty($condition['review_user_name'])) $where[] = ['o.review_user_name','LIKE', '%' .$condition['review_user_name'].'%' ];

        $list = Db::name("baison_balance_apply")->alias('o')->field('o.*')->whereNull("delete_time")->where($where)->order("o.id desc")
            ->paginate(20)->each(function($item, $key){
                $item['is_edit'] = 1;
                if($item['create_user_id']!=$this->user['id'] || $item['status']!=\app\Service\BaisonBalanceApply::STATUS_WAIT[0] ){
                    $item['is_edit']  = 0;
                }

                $item['type_name']=array_column(config('api.account_change_type'),null,'id')[$item['type']]['title']??'';
                $item['status_name']=\app\Service\BaisonBalanceApply::getConstPluck('STATUS_')[$item['status']]??'';
                $item['amount']=round($item['amount']/100,2);
                $item['before_amount']=round($item['before_amount']/100,2);
                $item['after_amount']=round($item['after_amount']/100,2);

                $item['create_user_remark_short'] = mb_substr($item['create_user_remark'],0,30).(strlen($item['create_user_remark'])>30?'.....':'');
                $item['review_user_remark_short'] = mb_substr($item['review_user_remark'],0,30).(strlen($item['review_user_remark'])>30?'.....':'');
                return $item;
            });
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'status_list' => \app\Service\BaisonBalanceApply::getConstSelect("STATUS_",'id','title'),
        ]);

        return view();
    }



    public function add(){
        if (!request()->isPost()) {
            $this->_assign();
            return view();
        } else {
            $inputs = $post = input("post.");

            $v = Validate::make([
                'account' => 'require',
                'type' => 'require',
                'amount' => 'require|number|min:1',
                'create_user_remark' => 'require|max:800',
            ], [
                'title.require' => '用户账号必填',
                'type.require' => '调整类型必填',
                'amount.require' => '调整金额必填',
                'amount.number' => '调整金额必须是整数',
                'amount.min' => '调整金额必须大于1',
                'create_user_remark.require' => '备注必填',
                'create_user_remark.max' => '备注最长800个字符',
            ]);

            if (!$v->check($inputs)) {
                json_fail_admin($v->getError());
            }

            $detail_member_info = DB::name('member_info')->where(['account'=>$post['account']])->find();
            if(empty($detail_member_info) || $detail_member_info['account']!=$post['account']){
                json_fail_admin('用户不存在');
            }

            $game_balance = $this->get_game_balance($post['account'],1);

            $post['member_id']=$detail_member_info['id'];
            $post['create_user_id']=$this->user['id'];
            $post['create_user_name']=$this->user['username'];
            $post['amount']=$post['amount']*100;
            $post['before_amount']=$game_balance['totalMoney']*100;
            $post['order_id']=env('ORDER_PREFIX','').StrHelper::sfOrderId();

            switch ($post['type']){
                case \app\Service\BaisonBalanceApply::TYPE_ADD[0]:
                    $post['after_amount']=$post['before_amount']+$post['amount'];
                    break;
                case \app\Service\BaisonBalanceApply::TYPE_REDUCE[0]:
                    $post['after_amount']=$game_balance['freeMoney']*100-$post['amount'];
                    if($post['after_amount']<0){
                        json_fail_admin('调整后的金额必须大于0');
                    }
                    break;
            }

            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            $this->addlog('添加人工增减','人工增减ID:'.$res.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        }
    }


    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->whereNull('delete_time')->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {

            if($res['status']!=\app\Service\BaisonBalanceApply::STATUS_WAIT[0] || $res['create_user_id']!=$this->user['id'] ){
                json_fail_admin('只有状态审核中的才能编辑，且只能发起人编辑');
            }

            $inputs = $post = input("post.");

            $v = Validate::make([
                'type' => 'require',
                'amount' => 'require|number|min',
                'create_user_remark' => 'require|max:800',
            ], [
                'title.require' => '用户账号必填',
                'type.require' => '调整类型必填',
                'amount.require' => '调整金额必填',
                'amount.number' => '调整金额必须是整数',
                'amount.min' => '调整金额必须大于0',
                'create_user_remark.require' => '备注必填',
                'create_user_remark.max' => '备注最长800个字符',
            ]);

            if (!$v->check($inputs)) {
                json_fail_admin($v->getError());
            }

            $update_data=[
                'type'=>$post['type'],
                'amount'=>$post['amount']*100,
                'create_user_remark'=>$post['create_user_remark'],
            ];

            $game_balance = $this->get_game_balance($res['account'],1);

            $update_data['before_amount']=$game_balance['totalMoney']*100;

            switch ($post['type']){
                case \app\Service\BaisonBalanceApply::TYPE_ADD[0]:
                    $update_data['after_amount']=$update_data['before_amount']+$update_data['amount'];
                    break;
                case \app\Service\BaisonBalanceApply::TYPE_REDUCE[0]:
                    $update_data['after_amount']=$game_balance['freeMoney']*100-$update_data['amount'];
                    if($update_data['after_amount']<0){
                        json_fail_admin('调整后的金额必须大于0');
                    }
                    break;
            }

            Db::name($this->name)->where('id', '=', $id)->whereNull('delete_time')->strict(false)->update($update_data);

            $this->addlog('修改人工增减','人工增减ID:'.$id.',post_data:'.json_encode($update_data,JSON_UNESCAPED_UNICODE));

            json_success_admin();
        } else {
            $res['amount']=$res['amount']/100;
            $this->_assign();
            $this->assign([
                'res' => $res,
            ]);
            return view();
        }
    }


    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->whereNull('delete_time')->find();
        if (empty($res)) json_fail_admin('数据不存在');

        if($res['status']!=\app\Service\BaisonBalanceApply::STATUS_WAIT[0] || $res['create_user_id']!=$this->user['id'] ){
            json_fail_admin('只有状态审核中的才能删除，且只能发起人删除');
        }

        $res =  Db::name($this->name)->where('id', '=', $id)->update(['delete_time'=>date('Y-m-d H:i:s')]);

        $this->addlog('删除人工增减','id:'.$id);

        $res ? json_success_admin() : json_fail_admin();
    }


    /**
     * 获取指定用户游戏余额
     * $inter_type 内部调用类型（这里保持兼容旧代码，controller当内部调用方法） 0非内部调用 1内部调用
     */
    public function get_game_balance(string $account='',int $inter_type=0){
        if(empty($account))$account = input('param.account');

        if(empty($account))json_success_admin('success',['freeMoney' => 0, 'online' => 0, 'totalMoney' => 0]);

        $detail_member_info = DB::name('member_info')->where(['account'=>$account])->find();
        if(empty($detail_member_info) || $detail_member_info['account']!=$account){
            json_fail_admin('用户不存在');
        }


        try {
            $data =  $this->baisonLogic->setAccount($account)->sendRequestToBaison(['account' => $this->baisonLogic->getAccount()], BaisonLogic::QUERY_ALL_GAME_BALANCE);
            if(!isset($data['code']) || $data['code']!=0){
                json_fail_admin('请求异常：'.is_string($data)?$data:json_encode($data,JSON_UNESCAPED_UNICODE));
            }

//            dump([$inter_type, $data['data']]);die();
            if($inter_type==1){
                return  $data['data'];
            }
            json_success_admin('success',$data['data']);
        }catch (\Throwable $e){
            json_fail_admin("请求异常：".$e->getMessage().$e->getFile().$e->getLine());
        }

    }


    /**
     * 审核
     * @param $id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function review($id)
    {
        $inputs = $post = input("post.");

        $v = Validate::make([
            'status' => 'require',
        ], [
            'status.require' => '审核状态必选',
            'review_user_remark.max' => '备注最长800个字符',
        ]);

        if (!$v->check($inputs)) {
            json_fail_admin($v->getError());
        }

        $update_data=[
            'status'=>$post['status'],
            'review_user_remark'=>$post['review_user_remark']??'',
            'review_user_id'=>$this->user['id'],
            'review_user_name'=>$this->user['username'],
            'review_time'=>date('Y-m-d H:i:s'),
        ];

        Db::startTrans();
        try {

            $detail = Db::name($this->name)->where('id', '=', $id)->whereNull('delete_time')->find();
            if(empty($detail)){
                json_fail_admin("审核记录不存在");
            }
            if($detail['status']!=\app\Service\BaisonBalanceApply::STATUS_WAIT[0]){
                json_fail_admin("只有状态审核中的记录才能审核");
            }

            Db::name($this->name)->where('id', '=', $id)->strict(false)->update($update_data);
            if($post['status']==\app\Service\BaisonBalanceApply::STATUS_AGREE[0]){

                switch ($detail['type']){
                    case \app\Service\BaisonBalanceApply::TYPE_ADD[0]:
                        /** @var BaisonLogic $baisonLogic */
                        $baisonLogic = \app(BaisonLogic::class);
                        $baisonLogic->setAccount($detail['account']);

                        $newOrderId       = sysconf('baison_channel_id').StrHelper::sfOrderId();
                        $baisonAmountItem = Db::name('baison_amount')->where(['account' => $detail['account']])->limit(1)
                            ->lock()->find();
                        if (!$baisonAmountItem) {
                            Db::name('baison_amount')->insert([
                                'order_id' => $newOrderId, 'account' => $detail['account'],
                                'amount'   => $detail['amount'],
                            ]);
                        } else {
                            $arr = $baisonLogic->remoteOrderStatus($baisonAmountItem['order_id']);
                            if ((int) $arr['status'] === 2) {
                                Db::name('baison_amount')->where(['account' => $detail['account']])->update([
                                    'order_id' => $newOrderId, 'amount' => $detail['amount'],
                                ]);
                            } else {
                                $r = $baisonLogic->signWithDeposit(BizConst::BAISON_PLATFORM_ADD_SUCCESS);
                                if (isset($r['code']) && (int) $r['code'] === 0) {
                                    Db::name('baison_amount')->where(['account' => $detail['account']])
                                        ->update(['order_id' => $newOrderId, 'amount' => $detail['amount'],]);
                                }
                            }
                        }

                        $baisonLogic->signWithDeposit(BizConst::BAISON_PLATFORM_ADD_SUCCESS);
                        break;
                    case \app\Service\BaisonBalanceApply::TYPE_REDUCE[0]:
                        $newOrderId       = sysconf('baison_channel_id').StrHelper::sfOrderId();
                        (new BaisonTransferService())->add([
                            'order_id'            => $newOrderId,
                            'type'                => BizConst::TYPE_TRANSFER_BAISON_WITHDRAWAL,
                            'status'              => BizConst::BAISON_PLATFORM_REDUCE,
                            'account'             =>$detail['account'],
                            'ask_amount'          => $detail['amount'],
                            'actual_amount'          => $detail['amount'],
                            'bank_name'           => '',
                            'bank_no'             => '',
                            'bank_branch'         => '',
                            'real_name'           => '',
                            'remote_free_amount'  => 0,
                            'remote_total_amount' => 0,
                        ]);

                        $withdrawalAmount = $this->baisonLogic->setAccount($detail['account'])->remotePlayerWithdrawal($detail['amount']/100);

                        logInfo(['info' => 'baison_review', 'data' => $withdrawalAmount,'detail'=>$detail],
                            __METHOD__,
                            '百胜下分请求日志',
                            'baison_review-req');

                        if (!isset($withdrawalAmount['subMoney']) || (int)($withdrawalAmount['subMoney']*100) != intval($detail['amount'])) {
                            json_fail_admin("请求异常：".is_string($withdrawalAmount)?$withdrawalAmount:json_encode($withdrawalAmount,JSON_UNESCAPED_UNICODE));
                        }

                        break;
                }

                //事务先提交，不要因为其他小事情影响到上下分数据
                Db::commit();

                $game_balance = $this->get_game_balance($detail['account'],1);
                Db::name($this->name)->where('id', '=', $id)->strict(false)->update(['after_amount'=>$game_balance['totalMoney']*100]);

            }else{
                Db::commit();
            }


        }catch (\Throwable $e){
            Db::rollback();
            logInfo(['info' => 'baison_review', 'error' => $e->getMessage().$e->getFile().$e->getLine()],
                __METHOD__,
                '百胜上下分请求失败日志',
                'baison_review-req');

            json_fail_admin("请求异常：".$e->getMessage().$e->getFile().$e->getLine());
        }

        $this->addlog('修改人工增减','人工增减ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

        json_success_admin();
    }

}