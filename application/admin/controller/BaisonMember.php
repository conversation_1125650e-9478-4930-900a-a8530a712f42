<?php
namespace app\admin\controller;

use app\api\logic\chess\BaisonLogic;
use app\common\BizConst;
use app\Helper\StrHelper;
use app\Service\BaisonMemberService;
use app\Service\BaisonTransferService;
use think\Db;
use think\facade\Validate;

class BaisonMember extends CommonController
{
    /**
     * @var BaisonLogic $baisonLogic
     */
    protected $baisonLogic;

    public function __construct()
    {
        $conf=[
            'title' => '百胜游戏用户',
            'name'=>'baison_member',
            'current_url'=>'/admin/baison_member',
        ];
        parent::__construct($conf);
        $this->baisonLogic = \app(BaisonLogic::class);
    }

    public function index()
    {

        $where = [];
        $condition['id'] = input('request.id');
        $condition['member_id'] = input('request.member_id');
        $condition['account'] = trim(input('request.account'));
        $condition['mobile'] = trim(input('request.mobile'));
        $condition['channelid'] = input('request.channelid');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['baison_group'] = input('request.baison_group');

        if (!empty($condition['baison_group'])) $where[] = ['o.baison_group','=', $condition['baison_group']];
        if (!empty($condition['id'])) $where[] = ['o.id','=', $condition['id']];
        if (!empty($condition['member_id'])) $where[] = ['o.member_id','=', $condition['member_id']];
        if (!empty($condition['account'])) $where[] = ['o.account','=', $condition['account']];
        if (!empty($condition['channelid'])) $where[] = ['o.channelid','=', $condition['channelid']];
        if (!empty($condition['begin_time'])) $where[] = ['o.create_time', '>=', $condition['begin_time'] . ' 00:00:00'];
        if (!empty($condition['end_time'])) $where[] = ['o.create_time', '<', $condition['end_time'] . ' 23:59:59'];
        if (!empty($condition['mobile'])) {
            $member_info_detail = Db::name('member_info')->where(['mobile'=>$condition['mobile']])->field('id')->find();
            if(!empty($member_info_detail)){
                $where[] = ['o.member_id', 'IN',[$member_info_detail['id']]];
            }else{
                $where[] = ['o.member_id', 'IN',[0]];
            }
        }

        $list = Db::name("baison_member")->alias('o')->field('o.*')->where($where)->order("o.id desc")
            ->paginate(20)->each(function($item, $key){
                return $item;
            });
        $page = $list->render();
        $list = $list->toArray();
        if(!empty($list['data'])){
            $member_ids = array_column($list['data'],'member_id');
            $member_info_list = Db::name('member_info')->whereIn('id',$member_ids)->field('id,device_id,mobile')->select();
            $member_info_column = array_column($member_info_list,null,'id');

            $member_accounts = array_column($list['data'],'account');
            $bank_card_list = Db::name('bank_card')->whereIn('account',$member_accounts)->order(['created_at'=>'desc'])->select();
            $bank_card_column=[];
            foreach ($bank_card_list as $k=>$v){
                $bank_card_column[$v['account']][]=$v;
            }
            foreach ($list['data'] as $k=>&$v){
                $v['mobile'] = $member_info_column[$v['member_id']]['mobile']??'';
                $v['device_id'] = $member_info_column[$v['member_id']]['device_id']??'';
                $v['bank_card_list'] = $bank_card_column[$v['account']]??[];
                if(empty($v['bank_card_list'])){
                    $v['bank_card_list']=[[
                        'bank_name'=>'',
                        'bank_no'=>'',
                        'bank_branch'=>'',
                    ]];
                }
            }
        }

//        dump($list['data']);die();
        $group_list = Db::name('baison_bet_active')->field("id,name")->select();
        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'group_list'=>$group_list,
        ]);

        return view();
    }

    public function details($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');

        $member_info_detail = Db::name('member_info')->where(['id'=>$res['member_id']])->find();
        $res['mobile']=$member_info_detail['mobile'];
        $res['device_id']=$member_info_detail['device_id'];

        $res['bank_card_list'] = Db::name('bank_card')->where(['account'=>$res['account']])->select();
        $group_list = Db::name('baison_bet_active')->field("id,name")->select();

        $this->_assign();
        $this->assign([
            'res' => $res,
            'group_list'=>$group_list,
        ]);

        return view();
    }


    public function is_warning($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $is_warning = ($res['is_warning']==BaisonMemberService::IS_WARNING_NO[0]?BaisonMemberService::IS_WARNING_YES[0]:BaisonMemberService::IS_WARNING_NO[0]);

        $res =  Db::name($this->name)->where('id', '=', $id)->update(['is_warning'=>$is_warning]);

        $this->addlog(($is_warning == BaisonMemberService::IS_WARNING_NO[0] ? '' : '取消') . '标记用户为警示','id:'.$id);

        $res ? json_success_admin() : json_fail_admin();
    }



    /**
     * 添加备注
     * @param $id
     * @return void
     * @throws \think\Exception
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     * @throws \think\exception\PDOException
     */
    public function add_remark($id)
    {
        $inputs = $post = input("post.");

        $v = Validate::make([
            'remark' => 'require',
        ], [
            'remark.require' => '备注内容必填',
        ]);

        if (!$v->check($inputs)) {
            json_fail_admin($v->getError());
        }

        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');

        Db::name($this->name)->where('id', '=', $id)->update(['remark'=>$post['remark']]);

        $this->addlog('游戏用户备注','id:'.$id.',备注内容：'.$post['remark']);

        json_success_admin();

    }

}
