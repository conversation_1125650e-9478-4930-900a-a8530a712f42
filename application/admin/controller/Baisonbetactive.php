<?php
namespace app\admin\controller;

use think\Db;
class Baisonbetactive extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '玩家分组',
            'name'=>'baisonbetactive',
            'current_url'=>'/admin/baisonbetactive',
        ];
        parent::__construct($conf);
    }

    //充值赠送活动列表
    public function index()
    {

        $where = [];
        $condition['min_order_price'] = input('request.min_order_price');
        $condition['max_order_price'] = input('request.max_order_price');
        $condition['status'] = input('request.status');
        if (!empty($condition['min_order_price'])) $where[] = ['min_order_price', '>=', $condition['min_order_price']];
        if (!empty($condition['max_order_price'])) $where[] = ['max_order_price', '<=', $condition['max_order_price']];
        if (!empty($condition['status'])) {
            $where[] = ['o.status', '=',$condition['status']];
        }
        $list = Db::name("baison_bet_active")->where($where)->paginate(20);
        foreach($list as $key=>$value){
            $temp = $list[$key];
            $temp['count']  = Db::name("baison_member")->where('baison_group', '=', $value['id'])->count();;
            $list[$key] = $temp;
        }
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
        ]);

        return view();
    }

    //前台活动说明配置
    public function editInfo()
    {
        $id = 1;
        $res = Db::name("baison_bet_active")->where('id', '=', 1)->find();
        if (request()->isPost()) {
            $post = input("post.");
            $data['extend'] = $post['extend'];
            $res = Db::name("baison_bet_active")->where('id', '=', $id)->update($data);

            $this->addlog('修改前台活动说明配置', '修改前台活动说明配置ID:' . $id . ',post_data:' . json_encode($post, JSON_UNESCAPED_UNICODE));
            if ($res !== false) {
                json_success_admin('修改成功');
            } else {
                json_fail_admin();
            }
        } else {
            $this->assign([
                'res' => $res,
            ]);
            return view();
        }
    }

    public function editInfo2()
    {
        $id = 1;
        $res = Db::name("baison_bet_active")->where('id', '=', 1)->find();
        if (request()->isPost()) {
            $post = input("post.");
            $data['info1'] = $post['info1'] ?? '';
            $data['info2'] = $post['info2'];
            $res = Db::name("baison_bet_active")->where('id', '=', $id)->update($data);

            $this->addlog('修改前台活动说明配置', '修改前台活动说明配置ID:' . $id . ',post_data:' . json_encode($post, JSON_UNESCAPED_UNICODE));
            if ($res !== false) {
                json_success_admin('修改成功');
            } else {
                json_fail_admin();
            }
        } else {
            $this->assign([
                'res' => $res,
            ]);
            return view();
        }
    }


    //添加分组
    public function add(){
        if (request()->isPost()) {
            $post = input("post.");
            $validate = new \think\Validate();
            $validate->rule([
                'name' => 'require',
                'remark' => 'require',
            ]);
            $validate->message([
                'name.require' => '组别不能为空',
                'remark.require' => '备注不能为空',
            ]);
            if (!$validate->check($post)) {
                json_fail_admin($validate->getError());
            }
            $data['name'] = $post['name'];
            $data['remark'] = $post['remark'];
            $data['create_by'] = $this->user['username'];
            $data['create_time'] = date('Y-m-d H:i:s',time());

            $res = Db::name("baison_bet_active")->insert($data);

            $this->addlog('添加分组','添加分组:,post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));
            if ($res !== false) {
                json_success_admin('添加成功');
            }else{
                json_fail_admin();
            }
        } else {
            return view();
        }
    }

    //编辑分组
    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name("baison_bet_active")->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin('记录不存在');
        if (request()->isPost()) {
            $post = input("post.");
            $validate = new \think\Validate();
            if($id == 1){
                $validate->rule([
                    'remark' => 'require',
                ]);
            }else{
                $validate->rule([
                    'name' => 'require',
                    'remark' => 'require',
                ]);
            }

            $validate->message([
                'name.require' => '组别不能为空',
                'remark.require' => '备注不能为空',
            ]);
            if (!$validate->check($post)) {
                json_fail_admin($validate->getError());
            }

            $res = Db::name("baison_bet_active")->where('id', '=', $id)->update($post);

            $this->addlog('编辑分组','编辑分组ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));
            if ($res !== false) {
                json_success_admin('修改成功');
            }else{
                json_fail_admin();
            }
        } else {
            $this->assign([
                'res' => $res,
            ]);
            return view();
        }
    }

    //返水配置
    public function sites(){
        $id = input('param.id');
        $redis = redis();
        $gameList = $redis->get("baisongamelist");
        if(!$gameList){
            json_fail_admin('未初初始化数据');
        }
        //初始化游戏列表
        $this->synx_data($id);
        $res = Db::name("baison_bet_active")->where('id', '=', $id)->find();
        $this->assign([
            'id' => $id,
            'game_list' => json_decode($res['configuration'],true),
            'current_url'=>'/admin/baisonbetactive',
        ]);
        return view();
    }
    public function sites_update(){
        $id = input('param.id');
        $game_id = input('param.game_id');
        $res = Db::name("baison_bet_active")->where('id', '=', $id)->find();
        $result = json_decode($res['configuration'],true);
        foreach($result as $key=>$value){
            if($value['game_id'] == $game_id){
                $list = $value;
                break;
            }
        }
        if (request()->isPost()) {
            $post = input("post.");
            $validate = new \think\Validate();
            $validate->rule([
                'coefficient' => 'float',
                'coefficient' => 'between:0,0.2',
            ]);
            $validate->message([
                'coefficient.float' => '返水系统只能是数字',
                'coefficient.between' => '返水系统只能在0.01到0.2之间',
            ]);
            if (!$validate->check($post)) {
                json_fail_admin($validate->getError());
            }
            foreach($result as $key=>$value){
                if($value['game_id'] == $post['game_id']){
                    $result[$key]['coefficient'] = $post['coefficient'];
                }
            }
            $res = Db::name("baison_bet_active")->where('id', '=', $id)->update(['configuration'=>json_encode($result)]);
            $this->addlog('修改返水配置','修改返水配置ID:'.$id.',post_data:'.json_encode($result,JSON_UNESCAPED_UNICODE));
            if ($res !== false) {
                json_success_admin('修改成功');
            }else{
                json_fail_admin();
            }
        }

        $this->assign([
            'id' => $id,
            'current_url'=>'/admin/baisonbetactive',
            'res'=>$list
        ]);
        return view();
    }

    //批量更改
    public function sites_updates(){
        $id = input('param.id');
        $game_id = input('param.game_id');
        $res = Db::name("baison_bet_active")->where('id', '=', $id)->find();
        $result = json_decode($res['configuration'],true);
        $post = input("post.");
        $validate = new \think\Validate();
        $validate->rule([
            'coefficient' => 'float',
            'coefficient' => 'between:0,0.2',
        ]);
        $validate->message([
            'coefficient.float' => '返水系统只能是数字',
            'coefficient.between' => '返水系统只能在0.01到0.2之间',
        ]);
        if (!$validate->check($post)) {
            json_fail_admin($validate->getError());
        }
        $game_id = explode(",",$game_id);
        foreach ($game_id as $k=>$v){
            foreach($result as $key=>$value){
                if($value['game_id'] == $v){
                    $result[$key]['coefficient'] = $post['coefficient'];
                }
            }
        }
        $res = Db::name("baison_bet_active")->where('id', '=', $id)->update(['configuration'=>json_encode($result)]);
        $this->addlog('修改返水配置','修改返水配置ID:'.$id.',post_data:'.json_encode($result,JSON_UNESCAPED_UNICODE));
        if ($res !== false) {
            json_success_admin('修改成功');
        }else{
            json_fail_admin();
        }
    }

    //从redis 同步数据到数据库，原始数据是从定时任务同步到redis中的。每天会同步一次
    private function synx_data($id){
        if(empty($id)){
            $id = input('param.id');
        }
        $redis = redis();
        $gameList = $redis->get("baisongamelist");
        $gameList = json_decode($gameList,true);

        $res = Db::name("baison_bet_active")->where('id', '=', $id)->find();
        if(empty($res['configuration'])){
            $data = array();
            foreach($gameList as $key=>$value){
                $data[$key]['game_id'] = $value['game_id'];
                $data[$key]['name'] = $value['name'];
                $data[$key]['coefficient'] = 0;
            }
            $res = Db::name("baison_bet_active")->where('id', '=', $id)->update(['configuration'=>json_encode($data)]);
        }else{
            //检查第三方游戏是否有新加的游戏
            $new_data = array();
            $gameListlocal =json_decode($res['configuration'],true);
            $game_ids = array_column($gameListlocal, 'game_id');
            foreach($gameList as $key=>$value){
                if(!in_array($value['game_id'],$game_ids)){
                    $new_data[$key]['game_id'] = $value['game_id'];
                    $new_data[$key]['name'] = $value['name'];
                    $new_data[$key]['coefficient'] = 0;
                }
            }
            if(!empty($new_data)){
                $gameListlocal = array_merge($gameListlocal, $new_data);
                $res = Db::name("baison_bet_active")->where('id', '=', $id)->update(['configuration'=>json_encode($gameListlocal)]);
            }

        }
    }

    //用户列表
    public function memberList(){
        $inputs = $post = input('param.');
        $where[] = ['baison_group', '=', $inputs['id']];
        $condition['account'] = input('request.account');
        $condition['channelid'] = input('request.channelid');
        $condition['avail_bet'] = input('request.avail_bet');
        if (!empty($condition['account'])) $where[] = ['account', '=', $condition['account']];
        if (!empty($condition['channelid'])) $where[] = ['channelid', '=', $condition['channelid']];
        if (!empty($condition['avail_bet'])) {
            $avail_bet = explode("-",$condition['avail_bet']);
            $where[] = ['avail_bet', '>=', $avail_bet[0]];
            $where[] = ['avail_bet', '<=', $avail_bet[1]];
        }
        $list = Db::name("baison_member")->where($where)->paginate(20);
        $page = $list->render();

        $group = Db::name("baison_bet_active")->select();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'group' => $group,
            'current_url' => '/admin/baisonbetactive/memberList/id/'.$inputs['id'],
        ]);

        return view();
    }

    //更新组别
    public function udpate_group(){
        $inputs = $post = $id = input('param.');

        $validate = new \think\Validate();
        $validate->rule([
            'id' => 'require',
            'group_id' => 'require',
        ]);
        $validate->message([
            'id.require' => 'id必填',
            'group_id.require' => '组别必填',
        ]);
        if (!$validate->check($post)) {
            json_fail_admin($validate->getError());
        }
        $res = Db::name("baison_member")->where('id','in',$inputs['id'])->find();
        if (empty($res)) json_fail_admin('数据为空');

        Db::name("baison_member")->where('id', 'in', $inputs['id'])->update(['baison_group'=>$post['group_id']]);

        $this->addlog('更改用户组','id:'.$inputs['id'].',内容：'.$post['group_id']);

        json_success_admin();
    }

    //会员返水报表
    public function report(){
        $where = [];
        $condition['date_label'] = input('request.date_label')??date('Y-m-d',time());
        $condition['channelid'] = input('request.channelid');
        if (!empty($condition['date_label'])) $where[] = ['date_label', '=', $condition['date_label']];
        if (!empty($condition['channelid'])) $where[] = ['channelid', '=', $condition['channelid']];

        $list = Db::name("baison_betting")
            ->field(
                'channelid,
                COUNT(DISTINCT user_id) AS user_total,
                SUM( avail_bet ) AS avail_bet,
                SUM( CASE WHEN `status` = 2 THEN bet_preferential ELSE 0 END ) AS y_total'
            )
            //SUM( CASE WHEN `status` = 1 THEN bet_preferential ELSE 0 END ) AS w_total,
            ->where($where)
            ->group('channelid')
            ->paginate(20);
            //echo Db::name("baison_betting")->getLastSql();
        $page = $list->render();
        $statistics = Db::name("baison_betting")
            ->field(
                'COUNT(DISTINCT user_id) AS user_total,
                 SUM( avail_bet ) AS avail_bet,
                SUM( CASE WHEN `status` = 2 THEN bet_preferential ELSE 0 END ) AS y_total'
            )
            ->where($where)->find();
        $this->_assign();
        $this->assign([
            'title' => '报表',
            'name'=>'Baisonbetactive',
            'current_url'=>'/admin/Baisonbetactive/report',
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'statistics' => $statistics,
        ]);
        return view();
    }

    //会员返水报表详情
    public function reportByUser(){
        $where = [];
        $condition['date_label'] = input('request.date_label')??date('Y-m-d',time());
        $condition['channelid'] = input('request.channelid');
        if (!empty($condition['date_label'])) $where[] = ['date_label', '=', $condition['date_label']];
        if (!empty($condition['channelid'])) $where[] = ['channelid', '=', $condition['channelid']];

        $list = Db::name("baison_betting")
            ->field(
                'channelid,
                user_id,
                SUM( avail_bet ) AS avail_bet,
                SUM( CASE WHEN `status` = 2 THEN bet_preferential ELSE 0 END ) AS y_total'
            )
            ->where($where)
            ->group('user_id')
            ->paginate(20);
        //echo Db::name("baison_betting")->getLastSql();
        $page = $list->render();
        $this->_assign();
        $this->assign([
            'title' => '报表',
            'name'=>'Baisonbetactive',
            'current_url'=>'/admin/Baisonbetactive/reportByUser',
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
        ]);
        return view();
    }

    //导出
    public function download(){
        $filename="导出文件";
        $title = ['日期','渠道号','用户','有效投注','实际返水'];
        $where = [];
        $condition['date_label'] = input('request.date_label')??date('Y-m-d',time());
        $condition['channelid'] = input('request.channelid');
        if (!empty($condition['date_label'])) $where[] = ['date_label', '=', $condition['date_label']];
        if (!empty($condition['channelid'])) $where[] = ['channelid', '=', $condition['channelid']];

        $list = Db::name("baison_betting")
            ->field(
                'date_label,
                channelid,
                user_id,
                SUM( avail_bet ) AS avail_bet,
                SUM( CASE WHEN `status` = 2 THEN bet_preferential ELSE 0 END ) AS y_total'
            )
            ->where($where)
            ->group('user_id')
            ->select();
        $this->excel_data($filename,$title,$list);exit;
    }

}
