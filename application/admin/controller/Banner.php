<?php
namespace app\admin\controller;

use think\Db;
class Banner extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "banner",
            'title' => "我的广告列表",
            'current_url' => "/admin/banner",
        ];
        $this->where = [
            'title' => 'like',
            'day' => '',
            'status' => '',
        ];
        $this->order = 'id asc';
        parent::__construct($conf);
    }
    public function index()
    {
        $list = Db::name($this->name)->order('id desc')->paginate(20)->each(function ($item,$res){
            $item['update_user'] = Db::name("admin_info")->where('id',$item['create_user'])->value('username');
            return $item;
        });
        $this->assign([
            'class_list'=>$this->class_list,
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }

    public function add()
    {
        if (request()->isPost()) {
            $post = input("post.");
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        $res = preg_match($v['rule'],$post[$k]);
                        if(!$res) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }

            $post['create_time'] = time();
            $post['create_user'] = $this->user['id'];

            $post['start_time'] = $post['start_time']?strtotime($post['start_time']):time();
            $post['end_time'] = $post['end_time']?strtotime($post['end_time']):0;

            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            $this->addlog('添加banner','banner:'.$res);

            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
                unset($role['cover_type']);
                unset($role['play_type']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if(isset($v['type']) && $v['type'] == 'class_list'){
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => '',
                            'role' => $v,
                        ];
                    }
                }
            }

            $this->assign([
                'res' => $arr,
            ]);
            return view();
        }
    }
    public function edit()
    {
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input('post.');
            // if (!empty($this->verify)) {
            //     foreach ($this->verify as $k => $v) {
            //         if (empty($post[$k])) json_fail_admin($v);
            //     }
            // }

            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        $res = preg_match($v['rule'],$post[$k]);
                        if(!$res) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }
            $post['update_time'] = time();
            $post['update_user'] = $this->user['id'];
            $post['start_time'] = $post['start_time']?strtotime($post['start_time']):time();
            $post['end_time'] = $post['end_time']?strtotime($post['end_time']):0;
            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);
            $this->addlog('修改banner','banner:'.$id);

            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $this->assign([
                'res' => $res,
                'is_parent' => $this->is_parent,
            ]);
            return view();
        }
    }

}