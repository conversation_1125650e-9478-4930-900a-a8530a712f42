<?php
namespace app\admin\controller;

use app\Helper\CacheHelper;
use app\Helper\JaegerHelper;
use app\Helper\ParamsMonitorHelper;
use app\Service\AdminLogService;
use think\Db;
use think\Controller;
use app\admin\controller\Login;
use app\admin\model\AdminNode;
class BaseController extends Controller
{
    public static $current_fix_url; //获取当前路由相对地址
    public static $current_fix_not_fun_url; //获取当前路由相对地址(不包含方法)
    public static $current_fix_not_module_url; //获取当前路由相对地址(不包含模块名)
    public static $menu_column_list; //后台菜单数据
    public static $current_menu_name; //当前菜单名

	public function initialize()
	{

        //设置后台运行大内存
        @ini_set('memory_limit','2048M');

//        //开启jaeger监控
        $jaegerHelper = JaegerHelper::getClass();
        $jaegerHelper->init([
            'jaeger_sampler_rate'=>1,
            'jaeger_min_gathering_time'=>10000,
            'is_pre_reduce'=>false,
            'jaeger_service_name'=>env('JAEGER_SERVICE_NAME','other_service').'_backend',
        ]);

        Db::listen(function($sql, $time, $explain)use ($jaegerHelper){

            $sql_type='other';
            foreach (['select','insert','update','delete'] as $k=>$v){
                if(strpos(strtolower($sql),$v) === 0)$sql_type=$v;
            }

            $jaegerHelper->startActiveSpan(JaegerHelper::ACTIVE_SPANS['mysql.'.$sql_type],[
                'start_time' => (int) ((microtime(true) - $time) * 1000 * 1000),
            ]);
            $jaegerHelper->log(['sql'=>$sql. ' ['.$time.'s]']);
        });

        ParamsMonitorHelper::paramsMonitor(['get'=>$_GET,'post'=>$_POST]);

        //后台白名单
        if (sysconf('white_status')) {
            $ips = sysconf('white_ip');
            $ips = array_unique(array_filter(explode(',',$ips)));
            $uip = request()->ip();
            if (!in_array($uip, $ips)) {
                // echo '<!DOCTYPE html><html><head><title>Error</title><style>body {width: 35em;margin: 0 auto;font-family: Tahoma, Verdana, Arial, sans-serif;}</style></head><body><h1>An error occurred.</h1><p>Sorry, the page you are looking for is currently unavailable.<br/>Please try again later.</p><p>If you are the system administrator of this resource then you should checkthe error log for details.</p><p><em>Faithfully yours, ^.^</em></p></body></html>';exit;
//                echo '<html><head><title>404 Not Found</title></head><body><center><h1>404 Not Found</h1></center><hr><center>nginx</center></body></html>';exit;
            }
        }


		parent::initialize();
		$this->check_login();
		$this->assign(['cache_sys_config'=>cache_sys_config()]);
		$this->assign(['static_url'=>"http://static.com"]);

        if((empty($this->user['is_bind_google_check']) || $this->user['is_bind_google_check']!=1)){
            $this->error("请重新登录并绑定谷歌验证器","/admin/login");
        }

        self::$current_fix_url = '/'.request()->module() . '/'.strtolower(request()->controller() ). '/' . request()->action();
        self::$current_fix_not_module_url = '/'.strtolower(request()->controller() ). '/' . request()->action();
        self::$current_fix_not_fun_url = '/'.request()->module() . '/'.strtolower(request()->controller() );

        $admin_node = Db::name('admin_node')->cache(true,600)->select();

        foreach ($admin_node as $k=>&$v){
            $v['path']=strtolower($v['path']);
        }
        self::$menu_column_list = array_column($admin_node,null,'path');

        $this->assign('current_menu_name', self::$menu_column_list[self::$current_fix_not_module_url]['title']??'');

	}

    public function __destruct(){
        if(!empty(JaegerHelper::$tracer)){
            $jaegerHelper = JaegerHelper::getClass();
            $jaegerHelper->startActiveSpan(JaegerHelper::ACTIVE_SPANS['end']);
            $jaegerHelper->close();
            $jaegerHelper->flush();
        }

    }

    public function get_menu(){
        $model = new AdminNode();
		$this->get_loginUser();
        if($this->user['role_id']==1){
            $menu = $model->getAll();
        }else{
            $menu = $model->getAll($this->user['ids']);
        }
        foreach ($menu as $k=>$v){
            if(in_array($this->user['username'],['audit','cindy'])){
                if($v['id']==662){
                    unset($menu[$k]);
                }
            }
        }

        return $menu;
    }
    private function get_loginUser(){
        $code = base64_decode(session("adminX"));
        session_write_close();
        $this->user = json_decode(decode($code,'userinfo'),true);
    }
    public function check_login(){
        $this->get_loginUser();

        if(isset($this->user['login']) && $this->user['login']=="ok"){
            $url = substr(get_uri(),6);
            $ext = strtolower(substr($url,strrpos($url,'/')+1));
            if($ext == 'index') {
                $url = substr(get_uri(),6,strrpos($url,'/'));
            }
            $url2 = strtolower('/'.request()->controller().'/'.request()->action());
			if(request()->action()=='index'){
				$url2 = strtolower('/'.request()->controller());
			}

            if ($this->user['role_id'] != 1 && !in_array($url2,config("api.NOT_AUTH_URL"))){

//                $url = '|'.str_replace('_','',$url).'|';
//                $url2 = '|'.str_replace('_','',$url2).'|';
                $url = '|'.str_replace('_','',$url);
                $url2 = '|'.str_replace('_','',$url2);
                $paths = $this->user['paths'];

				if(strpos($paths,$url)===false && strpos($paths,$url2)===false){
                    if ($url2 == '|/appreport|') {

                       echo "<h2 style='padding:10px'>"."你好 - ".$this->user['username']."!</h2>";exit;
                    }

            		header("Content-type: text/html; charset=utf-8");
                    json_fail_admin("没有权限!");
				}
            }
        }else{
			// $this->error("请重新登录系统","/admin/login");
			$this->redirect(Login::$login_url);
        }
    }
    public function addlog($title,$content){
        AdminLogService::addlog($title,$content,$this->user['id'],$this->user['username'],request()->ip(),time());
    }

    public function excel_data($filename,$title,$data){

        require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $spreadsheet->getDefaultStyle()->getFont()->setName('Arial');
        $spreadsheet->getDefaultStyle()->getFont()->setSize(12);
        $sheet = $spreadsheet->getActiveSheet();

        // 表头
        foreach ($title as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);

        }
        $sheet->getDefaultColumnDimension()->setWidth(20);
        // 写入内容
        $row = 2;
        foreach ($data as $item) {
            $column = 1;
            if (!is_array($item)) {

                continue;
            }
            foreach ($item as $value) {
                $sheet->setCellValueByColumnAndRow($column, $row, $value);
                $column ++;
            }
            $row ++;
        }
        // 输出 下载内容
        ob_end_clean();
        ob_start();
//        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
//        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8');
        header('Cache-Control: max-age=0');

        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }

    /**
     * 首页统计特殊表头
     * @param $filename
     * @param $data
     * @return void
     * @throws \PhpOffice\PhpSpreadsheet\Exception
     * @throws \PhpOffice\PhpSpreadsheet\Writer\Exception
     */
    public function excel_data_index($filename,$title,$data){

        require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $spreadsheet->getDefaultStyle()->getFont()->setName('Arial');
        $spreadsheet->getDefaultStyle()->getFont()->setSize(12);
        $sheet = $spreadsheet->getActiveSheet();

        $spreadsheet->getActiveSheet()->mergeCells('A1:A2');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('A1','日期');
        $spreadsheet->getActiveSheet()->mergeCells('B1:C2');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('B1','新增电影');
        $spreadsheet->getActiveSheet()->mergeCells('D1:H1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('D1','用户');
        $spreadsheet->getActiveSheet()->mergeCells('I1:M1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('I1','长视频');
        $spreadsheet->getActiveSheet()->mergeCells('N1:Q1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('N1','短视频');
        $spreadsheet->getActiveSheet()->mergeCells('R1:AA1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('R1','充值');
        $spreadsheet->getActiveSheet()->mergeCells('AB1:AE1');
        $spreadsheet->setActiveSheetIndex(0)->setCellValue('AB1','其他');

        // 表头
        foreach ($title as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 2, $value);
        }

        $sheet->getDefaultColumnDimension()->setWidth(20);
        // 写入内容
        $row = 3;
        foreach ($data as $item_one) {
//            dump($data);die();
            $column = 1;

            foreach ($item_one as $item){
                if (!is_array($item)) {
                    continue;
                }
                foreach ($item as $value) {
                    $sheet->setCellValueByColumnAndRow($column, $row, $value);
                    $column ++;
                }
            }
            $row ++;
        }
        // 输出 下载内容
        ob_end_clean();
        ob_start();
        header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8');
        header('Cache-Control: max-age=0');
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }




}
