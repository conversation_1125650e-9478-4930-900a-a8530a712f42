<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class CartoonChapterDetail extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "cartoon_chapter_detail";
        $this->title = "章节详情";
        $this->current_url = "/admin/cartoon_chapter_detail";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index($chapter_id)
    {
        $res = Db::name('cartoon_chapter')->where('id','=',$chapter_id)->find();
        if(empty($res)) json_fail_admin('该漫画已被删除');
        $where = [];
        $where[] = ['chapter_id','=',$chapter_id];

        $list = Db::name($this->name)->where($where)->order('index asc')->paginate(20);

        $this->assign([
            'chapter_id' => $chapter_id,
            'list' => $list,
        ]);
        $this->_assign();
        return view();
    }
    public function add($chapter_id)
    {
        $res = Db::name('cartoon_info')->where('id','=',$chapter_id)->find();
        if(empty($res)) json_fail_admin('该漫画已被删除');

        if (!request()->isPost()) {
            return view();
        } else {
            $post = input('post.');
            if (empty($post['title'])) json_fail_admin('标题不能为空');

            $data = [
                'chapter_id' => $chapter_id,
                'title' => $post['title'],
                'status' => $post['status'],
                'create_time' => time(),
                'create_date' => date('Y-m-d'),
                'create_user' => $this->user['id'],
            ];
            $file['thumb'] = request()->file("file_thumb");
            if (empty($file['thumb'])) json_fail_admin("请选择上传封面");
            $info['thumb'] = $file['thumb']->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/thumb');
            if (!$info['thumb']) json_fail_admin('上传封面', $file['thumb']->getError());
            $data['thumb'] = UPLOAD_NAME . '/thumb/' . $info['thumb']->getSaveName();

            $res = Db::name($this->name)->insert($data);
            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function edit($id)
    {
        $res = Db::name($this->name)->where('id','=',$id)->find();
        if(empty($res)) json_fail_admin('数据不存在');

        if (!request()->isPost()) {
            $this->assign(['res'=>$res]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['title'])) json_fail_admin('标题不能为空');

            $data = [
                'title' => $post['title'],
                'status' => $post['status'],
                'update_time' => time(),
                'update_user' => $this->user['id'],
            ];
            $file['thumb'] = request()->file("file_thumb");
            if (!empty($file['thumb'])) {
                $info['thumb'] = $file['thumb']->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/thumb');
                if (!$info['thumb']) json_fail_admin('上传封面', $file['thumb']->getError());
                $data['thumb'] = UPLOAD_NAME . '/thumb/' . $info['thumb']->getSaveName();
            }
            $res = Db::name($this->name)->where('id','=',$id)->update($data);
            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name('cartoon_chapter_detail')->where('chapter_id','=',$id)->find();
        if ($res) json_fail_admin('当前章节下有内容');
        $res ? json_success_admin() : json_fail_admin();
    }
    public function status($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');
        $res = Db::name($this->name)->where($where)->exp('status', 'if(status=1,2,1)')->update();
        $res ? json_success_admin() : json_fail_admin();
    }
}