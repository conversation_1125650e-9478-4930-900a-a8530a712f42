<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class CartoonInfo extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "cartoon_info";
        $this->title = "漫画列表";
        $this->current_url = "/admin/cartoon_info";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['id'] = input('request.id');
        $condition['category_id'] = input('request.category_id');
        $condition['title'] = input('request.title');
        $condition['status'] = input('request.status');

        if (!empty($condition['id'])) $where[] = ['a.id', '=', $condition['id']];
        if (!empty($condition['category_id'])) $where[] = ['a.category_id', '=', $condition['category_id']];
        if (!empty($condition['title'])) $where[] = ['a.title', 'like', '%' . $condition['title'] . '%'];
        if (!empty($condition['status'])) $where[] = ['a.status', '=', $condition['status']];

        $class_list = Db::name('app_category')->field('id,title,pinyin')->where('category_type', '=', 'cartoon')->order('pinyin asc')->select();
        $list = Db::name($this->name)->alias('a')->where($where)->order('a.create_time desc')->paginate(20);

        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'class_list' => $class_list,
        ]);
        $this->_assign();
        return view();
    }
    public function add()
    {
        if (!request()->isPost()) {
            $class_list = Db::name('app_category')->field('id,title,pinyin')->where('category_type', '=', 'cartoon')->order('pinyin asc')->select();
            $this->assign(['class_list'=>$class_list]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['title'])) json_fail_admin('标题不能为空');

            $data = [
                'category_id' => $post['category_id'],
                'title' => $post['title'],
                'detail' => $post['detail'],
                'chapter_num' => $post['chapter_num'],
                'is_publish' => $post['is_publish'],
                'status' => $post['status'],
                'create_time' => time(),
                'create_date' => date('Y-m-d'),
                'create_user' => $this->user['id'],
            ];
            $file['thumb'] = request()->file("file_thumb");
            if (empty($file['thumb'])) json_fail_admin("请选择上传封面");
            $info['thumb'] = $file['thumb']->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/thumb');
            if (!$info['thumb']) json_fail_admin('上传封面', $file['thumb']->getError());

            $data['thumb'] = UPLOAD_NAME . '/thumb/' . $info['thumb']->getSaveName();
            $res = Db::name($this->name)->insert($data);
            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function edit($id)
    {
        $res = Db::name($this->name)->where('id','=',$id)->find();
        if(empty($res)) json_fail_admin('数据不存在');
        if (!request()->isPost()) {
            $class_list = Db::name('app_category')->field('id,title,pinyin')->where('category_type', '=', 'cartoon')->order('pinyin asc')->select();
            $this->assign([
                'class_list'=>$class_list,
                'res'=>$res,
            ]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['title'])) json_fail_admin('标题不能为空');

            $data = [
                'category_id' => $post['category_id'],
                'title' => $post['title'],
                'detail' => $post['detail'],
                'chapter_num' => $post['chapter_num'],
                'is_publish' => $post['is_publish'],
                'status' => $post['status'],
                'update_time' => time(),
                'update_user' => $this->user['id'],
            ];
            $file['thumb'] = request()->file("file_thumb");
            if (!empty($file['thumb'])) {
                $info['thumb'] = $file['thumb']->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/thumb');
                if (!$info['thumb']) json_fail_admin('上传封面', $file['thumb']->getError());
                $data['thumb'] = UPLOAD_NAME . '/thumb/' . $info['thumb']->getSaveName();
            }
            $res = Db::name($this->name)->where('id','=',$id)->update($data);
            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name('cartoon_chapter')->where('cartoon_id','=',$id)->find();
        if ($res) json_fail_admin('当前漫画下有章节');
        $res = Db::name($this->name)->where($where)->delete();
        $res ? json_success_admin() : json_fail_admin();
    }
    public function status($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name($this->name)->where($where)->exp('status', 'if(status=1,2,1)')->update();
        $res ? json_success_admin() : json_fail_admin();
    }
}