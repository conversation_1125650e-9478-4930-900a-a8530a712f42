<?php
namespace app\admin\controller;

use app\Service\CategoryTypeListService;
use think\Db;
class CategoryTypeList extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '场馆分类管理',
            'name'=>'category_type_list',
            'current_url'=>'/admin/category_type_list',
        ];
        $id = input('id');

        if(!empty($id)){
            $detail = Db::name('category_type_list')->where(['id'=>$id])->find();
            if(!empty($detail)){
                $this->class_list[] = [
                    'id' => $detail['id'],
                    'title' => $detail['title'],
                ];
            }
        }

        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['is_hot'] = input('request.is_hot');
        $condition['title'] = input('request.title');
        $condition['status'] = input('request.status');
        $condition['category_type_id'] = input('request.category_type_id');

        if (!empty($condition['id'])) $where[] = ['o.id','=', $condition['id']];
        if (!empty($condition['title'])) $where[] = ['o.title','LIKE', '%' .$condition['title'].'%' ];
        if (!empty($condition['status'])) {
            $where[] = ['o.status', '=',$condition['status']];
        }
        if(isset($condition['is_hot']) && $condition['is_hot']!=='' ){
            $where[] = ['o.is_hot', '=',$condition['is_hot']];
        }
        if(isset($condition['category_type_id']) && $condition['category_type_id']!=='' ){
            $where[] = ['o.category_type_id', '=',$condition['category_type_id']];
        }

        $list = Db::name("category_type_list")->alias('o')->field('o.*')->where($where)->order("o.inside_sort desc");
        $list = $list ->paginate(20)->each(function($item, $key){
                return $item;
            });
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
        ]);

        return view();
    }

}
