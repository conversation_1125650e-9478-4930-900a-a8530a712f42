<?php
namespace app\admin\controller;
use think\Db;
class Chat extends BaseController
{
    public function room(){
    	$list = Db::name('chat_room')->order('sort desc')->paginate(20);     
        $this->assign([
            'list' => $list,
        ]);
        return view();
    }
    public function add_room()
    {
        if (request()->isPost()) { 
            $data = input('post.');
            if (!$data['title']) {
                json_fail_admin('聊天室名称不能为空!');
            }
            if (!$data['admin_id']) {
                json_fail_admin('管理员信息不能为空!');
            }
            if (!$data['type']) {
                json_fail_admin('房间类型不能为空!');
            }
            if (!$data['start_time']) {
                json_fail_admin('开始时间!');
            }
            if (!$data['end_time']) {
                json_fail_admin('结束时间!');
            }
            if ($data['start_time'] > $data['end_time']) {
                json_fail_admin('时间选取错误!');
            }
            $admin_id = Db::name('member_info')->where('id',$data['admin_id'])->whereor('account',$data['admin_id'])->value('id');
            if (!$admin_id) {
                json_fail_admin('管理员账号不存在!');
            }
            $insert = [
                'title' => $data['title'],
                'content' => $data['content'],
                'admin_id' => $admin_id,
                // 'user_id' => $data['user_id'],
                'sort' => $data['sort'],
                'back_img' => $data['back_img'],
                'room_img' => $data['room_img'],
                'type' => $data['type'],
                'status' => $data['status'],
                'start_time' => strtotime($data['start_time']),
                'end_time' => strtotime($data['end_time']),
                'add_time' => time(),
            ];
            $re = Db::name('chat_room')->insertGetId($insert);

            if (!$re) {
                json_fail_admin('添加失败,请重试!');
            }
            redis()->del('all_chat_room');

            $this->addlog('添加聊天室',$insert['title']);
            json_success_admin('添加成功!');  
        }
        return view('add_room');

    }
    public function edit_room($id)
    {
        if (request()->isPost()) { 
            $data = input('post.');
            if (!$data['title']) {
                json_fail_admin('聊天室名称不能为空!');
            }
            if (!$data['admin_id']) {
                json_fail_admin('管理员信息不能为空!');
            }
            if (!$data['type']) {
                json_fail_admin('房间类型不能为空!');
            }
            if (!$data['start_time']) {
                json_fail_admin('开始时间!');
            }
            if (!$data['end_time']) {
                json_fail_admin('结束时间!');
            }
            if ($data['start_time'] > $data['end_time']) {
                json_fail_admin('时间选取错误!');
            }

           $admin_id = Db::name('member_info')->where('id',$data['admin_id'])->whereor('account',$data['admin_id'])->value('id');
            if (!$admin_id) {
                json_fail_admin('管理员账号不存在!');
            }
            $update = [
            	'id' => $id,
                'title' => $data['title'],
                'content' => $data['content'],
                'admin_id' => $admin_id,
                // 'user_id' => $data['user_id'],
                'sort' => $data['sort'],
                'back_img' => $data['back_img'],
                'room_img' => $data['room_img'],
                'type' => $data['type'],
                'status' => $data['status'],
                'start_time' => strtotime($data['start_time']),
                'end_time' => strtotime($data['end_time']),
            ];
            $re = Db::name('chat_room')->update($update);

            if (!$re) {
                json_fail_admin('修改失败,请重试!');
            }
            redis()->del('chat_room_info_'.$id);
            redis()->del('all_chat_room');

            $this->addlog('修改聊天室',$update['title']);
            json_success_admin('修改成功!');  
        }else{
        	$room = Db::name('chat_room')->find($id);
        	$room['start_time'] = str_replace(' ', 'T', date('Y-m-d H:i:s',$room['start_time']));
        	$room['end_time'] = str_replace(' ', 'T', date('Y-m-d H:i:s',$room['end_time']));
        	return view('edit_room',['room'=>$room]);
        }
        
    }
    public function del_room($id)
    {
        $res = Db::name('chat_room')->delete($id);

        $this->addlog('删除聊天室', '数据ID: '.$id);
        redis()->del('chat_room_info_'.$id);
        redis()->del('all_chat_room');
        
        $res ? json_success_admin() : json_fail_admin();
    }



    public function active(){
    	$list = Db::name('chat_active')->order('sort desc')->paginate(20);     
        $this->assign([
            'list' => $list,
        ]);
        return view();
    }
    public function add_active()
    {
        if (request()->isPost()) { 
            $data = input('post.');
            if (!$data['user_id']) {
                json_fail_admin('用户账号或ID不能为空!');
            }
            $insert = [
                'user_id' => $data['user_id'],
                'sort' => $data['sort'],
                'status' => $data['status'],
                'add_time' => time(),
            ];
            $re = Db::name('chat_active')->insert($insert);

            if (!$re) {
                json_fail_admin('添加失败,请重试!');
            }

            $this->addlog('添加活跃会员',$insert['user_id']);
            json_success_admin('添加成功!');  
        }
        return view('add_active');

    }
    public function edit_active($id)
    {
        if (request()->isPost()) { 
            $data = input('post.');
            if (!$data['user_id']) {
                json_fail_admin('用户账号或ID不能为空!');
            }

            $update = [
            	'id' => $id,
                'user_id' => $data['user_id'],
                'sort' => $data['sort'],
                'status' => $data['status'],
            ];
            $re = Db::name('chat_active')->update($update);

            if (!$re) {
                json_fail_admin('修改失败,请重试!');
            }

            $this->addlog('修改活跃会员',$update['user_id']);
            json_success_admin('修改成功!');  
        }else{
        	$active = Db::name('chat_active')->find($id);
        	return view('edit_active',['active'=>$active]);
        }
        
    }
    public function del_active($id)
    {
        $res = Db::name('chat_active')->delete($id);

        $this->addlog('删除活跃会员', '数据ID: '.$id);

        $res ? json_success_admin() : json_fail_admin();
    }



    public function room_black(){
        $where = [];
        $condition['room_id'] = input('request.room_id');
        $condition['user_id'] = input('request.user_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['type'] = input('request.type');

         // exit;       
        if (!empty($condition['room_id'])) $where[] = ['room_id','=', $condition['room_id']];

        if (!empty($condition['user_id'])) $where[] = ['user_id', '=',$condition['user_id']];

        if (!empty($condition['type'])) $where[] = ['type', '=',$condition['type']];
        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'] . '23:59:59')];
         


        $list = Db::name('chat_room_black')->where($where)->order('id desc')->paginate(20);    

        $page = $list->render();
        $list = $list->toArray();
        $rooms = Db::name('chat_room')->column('*,id');
  
        foreach ($list['data'] as $key => &$value) {
            if (isset($rooms[$value['room_id']])) {
                $value['title'] = $rooms[$value['room_id']]['title'];
            }else{
                $value['title'] = '聊天室已删除!';
            }
            
        }

        $this->assign([
            'list' => $list,
            'page' => $page,
            'condition' => $condition,
            'rooms' => $rooms,
        ]);
        return view();
    }

    public function room_delblack($id)
    {
        $info = Db::name('chat_room_black')->where('id',$id)->find();
        if (!$info) {
            json_fail_admin();
        }
        $res = Db::name('chat_room_black')->delete($id);

        redis()->srem("chat_room_black{$info['type']}_{$info['room_id']}",$info['user_id']);  //删除redis

        $this->addlog('删除聊天室拉黑会员', '房间ID: '.$info['room_id'].' 用户ID: '.$info['user_id']);

        $res ? json_success_admin() : json_fail_admin();
    }
}
