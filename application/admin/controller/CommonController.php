<?php
namespace app\admin\controller;

use app\EasyAes;
use app\extend\Telegram\TelegramBot;
use app\Helper\CommonConstHelper;
use app\Service\AppAdvService;
use think\Db;
use think\facade\Env;
class CommonController extends BaseController
{
    public $name;
    public $role_name='';
    public $title;
    public $current_url;
    public $export_current_url;
    public $id = '';
    public $role = '';
    public $where = [];
    public $class_list = [];
    public $class_list2 = [];
    public $order = 'id desc';
    public $is_parent = 1;
    public $files = [];
    public $status_one = 0;
    public $verify = [];

    public $edit_default=[];

    public $index_export=false;


    public function __construct($conf = [])
    {
        parent::__construct();
        $this->name = $conf['name'];
        $this->role_name = $conf['role_name']??'';
        $this->title = $conf['title'];
        $this->current_url = $conf['current_url'];
        $this->export_current_url = $conf['current_url'].'/index_export'.'?'.http_build_query($_GET).'&is_export=1';

    }
    public function _assign()
    {
        $this->assign(['current_url' => $this->current_url]);
        $this->assign(['export_current_url' => $this->export_current_url]);
        $this->assign(['title' => $this->title]);
    }

    public function index()
    {
        $where = $condition = [];
        if(!empty($this->where)){
            foreach ($this->where as $key => $value) {

                $$key = input("{$key}");
                if (!empty($$key)) {

                    if($key=='keyfield_key'){
                        $field ='key';
                    }else{
                        $field = $key;
                    }
                    switch ($value) {
                        case 'like':
                            $where[] = [$field, $value, '%' . $$key . '%'];
                            break;
                        case 'exp':
                            $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$$key}',$field)")];
                            break;
                        default:
                            $where[] = [$field, '=', $$key];
                            break;
                    }
                }
                if($key=='keyfield_key'){
                    $condition['key'] = input("keyfield_key");
                }else{
                    $condition[$key] = $$key;
                }

            }
        }
        $orderlist=$this->order;
        if ($this->name == 'app_tips')
            $orderlist = 'sort desc,create_time desc';

        $adv_click_date_column=[];
        if($this->name=='app_adv'){
            $adv_click_date_column = Db::name('adv_click_date')->where(['date'=>date('Y-m-d')])->column('click_count','adv_id');
        }
        $admin_info_column = Db::name('admin_info')->cache(60)->column('username','id');
        $list = Db::name($this->name)->where($where)->order($orderlist)->paginate(20)->each(function ($item,$key)use ($admin_info_column,$adv_click_date_column){

            if($this->name=='app_adv'){
                if(isset($item['operator_user_id'])){
                    $item['operator_user_name']=$admin_info_column[$item['operator_user_id']]??'';
                }
                $item['today_click_count']=$adv_click_date_column[$item['id']]??0;
            }

            return $item;
        });
        $this->_assign();
        $this->assign([
            'list' => $list,
            'class_list' => $this->class_list,
            'class_list2' => $this->class_list2,
            'condition' => $condition,
        ]);
        return view("/{$this->name}/index");
    }
    public function add()
    {
        if (request()->isPost()) {
            $post = input("post.");

            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        if (
                            (isset($post['link_source']) &&  in_array($post['link_source'],[AppAdvService::LINK_SOURCE_BAISON,AppAdvService::LINK_SOURCE_GAME_RECHARGE]) && $k == 'url') ||  //特殊处理，百胜链接为空
                            (isset($post['belong_area']) && $post['belong_area'] == 'baison_wallet_adv')  //特殊处理，游戏钱包广告允许为空
                        ) {
                            $post['url']='';
                            continue;
                        }
                        if((isset($post['link_source']) &&  in_array($post['link_source'],[AppAdvService::LINK_SOURCE_GAME]) && $k == 'url')){
                            $post['url']='kumimi://home/<USER>';
                        }

                        $res = preg_match($v['rule'],$post[$k]);
                        if(!$res) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }

            $post['create_time'] = time();
            $post['create_user'] = $this->user['id'];
            if($this->name=='app_adv'){
                $post['operator_user_id'] =$this->user['id'];
            }elseif ($this->name=='vip'){
                $post['type'] =(Db::name('vip')->field("max(type) max_type")->find()['max_type']??0)+1;
            }
            if ($this->name=='vip'){
                $is_show_unlimit_count = is_numeric($post['is_show_unlimit_count'])?$post['is_show_unlimit_count']:0;
                $type_count=[
                    'ai_type_remove_clothes'=>is_numeric($post['ai_type_remove_clothes'])?$post['ai_type_remove_clothes']:0,
                    'ai_type_change_face'=>is_numeric($post['ai_type_change_face'])?$post['ai_type_change_face']:0,
                    'ai_type_movie'=>is_numeric($post['ai_type_movie'])?$post['ai_type_movie']:0,
                    'ai_type_diy_change_face'=>is_numeric($post['ai_type_diy_change_face'])?$post['ai_type_diy_change_face']:0,
                    'ai_type_draw'=>is_numeric($post['ai_type_draw'])?$post['ai_type_draw']:0,
                ];
                if($is_show_unlimit_count==1){
                    foreach ($type_count as $k=>&$v){
                        $v=0;
                    }
                }

                $post['ai_count_list']=json_encode(array_merge([
                    'ai_date'=>is_numeric($post['ai_date'])?$post['ai_date']:0,
                    'is_show_unlimit_count'=>$is_show_unlimit_count,
                ],$type_count),JSON_UNESCAPED_UNICODE);
            }

            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            $this->addlog('添加数据','表名: '.$this->name.' 数据ID: '.$res.',数据：'.json_encode($post,JSON_UNESCAPED_UNICODE));
            if ($this->name == 'vip_info') {
                redis()->del('vip_info');
            }
            if ($this->name == 'app_tips') {
                redis()->del('app_home_tips');
                redis()->del('app_home_tips_list');
            }
            cache('app_all_adv',null);
            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
				unset($role['icon']);
				unset($role['is_pc_top']);
				unset($role['is_pc_middle']);
                unset($role['cover_type']);
                unset($role['play_type']);
			}
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if(isset($v['type']) && $v['type'] == 'class_list'){
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'select') {
                        $this->class_list['0'] = '请选择';
                        ksort($this->class_list);
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'type' => $v['type'],
                                'value' => $this->class_list,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => '',
                            'role' => $v,
                        ];
                    }
                }
            }

            foreach ($arr as $k1=>$v1){
                $arr[$k1]['is_show']=1;
                if(!empty($v1['role']['show_condition'])){
                    foreach ($v1['role']['show_condition'] as $k2=>$v2){
                        if(isset($arr[$k2]['value']) && !$arr[$k2]['value']==$v2 ){
                            $arr[$k1]['is_show']=0;
                        }
                    }
                }

                if ($this->name=='vip' && $k1=='vip_info_ids'){
                    $vip_info_data_temp = Db::name('vip_info')->field(['title','id'])->select();
                    $vip_info_data=[];
                    foreach ($vip_info_data_temp as $k=>$v){
                        $vip_info_data[]=[
                            'name'=>$v['title'],
                            'value'=>$v['id'],
                        ];
                    }
                    $arr[$k1]['value']=$vip_info_data;
                }
            }

            $this->assign([
                'res' => $arr,
            ]);
            return view('/common/add');
        }
    }
    public function edit()
    {
        $id = input('param.id');

        if(empty($id)) $id = $this->id;
        $res = $detail = Db::name($this->name)->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input('post.');
            // if (!empty($this->verify)) {
            //     foreach ($this->verify as $k => $v) {
            //         if (empty($post[$k])) json_fail_admin($v);
            //     }
            // }

            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if (is_array($v)) {
                        if (
                            (isset($post['link_source']) &&  in_array($post['link_source'],[AppAdvService::LINK_SOURCE_BAISON,AppAdvService::LINK_SOURCE_GAME_RECHARGE]) && $k == 'url') ||  //特殊处理，百胜链接为空
                            (isset($post['belong_area']) && $post['belong_area'] == 'baison_wallet_adv')  //特殊处理，游戏钱包广告允许为空
                        ) {
                            $post['url']='';
                            continue;
                        }
                        if((isset($post['link_source']) &&  in_array($post['link_source'],[AppAdvService::LINK_SOURCE_GAME]) && $k == 'url')){
                            $post['url']='kumimi://home/<USER>';
                        }
                        $res = preg_match($v['rule'], $post[$k]);
                        if (!$res) json_fail_admin($v['message']);
                    } else {
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }

            foreach ($this->edit_default as $k => $v) {
                if (!isset($post[$v])) {
                    $post[$v] = '';
                }
            }

            $tableStructure = array_column(Db::query('SHOW COLUMNS FROM hg_' . $this->name), null, 'Field') ?? [];

            if (!empty($tableStructure['update_time']) && $tableStructure['update_time']['Type'] !== 'timestamp') {
                $post['update_time'] = time();
            }

            if($this->name=='category_type_list'){
                $post['operator_user_id2']=$this->user['id'];
                $post['operator_user_name2']=$this->user['username'];
                $post['last_operator_time2']=date('Y-m-d H:i:s');
            }else{
                $post['operator_user_id']=$this->user['id'];
                $post['operator_user_name']=$this->user['username'];
                $post['last_operator_time']=date('Y-m-d H:i:s');
            }

            $post['update_user'] = $this->user['id'];

            if($this->name=='app_adv'){
                $post['operator_user_id'] =$this->user['id'];
            }
            if ($this->name=='vip'){
                $is_show_unlimit_count = is_numeric($post['is_show_unlimit_count'])?$post['is_show_unlimit_count']:0;
                $type_count=[
                    'ai_type_remove_clothes'=>is_numeric($post['ai_type_remove_clothes'])?$post['ai_type_remove_clothes']:0,
                    'ai_type_change_face'=>is_numeric($post['ai_type_change_face'])?$post['ai_type_change_face']:0,
                    'ai_type_movie'=>is_numeric($post['ai_type_movie'])?$post['ai_type_movie']:0,
                    'ai_type_diy_change_face'=>is_numeric($post['ai_type_diy_change_face'])?$post['ai_type_diy_change_face']:0,
                    'ai_type_draw'=>is_numeric($post['ai_type_draw'])?$post['ai_type_draw']:0,
                ];
                if($is_show_unlimit_count==1){
                    foreach ($type_count as $k=>&$v){
                        $v=0;
                    }
                }

                $post['ai_count_list']=json_encode(array_merge([
                    'ai_date'=>is_numeric($post['ai_date'])?$post['ai_date']:0,
                    'is_show_unlimit_count'=>$is_show_unlimit_count,
                ],$type_count),JSON_UNESCAPED_UNICODE);
            }

            if ($this->name == 'system_config') {
                if (!in_array($this->user['role_id'], [\app\admin\model\AdminRole::ROLE_SUPER_ADMIN, \app\admin\model\AdminRole::ROLE_ADMIN])) {
                    json_fail_admin('只有超级管理员或管理员角色才能进行此操作!');
                }
            }
            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);
            if ($this->name == 'system_config') {
                $aes = new EasyAes("mpDPvfxv1pwGs3Bnxt6i", 128, "################");
                $key = !empty($detail['key'])?$detail['key']:'';
                $this->addlog('修改'.$this->title, '修改的key为'.$key."  :  ".$aes->encrypt(json_encode(['修改前' => $detail, '修改后' => $post], JSON_UNESCAPED_UNICODE)));
            }else{
                $this->addlog('修改数据','表名: '.$this->name.' 数据ID: '.$id);
            }

            if ($this->name == 'vip_info') {
                redis()->del('vip_info');
            }
            if ($this->name == 'app_tips') {
                redis()->del('app_home_tips');
                redis()->del('app_home_tips_list');
            }

            if($this->name == 'app_version'){
                if(trim($detail['url']) != trim($post['url'])){
                    TelegramBot::sendMessageTextEscape(sysconf('business_note_chat_id'),TelegramBot::makePHPError([
                        'from' =>sysconf('app_name'),
                        'subject' => '修改更新版本链接通知',
                        'date' => date('Y-m-d H:i:s'),
                        'content' =>  <<<text

                            修改者 : {$this->user['username']}
                            修改前 : {$detail['url']}
                            修改后 : {$post['url']}
                            text,
                    ]));
                }
            }

            cache('app_all_adv',null);
            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = !empty($this->role_name)? config("role.{$this->role_name}"):config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
				unset($role['icon']);
				unset($role['is_pc_top']);
				unset($role['is_pc_middle']);
			}

            if ($this->name=='vip'){
                $ia_count_list = json_decode($res['ai_count_list'],true);
                $res['ai_date']=$ia_count_list['ai_date']??0;
                $res['ai_type_remove_clothes']=$ia_count_list['ai_type_remove_clothes']??0;
                $res['ai_type_change_face']=$ia_count_list['ai_type_change_face']??0;
                $res['ai_type_movie']=$ia_count_list['ai_type_movie']??0;
                $res['ai_type_diy_change_face']=$ia_count_list['ai_type_diy_change_face']??0;
                $res['ai_type_draw']=$ia_count_list['ai_type_draw']??0;
                $res['is_show_unlimit_count']=$ia_count_list['is_show_unlimit_count']??0;
            }

            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if (isset($v['type']) && $v['type'] == 'class_list') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title']??'分类',
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'select') {
                        $this->class_list['0'] = '请选择';
                        ksort($this->class_list);
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title'],
                                'type' => $v['type'],
                                'value' => $this->class_list,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => $v,
                        ];
                    }
                }

                if(!in_array(input('param.belong_area'),['z_top','z_under','dy_top','dy_under','movie_top','movie_middle'])){
                    if(isset($arr['restriction']))unset($arr['restriction']);
                }

            }

            foreach ($arr as $k1=>$v1){
                $arr[$k1]['is_show']=1;
                if(!empty($v1['role']['show_condition'])){
                    foreach ($v1['role']['show_condition'] as $k2=>$v2){
                        if(isset($arr[$k2]['value']) && !$arr[$k2]['value']==$v2 ){
                            $arr[$k1]['is_show']=0;
                        }
                    }
                }
                if ($this->name=='vip' && $k1=='vip_info_ids'){

                    $vip_info_data_temp = Db::name('vip_info')->field(['title','id'])->select();
                    $vip_info_data=[];
                    foreach ($vip_info_data_temp as $k=>$v){
                        $vip_info_value = explode(',',$v1['value'])??[];
                        $vip_info_data[]=[
                            'name'=>$v['title'],
                            'value'=>$v['id'],
                            'selected'=> in_array($v['id'],$vip_info_value),
                        ];
                    }
                    $arr[$k1]['value']=$vip_info_data;
                }

            }
            $this->assign([
                'res' => $arr,
                'is_parent' => $this->is_parent,
            ]);
            return view('/common/edit');
        }
    }
    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        if (!empty($this->files)) {
            $files = [];
            foreach ($this->files as $v) {
                $files[] = $res[$v];
            }
            delPathList($files);
        }
        if ($this->name == 'vip_info') {
            redis()->del('vip_info');
        }
        if ($this->name == 'app_tips') {
            redis()->del('app_home_tips');
            redis()->del('app_home_tips_list');
        }
        $res = Db::name($this->name)->delete($id);
        $this->addlog('删除数据','表名: '.$this->name.' 数据ID: '.$id);
        cache('app_all_adv',null);
        $res ? json_success_admin() : json_fail_admin();
    }

    public function status($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        if($this->status_one){
            if($res['status']!=1) Db::name($this->name)->where('id', '>', 0)->update(['status' => 2]);
        }

        // 标签管理增加已使用标签不允许被禁用
        if ($this->name == "app_tag" && $res["status"] == 1) {
            // 标签是否被使用
            $result = Db::connect('short_video')->table("xg_video_pool")->where("find_in_set(".$id.",tag_ids)")->find();
            if ($result) {
                json_fail_admin("标签已被视频使用，不能禁用！");
            }
            $result = Db::connect('short_video')->table("xg_video_user_tags")->where("find_in_set(".$id.",tag_ids)")->find();
            if ($result) {
                json_fail_admin("标签已被用户画像使用，不能禁用！");
            }
        }elseif ($this->name=="turntable" && $res["status"] == 2){
            $detail = Db::name("turntable")->where(['id'=>$id])->find();

            $turntable_repeat = Db::name("turntable")->whereNull('delete_time')->where([
                ['end_time','>=',$detail['start_time']],
                ['start_time','<=',$detail['end_time']],
                ['status','=',CommonConstHelper::STATUS_OPEN],
                ['id','<>',$id],
            ])->field('id')->find();

            if(!empty($turntable_repeat)){
                json_fail_admin("不同活动，活动时间不可重叠");
            }
        }
        $res = Db::name($this->name)->where($where)->exp('status', 'if(status=1,2,1)')->update();

        $detail = Db::name($this->name)->where($where)->find();
        $update_log_data=[];
        if(isset($detail['operator_user_id']))$update_log_data['operator_user_id']=$this->user['id'];
        if(isset($detail['last_operator_time']))$update_log_data['last_operator_time']=date('Y-m-d H:i:s');
        if(isset($detail['operator_user_name']))$update_log_data['operator_user_name']=$this->user['username'];

        if(!empty($update_log_data)){
            Db::name($this->name)->where($where)->update($update_log_data);
        }

        if (!$res) {
            json_fail_admin();
        }
        if ($this->name == 'app_tips') {
            redis()->del('app_home_tips');
            redis()->del('app_home_tips_list');
        }
        $this->addlog('数据状态','表名: '.$this->name.' 数据ID: '.$id);
        cache('app_all_adv',null);
        json_success_admin();
    }

}
