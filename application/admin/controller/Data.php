<?php
namespace app\admin\controller;

use think\Db;
/**
 * 观影点赞次数增长配置
 *
 */
class Data extends BaseController
{
    private $basicId = 1;
    public function initialize()
    {
        parent::initialize();
    }
    
    /**
     * 基础配置
     */
    public function beauty() {


        if ($this->request->isGet()) {

            return view('beauty');
            
        }else{    
            $post = $this->request->post();
            if ($post['init_num_start'] && $post['init_num_end'] && $post['init_num_start'] > $post['init_num_end'])    {
                json_success_admin('初始观影值设置错误!');
            }
            if ($post['init_rate_start'] && $post['init_rate_end'] && $post['init_rate_start'] > $post['init_rate_end'])    {
                json_success_admin('初始点赞率设置错误!');
            }
            if ($post['after_rate_start'] && $post['after_rate_end'] && $post['after_rate_start'] > $post['after_rate_end'])    {
                json_success_admin('后续点赞率设置错误!');
            }
            foreach ($post as $key => $vo) {
                sysconf($key, $vo);
            }


            $this->addlog('观影点赞增长配置','');
            json_success_admin('配置成功!');

        }


        // 查询数据
        $where = [
            ["id", "=", $this->basicId],
            ["status", "=", 1]
        ];
        $data = Db::name("data_beauty")->where($where)->find();
        if (request()->isPost()) {
            if (! $data) json_fail_admin('配置文件不存在！');
            $post = input('post.');
            $update = $post;
            $update['update_time'] = time();
            $update['update_user'] = $this->user['id'];
            $res = Db::name('data_beauty')->where($where)->update($update);
            if (! $res) json_fail_admin('更新失败！');
            \Cache::set('data_update_mark', '于'.date('Y-m-d H:i:s').'修改成功');
            json_success_admin();
        }
        // 整理
        $updateMark = \Cache::get('data_update_mark', '');
        if ($updateMark) \Cache::rm('data_update_mark');
        $this->assign([
            "data" => $data,
            "update" => $updateMark
        ]);

        $this->addlog('数据状态','表名: data_beauty'.' 数据ID: '.$this->basicId);
        
        return view();
    }
    
    
}

