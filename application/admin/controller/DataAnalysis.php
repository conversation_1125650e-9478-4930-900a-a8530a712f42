<?php
namespace app\admin\controller;

use app\Service\AppMovieService;
use app\Service\PrizeService;
use think\Db;
/**
 * 数据分析
 *
 */
class DataAnalysis extends BaseController
{
    private $url = "/admin/data_analysis";

    /**
     * 数据汇总
     */
    public function total(string $view_path='') {

        $start = input("request.start", null, "trim");
        $end = input("request.end", null, "trim");
        $channel = input("request.channel", null, "trim");
        $orderby = input("request.orderby", 'id', "trim");

        $channelType = input("request.channel_type", null, "trim");
        $channelAll = Db::name('channel')->distinct(true)->field('channelid')->select();

        // 默认当天
        $start = $start ? $start : date('Y-m-d');
        $end = $end ? $end : date('Y-m-d');


        $channelType = $channelType ? 1 : 0;

        // 获取数据
        $list = $this->getData($start, $end, $channel, $channelType, $channelAll,1,$orderby);

        // 汇总
        $total = [
            "total_app" => 0,
            "total_reg" => 0,
            'recharge' => 0,
            'first_recharge' => 0,
            'all_recharge' => 0,
            'all_first_recharge' => 0,
        ];
        foreach ($list as $item) {
            $total["total_app"] += $item["install"];
            $total["total_reg"] += $item["reg"];
            $total["recharge"] += $item["recharge"];
            $total["first_recharge"] += $item["first_recharge"];
            $total["all_recharge"] += $item["all_recharge"];
            $total["all_first_recharge"] += $item["all_first_recharge"];
        }


        // 显示页面
        $this->assign([
            "baseUrl" => $this->url,
            "list" => $list,
            "channelAll" => $channelAll,
            "start" => $start,
            "end" => $end,
            "orderby" => $orderby,
            "channel" => $channel,
            "channelType" => $channelType,
            "total" => $total
        ]);
        return view($view_path);
    }

    /**
     * 数据汇总(特殊处理，这里显示包含ai订单等外部订单金额和首冲)
     */
    public function all_total() {
        return $this->total('data_analysis/all_total');
    }

    /**
     * 渠道月数据
     */
    public function channel() {
        $start = input("request.start", null, "trim");
        if (!$start) {
            $start = date('Y-m');
        }
        $nstart = date('Ym',strtotime($start));

        $channelid = input("request.channelid", null, "trim");
        $db = Db::name('channel')->where('month',$nstart);
        if ($channelid) {
            $db->where('channelid','like',"%{$channelid}%");
        }
        $sum = [];
        $sum['install'] = $db->sum('install');
        $sum['reg'] = $db->sum('reg');
        $sum['recharge'] = $db->sum('recharge') / 100;
        $sum['recharge_member'] = $db->sum('recharge_member');
        $sum['first_recharge'] = $db->sum('first_recharge') / 100;
        $sum['first_recharge_member'] = $db->sum('first_recharge_member');

        $channel = $db->order('install desc')->select();

        if(!empty($_GET['is_export']) && $_GET['is_export']==1){
            $export_data=[];
            foreach ($channel as $k=>$v){
                $export_data[]=[
                    'month' => $v['month'],
                    'channelid' => $v['channelid'],
                    'install' => $v['install'],
                    'reg' => $v['reg'],
                    'first_recharge_member' => $v['first_recharge_member'],
                    'first_recharge' => $v['first_recharge'] / 100,
                    'recharge_member' => $v['recharge_member'],
                    'recharge' => $v['recharge'] / 100,
                    'active' => $v['active'],
                    'active_visitor' => $v['active_visitor'],
                    'up' => $v['up'],
                    'comment' => $v['comment'],
                ];
            }

            $title = ['月份','渠道','月安装','月注册','月首充人数','月首充总额','月充值人数','月充值总额','月活跃人数(注册)','月活跃人数(游客)','月点赞人数','月评论人数'];
            $filename = '渠道月数据';
            $this->excel_data($filename,$title,$export_data);
            exit;
        }

        $this->assign([
            "baseUrl" => $this->url,
            "data" => $channel,
            "start" => $start,
            "channelid" => $channelid,
            'sum' => $sum
        ]);
        return view();
    }
    /**
     * 渠道月数据(本月注册)
     */
    public function channel_month() {
        $start = input("request.start", null, "trim");
        if (!$start) {
            $start = date('Y-m');
        }
        $nstart = date('Ym',strtotime($start));

        $channelid = input("request.channelid", null, "trim");
        $db = Db::name('channel')->where('month',$nstart);
        if ($channelid) {
            $db->where('channelid','like',"%{$channelid}%");
        }
        $sum = [];
        $sum['month_pay'] = $db->sum('month_pay');
        $sum['month_vip'] = $db->sum('month_vip');
        $sum['month_movie'] = $db->sum('month_movie');
        $sum['month_recharge'] = $db->sum('month_recharge') / 100;
        $sum['month_vip_recharge'] = $db->sum('month_vip_recharge') / 100;
        $sum['month_movie_recharge'] = $db->sum('month_movie_recharge') / 100;

        $channel = $db->field('id,channelid,month,month_pay,month_vip,month_movie,month_recharge,month_vip_recharge,month_movie_recharge')->order('install desc')->select();
        $this->assign([
            "baseUrl" => $this->url,
            "data" => $channel,
            "start" => $start,
            "channelid" => $channelid,
            'sum' => $sum
        ]);
        return view();
    }

    /**
     * 数据导出
     */
    public function export() {
        // 获取数据
        $start = input("request.start", null, "trim");
        $end = input("request.end", null, "trim");
        $channel = input("request.channel", null, "trim");
        $orderby = input("request.orderby", 'id', "trim");

        $channelType = input("request.channel_type", null, "trim");

        // 默认当天
        $start = $start ? $start : date('Y-m-d');
        $end = $end ? $end : date('Y-m-d');


        $channelType = $channelType ? 1 : 0;

        $type = input("request.type", null, "trim");


        $channelAll =  Db::name('channel')->select();

        $data_temp = $this->getData($start, $end, $channel, $channelType, $channelAll, 2,$orderby);

        if (! $data_temp) {
            json_fail_admin("无数据可供导出！");
        }

        if($type==1){
            $title_data = [
                'date'=>'日期',
                'channelid'=>'渠道名称',
                'install'=>'今日安装',
                'reg'=>'今日注册',
                'reg_a'=>'今日注册(安卓)',
                'order_count'=>'支付拉单数',
                'order_count_success_rate'=>'支付成功率',
                'first_pay'=>'今日首充人数',
                'first_recharge'=>'今日首充总额',
                'all_pay'=>'今日充值人数',
                'recharge'=>'今日充值总额',
                'click_ob'=>'点击OB人数',
                'active'=>'日活人数(注册)',
                'active_visitor'=>'日活人数(游客)',
                'up_member'=>'点赞人数',
                'comment_member'=>'评论人数',
                'total_install'=>'累计安装',
                'total_reg'=>'累计注册',
            ];

        }elseif($type==2){
            $title_data = [
                'date'=>'日期',
                'channelid'=>'渠道名称',
                'install'=>'今日安装',
                'reg'=>'今日注册',
                'reg_a'=>'今日注册(安卓)',
                'all_first_pay'=>'今日首充人数',
                'all_first_recharge'=>'今日首充总额',
                'all_all_pay'=>'今日充值人数',
                'all_recharge'=>'今日充值总额',
                'click_ob'=>'点击OB人数',
                'active'=>'日活人数(注册)',
                'active_visitor'=>'日活人数(游客)',
                'up_member'=>'点赞人数',
                'comment_member'=>'评论人数',
                'total_install'=>'累计安装',
                'total_reg'=>'累计注册',
            ];
        }

        $data = [];
        foreach ($data_temp as $k=>$v){
            foreach ($title_data  as $k1=>$v1){
                foreach ($v as $k2=>$v2){
                    if($k1==$k2){
                        if(isset($title_data[$k2]))$data[$k][$k2]=$v2;
                    }
                }
            }
        }


        $title = array_values($title_data);

        require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // 表头
        foreach ($title as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
        }
        // 写入内容
        $row = 2;
        foreach ($data as $item) {
            $column = 1;
            foreach ($item as $value) {
                $sheet->setCellValueByColumnAndRow($column, $row, $value);
                $column ++;
            }
            $row ++;
        }
        // 输出 下载内容
        $filename = '数据统计('. $start . '至' . $end . ')';
        ob_end_clean();
        ob_start();
        header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');
        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }


    /**
     * 获取查询数据
     * @param string $start
     * @param string $end
     * @param string $channel
     * @param number $channelType
     * @param array $channelAll
     * @param number $type
     * @return array
     */
    private function getData($start, $end, $channel, $channelType = 0, $channelAll, $type = 1,$orderby = '') {
        // echo $orderby;exit;
        // 日期格式
        $pattern = "/^20[0-9]{2}\-[0|1][0-9]\-[0-3][0-9]$/";
        $sql = "select date,channelid,install,reg,total_install,total_reg,first_pay,round(first_recharge/100,2) first_recharge,all_pay,round(recharge/100,2) recharge,all_first_pay,round(all_first_recharge/100,2) all_first_recharge,all_all_pay,round(all_recharge/100,2) all_recharge,click_ob,active,active_visitor,up_member,comment_member,order_count,order_count_success,round(order_count_success/order_count*100,2) order_count_success_rate,install_a,reg_a from hg_channel_day where 1=1";

        $params = [];
        if (preg_match($pattern, $start) && preg_match($pattern, $end) && $end >= $start) {
            $sql .= " and date>=? and date<=? ";
            $params[] = str_replace("-", "", $start);
            $params[] = str_replace("-", "", $end);
        } else {
            // 无日期不查询
            return [];
        }
        // 渠道支持单个模糊查询及多个模糊查询，多个以英文逗号分割
        if ($channel) {
            $temp = explode(",", $channel);
            $str = "";
            // 区分精准查询和模糊查询
            if ($channelType) {
                // 模糊查询
                $relation = "like";
            } else {
                // 精准查询
                $relation = "=";
            }
            foreach ($temp as $item) {
                if (! $item) {
                    continue;
                }
                $str .= $str ? " or lower(channelid) {$relation} ? " : " lower(channelid) {$relation} ? ";
                if ($relation == "like") {
                    $params[] = "%" . strtolower($item) . "%";
                } else {
                    $params[] = strtolower($item);
                }
            }
            if ($str) {
                $sql .= " and (" . $str . ") ";
            }
        }

        //这里特殊私人定制化需求，非管理员账号，不得展示指定渠道的列表数据
        if($this->user['role_id']!=1){
            $sql.=' and ( channelid NOT LIKE "OS%" OR channelid NOT LIKE "BE%" ) ';
        }

        $sql .= "group by date,channelid ";
        if ($orderby) {
            $sql .= 'order by '.$orderby.' desc';
        }else{
            $sql .= 'order by id desc';
        }

        $list = Db::query($sql, $params);
        foreach ($list as $key => $item) {
            $list[$key]['date'] = date('Y-m-d', strtotime($item['date']));

        }
        // 返回数据
        return $list;
    }

    /**
     * 获取渠道配置
     * @param array $array
     * @param string $channel
     * @param number $type
     */
    private function getChannelNumber($array, $channel, $type = 1) {
        $temp = [];
        foreach ($array as $item) {
            if ($item["channel"] == $channel) {
                $temp = $item;
                break;
            }
        }
        if (empty($temp)) {
            return 0;
        } else {
            if ($type == 1) {
                return isset($item['total']) ? $item['total'] / 100 : 0;
            } else {
                return isset($item['price']) ? $item['price'] / 100 : 0;
            }
        }
    }
    /**
     * 渠道分享
     */
    public function mychannel()
    {
        $post = input('param.');

        $days = $post['days'] ?? 7;

        $mychannels = explode(',',$this->user['channels']);

        $dates = [];
        for ($i=0; $i < $days; $i++) {
            $dates[] = date('Ymd',strtotime('-'.$i.' day'));
        }
        $dates = array_reverse($dates);
        $mindate = $dates[0];
        //->where('channel','in',$mychannels)
        $list = Db::name('channel_day')->where('date','>',$mindate)->where('channelid','in',$mychannels)->field('id,date as query_date,channelid as channel,install as day_app,reg as day_reg,total_install as total_app,total_reg')->select();

        $data = [];

        foreach ($mychannels as $c) {
            $data[$c]['day_app'] = [];
            $data[$c]['day_reg'] = [];
            $data[$c]['total_app'] = [];
            $data[$c]['total_reg'] = [];
        }
        foreach ($list as $key => &$value) {
            $value['query_date'] = date('Y-m-d',strtotime($value['query_date']));
            $data[$value['channel']]['day_app'][$value['query_date']] = $value['day_app'];
            $data[$value['channel']]['day_reg'][$value['query_date']] = $value['day_reg'];
            $data[$value['channel']]['total_app'][$value['query_date']] = $value['total_app'];
            $data[$value['channel']]['total_reg'][$value['query_date']] = $value['total_reg'];

            foreach ($dates as $k => &$v) {
                $v= date('Y-m-d',strtotime($v));
               if (!isset($data[$value['channel']]['day_app'][$v])) {
                   $data[$value['channel']]['day_app'][$v] = 0;
               }
               if (!isset($data[$value['channel']]['day_reg'][$v])) {
                   $data[$value['channel']]['day_reg'][$v] = 0;
               }
               if (!isset($data[$value['channel']]['total_app'][$v])) {
                   $data[$value['channel']]['total_app'][$v] = 0;
               }
               if (!isset($data[$value['channel']]['total_reg'][$v])) {
                   $data[$value['channel']]['total_reg'][$v] = 0;
               }
            }

        }

        foreach ($data as $key => &$value) {

            foreach ($value as &$v) {

                foreach ($dates as &$d) {
                    $d= date('Y-m-d',strtotime($d));
                    if (!isset($v[$d])) {
                        $v[$d] = 0;
                    }
                }
                ksort($v);
            }

            $value['channel'] = $key;
        }
        $this->assign([
            "mychannels" => $mychannels,
            'days' => $days,
            'dates' => $dates,
            'data' => $data,
            'current_url' => "/admin/data_analysis/mychannel",
            'today' => date('Y-m-d'),
            'yestoday' => date('Y-m-d',strtotime('-1 day')),
            'qtday' => date('Y-m-d',strtotime('-2 day')),
        ]);

        if (!request()->isPost()) {


            return view();
        }else{
            json_success_admin('请求成功!',[
                "mychannels" => $mychannels,
                'days' => $days,
                'dates' => $dates,
                'data' => $data,
                'current_url' => "/admin/data_analysis/mychannel",
                'today' => date('Y-m-d'),
                'yestoday' => date('Y-m-d',strtotime('-1 day')),
                'qtday' => date('Y-m-d',strtotime('-2 day')),
            ]);
        }
    }

    /**
     * 渠道视频分类统计(行为)
     */
    public function channel_classify() {

        $condition['start'] = input("request.start", null, "trim")??date('Y-m-d');
        $condition['end'] = input("request.end", null, "trim")??date('Y-m-d');
        $condition['channel'] = input("request.channel", null, "trim");
        $orderby = input("request.orderby", 'id', "trim");

        $channelType = input("request.channel_type", null, "trim");
        $channelAll = Db::name('channel')->distinct(true)->field('channelid')->select();

        $where=[];
        if(!empty($condition['channel'])){
            if($channelType==1){
                $where[] = ['channelid', 'IN', explode(',',$condition['channel'])];
            }else{
                $where[] = ['channelid', '=', $condition['channel']];
            }
        }
        if (!empty($condition['start'])) $where[] = ['date', '>=', $condition['start']];
        if (!empty($condition['end'])) $where[] = ['date', '<=', $condition['end']];

        $category_list = Db::name('app_category')->where(['category_type'=>'movie'])->field("id,title")->select();
        $category_column = array_column($category_list,null,'id');

        $limit=20;
        if(!empty($_GET['is_export']) && $_GET['is_export']==1){
            $limit = 999999;
        }

        $list=Db::name("channel_classify")->field('*')->where($where)->order($orderby." desc")
            ->paginate($limit)->each(function($item, $key)use ($category_column){
                $item['category_name'] = $category_column[$item['category_id']]['title']??'';
                return $item;
            });

        $page = $list->render();

        if(!empty($_GET['is_export']) && $_GET['is_export']==1){
            if(!empty($list->toArray()['data'])){
                $list = $list->toArray()['data'];

                $export_data=[];
                foreach ($list as $k=>$v){
                    $export_data[]=[
                        'date'=>$v['date'],
                        'channelid'=>$v['channelid'],
                        'category_name'=>$v['category_name'],
                        'category_click_uv'=>$v['category_click_uv'],
                        'category_click_pv'=>$v['category_click_pv'],
                        'preview_uv'=>$v['preview_uv'],
                        'preview_pv'=>$v['preview_pv'],
                        'free_play_uv'=>$v['free_play_uv'],
                        'free_play_pv'=>$v['free_play_pv'],
                        'play_uv'=>$v['play_uv'],
                        'play_pv'=>$v['play_pv'],
                    ];

                }
                $title = ['日期','渠道名称','视频分类','分类点击人数','分类点击次数','试看播放人数','试看播放次数','免费播放人数','免费播放次数','播放人数','播放次数'];
                $filename = '导出渠道视频分类统计(行为)记录';
                $this->excel_data($filename,$title,$export_data);
                // 日志记录
                $this->addlog('导出渠道视频分类统计(行为)记录',json_encode(['before_data'=>[],'after_data'=>$where]));
                exit;
            }

            exit;
        }

        // 显示页面
        $this->assign([
            'page' => $page,
            "baseUrl" => $this->url,
            "list" => $list,
            "channelAll" => $channelAll,
            "orderby" => $orderby,
            "condition" => $condition,
            "channelType" => $channelType
        ]);
        return view();
    }


    /**
     * 充值归因
     */
    public function recharge_attribution() {

        $condition['start'] = input("request.start", null, "trim")??date('Y-m-d');
        $condition['end'] = input("request.end", null, "trim")??date('Y-m-d');
        $condition['channel'] = input("request.channel", null, "trim");
        $condition['movie_type'] = input('request.movie_type', null, "trim");
        $condition['category_ids'] = input('request.category_ids', null, "trim");

        $channelType = input("request.channel_type", null, "trim");
        $channelAll = Db::name('channel')->distinct(true)->field('channelid')->select();

        $where=[];
        if(!empty($condition['channel'])){
            if($channelType==1){
                $where[] = ['channelid', 'IN', explode(',',$condition['channel'])];
            }else{
                $where[] = ['channelid', '=', $condition['channel']];
            }
        }
        if (!empty($condition['start'])) $where[] = ['date', '>=', $condition['start']];
        if (!empty($condition['end'])) $where[] = ['date', '<=', $condition['end']];

        if(!empty($condition['movie_type'])){
            $movie_list = Db::name('app_movie')->where(['type'=>$condition['movie_type']])->field("id,title")->select();
            $movie_column = array_column($movie_list,'id');
            if(!empty($movie_column)){
                $where[] = ['movie_id', 'IN', $movie_column];
            }else{
                $where[] = ['id', '=', 0];
            }
        }

        if (!empty($condition['category_ids'])) {
            $movie_where=[];
            if($condition['category_ids']=='-1'){
                $movie_where[] = ['category_ids', 'IN', ['',null,0]];
            }else{
                $movie_where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$condition['category_ids']}',category_ids)")];
            }
            $movie_list = Db::name('app_movie')->where($movie_where)->field("id,title")->select();
            $movie_column = array_column($movie_list,'id');
            if(!empty($movie_column)){
                $where[] = ['movie_id', 'IN', $movie_column];
            }else{
                $where[] = ['id', '=', 0];
            }
        }


        $condition['movie_type'] = input('request.movie_type');

        $movie_list = Db::name('app_movie')->select();
        $movie_column = array_column($movie_list,null,'id');

        $category_list = Db::name('app_category')->where(['category_type'=>'movie'])->field("id,title")->select();
        $category_column = array_column($category_list,null,'id');

        $app_category = Db::name('app_category')->field('id,title,pinyin')->where('category_type', '=', 'movie')->order('pinyin asc')->select();

        $limit=20;
        if(!empty($_GET['is_export']) && $_GET['is_export']==1)$limit=999999999;


        $list=Db::name("channel_recharge_attr")->field('*')->where($where)->order("movie_id desc")
            ->paginate($limit)->each(function($item, $key)use ($category_column,$movie_column){
                $category_ids = $movie_column[$item['movie_id']]['category_ids']??'';

                $movie_category_names='';
                if(!empty($category_ids)){
                    foreach (explode(',',$category_ids) as $k=>$v){
                        if(!empty($v)){
                            $movie_category_names .= ($category_column[$v]['title']??'').' ，';
                        }

                    }
                    $movie_category_names=rtrim($movie_category_names,'，');
                }


                $item['movie_category_names'] = $movie_category_names;
                $item['movie_type_name'] = AppMovieService::getConstPluck('TYPE_')[$movie_column[$item['movie_id']]['type']??'']??'';
                $item['movie_title'] = $movie_column[$item['movie_id']]['title']??'';
                $item['recharge'] = $item['recharge']/100;
                return $item;
            });

        $page = $list->render();

        if(!empty($_GET['is_export']) && $_GET['is_export']==1){
            if(!empty($list->toArray()['data'])){
                $list = $list->toArray()['data'];

                $export_data=[];
                foreach ($list as $k=>$v){
                    $export_data[]=[
                        'date'=>$v['date'],
                        'channelid'=>$v['channelid'],
                        'movie_type_name'=>$v['movie_type_name'],
                        'movie_category_names'=>$v['movie_category_names'],
                        'movie_id'=>$v['movie_id'],
                        'movie_title'=>$v['movie_title'],
                        'recharge_count'=>$v['recharge_count'],
                        'recharge'=>$v['recharge'],
                    ];

                }
                $title = ['日期','渠道名称','视频权限类型','视频分类','视频id','视频名称','充值人数','充值金额'];
                $filename = '导出充值归因录';
                $this->excel_data($filename,$title,$export_data);
                // 日志记录
                $this->addlog('导出充值归因录',json_encode(['before_data'=>[],'after_data'=>$where]));
                exit;
            }

            exit;
        }


        // 显示页面
        $this->assign([
            'page' => $page,
            "baseUrl" => $this->url,
            "list" => $list,
            "channelAll" => $channelAll,
            "condition" => $condition,
            "channelType" => $channelType,
            'app_category' => $app_category,
            'movie_type_list' => AppMovieService::getConstSelect('TYPE_','id','title'),
        ]);
        return view();
    }


}

