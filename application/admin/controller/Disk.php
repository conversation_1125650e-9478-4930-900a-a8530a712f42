<?php
namespace app\admin\controller;

use think\Db;
use app\admin\validate\Disk as DiskValidate;
class Disk extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "disk";
        $this->title = "片单管理";
        $this->current_url = "/admin/disk";
    }

    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['title'] = $this->request->post('title','');
        $condition['status'] = $this->request->post('status','');

        if($condition['title']) $where[] = ['title', 'like', '%' . $condition['title'] . '%'];
        if (isset($condition['status']) && $condition['status'] !== "") $where[] = ['status', '=', $condition['status']];

        $list = Db::name($this->name)->where($where)->order('id desc')->paginate(20)->each(function ($item,$res){
            $idArr = explode(',',$item['video_id']);
            $like_num = Db::name('member_disk')->distinct(true)->where('disk_id',$item['id'])->count();
            $item['video_num'] = count($idArr);
            $item['like_num'] = $like_num;
            return $item;
        });

        $this->assign([
            'condition' => $condition,
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }


    public function add()
    {
        if($this->request->isPost())
        {
            $data = [];
            $validate = new DiskValidate();
            if(!$validate->check($this->request->post()))
            {
                json_fail_admin($validate->getError());
            }
            $result = Db::name($this->name)->where('title',$this->request->post('title'))->find();
            if($result) json_fail_admin("片单标题不能重复");

            $data['cover'] = $this->request->post('cover');
            if(empty($data['cover'])) json_fail_admin("封面不能为空");
            $data['title'] = $this->request->post('title');
            $data['video_id'] = str_replace("，",",",$this->request->post('video_id'));
            $data['detail'] = $this->request->post('detail');
            $data['sort'] = $this->request->post('sort');
            $data['status'] = $this->request->post('status');
            try {
                $id = Db::name($this->name)->insertGetId($data);
                $this->addlog('添加片单','片单ID: '.$id);

                json_success_admin();
            }catch (\Exception $e)
            {
                json_fail_admin($e->getMessage());
            }
        }else{
            return view();
        }

    }


    public function status($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        if ($res['status'] == 0) {
            $title = '启用';
        }else{
            $title = '禁用';
        }
        
        $res = Db::name($this->name)->where($where)->exp('status', 'if(status=1,0,1)')->update();
        $this->addlog($title.'片单状态','片单ID: '.$id);
        $res ? json_success_admin() : json_fail_admin();
    }



    public function edit($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');
        if($this->request->isPost())
        {
            $did = $this->request->post('did');
            $data = [];
            $validate = new DiskValidate();
            if(!$validate->check($this->request->post()))
            {
                json_fail_admin($validate->getError());
            }
            if($did != $id)
            {
                json_fail_admin("id参数不正确");
            }

            $data['title'] = $this->request->post('title');
            $data['cover'] = $this->request->post('cover');
            if (!$data['cover']) {
                json_fail_admin("封面不能为空");
            }
            $where = [
                ['title','=',$data['title']],
                ['id','<>',$id]
            ];
            $result = Db::name($this->name)->where($where)->find();
            if($result) json_fail_admin("片单标题不能重复");
            $data['video_id'] = str_replace("，",",",$this->request->post('video_id'));;
            $data['detail'] = $this->request->post('detail');
            $data['sort'] = $this->request->post('sort');
            $data['status'] = $this->request->post('status');
            try {
                Db::name($this->name)->where('id',$did)->update($data);
                $this->addlog('修改片单','片单ID: '.$id);

                json_success_admin();
            }catch (\Exception $e)
            {
                json_fail_admin("更新失败,".$e->getMessage());
            }
        }else{
            $this->assign([
                'res' => $res,
            ]);
            return view();
        }

    }


    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('视频不存在');
        try {
            Db::name($this->name)->where($where)->delete();
            $this->addlog('删除片单','片单ID: '.$id);

            //还要删除用的文件夹
            json_success_admin();
        }catch (\Exception $e)
        {
            json_fail_admin("删除失败,".$e->getMessage());
        }
    }


    public function disk_show(){
        if ($this->request->isGet()) {
            return view('disk_show');
        }else{      
            foreach ($this->request->post() as $key => $vo) {
                sysconf($key, $vo);
            }
            $this->addlog('片单开关', json_encode($this->request->post()));
            json_success_admin('片单开关配置成功!');
        }
    }
}