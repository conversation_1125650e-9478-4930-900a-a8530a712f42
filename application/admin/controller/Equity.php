<?php
namespace app\admin\controller;

use think\Db;
/**
 * 权益配置
 *
 */
class Equity extends BaseController
{
    private $basicId = 1;
    public function initialize()
    {
        parent::initialize();
    }

    /**
     * 基础配置
     */
    public function basic() {

//        echo json_encode(['100'=>1,'200'=>2]);die();

        // 查询数据
        $where = [
            ["id", "=", $this->basicId],
            ["status", "=", 1]
        ];
        $before_data = $data = Db::name("equity_basic")->where($where)->find();
        if ($data) {
            $data["day_sign_view_times"] = json_decode($data["day_sign_view_times"],true);
            $data["recharge_get_times"] = json_decode($data["recharge_get_times"],true);
            $data["transfer_ob_get_times"] = json_decode($data["transfer_ob_get_times"],true);
            $data["ai_vip_show_condition"] = json_decode($data["ai_vip_show_condition"],true);
            if(!isset($data["ai_vip_show_condition"]['ai_vip_show_condition']))$data["ai_vip_show_condition"]['ai_vip_show_condition']=0;
            if(!isset($data["ai_vip_show_condition"]['ai_vip_show_regist_time']))$data["ai_vip_show_condition"]['ai_vip_show_regist_time']='';
        } else {
            $data["day_sign_view_times"] = [];
            $data["recharge_get_times"] = [];
            $data["transfer_ob_get_times"] = [];
        }
        if (request()->isPost()) {
            if (! $data) json_fail_admin('配置文件不存在！');
            $mustKeys = [
//                'day_sign_view_times' => ['每日签到增送金币','length:1,80'],
//                'recharge_get_times' => ['充值赠送观影次数','length:5,255'],
//                'transfer_ob_get_times' => ['转入欧宝钱包赠送观影次数','length:5,255'],
                'visitor_long_view_times' => ['非会员每日长视频观影次数','number|length:1,10'],
//                'visitor_d_long_view_times' => ['游客每日长视频观影次数','number|length:1,10'],
                'visitor_d_s_view_times' => ['游客每日短视频观影次数','number|length:1,10'],
                'new_user_init_view_times' => ['新用户基础观影次数','number|length:1,10'],
                'direct_success_view_times' => ['填写推广码观影次数','number|length:1,10'],
                'code_success_view_times' => ['推广成功观影次数','number|length:1,10'],
                'code_success_vip_times' => ['推广成功vip时长','number|length:1,10'],
                'ai_clothing_count' => ['推广成功赠送AI去衣次数','number|length:1,10'],
                'day_game_view_times' => ['每日游戏观影次数','number|length:1,10'],
                'register_send_vip_times' => ['注册用户赠送VIP（天）','number|length:1,10'],
                'day_preview_times' => ['所有用户每日试看次数','number|length:1,4|min:0|max:9999'],
                'ai_remove_clothes_vip' => ['AI创作免费次数（VIP）(仅限AI去衣)','number|length:1,4|min:0|max:9999'],
                'ai_remove_clothes_svip' => ['AI创作免费次数（SVIP）(仅限AI去衣)','number|length:1,4|min:0|max:9999'],
                'ai_vip_show_condition' => ['AI会员卡显示条件','number|between:0,4'],
            ];
            $validate = [];
            foreach ($mustKeys as $key => $value) {
                $validate[$key . '|' . $value[0]] = 'require|' . $value[1];
            }
            $post = input('post.');
            $role = new \think\Validate($validate);
            if (! $role->check($post)) json_fail_admin($role->getError());
            $update = [];
            foreach ($post as $key => $value) {
                if (! key_exists($key, $mustKeys)) {
                    continue;
                }
                if ($key == 'day_sign_view_times' || $key == 'recharge_get_times' || $key == 'transfer_ob_get_times') {
                    $temp = json_decode($post[$key], true);
                    if (count($temp) <= 0) {
                        json_fail_admin('数据错误！');
                    }
                }

                if($post['ai_vip_show_condition']==4 && (empty($post['ai_vip_show_regist_time']) || !is_numeric($post['ai_vip_show_regist_time']) && $post['ai_vip_show_regist_time']>=1) ){
                    json_fail_admin('AI会员卡显示条件->注册时长必须大于1天');
                }

                $update[$key] = $post[$key];
            }

            if(!empty($update['ai_vip_show_condition'])){
                $update['ai_vip_show_condition']=json_encode([
                    'ai_vip_show_condition'=>$update['ai_vip_show_condition'],
                    'ai_vip_show_regist_time'=>$post['ai_vip_show_regist_time']??'',
                ]);
            }
            if(isset($update['ai_vip_show_regist_time']))unset($update['ai_vip_show_regist_time']);

            $update['update_time'] = time();
            $update['update_user'] = $this->user['id'];
            $res = Db::name('equity_basic')->where($where)->update($update);
            if (!$res) json_fail_admin('更新失败！');
            redis()->set('equity_basic_table',json_encode($update));

            // 更新原数据
            $updateApp = [
                // 原app配置同步修改
                'click_adv_view_times' => 0,  // 点击广告增加的观影次数
                'visitor_reg_view_times' => $update['visitor_long_view_times'], // APP端游客用户观影次数
                'pc_visitor_reg_view_times' => $update['visitor_long_view_times'], // PC端游客用户观影次数
                'username_reg_view_times' => 0, // 用户名注册增加的观影次数
                'bind_phone_view_times' => $update['new_user_init_view_times'], // 绑定手机增加的观影次数
                'reg_promo_view_times' => 0, // 注册并填写推广码增加的观影次数
                'save_promo_qrcode_view_times' => $update['direct_success_view_times'] // 保存推广码增加的观影次数
            ];
            $res = Db::name('app_config')->where('id', '=', 1)->update($updateApp);
            \Cache::set('visitor_video_clip_times',$update['visitor_d_s_view_times']);
            \Cache::set('equity_update_mark', '于'.date('Y-m-d H:i:s').'修改成功');
            $this->addlog('修改基础权益设置',json_encode(['before_data'=>$before_data,'after_data'=>$update]));

            json_success_admin();
        }
        // 整理
        $updateMark = \Cache::get('equity_update_mark', '');
        if ($updateMark) \Cache::rm('equity_update_mark');
        $this->assign([
            "data" => $data,
            "update" => $updateMark
        ]);
        return view();
    }


}

