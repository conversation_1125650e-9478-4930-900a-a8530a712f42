<?php
namespace app\admin\controller;

use think\Db;
use think\Controller;
use app\Aes;
use think\facade\Validate;

class File extends Controller
{


    /**
     * 上传图片
     */
    public function upload()
    {
        $file = request()->file("file");
        if (!$file) {
            json_fail_admin('请重新上传！');
        }
        $fileSize=1024*1024*2;
        $fileExt='jpg,gif,png,jpeg,jp2,webp,wbmp,avif';
        $v = Validate::make([
            'file' => "require|fileSize:{$fileSize}|fileExt:{$fileExt}",
        ], [
            'file.require' => '图片必传',
            'file.fileSize' => '图片大小最大2MB,节约带宽，人人有责！',
            'file.fileExt' => "图片格式仅支持:{$fileExt}",
        ]);

        if (!$v->check(['file'=>$file])) {
            json_fail_admin($v->getError());
        }


//        $image = \think\Image::open($file);
//        $path_in_folder = UPLOAD_PATH . '/uploads/'.uniqid();
//        $image->save($path_in_folder,null,100,false);

        $path_in_folder = UPLOAD_PATH . '/uploads/'.uniqid();
        move_uploaded_file($_FILES['file']['tmp_name'],$path_in_folder);
//        dump($_FILES);die();

//        $info = $file->rule('uniqid')->move(UPLOAD_PATH . '/uploads/');
//        dump($info);die();
//        $path_in_folder = UPLOAD_PATH . '/uploads/'.$info->getSaveName();

//        if(!in_array($info->getExtension(),['jpg','gif','png','jpeg','jp2','webp','wbmp','avif'])){
//            json_fail_admin('图片格式仅支持jpg,gif,png,jpeg,jp2,webp,wbmp,avif');
//        }

//        $data = uploadImgToMinio($path_in_folder);
        $data = uploadImgServer($path_in_folder);
        $info = getimagesize($path_in_folder);
        $width = $info[0];
        $height = $info[1];
        @unlink($path_in_folder);
        if ($data && $data['code'] == 0) {
            echo json_encode(['code'=>0,'msg'=>'success','data'=>['src'=> parse_url($data['data'], PHP_URL_PATH),'base64'=>readBase( $data['data'] ),'width'=>$width,'height' => $height]]);exit;
        }
        echo json_encode(['code'=>1,'msg'=>$data['message'],'data'=>['src'=>'','base64'=>'']]);exit;
    }

    /**
     * 酷米老的，废弃掉
     */
    public function uploadOld()
    {
        $file = request()->file("file");
        if (!$file) {
            json_fail_admin('请重新上传！');
        }

        $info = $file->rule('uniqid')->move(UPLOAD_PATH . '/uploads/');
        $path_in_folder = UPLOAD_PATH . '/uploads/'.$info->getSaveName();
        $data = uploadImgToOss($path_in_folder);
        $info = getimagesize($path_in_folder);
        $width = $info[0];
        $height = $info[1];
        @unlink($path_in_folder);
        if ($data && $data['code'] == 0) {
            echo json_encode(['code'=>0,'msg'=>'success','data'=>['src'=> $data['data'],'base64'=>readBase( $data['data'] ),'width'=>$width,'height' => $height]]);exit;
        }
        echo json_encode(['code'=>1,'msg'=>$data['message'],'data'=>['src'=>'','base64'=>'']]);exit;
    }
}
