<?php
namespace app\admin\controller;

use think\Db;
class HomeMovie extends CommonController
{
    public function __construct()
    {

        $conf = [
            'name' => "home_movie",
            'title' => "首页推荐视频",
            'current_url' => "/admin/home_movie",
        ];
        $this->where = [
            'id'=>'',
            'title' => 'like',
            'new' => '',
            'hot' => '',
            'rec' => '',
        ];

        parent::__construct($conf);
    }

    public function index()
    {
        $where = $condition = [];
        if(!empty($this->where)){
            foreach ($this->where as $key => $value) {
                $$key = input("{$key}");
                if (!empty($$key)) {
                    switch ($value) {
                        case 'like':
                            $where[] = ['h.'.$key, $value, '%' . $$key . '%'];
                            break;
                        case 'exp':
                            $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$$key}',$key)")];
                            break;
                        default:
                            $where[] = ['h.'.$key, '=', $$key];
                            break;
                    }
                }
                $condition[$key] = $$key;
            }
        }
        $list = Db::name($this->name)
            ->alias('h')
            ->join('hg_app_movie m','h.movie_id = m.id')
            ->field('h.*')
            ->where('m.status=1')
            ->where($where)
            ->order($this->order)
            ->paginate(20);
        $this->_assign();
        $this->assign([
            'list' => $list,
            'class_list' => $this->class_list,
            'class_list2' => $this->class_list2,
            'condition' => $condition,
        ]);
        return view("/{$this->name}/index");
    }



    public function add()
    {
        if($this->request->isPost())
        {
            $post = input('post.');
            if(!empty($post['movie_ids'])){

            }else{
                if(empty($post['movie_ids_'])){
                    json_fail_admin("请选择影片");
                }else{
                    $post['movie_ids'] = explode(",",$post['movie_ids_']);
                }
            }
            switch ($post['type']){
                case 1:
                    $type = "new";
                    break;
                case 2:
                    $type = "hot";
                    break;
                case 3:
                    $type = "rec";
                    break;
            }
            $padd = [];
            foreach ($post['movie_ids'] as $key=>$value){
                if($value){
                    $movie = Db::name("app_movie")->where('id',$value)->find();
                    $repeat = Db::name("home_movie")->where('movie_id',$value)->find();
                    if($repeat){
                        json_fail_admin("视频：".$movie['title']."已添加过");
                    }
                    $padd []= [
                        'movie_id'=>$value,
                        $type=>1,
                        'title'=>$movie['title'],
                        'create_time'=>time(),
                    ];
                }

            }
            Db::name("home_movie")->insertAll($padd);
            json_success_admin("添加成功");
        }else{
            $app_movie = Db::name("app_movie")->where('status',1)->field('id,title')->select();
            $this->assign([
                'app_movie'=>$app_movie,
            ]);
            return view();
        }

    }

    public function news($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        if($this->status_one){
            if($res['new']!=1) Db::name($this->name)->where('id', '>', 2)->update(['new' => 2]);
        }
        $res = Db::name($this->name)->where($where)->exp('new', 'if(new=1,2,1)')->update();
        if (!$res) {
            json_fail_admin();
        }

        json_success_admin();
    }

    public function hot($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        if($this->status_one){
            if($res['hot']!=1) Db::name($this->name)->where('id', '>', 0)->update(['hot' => 2]);
        }
        $res = Db::name($this->name)->where($where)->exp('hot', 'if(hot=1,2,1)')->update();
        if (!$res) {
            json_fail_admin();
        }

        json_success_admin();
    }

    public function rec($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        if($this->status_one){
            if($res['rec']!=1) Db::name($this->name)->where('id', '>', 0)->update(['rec' => 2]);
        }
        $res = Db::name($this->name)->where($where)->exp('rec', 'if(rec=1,2,1)')->update();
        if (!$res) {
            json_fail_admin();
        }

        json_success_admin();
    }
}