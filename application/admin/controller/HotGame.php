<?php
namespace app\admin\controller;

use app\Service\CategoryTypeListService;
use think\Db;
class HotGame extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '游戏管理',
            'name'=>'game_list',
            'role_name'=>'hot_game',
            'current_url'=>'/admin/hot_game',
        ];
        $id = input('id');

        if(!empty($id)){
            $detail = Db::name('game_list')->where(['id'=>$id])->find();
            if(!empty($detail)){
                $detail = Db::name('category_type_list')->where(['id'=>$detail['category_type_id']])->find();

                if(!empty($detail)){
                    $this->class_list[] = [
                        'id' => $detail['id'],
                        'title' => $detail['title'],
                    ];
                }

            }
        }

        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['is_hot'] = input('request.is_hot');
        $condition['title'] = input('request.title');
        $condition['status'] = 1;
        $condition['category_type_id'] = input('request.category_type_id');

        if (!empty($condition['id'])) $where[] = ['o.id','=', $condition['id']];
        if (!empty($condition['title'])) $where[] = ['o.title','LIKE', '%' .$condition['title'].'%' ];
        if (!empty($condition['status'])) {
            $where[] = ['o.status', '=',$condition['status']];
        }
//        if(isset($condition['is_hot']) && $condition['is_hot']!=='' ){
            $where[] = ['o.is_hot', '=',1];
//        }
        if(isset($condition['category_type_id']) && $condition['category_type_id']!=='' ){
            $where[] = ['o.category_type_id', '=',$condition['category_type_id']];
        }

        $game_type_list = CategoryTypeListService::getListColumn();

        $list = Db::name("game_list")->alias('o')->field('o.*')->where($where)->order("o.hot_sort desc , o.status asc");

        $list = $list ->paginate(20)->each(function($item, $key)use($game_type_list){
                $item['game_type_name']=$game_type_list[$item['category_type_id']]??'';
                return $item;
            });
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'game_type_list' => $game_type_list,
            'condition' => $condition,
            'page' => $page,
        ]);

        return view();
    }

}
