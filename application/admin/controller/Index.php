<?php
namespace app\admin\controller;

use think\Db;
class Index extends BaseController
{
	public function __construct()
	{
		parent::__construct();
        $this->assign(['user'=>$this->user]);
	}
    public function index()
    {
        $list = parent::get_menu();

    	$this->assign(['list'=>$list]);
        return view();
    }
    public function welcome()
    {
        var_dump(get_client_ip());
        var_dump(request()->ip());
    }
    public function clear()
    {
        json_success_admin("已清除缓存!");
    }
    public function change_password()
    {
        if(request()->isPost()){
            $model_name = 'admin_info';
            $pwd = input('post.pwd');
            $password = input('post.password');
            $password2 = input('post.password2');
            if(empty($pwd) || empty($password) || empty($password2)) json_fail_admin('参数错误!');
            $res = Db::name($model_name)->where('id','=',$this->user['id'])->find();
            if(empty($res)) json_fail_admin('用户不存在!');
            if(encrypt_password($pwd,$res['salt']) != $res['password']) json_fail_admin('原密码错误!');
            if($pwd == $password2) json_fail_admin('新密码不能和老密码相同!');
            if($password != $password2) json_fail_admin('两次密码不一致!');

            $res = Db::name($model_name)->where('id','=',$this->user['id'])->update(['password'=>encrypt_password($password,$res['salt'])]);
            if(!$res) json_fail_admin();
            session("adminX",null);

            $this->addlog('修改个人后台密码','');

            json_success_admin();
        }else{
            return view();
        }
    }
}
