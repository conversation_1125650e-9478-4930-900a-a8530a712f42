<?php
namespace app\admin\controller;

use app\Helper\CommonConstHelper;
use app\Helper\VideoHelper;
use app\Service\PrizeService;
use app\Service\IntegralGoodsService;
use think\Db;
class InnerM3u8 extends CommonController
{

    public function __construct()
    {
        $conf=[
            'title' => '',
            'name'=>'',
            'current_url'=>'',
        ];
        parent::__construct($conf);
    }

    public function index()
    {
        $url = input('get.url');
        $url = str_replace(CDN_URL_TAG,source_config()['h5_movie_url'],$url);

        $parse_url = parse_url($url);
        $m3u8Raw =  (new VideoHelper())->rebuildM3u8RawLinks(0, $parse_url['path'], $parse_url['scheme'].'://'.$parse_url['host']);
        header('Access-Control-Allow-Origin: *');
        header("Content-Type: video/vnd.mpegurl");

        return $m3u8Raw??'';

    }





}
