<?php
namespace app\admin\controller;

use app\Helper\CommonConstHelper;
use app\Service\PrizeService;
use app\Service\IntegralGoodsService;
use think\Db;
class Integral extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '',
            'name'=>'',
            'current_url'=>'',
        ];
        parent::__construct($conf);
    }

    /**
     * 配置
     * @return \think\response\View|void
     */
    public function exchange_set()
    {
        if ($this->request->isGet()) {
            $this->assign([
                'detail' => json_decode(sysconf("exchange_set"),true),
            ]);
            return view();
        }else{
            $data =  $this->request->post();
            foreach ($data as $k=>$v){
                if(!is_numeric($v)){
                    json_fail_admin('请填入整数');
                }
            }

            sysconf('exchange_set', json_encode([
                'gold_to_integral'=>$data['gold_to_integral']??0, //金币积分兑换配置
                'buy_movie_send_integral'=>$data['buy_movie_send_integral']??0,  //购买视频金币赠送积分比例
            ]));

            $this->addlog('金币积分兑换配置', json_encode($this->request->post()));
            json_success_admin('配置成功!');
        }

    }


}
