<?php
namespace app\admin\controller;

use app\Helper\CommonConstHelper;
use app\Service\PrizeService;
use app\Service\IntegralGoodsService;
use think\Db;
class IntegralConsumeRecord extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '用户积分消费记录',
            'name'=>'integral_consume_record',
            'current_url'=>'/admin/integral_consume_record',
        ];

        parent::__construct($conf);
    }

    public function index()
    {
        $where = [];
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['member_id'] = input('request.member_id');
        $condition['order_no'] = input('request.order_no');
        $condition['channel'] = $channel = input("request.channel", null, "trim");



        if (!empty($condition['begin_time'])) $where[] = ['o.create_time', '>=', $condition['begin_time'] . ' 00:00:00'];
        if (!empty($condition['end_time'])) $where[] = ['o.create_time', '<=', $condition['end_time'] . ' 23:59:59'];
        if (!empty($condition['member_id'])) $where[] = ['o.member_id', '=', $condition['member_id']];
        if (!empty($condition['order_no'])) $where[] = ['o.order_no', '=', $condition['order_no']];
        if(!empty($condition['channel']))$where[] = ['o.channelid', 'IN', explode(',',$condition['channel'])];

        $integral_goods_list = Db::name('integral_goods')->field(['title,id,type'])->select();
        $integral_goods_column=[];
        if(!empty($integral_goods_list)){
            $integral_goods_column = array_column($integral_goods_list,null,'id');
        }

        $list = Db::name("integral_consume_record")->alias('o')->field('o.*')->where($where)->order("o.id desc")
            ->paginate($this->index_export?999999:10)->each(function($item, $key)use ($integral_goods_column){
                $item['integral_goods_name']=$integral_goods_column[$item['integral_goods_id']]['title']??'未知';
                $item['integral_goods_type_name']=IntegralGoodsService::getConstPluck("TYPE_")[$integral_goods_column[$item['integral_goods_id']]['type']??0]??'未知';
                $item['consume_integral']=$item['consume_integral']/100;
                $item['consume_gold']=$item['consume_gold']/100;
                return $item;
            });

        if($this->index_export){
            $list = $list->toArray()['data'];
            // 日志记录
            $this->addlog($this->title.'导出',json_encode(['before_data'=>[],'after_data'=>$condition]));

            $export_data=[];
            foreach ($list as $k=>$v) {
                $export_data[] = [
                    'order_no' => $v['order_no'],
                    'channelid' => $v['channelid'],
                    'member_id' => $v['member_id'],
                    'integral_goods_name' => $v['integral_goods_name'],
                    'integral_goods_type_name' => $v['integral_goods_type_name'],
                    'consume_integral' => $v['consume_integral'],
                    'consume_gold' => $v['consume_gold'],
                    'num' => $v['num'],
                    'create_time' => $v['create_time'],
                ];
            }

            $title = ['订单号','所属渠道','用户ID','商品名称','商品分类','兑换积分','兑换金币','兑换数量','兑换时间'];
            $filename = $this->title;
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $page = $list->render();

        $count_find = Db::name("integral_consume_record")->alias('o')->fieldRaw('count(DISTINCT o.member_id) distinct_member_count ,sum(consume_integral) sum_consume_integral ,sum(consume_gold) sum_consume_gold ')->where($where)->find();

        $count_find['sum_consume_integral']=$count_find['sum_consume_integral']/100;
        $count_find['sum_consume_gold']=$count_find['sum_consume_gold']/100;

        $original_current_url=$this->current_url;
        $this->current_url=$this->current_url.'?'.http_build_query($_GET);

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'count_find' => $count_find,
            'original_current_url' => $original_current_url,
            "channel" => $channel,
            'channelAll'=> Db::name('channel')->distinct(true)->field('channelid')->select(),
        ]);

        return view(input('request.is_user_detail')==1?'user_detail':'');
    }

    public function index_export(){
        $this->index_export=true;
        $this->index();
    }




}
