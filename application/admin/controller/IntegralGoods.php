<?php
namespace app\admin\controller;

use app\common\Constants\CommonConst;
use app\Helper\CommonConstHelper;
use app\Service\PrizeService;
use app\Service\IntegralGoodsService;
use think\Db;
use think\facade\Validate;

class IntegralGoods extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '商城商品管理',
            'name'=>'integral_goods',
            'current_url'=>'/admin/integral_goods',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['title'] = input('request.title');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['valid_begin_time'] = input('request.valid_begin_time');
        $condition['valid_end_time'] = input('request.valid_end_time');
        $condition['type'] = input('request.type');
        $condition['status'] = input('request.status');

        if (!empty($condition['title'])) $where[] = ['o.title','LIKE', '%' .$condition['title'].'%' ];
        if (!empty($condition['begin_time'])) $where[] = ['o.create_time', '>=', $condition['begin_time'] . ' 00:00:00'];
        if (!empty($condition['end_time'])) $where[] = ['o.create_time', '<=', $condition['end_time'] . ' 23:59:59'];
        if (!empty($condition['valid_begin_time'])) $where[] = ['o.end_time', '>=', $condition['valid_begin_time'] . ' 00:00:00'];
        if (!empty($condition['valid_end_time'])) $where[] = ['o.end_time', '<=', $condition['valid_end_time'] . ' 23:59:59'];
        if (!empty($condition['status'])) $where[] = ['o.status', '=',$condition['status']];
        if (!empty($condition['type'])) $where[] = ['o.type', '=',$condition['type']];

        $list = Db::name($this->name)->alias('o')->field('o.*')->whereNull("delete_time")->where($where)->order("o.id desc")
            ->paginate(20)->each(function($item, $key){
                $item['type_name']=IntegralGoodsService::getConstPluck('TYPE_')[$item['type']]??'未知';
                $item['exchange_way_name']=IntegralGoodsService::getChangeType(intval($item['integral']),intval($item['gold']))[1];
                $item['all_num']=$item['inventory_num']+$item['real_sales'];
                $item['integral']=$item['integral']/100;
                $item['gold']=$item['gold']/100;
                return $item;
            });
        $page = $list->render();

//        $count_find = Db::name($this->name)->alias('o')->fieldRaw('count(DISTINCT o.member_id) distinct_member_count ,sum(amount) sum_amount,sum(consume_amount) sum_consume_amount ,sum(consume_gold_amount) sum_consume_gold_amount ')->where($where)->find();
//
//        $count_find['sum_consume_integral']=$count_find['sum_consume_integral']/100;
//        $count_find['sum_consume_gold']=$count_find['sum_consume_gold']/100;

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'type_list' => IntegralGoodsService::getConstSelect('TYPE_','id','title'),
            'page' => $page,
//            'count_find' => $count_find,
        ]);

        return view();
    }

    public function add(){
        if (!request()->isPost()) {
            $this->assign([
                'type_list' => IntegralGoodsService::getConstSelect('TYPE_'),
            ]);
            return view();
        } else {
            $post = input("post.");
            $post['title']=trim($post['title']);

            $v = Validate::make([
                'title' => 'require',
                'type' => 'require',
                'front_cover_url' => 'require',
                'end_time' => 'require',
                'inventory_num' => 'require',
            ], [
                'title.require' => '商品名称必填',
                'type.require' => '商品分类必选',
                'front_cover_url.require' => '封面必传',
                'end_time.require' => '有效时间必填',
                'inventory_num.require' => '库存数必填',
            ]);

            if (!$v->check($post)) {
                json_fail_admin($v->getError());
            }

            if($post['inventory_num']>9999)json_fail_admin("库存数最大9999");

            if($post['type']==IntegralGoodsService::TYPE_VIP[0] && empty($post['number'])){
                json_fail_admin("商品分类vip时，vip兑换天数必填");
            }

            $post['integral']=(!empty($post['integral'])?$post['integral']:0)*100;
            $post['gold']=(!empty($post['gold'])?$post['gold']:0)*100;

            if(empty($post['integral']) && empty($post['gold'])){
                json_fail_admin("兑换所需积分或金币至少填一个");
            }
            $post['status']=CommonConst::ENABLED[0];

            $restar = Db::name($this->name)->where('title',$post['title'])->whereNull('delete_time')->find();
            if($restar){
                json_fail_admin("商品名称不能重复");
            }

            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            $this->addlog('添加积分商城商品','积分商城商品ID:'.$res.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        }
    }


    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->whereNull('delete_time')->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input("post.");
            $post['title']=trim($post['title']);

            $v = Validate::make([
                'title' => 'require',
                'type' => 'require',
                'front_cover_url' => 'require',
                'end_time' => 'require',
                'inventory_num' => 'require',
            ], [
                'title.require' => '商品名称必填',
                'type.require' => '商品分类必选',
                'front_cover_url.require' => '封面必传',
                'end_time.require' => '有效时间必填',
                'inventory_num.require' => '库存数必填',
            ]);


            if (!$v->check($post)) {
                json_fail_admin($v->getError());
            }

            if($post['inventory_num']>9999)json_fail_admin("库存数最大9999");


            if($post['type']==IntegralGoodsService::TYPE_VIP[0] && empty($post['number'])){
                json_fail_admin("商品分类vip时，vip兑换天数必填");
            }

            $post['integral']=$post['integral']*100;
            $post['gold']=$post['gold']*100;

            if(empty($post['integral']) && empty($post['gold'])){
                json_fail_admin("兑换所需积分或金币至少填一个");
            }

            $restar = Db::name($this->name)->where('title',$post['title'])->where('id','<>',$id)->whereNull('delete_time')->find();
            if($restar){
                json_fail_admin("商品名称不能重复");
            }

            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);

            $this->addlog('修改积分商城商品','积分商城商品ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        } else {

            if(!empty($res)){
                $res['integral']=$res['integral']/100;
                $res['gold']=$res['gold']/100;
            }

            $this->assign([
                'res' => $res,
                'type_list' => IntegralGoodsService::getConstSelect('TYPE_'),
            ]);
            return view();
        }
    }

    /**
     * 批量修改库存
     * @return \think\response\View|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function bath_update_inventory(){
        if (!request()->isPost()) {
            return view();
        } else {
            $post = input("post.");
            $v = Validate::make([
                'ids' => 'require',
                'inventory_num' => 'require',
            ], [
                'ids.require' => '商品id必选',
                'inventory_num.require' => '库存数必选',
            ]);

            if (!$v->check($post)) {
                json_fail_admin($v->getError());
            }

            if($post['inventory_num']>9999)json_fail_admin("库存数最大9999");

            $res = Db::name($this->name)->whereIn('id',explode(',',$post['ids']))->update([
                'inventory_num'=>$post['inventory_num'],
            ]);
            $this->addlog('批量修改积分商城商品库存','积分商城商品ID:'.$post['ids'].',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        }
    }




}
