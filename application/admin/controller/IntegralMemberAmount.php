<?php
namespace app\admin\controller;

use app\Helper\CommonConstHelper;
use app\Service\PrizeService;
use app\Service\IntegralGoodsService;
use think\Db;
class IntegralMemberAmount extends CommonController
{

    public function __construct()
    {
        $conf=[
            'title' => '积分管理',
            'name'=>'integral_member_amount',
            'current_url'=>'/admin/integral_member_amount',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['begin_last_get_integral_time'] = input('request.begin_last_get_integral_time');
        $condition['end_last_get_integral_time'] = input('request.end_last_get_integral_time');
        $condition['member_id'] = input('request.member_id');
        $condition['channel'] = $channel = input("request.channel", null, "trim");

        if (!empty($condition['begin_time'])) $where[] = ['o.last_get_integral_time', '>=', $condition['begin_last_get_integral_time'] . ' 00:00:00'];
        if (!empty($condition['end_last_get_integral_time'])) $where[] = ['o.last_get_integral_time', '<=', $condition['end_last_get_integral_time'] . ' 23:59:59'];
        if (!empty($condition['member_id'])) $where[] = ['o.member_id', '=', $condition['member_id']];
        if(!empty($condition['channel']))$where[] = ['o.channelid', 'IN', explode(',',$condition['channel'])];

        $list = Db::name($this->name)->alias('o')->field('o.*')->where($where)->order("o.amount desc")
            ->paginate($this->index_export?999999:10)->each(function($item, $key){
                $item['amount']=$item['amount']/100;
                $item['consume_amount']=$item['consume_amount']/100;
                $item['consume_gold_amount']=$item['consume_gold_amount']/100;
                return $item;
            });

        if($this->index_export){

            $list = $list->toArray()['data'];

            // 日志记录
            $this->addlog('积分列表数据导出',json_encode(['before_data'=>[],'after_data'=>$condition]));

            $export_data=[];
            foreach ($list as $k=>$v) {
                $export_data[] = [
                    'id' => $v['id'],
                    'channelid' => $v['channelid'],
                    'member_id' => $v['member_id'],
                    'amount' => $v['amount'],
                    'consume_amount' => $v['consume_amount'],
                    'consume_gold_amount' => $v['consume_gold_amount'],
                    'last_consume_time' => $v['last_consume_time'],
                    'last_get_integral_time' => $v['last_get_integral_time'],
                ];
            }

            $title = ['ID','所属渠道','用户ID','积分数','积分消费数','金币消费数','消费时间','积分获取时间'];
            $filename = '积分列表';
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $page = $list->render();

        $count_find = Db::name($this->name)->alias('o')->fieldRaw('count(DISTINCT o.member_id) distinct_member_count ,sum(amount) sum_amount,sum(consume_amount) sum_consume_amount ,sum(consume_gold_amount) sum_consume_gold_amount')->where($where)->find();

        $count_find['sum_amount']=$count_find['sum_amount']/100;
        $count_find['sum_consume_amount']=$count_find['sum_consume_amount']/100;
        $count_find['sum_consume_gold_amount']=$count_find['sum_consume_gold_amount']/100;

        $original_current_url=$this->current_url;
        $this->current_url=$this->current_url.'?'.http_build_query($_GET);


        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'count_find' => $count_find,
            'original_current_url' => $original_current_url,
            "channel" => $channel,
            'channelAll'=> Db::name('channel')->distinct(true)->field('channelid')->select(),
        ]);

        return view();
    }

    public function index_export(){
        $this->index_export=true;
        $this->index();
    }


}
