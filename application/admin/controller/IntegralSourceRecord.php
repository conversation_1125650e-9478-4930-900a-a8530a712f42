<?php
namespace app\admin\controller;

use app\Helper\CommonConstHelper;
use app\Service\IntegralSourceRecordService;
use app\Service\PrizeService;
use app\Service\IntegralGoodsService;
use think\Db;
class IntegralSourceRecord extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '用户积分来源记录',
            'name'=>'integral_source_record',
            'current_url'=>'/admin/integral_source_record',
        ];
        parent::__construct($conf);
    }

    public function index()
    {
        $where = [];
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['member_id'] = input('request.member_id');

        if (!empty($condition['begin_time'])) $where[] = ['o.create_time', '>=', $condition['begin_time'] . ' 00:00:00'];
        if (!empty($condition['end_time'])) $where[] = ['o.create_time', '<=', $condition['end_time'] . ' 23:59:59'];
        if (!empty($condition['member_id'])) $where[] = ['o.member_id', '=', $condition['member_id']];

        $list = Db::name("integral_source_record")->alias('o')->field('o.*')->where($where)->order("o.id desc")
            ->paginate(10)->each(function($item, $key){
                $item['source_name']=IntegralSourceRecordService::getConstPluck('SOURCE_')[$item['source']]??'未知';
                $item['integral']=$item['integral']/100;
                return $item;
            });
        $page = $list->render();

        $this->current_url=$this->current_url.'?'.http_build_query($_GET);

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
        ]);

        return view();
    }


}
