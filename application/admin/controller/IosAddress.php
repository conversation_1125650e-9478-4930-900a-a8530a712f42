<?php
namespace app\admin\controller;

use think\Db;

class IosAddress extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "ios_address",
            'title' => "IOS发布",
            'current_url' => "/admin/ios_address",
        ];
        $this->where = [
        ];
        parent::__construct($conf);
    }


    public function add()
    {
        if (request()->isPost()) {
            $post = input("post.");
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        $res = preg_match($v['rule'],$post[$k]);
                        if(!$res) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }
            if (!empty($this->files)) {
                $file = $info = [];
                foreach ($this->files as $v) {
                    $file[$v] = request()->file("file_{$v}");
                }
                foreach ($this->files as $v) {
                    if (!empty($file[$v])) {
                        $info[$v] = $file[$v]->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png,gif'])->move(UPLOAD_PATH . UPLOAD_NAME . '/' . $this->name);
                        if (!$info[$v]) json_fail_admin($file[$v]->getError());
                        $post[$v] = UPLOAD_NAME . '/' . $this->name . '/' . $info[$v]->getSaveName();
                    }
                    // if (!$file[$v]) json_fail_admin("请选择上传{$v}的文件");
                    unset($post["file_{$v}"]);
                }
            }
            $post['create_time'] = time();
            $post['create_user'] = $this->user['id'];
            $res = Db::name($this->name)->insertGetId($post);

            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if(isset($v['type']) && $v['type'] == 'class_list'){
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => '',
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => '',
                            'role' => $v,
                        ];
                    }
                }
            }
            $this->assign([
                'res' => $arr,
                'current_url'=>$this->current_url,
            ]);
            return view();
        }
    }


    public function shengcheng(){
        $url = "http://".$_SERVER['HTTP_HOST']."/api/server/address/code/";
        $code = random(6,'upper');
        $url .= $code;
        json_success_admin("成功",$url);
    }




}