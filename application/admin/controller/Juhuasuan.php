<?php
namespace app\admin\controller;

use think\Db;
class <PERSON><PERSON>su<PERSON> extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '聚划算管理',
            'name'=>'juhuasuan',
            'current_url'=>'/admin/juhuasuan',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['id'] = input('request.id');
        $condition['title'] = input('request.title');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['status'] = input('request.status');

        if (!empty($condition['id'])) $where[] = ['o.id','=', $condition['id']];
        if (!empty($condition['title'])) $where[] = ['o.title','LIKE', '%' .$condition['title'].'%' ];
        if (!empty($condition['begin_time'])) $where[] = ['o.create_time', '>=', $condition['begin_time'] . ' 00:00:00'];
        if (!empty($condition['end_time'])) $where[] = ['o.create_time', '<', $condition['end_time'] . ' 23:59:59'];
        if (!empty($condition['status'])) {
            $where[] = ['o.status', '=',$condition['status']];
        }

        $list = Db::name("juhuasuan")->alias('o')->field('o.*')->whereNull("delete_time")->where($where)->order("o.id desc");

        if(!empty($_GET['is_export']) && $_GET['is_export']==1){

            // 日志记录
            $this->addlog('导出聚划算列表数据',json_encode(['before_data'=>[],'after_data'=>$where]));

            $list=$list->select();

            $export_data=[];
            foreach ($list as $v){
                $export_data[]=[
                    'id'=>$v['id'],
                    'title'=>$v['title'],
                    'price'=>$v['price'],
                    'real_sales'=>$v['real_sales'],
                    'sort'=>$v['sort'],
                    'create_time'=>$v['create_time'],
                    'status_name'=>$v['status']==1?'启用':'禁用',
                ];
            }

            $title = ['ID','标题','价格','实际售卖数量','排序（数值越大越靠前）','新增时间','状态'];
            $filename = '聚划算列表数据';
            $this->excel_data($filename,$title,$export_data);exit;
        }else{

            $list = $list ->paginate(20)->each(function($item, $key){
                    $item['synopsis_short'] = mb_substr($item['synopsis'],0,100).(strlen($item['synopsis'])>100?'.....':'');
                    return $item;
                });
            $page = $list->render();

        }

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
        ]);

        return view();
    }



    public function add(){
        if (!request()->isPost()) {
            return view();
        } else {
            $post = input("post.");
                    
            $post['title'] = trim($post['title']);
            if (!$post['title']) {
                json_fail_admin("活动标题不能为空");
            }
            $restar = Db::name("juhuasuan")->where('title',$post['title'])->whereNull('delete_time')->find();
            if($restar){
                json_fail_admin("活动标题不能重复");
            }

            if(!empty($post['movie_ids'])){
                $post['movie_ids']=implode(',',$post['movie_ids']);
            }

            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            $this->addlog('添加聚划算活动','聚划算活动ID:'.$res.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        }
    }


    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->whereNull('delete_time')->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input("post.");

            $post['title'] = trim($post['title']);
            if (!$post['title']) {
                json_fail_admin("活动标题不能为空");
            }

            $restar = Db::name($this->name)->where('title',$post['title'])->where('id','<>',$id)->whereNull('delete_time')->find();
            if($restar){
                json_fail_admin("活动标题不能重复");
            }

            if(!empty($post['movie_ids'])){
                $post['movie_ids']=implode(',',$post['movie_ids']);
            }

            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);

            $this->addlog('修改聚划算活动','聚划算活动ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        } else {

            $app_movie = Db::name("app_movie")->whereIn('id',explode(',',$res['movie_ids']))->field('id,title')->select();
            $this->assign([
                'res' => $res,
                'app_movies' => $app_movie,
            ]);
            return view();
        }
    }


    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');

        $res =  Db::name($this->name)->where('id', '=', $id)->update(['delete_time'=>date('Y-m-d H:i:s')]);

        $this->addlog('删除聚划算活动','活动id:'.$id);

        $res ? json_success_admin() : json_fail_admin();
    }

}