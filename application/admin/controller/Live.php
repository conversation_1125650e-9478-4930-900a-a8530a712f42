<?php
namespace app\admin\controller;

use think\Db;

class Live extends BaseController
{

    public function index(){
        $where = [];
        $condition['uid'] = input('request.uid');
        $condition['order_id'] = input('request.order_id');
        $condition['create_time'] = input('request.create_time');
        $condition['update_time'] = input('request.update_time');
        $condition['status'] = input('request.status');
        if (!empty($condition['uid'])) $where[] = ['uid','=', $condition['uid']];
        if (!empty($condition['order_id'])) $where[] = ['order_id', '=',$condition['order_id']];
        if (!empty($condition['create_time'])) $where[] = ['create_time', '>=', $condition['create_time']. ' 00:00:00'];
        if (!empty($condition['update_time'])) $where[] = ['create_time', '<=', $condition['update_time']. ' 23:59:59'];
        if ($condition['status'] > 0 ) {
            $where[] = ['status', '=',$condition['status']];
        }

        $list = Db::connect('short_video')->table("xg_transfer_record")->where($where)->order("id desc")->paginate(20);
        $km_money = Db::connect('short_video')->table("xg_transfer_record")->where($where)->sum('km_money');
        $zs_money = Db::connect('short_video')->table("xg_transfer_record")->where($where)->sum('zs_money') ;

        $page = $list->render();
        $list = $list->toArray();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => '/admin/live/index',
            'page' => $page,
            'km_money' => round($km_money,2),
            'zs_money' => $zs_money,
            'title' => '金币买钻石记录',
        ]);
        return view();
    }

    public function export_excel_live(){
        $where = [];
        $condition['uid'] = input('request.uid');
        $condition['order_id'] = input('request.order_id');
        $condition['create_time'] = input('request.create_time');
        $condition['update_time'] = input('request.update_time');
        $condition['status'] = input('request.status');
        if (!empty($condition['uid'])) $where[] = ['uid','=', $condition['uid']];
        if (!empty($condition['order_id'])) $where[] = ['order_id', '=',$condition['order_id']];
        if (!empty($condition['create_time'])) $where[] = ['create_time', '>=', $condition['create_time']. ' 00:00:00'];
        if (!empty($condition['update_time'])) $where[] = ['create_time', '<=', $condition['update_time']. ' 23:59:59'];
        if ($condition['status'] > 0 ) {
            $where[] = ['status', '=',$condition['status']];
        }
        $list = Db::connect('short_video')->table("xg_transfer_record")->where($where)->order("id desc")->select();

        $title = ['ID','订单号','用户名称','用户ID','金币订单金额','钻石订单金额','提交时间','成功时间','状态'];
        $filename = '金币买钻石记录';

        $this->excel_data($filename,$title,$list);exit;
    }
}