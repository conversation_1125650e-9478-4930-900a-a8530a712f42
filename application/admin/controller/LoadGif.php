<?php 
namespace app\admin\controller;

use think\Db;

class LoadGif extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->title = "彩蛋动图管理";
        $this->current_url = "/admin/load_gif";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }
    /**
     * 列表
     * @return \think\response\View
     */
    public function index() {
        // 设置搜索条件
        // $where = [];

        // $condition['name'] =  input('name', '', 'trim');
        // $condition['url'] =  input('url', '', 'trim');
        // if (!empty($condition['url'])) $where[] = ['url', '=', $condition['url']];
        // if (!empty($condition['name'])) $where[] = ['name', 'like', '%' . $condition['name'] . '%'];

        $list = Db::name('app_load_gif')->paginate(20);
        $page = $list->render();
        $list = $list->toArray();    
        foreach ($list['data'] as $key => &$value) {
            $value['img'] = get_host($value['img']);
        }     
        	  
        $this->assign([
            'list' => $list,
            'page' => $page,

        ]);
        $this->_assign();
        return view();
    }

    /**
     * 新增
     */
    public function add() {

        if (request()->isPost()) { 
            $data = input('post.');
            if (!$data['img']) {
                json_fail_admin('图片不能为空');
            }
            $insert = [
            	'name' => $data['name'],
            	'img' => $data['img'],
            	'addtime' => time(),
            ];
                	
            $re = Db::name('app_load_gif')->insertGetId($insert);
            $this->addlog('添加加载动图','数据ID:' .$re);
        	$re ? json_success_admin() : json_fail_admin();

        }
        return view('form');
    }



    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name('app_load_gif')->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
    
        $res = Db::name('app_load_gif')->delete($id);
        $this->addlog('删除加载动图','数据ID:' .$id);

        $res ? json_success_admin() : json_fail_admin();
    }
}