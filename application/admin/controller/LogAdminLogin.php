<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
class LogAdminLogin extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "log_admin_login";
        $this->title = "管理员登录日志";
        $this->current_url = "/admin/log_admin_login";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['username'] = input('request.username');
        if (!empty($condition['username'])) $where[] = ['b.username', 'like', '%' . $condition['username'] . '%'];

        $list = Db::name($this->name)->alias('a')->join('admin_info b','a.admin_id=b.id','left')->field('b.username,a.*')->where($where)->order('id desc')->paginate(20);
        $this->assign([
            'list' => $list,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }
    public function oplog()
    {
        $where = [];
        $condition['title'] = input('request.title');
        $condition['admin'] = input('request.admin');
        $condition['ip'] = input('request.ip');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        if (!empty($condition['title'])) $where[] = ['title', 'like', '%' . $condition['title'] . '%'];
        if (!empty($condition['admin'])) $where[] = ['admin', '=', $condition['admin']];
        if (!empty($condition['ip'])) $where[] = ['ip', '=', $condition['ip']];
        if (!empty($condition['begin_time'])) $where[] = ['time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['time', '<', strtotime($condition['end_time'])+60*60*24];
        $list = Db::name('admin_log')->alias('a')->where($where)->order('id desc')->paginate(20);
        $this->assign([
            'list' => $list,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }
}