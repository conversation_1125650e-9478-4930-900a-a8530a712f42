<?php
namespace app\admin\controller;

use app\common\util\GoogleAuthenticator;
use app\Service\AdminInfoService;
use think\captcha\Captcha;
use think\Controller;
use think\Db;

class Login extends Controller {
	static public $login_url = '/admin/login/index';
	public function index($pass = '') {
		$cache_sys_config = cache_sys_config();
		$this->assign([
            'cache_sys_config' => $cache_sys_config,
            'is_open_google_check' => 1,

        ]);
		return view();
	}
	public function logout() {
		session("adminX", null);
		$this->redirect('/admin/login');
	}
	static public function getuser() {
		$code = session("adminX");
		return json_decode(decode(base64_decode($code), 'userinfo'), true);
	}

	public function checklogin() {

		$post = input('post.');
        if(empty($post['is_bind_google']))$post['is_bind_google']=0;
		if (empty($post['username'])) {
			json_fail_admin('用户名不能为空!');
		}

		if (empty($post['password'])) {
			json_fail_admin('密码不能为空!');
		}

        $user = Db::name('admin_info')->where(['username' => $post['username']])->find();
        if (empty($user)) {
            json_fail_admin('用户不存在!');
        }

        if ($user['status'] != 1) {
            json_fail_admin('账号已经被禁用,请联系管理员!');
        }

        if ($user['password'] != encrypt_password($post['password'],$user['salt'])) {
            json_fail_admin('密码错误!');
        }

//        switch (sysconf('is_open_google_check')){
//
//            case 0:
//                $captcha = new Captcha();
//                if (!$captcha->check($post['verify_code'])) {
//                    json_fail_admin('验证码错误!');
//                }
//                break;
//            case 1:
                //谷歌验证码
                $google=new GoogleAuthenticator();
                if($user['is_bind_google_check']==AdminInfoService::IS_BIND_GOOGLE_CHECK_NO && $post['is_bind_google']!=1){
                    //生成验证秘钥
                    $secret=$google->createSecret();
                    //生成验证二维码 $username 需要绑定的用户名
                    $username=get_app_name().'运营后台-'.$user['username'];
                    $qrCodeUrl = $google->getQRCodeGoogleUrl($username, $secret);
                    //存入数据库
                    Db::name('admin_info')->where(['id' => $user['id'],'is_bind_google_check'=>AdminInfoService::IS_BIND_GOOGLE_CHECK_NO])->update(['google_secret'=>$secret]);
                    json_success_admin('请先绑定谷歌验证', [
                        'qrCodeUrl'=>$qrCodeUrl,
                    ]);
                }else{
                    // 误差时间，谷歌身份验证器30秒更换一次，这个表示最大的误差时间
                    $time = 2;//0表示实时

                    if(empty($post['google_code'])){
                        json_success_admin('请输入谷歌验证码');
                    }

                    // 获取验证结果 返回结果true，false
                    $checkResult = $google->verifyCode($user['google_secret'],$post['google_code'],$time);

                    $is_approved=false;
                    //允许非生产环境超级管理员账号方便开发直接绕过验证
                    if(env('APP_ENV')!='pro' && $user['id']==1 && $post['google_code']==666666 ){
                        $is_approved=true;
                    }

                    if(!$checkResult && !$is_approved){
                        json_fail_admin('谷歌验证码错误!');
                    }

                    Db::name('admin_info')->where(['id' => $user['id'],'is_bind_google_check'=>AdminInfoService::IS_BIND_GOOGLE_CHECK_NO])->update(['is_bind_google_check'=>AdminInfoService::IS_BIND_GOOGLE_CHECK_OK]);
                }
//                break;
//        }


		$ids = Db::name('admin_role_node')->where('role_id', '=', $user['role_id'])->column('node_id');
		$user['ids'] = implode(',', $ids);
		$paths = Db::name('admin_node')->where('id', 'in', $user['ids'])->column('path');
		$user['paths'] = strtolower('|' . implode('|', $paths) . '|');
		$user['paths'] = str_replace('_', '', $user['paths']);

		unset($user['password']);
		$login_id = Db::name('log_admin_login')->insertGetId([
			'admin_id' => $user['id'],
			'ip' => get_client_ip(),
			'addr' => get_addr(),
			'create_time' => time(),
		]);
		$user['login'] = 'ok';
		$user['login_id'] = $login_id;
        $user['is_bind_google_check']= AdminInfoService::IS_BIND_GOOGLE_CHECK_OK;
		$code = base64_encode(encode(json_encode($user), 'userinfo'));
		session("adminX", $code);
		json_success_admin('登录成功!', '/admin');

	}
	public function verify() {
		$config = array(
			'fontSize' => 30, // 验证码字体大小
			'length' => 4, // 验证码位数
			'useNoise' => false, // 关闭验证码杂点
			'codeSet' => '0123456789',
		);
		ob_clean();
		$captcha = new Captcha($config);
		return $captcha->entry();
	}
}
