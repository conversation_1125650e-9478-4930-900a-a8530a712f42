<?php


namespace app\admin\controller;
use think\Db;



class Marquee extends BaseController
{
    private $title, $current_url, $name, $error;
    public function initialize()
    {
        parent::initialize();
        $this->name = "video";
        $this->title = "跑马灯";
        $this->current_url = "/admin/marquee";
    }
    private function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    function index(){
        $where = [];
        $query   = Db::connect('short_video')->table("xg_marquee")
            ->where($where);
        $list  = $query->order('type')->paginate(20);
        $item  = $list->toArray();
        $data  = $item["data"];
        $this->assign([
            'list'      => $list,
            'condition' => '',
            'data'      => $data
        ]);
        $this->_assign();
        return view();
    }

    function del($id){
        if (!Db::connect('short_video')->table("xg_marquee")->where("id", $id)->delete()) {
            json_fail_admin('跑马灯不存在');
        }
        json_success_admin('删除成功');
    }

    function ad(){
        if (request()->isPost()) {
            $post = input("post.");
            if(isset($post['id']) && $post['id']) {
//                mb_strlen($post['msg'],'utf8')
                if(strlen($post['msg']) > 75){
                    json_fail_admin('标题不能超过25个字符！！！');
                }
                $id =     $post['id'] ;
                unset($post['id']) ;
                $s = Db::connect('short_video')->table("xg_marquee")->where("id", $id)->update($post);
                if($s){
                    json_success_admin('修改成功');
                }else{
                    json_fail_admin('修改失败！！！');
                }
            }else{
                if(strlen($post['msg']) > 75){
                    json_fail_admin('标题不能超过25个字符！！！');
                }
                $id = Db::connect('short_video')->table("xg_marquee")->insertGetId($post);
                if ($id) {
                    json_success_admin('添加成功！！！');
                } else {
                    json_fail_admin('添加失败！！！');
                }
            }
        }
        $data['type'] = 1;
        $data['jump_type'] = 0;
        $this->assign([
            'data'      => $data
        ]);
        $this->_assign();
        return view();
    }
    function  add(){
        $post = input("post.");
        if(strlen($post['msg']) > 75){
            json_fail_admin('标题不能超过25个字符！！！');
        }
        $data=[];
        $data['msg']=$post['msg'];
        $data['type']=$post['type'];
        $data['hits']=0;
        $data['url']=$post['url'];
//        $data['time']=$post['time'];
        $id = Db::connect('short_video')->table("xg_marquee")->insertGetId($data);
        json_success_admin('成功！！！'.$id);

    }

    function edit($id){
        $data = Db::connect('short_video')->table("xg_marquee")->where('id',$id)->find() ;
        $this->assign([
            'data'      => $data
        ]);
        $this->_assign();
        return view('ad');
    }


}
