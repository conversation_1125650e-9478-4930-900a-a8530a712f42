<?php
namespace app\admin\controller;

use app\Service\MemberAutoMsgService;
use app\Service\PrizeService;
use think\Db;
class MemberAutoMsg extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '自动站内信管理',
            'name'=>'auto_msg',
            'current_url'=>'/admin/member_auto_msg',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];

        $list = Db::name("auto_msg")->alias('o')->field('o.*')->where($where)->order("o.id asc")
            ->paginate(20)->each(function($item, $key){
                $item['msg_type_name']=MemberAutoMsgService::getConstPluck('TYPE_')[$item['id']]??'';
                $item['content_short'] = mb_substr($item['content'],0,100).(strlen($item['content'])>100?'.....':'');
                return $item;
            });
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'page' => $page,
        ]);

        return view();
    }


    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input("post.");

            $post['content'] = trim($post['content']);
            if (empty($post['content'])) {
                json_fail_admin("发送内容不能为空");
            }
            $post['title'] = trim($post['title']);
            if (empty($post['title'])) {
                json_fail_admin("标题不能为空");
            }
            
            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);

            $this->addlog('修改自动站内信','自动站内信ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            json_success_admin();
        } else {

            $this->assign([
                'res' => $res,
            ]);
            return view();
        }
    }

    public function status($id)
    {
        $where[] = ['id', '=', $id];
        $detail = Db::name($this->name)->where($where)->find();
        if (empty($detail)) json_fail_admin('数据不存在');
        if( $detail['status']!=1 && (empty($detail['title']) || empty($detail['content'])) ){
            json_fail_admin('开启配置必须先填写标题和内容');
        }
        Db::name($this->name)->where('id', '=', $id)->update(['status' => $detail['status']==1?2:1]);
        json_success_admin();
    }


}