<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
class MemberClickAdv extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_click_adv";
        $this->title = "用户点击记录";
        $this->current_url = "/admin/member_click_adv";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['account'] = input('request.account');
        $condition['title'] = input('request.title');
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if (!empty($condition['account'])) $where[] = ['b.account', 'like', '%' . $condition['account'] . '%'];
        if (!empty($condition['title'])) $where[] = ['c.title', 'like', '%' . $condition['title'] . '%'];
        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];

        $list = Db::name($this->name)->alias('a')
        ->join('member_info b', 'a.member_id=b.id','left')
        ->join('app_adv c', 'a.adv_id=c.id','left')
        ->field('a.*,b.account,b.is_visitor,c.title,c.url')
        ->where($where)->order('id desc')->paginate(20);
        $this->assign([
            'list' => $list,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if(empty($res)) json_fail_admin('用户不存在');
        $res = Db::name($this->name)->where($where)->delete();
        $this->addlog('删除用户点击广告记录','数据ID:' .$id);
        $res ? json_success_admin() : json_fail_admin();
    }
}
