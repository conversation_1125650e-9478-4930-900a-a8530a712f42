<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
class MemberComment extends BaseController
{
    public $title, $current_url, $name,$index_export;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_comment";
        $this->title = "用户评论记录";
        $this->current_url = "/admin/member_comment/index";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
        $this->assign("export_current_url", $this->current_url.'/index_export'.'?'.http_build_query($_GET).'&is_export=1');
    }

    public function index()
    {
        $where = [];
        $condition['account'] = input('request.account');
        $condition['title'] = input('request.title');
        $condition['content'] = input('request.content');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['channel'] = $channel = input("request.channel", null, "trim");

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d',strtotime('-3 months'));
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        $fid = input('param.fid');
        if(empty($fid)) {
            $fid = 0;
        }else{
            $this->current_url .= "/fid/".$fid;
        }
        $movie_id = input('param.movie_id');
        if (!empty($movie_id)) {
            $where[] = ['a.movie_id', '=', $movie_id];
            $this->current_url .= "/movie_id/" . $movie_id;
        }
        $where[] = ['a.fid', '=', $fid];
        if (!empty($condition['account'])) $where[] = ['b.account', 'like', '%' . $condition['account'] . '%'];
        if (!empty($condition['title'])) $where[] = ['c.title', 'like', '%' . $condition['title'] . '%'];
        if (!empty($condition['content'])) $where[] = ['a.content', 'like', '%' . $condition['content'] . '%'];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];
        if(!empty($condition['channel']))$where[] = ['a.channelid', 'IN', explode(',',$condition['channel'])];

        $list = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id', 'left')
            ->join('app_movie c', 'a.movie_id=c.id', 'left')
            ->field('a.*,b.account,b.is_visitor,c.title')
            ->where($where)->order('id desc')->paginate($this->index_export?999999:20)->each(function ($item, $key) {
                $item['reply_num'] = Db::name('member_comment')->where('fid', '=', $item['id'])->count();
                re_connect();
                return $item;
            });

        if($this->index_export){
            $list = $list->toArray()['data'];
            // 日志记录
            $this->addlog($this->title.'导出',json_encode(['before_data'=>[],'after_data'=>$condition]));

            $export_data=[];
            foreach ($list as $k=>$v) {
                $export_data[] = [
                    'id' => $v['id'],
                    'channelid' => $v['channelid'],
                    'account' => $v['account'],
                    'title' => $v['title'],
                    'content' => emoji_decode($v['content']),
                    'up_num' => $v['up_num'],
                    'reply_num' => $v['reply_num'],
                    'create_time' => date('Y-m-d H:i:s',$v['create_time']),
                ];
            }

            $title = ['ID','所属渠道','用户','视频名称','评论内容','点赞数','回复数','评论时间'];
            $filename = $this->title;
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $count_find = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id', 'left')
            ->join('app_movie c', 'a.movie_id=c.id', 'left')
            ->fieldRaw('count(DISTINCT a.member_id) distinct_member_count ,count(DISTINCT a.movie_id) distinct_movie_id_count')
            ->where($where)
            ->find();

        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'fid' => $fid,
            'movie_id' => $movie_id,
            "channel" => $channel,
            "count_find" => $count_find,
            'channelAll'=> Db::name('channel')->distinct(true)->field('channelid')->select(),
        ]);
        $this->_assign();
        return view();
    }
    public function del($id)
    {
        $ids = explode(',', $id);
        $where[] = ['id', 'in', $ids];

        $res = Db::name($this->name)->where($where)->field('id,movie_id')->select();

        foreach ($res as $key => $value) {

            $re = Db::name($this->name)->where(['id'=>$value['id']])->delete();  //删除评论

            Db::name('app_movie')->where('id', $value['movie_id'])->setDec('comment_num'); //电影自减一

            $subcount = Db::name($this->name)->where('fid', $value['id'])->delete(); //删除子评论

            Db::name('app_movie')->where('id', $value['movie_id'])->setDec('comment_num',$subcount); //评论数减去子评论数

        }
        $this->addlog('删除用户评论记录','数据ID:' .$id);
        json_success_admin();

    }

    public function index_export(){
        $this->index_export=true;
        $this->index();
    }
}
