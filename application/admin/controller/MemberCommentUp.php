<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
class MemberCommentUp extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_comment_up";
        $this->title = "评论点赞记录";
        $this->current_url = "/admin/member_comment_up";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index($id)
    {
        $list = Db::name($this->name)->alias('a')
        ->join('member_info b', 'a.member_id=b.id','left')
        ->join('member_comment c', 'a.comment_id=c.id','left')
        ->field('a.*,b.account,b.is_visitor')
        ->where('a.comment_id', '=', $id)->order('id desc')->paginate(20);
        $this->assign([
            'list' => $list,
        ]);
        $this->_assign();
        return view();
    }
}