<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class MemberFeedback extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_feedback";
        $this->title = "用户反馈列表";
        $this->current_url = "/admin/member_feedback";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['account'] = input('request.account');
        $condition['is_read'] = input('request.is_read');
        $condition['admin_is_read'] = input('request.admin_is_read');
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['feedback_type'] = input('request.feedback_type');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        if (!empty($condition['account'])) $where[] = ['b.account', 'like', '%' . $condition['account'] . '%'];
        if (!empty($condition['is_read'])) $where[] = ['a.is_read', '=', $condition['is_read']];
        if (!empty($condition['admin_is_read'])) $where[] = ['a.admin_is_read', '=', $condition['admin_is_read']];
        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['feedback_type'])) $where[] = ['a.feedback_type', '=', $condition['feedback_type']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];

        $class_list = config('api.feedback_type');
        $list = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id', 'left')
            ->field('a.*,b.account,b.is_visitor')
            ->where($where)->order('id desc')->paginate(20);
        $this->assign([
            'list' => $list,
            'class_list' => $class_list,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }
    public function status($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('消息不存在');
        $res = Db::name($this->name)->where($where)->exp('admin_is_read', 'if(admin_is_read=1,2,1)')->update();

        $res ? json_success_admin() : json_fail_admin();
    }
}
