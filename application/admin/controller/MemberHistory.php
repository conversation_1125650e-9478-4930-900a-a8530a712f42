<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
class MemberHistory extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_history";
        $this->title = "用户播放记录";
        $this->current_url = "/admin/member_history";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['account'] = trim(input('request.account'));
        $condition['title'] = trim(input('request.title'));
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        $movie_id = input('param.movie_id');
        if (!empty($movie_id)) {
            $where[] = ['a.movie_id', '=', $movie_id];
            $this->current_url .= "/index/movie_id/". $movie_id;
        }

//        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
//        if (!empty($condition['account'])) $where[] = ['b.account', 'like', '%' . $condition['account'] . '%'];
//        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];


        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];

        if (!empty($condition['title'])){
            $where_movie[] = ['title', 'like', '%' . $condition['title'] . '%'];
            $movie_column = Db::name('app_movie')->where($where_movie)->column('title','id');
            $movie_ids = array_keys($movie_column);
            $where[] = ['a.movie_id', 'IN', empty($movie_ids)?[0]:$movie_ids];
        }


        if (!empty($condition['account'])) {
            $where_member_info[] = ['account', '=',  $condition['account'] ];
            $member_column_list = Db::name('member_info')->where($where_member_info)->column('account,is_visitor','id');
//            dump($member_column_list);die();
            $use_ids = array_keys($member_column_list);
            $where[] = ['a.member_id', 'IN', empty($use_ids)?[0]:$use_ids];
        }


        $list = Db::name($this->name)->alias('a')
//        ->join('member_info b', 'a.member_id=b.id','left') b.account,b.is_visitor,
//            ->join('app_movie c', 'a.movie_id=c.id','left')
            ->field('a.*')
            ->where($where)->order('id desc')->paginate(20);

        $data = $list->items();

        if(!empty($data)){


            if(empty($movie_column)){
                $movie_ids = array_column($data,'movie_id');
                $movie_column = Db::name('app_movie')->whereIn('id',$movie_ids)->column('title','id');
            }


            if(empty($member_column_list)){
                $use_ids = array_column($data,'member_id');
//            dump($use_ids);die();
                $member_column_list = Db::name('member_info')->whereIn('id',$use_ids)->column('is_visitor,account','id');
            }

//            dump($member_column_list);die();
            foreach ($data as &$item) {
                $item['is_visitor'] = $member_column_list[$item['member_id']]['is_visitor']??1;
                $item['account'] = $member_column_list[$item['member_id']]['account']??'';
                $item['title'] = $movie_column[$item['movie_id']]??'';
            }
        }

        $this->assign([
            'list' => $list,
            'data' => $data,
            'condition' => $condition,
            'movie_id' => $movie_id,
        ]);
        $this->_assign();
        return view();
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if(empty($res)) json_fail_admin('用户不存在');
        $res = Db::name($this->name)->where($where)->delete();
        $this->addlog('删除观看长视频记录','数据ID:' .$id);

        $res ? json_success_admin() : json_fail_admin();
    }
}
