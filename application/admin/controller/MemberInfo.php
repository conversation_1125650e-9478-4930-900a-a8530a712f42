<?php
namespace app\admin\controller;

use app\Helper\MemberInfoHelper;
use app\Service\MemberInfoExtraService;
use app\Service\UserLoginService;
use think\Db;
use think\Validate;
use think\facade\Env;
use function Matrix\transpose;

class MemberInfo extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_info";
        $this->title = "用户列表";
        $this->current_url = "/admin/member_info";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $having = '';
        $condition['id'] = input('request.id');
        $condition['account'] = trim(input('request.account'));
        $condition['name'] = input('request.name');
        $condition['mobile'] = input('request.mobile');
        $condition['is_visitor'] = input('request.is_visitor',2);
        $condition['status'] = input('request.status');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['reg_promo_code'] = input('request.reg_promo_code');
        $condition['promo_code'] = input('request.promo_code');
        $condition['is_vip'] = input('request.is_vip');
        $condition['create_ip'] = input('request.create_ip');
        $condition['channelid'] = input('request.channelid');
        $condition['device_type'] = input('request.device_type');
        $condition['begin_expire_time'] = input('request.begin_expire_time');
        $condition['end_expire_time'] = input('request.end_expire_time');

        if (!empty($condition['id'])) $where[] = ['id', '=', $condition['id']];

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }


        if (!empty($condition['mobile'])) $where[] = ['mobile', '=', $condition['mobile']];
        if (!empty($condition['is_visitor'])) $where[] = ['is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['status'])) $where[] = ['status', '=', $condition['status']];
        if (!empty($condition['create_ip'])) $where[] = ['create_ip', '=', $condition['create_ip']];
        if (!empty($condition['begin_time'])) $where[] = ['create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['create_time', '<', strtotime($condition['end_time'])+60*60*24];
        if (!empty($condition['begin_expire_time'])) $where[] = ['expire_time', '>=', strtotime($condition['begin_expire_time'])];
        if (!empty($condition['end_expire_time'])) $where[] = ['expire_time', '<', strtotime($condition['end_expire_time'])+60*60*24];
        if (!empty($condition['reg_promo_code'])) $where[] = ['reg_promo_code', '=', $condition['reg_promo_code'] ];
        if (!empty($condition['promo_code'])) $where[] = ['promo_code', '=', $condition['promo_code']];
        if (!empty($condition['channelid'])) $where[] = ['channelid', '=', $condition['channelid']];
        if (!empty($condition['device_type'])) $where[] = ['device_type', '=', $condition['device_type']];
        if (!empty($condition['account'])) $where[] = ['account', '=', $condition['account']];
        if (!empty($condition['is_vip'])) {
            if($condition['is_vip']==1) {
                $where[] = ['expire_time', '<=', time()];
                $where[] = ['super_vip_expire_time', '<=', time()];
            }else if($condition['is_vip'] == 2) {
                $where[] = ['expire_time', '>', time()];
            }else if($condition['is_vip'] == 3) {
                $where[] = ['super_vip_expire_time', '>', time()];
            }
        }

        if (!empty($condition['name'])){
            $name = $condition['name'];
            $member_id = Db::name('member_ob_info')->whereLike('name',"%$name%")->value('member_id');
            if ($member_id) {
                $where[] = ['id', '=', $member_id];
            }else{
                $where[] = ['id', '=', 0];
            }
        }
        $where[] = ['is_deleted','=',1];
        $fields  = 'id,account,mobile,create_ip,is_visitor,status,create_addr,reg_promo_code,promo_code,device_id,create_time,channelid,expire_time,super_vip_expire_time,device_type,app_version';
        $list    = Db::name($this->name)
                       ->where($where)
                       ->order('id desc')
                       ->field($fields);

        if(!empty($_GET['is_export']) && $_GET['is_export']==1){

            // 日志记录
            $this->addlog('导出用户数据',json_encode(['before_data'=>[],'after_data'=>$where]));
//            $rolename=db::name('admin_role')->where('status',1)->where('id',$this->user['role_id'])->value('name');
//            if(in_array($rolename,['系统管理员','管理员'])){
//                json_fail_admin('数据导出中，请稍等');
//                echo "<script>alert('数据导出中，请稍等'); window.history.back(-1);  </script>";exit();
//            }

            if(count($where)<=2){
                json_fail_admin('禁止全部用户数据导出!');
//                echo "<script>alert('禁止全部用户数据导出!'); window.history.back(-1);  </script>";exit();
            }

            $list=$list->select();
//            dump($list);die();

            $export_data=[];
            foreach ($list as $k=>$v){
                $export_data[]=[
                    'id'=>$v['id'],
                    'account'=>$v['account'],
                    'mobile'=>dataDesensitization(strval($v['mobile']),3,4),
                    'expire_time'=>empty($v['expire_time'])?'':date('Y-m-d H:i:s',$v['expire_time']),
                    'super_vip_expire_time'=>empty($v['super_vip_expire_time'])?'':date('Y-m-d H:i:s',$v['super_vip_expire_time']),
                    'create_ip'=>$v['create_ip'],
                    'create_addr'=>$v['create_addr'],
                    'channelid'=>$v['channelid'],
                    'app_version'=>$v['app_version'],
//                    'reg_promo_code'=>$v['reg_promo_code'],
                    'promo_code'=>$v['promo_code'],
                    'device_id'=>$v['device_id'],
                    'create_time'=>date('Y-m-d H:i:s',$v['create_time']),
                ];
            }

            $title = ['ID','账号','手机号','VIP过期时间','超级VIP过期时间','注册IP','注册地址','渠道号','注册版本','推广码','设备码','加入时间'];
            $filename = '用户列表';
            $this->excel_data($filename,$title,$export_data);exit;
        }else{
            $remove_vip_log = Db::name('remove_vip_log')->field(['user_id'])->select();
            $remove_vip_log_column = array_column($remove_vip_log,'user_id','user_id');

            $list=$list->paginate(18)->each(function ($item)use ($remove_vip_log_column){
                $item['mobile'] = dataDesensitization(strval($item['mobile']),3,4);
                $item['remove_vip_name'] = !empty($remove_vip_log_column[$item['id']])?'是':'否';
                $item['device_type_name'] = MemberInfoHelper::DEVICE_TYPE[$item['device_type']]??'未知';
                return $item;
            });
        }

        $this->assign([
            'list' => $list,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }
    public function add()
    {
        if (!request()->isPost()) {
            return view();
        } else {
            $post = input('post.');
            if (empty($post['account'])) json_fail_admin('用户名不能为空!');
            if (empty($post['password'])) json_fail_admin('密码不能为空!');
            if ($post['password'] != $post['password2']) json_fail_admin('两次输入密码不一致!');
            $data = [
                'account' => $post['account'],
                'nick_name' => $post['account'],
                'is_visitor' => 2,
                'token' => create_token(),
                'password' => encode($post['password']),
                'create_time' => time(),
                'create_date' => date('Y-m-d'),
                'create_user' => $this->user['id'],
            ];
            $count = Db::name($this->name)->where('account', '=', $data['account'])->count();
            if ($count > 0) json_fail_admin('用户名重复!');

            $res = Db::name($this->name)->insertGetId($data);
            $this->addlog('添加用户','数据ID:' .$res);

            $res ? json_success_admin() : json_fail_admin();
        }
    }


    public function change_password($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        $res['nick_name'] = emoji_decode($res['nick_name']);

        if (empty($res)) json_fail_admin('用户不存在');
        if (!request()->isPost()) {
            return view();
        } else {
            $post = input('post.');
            if (empty($post['password'])) json_fail_admin('新密码不能为空!');
            if ($post['password'] != $post['password2']) json_fail_admin('两次输入密码不一致!');

            $data = [
                'password' => encode($post['password']),
                'update_time' => time(),
                'update_user' => $this->user['id'],
            ];

            $res = Db::name($this->name)->where($where)->update($data);
            $this->addlog('修改用户密码','数据ID:' .$id);

            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function edit($id)
    {
        $where[] = ['id', '=', $id];
        // 增加是否允许上传短视频信息
        $res = Db::name($this->name)
                   ->where($where)
                   ->find();

        if (empty($res)) json_fail_admin('用户不存在');
        $s = Db::connect('short_video')->table("xg_user_property")->where('user_id',$id)->find();
        if(!$s){
            $s = [
                "is_upload"  =>1,
                "is_comment" =>  1 ,
            ];
            $insert = [
                "is_update"  =>1,
                "is_comment" =>  1 ,
                "user_id" =>  $id ,
                "user_type" => 1,
            ];
           Db::connect('short_video')->table("xg_user_property")->insert($insert);
        }else{
            $s['is_upload'] = $s['is_update'] ;
        }
        $res = array_merge($res,$s);
        $res['nick_name'] = emoji_decode($res['nick_name']);

        if (!request()->isPost()) {
            $this->assign([
                'res' => $res,
            ]);
            return view();
        } else {
            $post = input('post.');
            $validate = Validate::make([
                'nick_name'=>'require|max:12|min:1'
            ],[
                'nick_name.require'=>'昵称必须',
                'nick_name.max'=>'昵称长度最大是12位',
                'nick_name.min'=>'昵称长度最小是1位',
                // 'nick_name.chsDash'=>'昵称只能是汉字，字母，数字，下划线或者-',
            ]);
            if (!$validate->check($post)) {
                json_fail_admin($validate->getError());
            }
            $data = [
                'is_comment' => $post['is_comment'],
                'nick_name' => emoji_encode($post['nick_name']),
                'day_view_times' => $post['day_view_times'],
                'today_view_times' => $post['today_view_times'],
                're_today_view_times' => $post['re_today_view_times'],
                'day_cache_times' => $post['day_cache_times'],
                'today_cache_times' => $post['today_cache_times'],
                're_today_cache_times' => $post['re_today_cache_times'],
//                'expire_time' => strtotime($post['expire_time']),
                'update_time' => time(),
                'update_user' => $this->user['id'],
                'avatar' => $post['avatar'],
            ];
            if (!empty($post['sex'])) $data['sex'] = $post['sex'];
            if (!empty($post['mobile'])) {
                $count = Db::name($this->name)->where([['id', '<>', $id], ['mobile', '=', $post['mobile']]])->count();
                if ($count > 0) json_fail_admin('手机号码重复');
                $data['mobile'] = $post['mobile'];
            }
            if (!empty($post['password'])) {
                $data['password'] = md5($post['password']);
            }

            // $file = request()->file("file_avatar");
            // if (!empty($file)) {
            //     $info = $file->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(UPLOAD_PATH . UPLOAD_NAME . '/' . $this->name);
            //     if (!$info) json_fail_admin($file->getError());
            //     delPathList([$res['avatar']]);
            //     $data['avatar'] = UPLOAD_NAME . '/' . $this->name . '/' . $info->getSaveName();
            // }
            if ($post["is_upload"] != 1 && $post["is_upload"] != 0) {
                json_fail_admin("是否上传短视频值有误");
            }
            Db::startTrans();
            try {
                $result = Db::name($this->name)->where("id", "=", $id)->strict(false)->update($data);
                if (! $result) {
                    throw new \Exception("修改用户信息失败！");
                }
                // 增加是否上传短视频
                if ($post["is_upload"] != $res["is_upload"] || $post["is_comment"] != $res["is_comment"]) {
                        // 修改
                        $update = [
                            "is_update"  => $post["is_upload"],
                            "is_comment" => $post["is_comment"] == 1 ? 1 : 0
                        ];
                        $result = Db::connect('short_video')->table("xg_user_property")->where("user_id", $res["user_id"])->update($update);
                    if (! $result) {
                        throw new \Exception("修改是否上传短视频值失败！");
                    }
                }
                Db::commit();
                $this->addlog('修改用户信息','数据ID:' .$id);
                json_success_admin();
            } catch (\Exception $e) {
                echo $e->getLine();
                Db::rollback();
                json_fail_admin("编辑失败：" . $e->getMessage());
            }
        }
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('用户不存在');
        if ($res['is_deleted'] == 0) json_fail_admin('用户已删除');

        $res = Db::name($this->name)->where($where)->update(['is_deleted'=>0]);
        if (!$res) {
            json_fail_admin('失败,请重试!');
        }
        $this->addlog('删除用户信息','数据ID:' .$id);
        json_success_admin();
        // $del = [$res['avatar'], $res['promo_qrcode']];
        // $res = Db::name($this->name)->where($where)->delete();
        // if ($res) {
        //     Db::name('member_cache')->where('member_id', '=', $id)->delete();
        //     Db::name('member_click_adv')->where('member_id', '=', $id)->delete();
        //     Db::name('member_comment')->where('member_id', '=', $id)->delete();
        //     Db::name('member_comment_up')->where('member_id', '=', $id)->delete();
        //     Db::name('member_exchange')->where('member_id', '=', $id)->delete();
        //     Db::name('member_feedback')->where('member_id', '=', $id)->delete();
        //     Db::name('member_history')->where('member_id', '=', $id)->delete();
        //     Db::name('member_like')->where('member_id', '=', $id)->delete();
        //     Db::name('member_message')->where('member_id', '=', $id)->delete();
        //     Db::name('member_movie_up_down')->where('member_id', '=', $id)->delete();
        //     Db::name('member_promo')->where('member_id', '=', $id)->delete();
        //     Db::name('member_promo')->where('to_member_id', '=', $id)->delete();
        //     Db::name('member_search')->where('member_id', '=', $id)->delete();
        //     Db::name('member_sms')->where('member_id', '=', $id)->delete();
        //     delPathList($del);
        //     $this->addlog('删除用户信息','数据ID:' .$id);

        //     json_success_admin();
        // } else {
        //     json_fail_admin();
        // }
    }

    public function status($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if ($res['status'] == 1) {
            $title = '禁用';
        }else{
            $title = '启用';
        }
        if (empty($res)) json_fail_admin('用户不存在');
        $res = Db::name($this->name)->where($where)->exp('status', 'if(status=1,2,1)')->update();
        $this->addlog($title.'用户','数据ID:' .$id);

        $res ? json_success_admin() : json_fail_admin();
    }

    public function is_comment($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if ($res['is_comment'] == 1) {
            $title = '关闭';
        }else{
            $title = '开启';
        }
        if (empty($res)) json_fail_admin('用户不存在');
        $res = Db::name($this->name)->where($where)->exp('is_comment', 'if(is_comment=1,2,1)')->update();
        $this->addlog($title.'用户评论','数据ID:' .$id);

        $res ? json_success_admin() : json_fail_admin();
    }

    public function details($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) die('用户不存在,可能该用户已被清理删除');
        $res['nick_name'] = emoji_decode($res['nick_name']);
        $res['amount'] = Db::name('member_amount')->where('member_id',$id)->value('amount');
        $res['recharge'] = Db::name('member_amount')->where('member_id',$id)->value('recharge');
        $res['ob_account'] = Db::name('member_ob_info')->where('member_id',$id)->value('name');
        $class_list = Db::name('app_level')->field('id,icon,code,grade,title')->where('status', '=', 1)->order('id desc')->select();
        if(!empty($res['expire_time'])){
            $res['expire_time']=date('Y-m-d H:i:s',$res['expire_time']);
        }else{
            $res['expire_time']='';
        }
        if(!empty($res['super_vip_expire_time'])){
            $res['super_vip_expire_time']=date('Y-m-d H:i:s',$res['super_vip_expire_time']);
        }else{
            $res['super_vip_expire_time']='';
        }
        $member_info_extra_detail =MemberInfoExtraService::getDetail(intval($id));
        $res['ai_vip_time'] = $member_info_extra_detail['ai_vip_time']??'';
        $res['unlimit_ai_vip_time'] = $member_info_extra_detail['unlimit_ai_vip_time']??'';

        $user_login_detail = Db::name('user_login')->where(['member_id'=>$id])->find();
        $res['last_app_version']='';
        $res['last_login_time']='';
        if(!empty($user_login_detail)){
            $res['last_app_version'] = $user_login_detail['last_app_version'];
            $res['last_login_time'] = date('Y-m-d H:i:s',$user_login_detail['last_login_time']);
        }

        $this->assign([
            'res' => $res,
            'class_list' => $class_list,
        ]);
        $this->_assign();
        return view();
    }

    public function change_review(){
        $condition = [];
        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if (!empty($condition['member_id'])) $where[] = ['member_id','=', $condition['member_id']];

        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'] . '23:59:59')];


        $list = Db::name('member_info_change')->where($where)->where('status',0)->order('addtime','desc')->paginate(20);
        $page = $list->render();
        $list = $list->toArray();

        foreach ($list['data'] as $key => &$value) {
            if ($value['type'] == 2) {
                $value['update_before'] = $value['update_before'];
                $value['update'] = $value['update'];
            }
        }
        $this->assign([
            'list' => $list,
            'page' => $page,
            'condition' => $condition,
        ]);
        $this->assign("current_url", '/admin/member_info/change_review');
        $this->assign("title", '用户信息调整审核');
        return view();
    }
    public function change_submit()
    {
        $ids = input('id');
        $type = input('type');

        if ($type != 1 && $type != 2) {
            json_fail_admin("非法操作");
        }
        $ids = array_filter(array_unique(explode(',',$ids)));
        $info = Db::name("member_info_change")->where('id','in',$ids)->select();
        $update_ids = [];
        foreach ($info as $key => $value) {
            if ($value['status'] != 0) {
                continue;
            }
            $update_ids[] = $value['id'];

            $tmp_info = "昵称";
            if ( $value['type'] == 2 ) {
                $tmp_info = "头像";
            }
            $info_title = "{$tmp_info}调整成功";
            $info_content = "尊敬的陌陌用户，您的{$tmp_info}已经调整成功，恭喜！";
            if ($type == 2) {  //拒绝
                $info_title = "{$tmp_info}调整失败";
                $info_content = "尊敬的陌陌用户，很抱歉地通知您，您的{$tmp_info}调整失败，请调整{$tmp_info}后重新发起申请。";
            }else{
                $update = [
                    'id' => $value['member_id']
                ];
                if ($value['type'] == 1) {
                    $update['nick_name'] = $value['update'];
                }else{
                    $update['avatar'] = $value['update'];
                }
                $sql = Db::name('member_info')->fetchsql()->update($update);
                pushsql($sql);
            }
            $infoData = [
                'member_id' => $value['member_id'],
                'message_type' => 1,
                'title' => $info_title,
                'content' => $info_content,
                'create_time' => time()
            ];
            $sql = Db::name('member_message')->fetchsql()->insert($infoData);
            pushsql($sql);
        }
        Db::name("member_info_change")->where('id','in',$update_ids)->update(['status'=>$type,'updatetime'=>time()]);
        $this->addlog('用户信息审核','审核用户信息'.implode(",",$update_ids));
        json_success_admin();

    }

    public function default_avatar()
    {
        $list = Db::name('member_default_avatar')->paginate(20);
        $page = $list->render();
        $list = $list->toArray();
//        foreach ($list['data'] as $key => &$value) {
//            $value['img'] = $value['img'];
//        }

        $this->assign([
            'list' => $list,
            'page' => $page,
            'current_url' => '/admin/member_info/add_avatar',
            'title' => '默认头像管理'
        ]);
        return view();
    }

    /**
     * 新增
     */
    public function add_avatar() {

        if (request()->isPost()) {
            $data = input('post.');
            if (!$data['img']) {
                json_fail_admin('图片不能为空');
            }
            $insert = [
                'name' => $data['name'],
                'img' => $data['img'],
                'addtime' => time(),
            ];

            $re = Db::name('member_default_avatar')->insertGetId($insert);
            $this->addlog('添加默认头像','数据ID:' .$re);
            $re ? json_success_admin() : json_fail_admin();

        }
        return view('form');
    }



    public function del_avatar($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name('member_default_avatar')->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');

        $res = Db::name('member_default_avatar')->delete($id);
        $this->addlog('删除默认头像','数据ID:' .$id);

        $res ? json_success_admin() : json_fail_admin();
    }

    public function add_amount($id)
    {
        $res = Db::name('member_info')->where('id', '=', $id)->find();
        if (empty($res)) {
            json_fail_admin('用户不存在');
        }

        if (!request()->isPost()) {
            $this->assign(['res' => $res]);
            return view('/member_info/add_amount');
        } else {
            $post = input('post.');


            if (empty($post['amount']) || !is_numeric($post['amount']) || $post['amount'] < 0 || $post['amount'] > 9999999) {
                json_fail_admin('金额不正确!');
            }

            $order_no = CreateDingdan($id, TTYPE_TZ_GIVE, TTYPE_OL_GIVE_PAAS, rand(0, 9));
            (new \app\admin\model\Trad())->addTrad($id,$post['amount'] * 100,$order_no,TTYPE_TZ_GIVE,TTYPE_OL_GIVE_PAAS); //操作余额

            json_success_admin();
        }
    }



    /**
     * 删除vip (同时禁止评论，记录用户黑名单)
     * @return void
     */
    public function remove_vip($id){

        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();

        $time=time();
        if($res['expire_time']>$time || $res['super_vip_expire_time']>$time){
            Db::startTrans();
            try {
                Db::name($this->name)->where($where)->update(['expire_time' => 0, 'super_vip_expire_time' => 0, 'is_comment' => 2, ]);
                $before_vip_data = json_encode(['expire_time' => $res['expire_time'], 'super_vip_expire_time' => $res['super_vip_expire_time']], JSON_UNESCAPED_UNICODE);
                Db::name('remove_vip_log')->insert([
                    'user_id' => $res['id'],
                    'before_vip_data' => $before_vip_data,
                    'operate_user_id' => $this->user['id'],
                ]);

                $this->addlog('删除vip会员', json_encode(['before_data' => ['user_id' => $res['id'], 'account' => $res['account'], 'before_vip_data' => $before_vip_data], 'after_data' => []]));
                Db::commit();
            }catch (\Throwable $e){
                Db::rollback();
                json_fail_admin('操作失败：reason：'.$e->getMessage().$e->getFile().$e->getLine());
            }
        }else{
            json_fail_admin('非会员，无法操作');
        }

        json_success_admin();
    }
}
