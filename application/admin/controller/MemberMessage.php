<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
use think\facade\Validate;

class MemberMessage extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_message";
        $this->title = "消息列表";
        $this->current_url = "/admin/member_message";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['account'] = trim(input('request.account'));
        $condition['is_read'] = input('request.is_read');
        $condition['message_type'] = input('request.message_type');
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }


        if (!empty($condition['account'])) $where[] = ['b.account', '=', $condition['account']];
        if (!empty($condition['is_read'])) $where[] = ['a.is_read', '=', $condition['is_read']];
        if (!empty($condition['message_type'])) $where[] = ['a.message_type', '=', $condition['message_type']];
        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];

        $level_list = Db::name('app_level')->field('id,title')->order('id desc')->select();
        $list = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id', 'left')
            ->field('a.*,b.account,b.is_visitor')
            ->where($where)->order('id desc')->paginate(20);
        $this->assign([
            'list' => $list,
            'level_list' => $level_list,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }
    public function add()
    {

        $ids=input('ids');
        if(!empty($ids))$ids=explode(',',$ids);

        $id=input('id');
        if(!empty($id))$ids=[$id];

        $res = Db::name('member_info')->whereIn('id', $ids)->field("account")->select();
        if (empty($res)) json_fail_admin('用户不存在');
        if (!request()->isPost()) {
            $this->assign(['res' => ['account'=>implode(' , ',array_column($res,'account'))]]);
            return view('/member_message/add');
        } else {
            $post = input('post.');
            if(!check_length($post['title'],4,12)) json_fail_admin("标题最短4为最长12位");
            if (empty($post['title'])) json_fail_admin('标题不能为空!');
            if (empty($post['content'])) json_fail_admin('内容不能为空!');

            $install_data = [];
            $time = time();
            foreach ($ids as $k=>$v){
                $install_data[]=[
                    'member_id' => $v,
                    'message_type' => 1,
                    'title' => $post['title'],
                    'content' => $post['content'],
                    'create_time' => $time,
                    'create_user' => $this->user['id'],
                ];
            }

            if(!empty($install_data)){
                $res = Db::name($this->name)->insertAll($install_data);
                $this->addlog('发送用户消息','用户id:'.implode(',',$ids).' ,内容: '.$post['content']);
            }

            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function add_file()
    {

        if (!request()->isPost()) {
            return view('/member_message/add_file');
        } else {
            $post = input('post.');
            if(!check_length($post['title'],4,12)) json_fail_admin("标题最短4为最长12位");
            if (empty($post['title'])) json_fail_admin('标题不能为空!');
            if (empty($post['content'])) json_fail_admin('内容不能为空!');

            $msg_file_info = $this->msg_file_info();

            $user_ids=$msg_file_info['user_ids'];

            if(count($user_ids)>100000){
                json_fail_admin('一次性发送用户数不能超过10W');
            }

            $time = time();
            $res=false;
            $chunk_list = array_chunk($user_ids,3000);

            foreach ($chunk_list as $k=>$user_ids){

                $install_data = [];
                foreach ($user_ids as $k1=>$v1){
                    $install_data[]=[
                        'member_id' => $v1,
                        'message_type' => 1,
                        'title' => $post['title'],
                        'content' => $post['content'],
                        'create_time' => $time,
                        'create_user' => $this->user['id'],
                    ];
                }
                if(!empty($install_data)){
                    $res = Db::name($this->name)->insertAll($install_data);
                }
            }

            if($res){
                $this->addlog('发送用户消息(txt文件上传)','用户id:'.' ,内容: '.$post['content']);
            }

            $res ? json_success_admin("发送成功") : json_fail_admin();

        }
    }


    public function msg_file_check(){
        $type = input('get.type',1);
//        dump(222);die();
        $msg_file_info = $this->msg_file_info($type);
        switch ($type){
            case 1:
                $msg = "文件解析成功：预计发送用户数：".count($msg_file_info['user_ids']).'，确认无误的话点击提交发送';
                break;
            case 2:
                $msg = "文件解析成功：预计发送用户数：".count($msg_file_info['user_ids']).'，其中已完成问卷用户数：'.$msg_file_info['finish_user_id_count'].'，已完成问卷不会再发送，确认无误的话点击提交发送';
                break;
        }

         json_success_admin($msg);
    }


    /**
     * $type 1站内信 2消息首页弹窗
     * @return array
     */
    public function msg_file_info(int $type=1){
        $file = request()->file("file");
        if (!$file) {
            json_fail_admin('请重新上传！');
        }
        $fileExt='txt';
        $v = Validate::make([
            'file' => "require|fileExt:{$fileExt}",
        ], [
            'file.require' => '文件必传',
            'file.fileExt' => "文件格式仅支持:{$fileExt}",
        ]);

        if (!$v->check(['file'=>$file])) {
            json_fail_admin($v->getError());
        }

        $info = $file->rule('uniqid')->move(UPLOAD_PATH . '/uploads/');
        $path_in_folder = UPLOAD_PATH . '/uploads/'.$info->getSaveName();

        $content = file_get_contents($path_in_folder);
        $user_ids = explode("\n",$content);

        $false_user_ids_count = 0;
        $success_user_id_count = 0;
        $finish_user_id_count = 0;
        foreach ($user_ids as $k=>$v){
            $v=trim($v);
            if(empty($v))unset($user_ids[$k]);
            if(!is_numeric($v)){
                $false_user_ids_count++;
            }else{
                $success_user_id_count++;
            }
        }

        if(!empty($user_ids) && $type=2 ){
            $user_answers_list = Db::name('user_answers')->whereIn('member_id',$user_ids)->where(['is_finish_answer'=>1])->select();
            foreach ($user_answers_list as $k=>$v){
                    foreach ($user_ids as $k1=>$v1){
                        if($v['member_id']==$v1)unset($user_ids[$k1]);
                    }
                    $false_user_ids_count--;
                    $success_user_id_count--;
                    $finish_user_id_count++;
            }
            $user_ids = array_values($user_ids);
        }

        return ['user_ids'=>$user_ids,'false_user_ids_count'=>$false_user_ids_count,'success_user_id_count'=>$success_user_id_count,'finish_user_id_count'=>$finish_user_id_count];
    }

    public function edit($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('消息不存在');
        $user = Db::name('member_info')->where('id', '=', $res['member_id'])->find();
        if (empty($user)) json_fail_admin('用户不存在');
        if (!request()->isPost()) {
            $this->assign([
                'res' => $res,
                'user' => $user,
            ]);
            return view();
        } else {
            $post = input('post.');
            if (empty($post['title'])) json_fail_admin('标题不能为空!');
            if (empty($post['content'])) json_fail_admin('内容不能为空!');
            $data = [
                'title' => $post['title'],
                'content' => $post['content'],
                'update_time' => time(),
                'update_user' => $this->user['id'],
            ];
            $res = Db::name($this->name)->where($where)->update($data);
             $this->addlog('修改用户消息',' 内容: '.$post['content']);

            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('消息不存在');
        $res = Db::name($this->name)->where($where)->delete();

        $this->addlog('删除用户消息',' 数据ID: '.$id);

        $res ? json_success_admin() : json_fail_admin();
    }
}
