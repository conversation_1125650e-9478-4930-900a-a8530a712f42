<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
class MemberMovieUpDown extends BaseController
{
    public $title, $current_url, $name,$index_export;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_movie_up_down";
        $this->title = "用户点赞记录";
        $this->current_url = "/admin/member_movie_up_down";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
        $this->assign("export_current_url", $this->current_url.'/index_export'.'?'.http_build_query($_GET).'&is_export=1');
    }

    public function index()
    {
        $where = [];
        $condition['account'] = input('request.account');
        $condition['title'] = input('request.title');
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['up_down'] = input('request.up_down');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['channel'] = $channel = input("request.channel", null, "trim");

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }


        $movie_id = input('param.movie_id');
        $up_down = input('param.up_down');
        if (!empty($movie_id)) {
            $where[] = ['a.movie_id', '=', $movie_id];
            $where[] = ['a.up_down', '=', $up_down];
            $this->current_url .= "/index/movie_id/" . $movie_id . '/up_down' . $up_down;
        }
        if (!empty($condition['account'])) $where[] = ['b.account', 'like', '%' . $condition['account'] . '%'];
        if (!empty($condition['title'])) $where[] = ['c.title', 'like', '%' . $condition['title'] . '%'];
        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['up_down'])) $where[] = ['a.up_down', '=', $condition['up_down']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];
        if(!empty($condition['channel']))$where[] = ['a.channelid', 'IN', explode(',',$condition['channel'])];

        $list = Db::name($this->name)->alias('a')
        ->join('member_info b', 'a.member_id=b.id','left')
        ->join('app_movie c', 'a.movie_id=c.id','left')
        ->field('a.*,b.account,b.is_visitor,c.title')
        ->where($where)->order('id desc')->paginate($this->index_export?999999:20);

        if($this->index_export){
            $list = $list->toArray()['data'];
            // 日志记录
            $this->addlog($this->title.'导出',json_encode(['before_data'=>[],'after_data'=>$condition]));

            $export_data=[];
            foreach ($list as $k=>$v) {
                $export_data[] = [
                    'id' => $v['id'],
                    'channelid' => $v['channelid'],
                    'account' => $v['account'],
                    'title' => $v['title'],
                    'up_down' => $v['up_down']==1?'点赞':'点踩',
                    'create_time' => date('Y-m-d H:i:s',$v['create_time']),
                ];
            }

            $title = ['ID','所属渠道','用户','视频名称','点赞点踩','操作时间'];
            $filename = $this->title;
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $count_find = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id','left')
            ->join('app_movie c', 'a.movie_id=c.id','left')
            ->fieldRaw('count(DISTINCT a.member_id) distinct_member_count ,count(DISTINCT a.movie_id) distinct_movie_id_count')
            ->where($where)
            ->find();

        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'movie_id' => $movie_id,
            "channel" => $channel,
            "count_find" => $count_find,
            'channelAll'=> Db::name('channel')->distinct(true)->field('channelid')->select(),
        ]);
        $this->_assign();
        return view();
    }

    public function index_export(){
        $this->index_export=true;
        $this->index();
    }

}
