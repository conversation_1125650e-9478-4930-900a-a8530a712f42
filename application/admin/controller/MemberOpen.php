<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class MemberOpen extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_open";
        $this->title = "用户打开记录";
        $this->current_url = "/admin/member_open";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        // $condition['account'] = input('request.account');
        $condition['member_id'] = input('request.member_id');
        $condition['device_id'] = input('request.device_id');
        $condition['device_type'] = input('request.device_type');
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }


        // if (!empty($condition['account'])) $where[] = ['b.account', 'like', '%' . $condition['account'] . '%'];
        if (!empty($condition['device_type'])) $where[] = ['a.device_type', '=', $condition['device_type']];
        if (!empty($condition['member_id'])) $where[] = ['a.member_id', '=', $condition['member_id']];
        if (!empty($condition['device_id'])) $where[] = ['a.device_id', '=', $condition['device_id']];
        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];
        $list = Db::name($this->name)->alias('a')
            ->LeftJoin('member_info b', 'a.member_id=b.id', 'left')
            ->field('a.*,b.account,b.is_visitor')
            ->where($where)
            ->order('id desc')->paginate(20);
        $where = [];
        if (!empty($condition['begin_time'])) $where[] = ['create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['create_time', '<', strtotime($condition['end_time'])];

        $membercount = Db::name($this->name)->where($where)
        ->group('member_id')->count();
        $devicecount = Db::name($this->name)->where($where)
        ->group('device_id')->count();
        $this->title = '活跃用户数:'.$membercount.' 活跃设备数:'.$devicecount;

        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'title' => $this->title,
        ]);
        $this->_assign();
        return view();
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name($this->name)->where($where)->delete();
        $res ? json_success_admin() : json_fail_admin();
    }
}
