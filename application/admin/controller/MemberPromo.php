<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
use think\facade\Request;

class MemberPromo extends BaseController
{
    public $title, $current_url, $name,$index_export;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_promo";
        $this->title = "用户推荐记录";
        $this->current_url = "/admin/member_promo";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
        $this->assign("export_current_url", $this->current_url.'/index_export'.'?'.http_build_query($_GET).'&is_export=1');
    }

    public function index()
    {
        $where = [];
        $condition['account'] = input('request.account');
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['channel'] = $channel = input("request.channel", null, "trim");

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];
        if(!empty($condition['channel']))$where[] = ['a.channelid', 'IN', explode(',',$condition['channel'])];
        $list = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id', 'left')
            ->join('member_info c', 'a.to_member_id=c.id', 'left')
            ->field('a.*,b.account,b.is_visitor,c.account account2,c.is_visitor is_visitor2')
            ->where($where)
            ->order('id desc')->paginate($this->index_export?999999:20);


        if($this->index_export){
            $list = $list->toArray()['data'];
            // 日志记录
            $this->addlog($this->title.'导出',json_encode(['before_data'=>[],'after_data'=>$condition]));

            $export_data=[];
            foreach ($list as $k=>$v) {
                $export_data[] = [
                    'id' => $v['id'],
                    'member_channelid' => $v['member_channelid'],
                    'account' => $v['is_visitor']==1?'游客':$v['account'],
                    'account2' => $v['is_visitor2']==1?'游客':$v['account2'],
                    'create_time' => date('Y-m-d H:i:s',$v['create_time']),
                ];
            }

            $title = ['ID','推荐用户所属渠道','推荐用户','被推荐用户','推荐时间'];
            $filename = $this->title;
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $count_find = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id', 'left')
            ->fieldRaw('count(DISTINCT a.member_id) distinct_member_count ,count(DISTINCT a.to_member_id) distinct_to_member_count')
            ->where($where)
            ->find();

        $this->assign([
            'list' => $list,
            'condition' => $condition,
            "channel" => $channel,
            "count_find" => $count_find,
            'channelAll'=> Db::name('channel')->distinct(true)->field('channelid')->select(),
        ]);
        $this->_assign();
        return view();
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name($this->name)->where($where)->delete();
        $res ? json_success_admin() : json_fail_admin();
    }

    public function index_export(){
        $this->index_export=true;
        $this->index();
    }

}
