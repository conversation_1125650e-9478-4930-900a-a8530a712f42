<?php
namespace app\admin\controller;

use think\Db;
class MemberSave extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_info";
        $this->title = "用户保存记录";
        $this->current_url = "/admin/member_save";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['account'] = input('request.account');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        $where[] = ['a.is_save_qrcode', '=', '1'];
        if (!empty($condition['account'])) $where[] = ['a.account', 'like', '%' . $condition['account'] . '%'];
        if (!empty($condition['begin_time'])) $where[] = ['a.save_qrcode_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.save_qrcode_time', '<', strtotime($condition['end_time'])];

        $list = Db::name($this->name)->alias('a')->where($where)->order('id desc')->paginate(20);
        $this->assign([
            'list' => $list,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }
}
