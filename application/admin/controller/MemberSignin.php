<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class MemberSignin extends BaseController
{
    public $title, $current_url, $name,$index_export;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_signin";
        $this->title = "用户签到列表";
        $this->current_url = "/admin/member_signin";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
        $this->assign("export_current_url", $this->current_url.'/index_export'.'?'.http_build_query($_GET).'&is_export=1');
    }

    public function index()
    {
        $where = [];
        $condition['account'] = trim(input('request.account'));
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['channel'] = $channel = input("request.channel", null, "trim");

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        if (!empty($condition['account'])) {
            $where_member_info[] = ['account', '=',  $condition['account'] ];
            $member_column_list = Db::name('member_info')->where($where_member_info)->column('account,is_visitor','id');
//            dump($member_column_list);die();
            $use_ids = array_keys($member_column_list);
            $where[] = ['a.member_id', 'IN', empty($use_ids)?[0]:$use_ids];
        }

//        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];
        if(!empty($condition['channel']))$where[] = ['a.channelid', 'IN', explode(',',$condition['channel'])];

        $list = Db::name($this->name)->alias('a')
//            ->join('member_info b', 'a.member_id=b.id', 'left')
            ->field('a.*')
            ->where($where)->order('id desc')->paginate($this->index_export?999999:20);

        $data = $list->items();
        if(empty($condition['account'])){
            $use_ids = array_column($data,'member_id');
            $member_column_list = Db::name('member_info')->whereIn('id',$use_ids)->column('is_visitor,account','id');
        }

        foreach ($data as &$item) {
            $item['is_visitor'] = $member_column_list[$item['member_id']]['is_visitor']??1;
            $item['account'] = $member_column_list[$item['member_id']]['account']??'';
        }

        if($this->index_export){
            // 日志记录
            $this->addlog($this->title.'导出',json_encode(['before_data'=>[],'after_data'=>$condition]));

            $export_data=[];
            foreach ($data as $k=>$v) {
                $export_data[] = [
                    'id' => $v['id'],
                    'channelid' => $v['channelid'],
                    'account' => $v['is_visitor']==1?'游客['.$v['member_id'].']':$v['account'],
                    'view_times' => $v['view_times'],
                    'count' => $v['count'],
                    'create_time' => date('Y-m-d H:i:s',$v['create_time']),
                ];
            }

            $title = ['ID','所属渠道','用户名','奖励观影次数','周期内第几天','创建时间'];
            $filename = $this->title;
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $count_find = Db::name($this->name)->alias('a')
            ->fieldRaw('count(DISTINCT a.member_id) distinct_member_count ,count(DISTINCT a.view_times) distinct_view_times_count')
            ->where($where)->find();

        $this->assign([
            'list' => $list,
            'data' => $data,
            'condition' => $condition,
            "channel" => $channel,
            "count_find" => $count_find,
            'channelAll'=> Db::name('channel')->distinct(true)->field('channelid')->select(),
        ]);
        $this->_assign();
        return view();
    }

    public function index_export(){
        $this->index_export=true;
        $this->index();
    }

}
