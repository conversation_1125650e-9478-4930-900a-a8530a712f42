<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
class MemberVip extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_vip";
        $this->title = "VIP购买记录";
        $this->current_url = "/admin/member_vip";


    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['account'] = trim(input('request.account'));
        $condition['order_no'] = trim(input('request.order_no'));
        $condition['title'] = trim(input('request.title'));
        $condition['type'] = input('request.type');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        if (!empty($condition['type'])) $where[] = ['c.type', '=', $condition['type']];
        if (!empty($condition['account'])) {
            $member_info_detail = Db::name('member_info')->where(['account'=>$condition['account']])->find();
            if(!empty($member_info_detail['id'])){
                $where[] = ['a.member_id', '=', $member_info_detail['id']];
            }else{
                $where[] = ['a.id', '=', 0];
            }
//            $where[] = ['b.account', 'like', '%' . $condition['account'] . '%'];
        }
        if (!empty($condition['order_no'])) $where[] = ['a.order_no', '=', $condition['order_no']];
        if (!empty($condition['title'])) $where[] = ['c.title', 'like', '%' . $condition['title'] . '%'];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'] . ' 00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'] . ' 23:59:59')];


//        b.account,b.is_visitor,
        $list = Db::name($this->name)->alias('a')
//            ->join('member_info b', 'a.member_id=b.id','left')
            ->join('vip c', 'a.vip_id=c.id','left')
            ->field('a.*,c.name')
            ->where($where)->order('id desc');
        //这里我标记下，type数据库里标记不用了。所以程序里也最好不要用type了。nash
        $class_list = array_column((array) Db::name('vip')->field('type,name')->select(),'name','type');
        if(!(!empty($_GET['is_export']) && $_GET['is_export']==1)){
            $list = $list->paginate(20);
            $data = $list->items();

            if(!empty($data)){
                $member_info_column = [];
                if(!empty($member_info_detail)){
                    $member_info_column=[$member_info_detail['id']=>$member_info_detail];
                }else{
                    $member_ids = array_column($data,'member_id');
                    $member_info_list= Db::name('member_info')->whereIn('id',$member_ids)->field('id,account,is_visitor')->select();
                    foreach ($member_info_list as $k=>$v){
                        $member_info_column[$v['id']]=$v;
                    }
                }

                foreach ($data as $k=>&$v){
                    $v['account']=$member_info_column[$v['member_id']]['account']??'';
                    $v['is_visitor']=$member_info_column[$v['member_id']]['is_visitor']??'';
                }

            }
        } else {
            $list = $list->select();
            if(!empty($list)){
                $member_info_column = [];
                if(!empty($member_info_detail)){
                    $member_info_column=[$member_info_detail['id']=>$member_info_detail];
                }else{
                    $member_ids = array_column($list,'member_id');
                    $member_info_list= Db::name('member_info')->whereIn('id',$member_ids)->field('id,account,is_visitor')->select();
                    foreach ($member_info_list as $k=>$v){
                        $member_info_column[$v['id']]=$v;
                    }
                }



                foreach ($list as $k=>&$v){
                    $v['account']=$member_info_column[$v['member_id']]['account']??'';
                    $v['is_visitor']=$member_info_column[$v['member_id']]['is_visitor']??'';
                }

            }
            $export_data = [];
            foreach ($list as $k=>$v3){
                $export_data[]=[
                    'id'=>$v3['id'],
                    'order_no'=>$v3['order_no'],
                    'account'=>$v3['account'],
                    //'type'=>$class_list[$v['type']],
                    'type'=>$v3['name'],
                    'amount'=>$v3['amount']/100,
                    'status'=>$v3['status']==1 ? '成功' : '失败',
                    'create_time'=>date('Y-m-d H:i:s',$v3['create_time']),
                ];
            }



            $title = ['ID','订单号','用户','类型','消费金额','状态','购买时间'];
            $filename = 'VIP购买记录';
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $this->assign([
            'list' => $list,
            'data' => $data,
            'condition' => $condition,
            'class_list'=> $class_list,
        ]);
        $this->_assign();
        return view();
    }
}
