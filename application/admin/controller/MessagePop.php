<?php
namespace app\admin\controller;

use app\common\Constants\CommonConst;
use app\Helper\MemberInfoHelper;
use app\Service\MemberInfoExtraService;
use think\Db;
use think\Validate;
use think\facade\Env;
use function Matrix\transpose;

class MessagePop extends CommonController
{

    public function __construct()
    {
        $conf=[
            'title' => '首页消息弹窗',
            'name'=>'message_pop',
            'current_url'=>'/admin/message_pop',
        ];
        parent::__construct($conf);
    }


    public function index()
    {

        $where=[];
        $fields  = '*';
        $list    = Db::name($this->name)
                       ->where($where)
                       ->order('id desc')
                       ->field($fields);

        $list=$list->paginate(20);

        $page = $list->render();

        $list = $list->toArray()??[];
        if(!empty($list['data'])){
            $user_answers = Db::name('user_answers')->whereIn('message_pop_id',array_column($list['data'],'id'))->field('message_pop_id,member_id')->select();
            $user_answers_column = [];
            foreach ($user_answers as $k=>$v){
                $user_answers_column[$v['message_pop_id']][]=$v['member_id'];
            }
            foreach ($list['data'] as $k1=>&$v1){
                $v1['member_ids']='';
                $v1['member_ids_short']='';
                if(!empty($user_answers_column[$v1['id']])){
                    $v1['member_ids']=implode(',',$user_answers_column[$v1['id']]);
                    $v1['member_ids_short'] = mb_substr($v1['member_ids'],0,100).(strlen($v1['member_ids'])>100?'.....':'');
                }
            }
        }
        $this->assign([
            'list' => $list,
            'page' => $page,
            'condition' => [],
        ]);
        $this->_assign();
        return view();
    }


    public function add_file()
    {

        if (!request()->isPost()) {
            return view('/message_pop/add_file');
        } else {

            if(empty(businessConfig('pop_title')) || empty(businessConfig('pop_content')) || empty(businessConfig('pop_url')) || businessConfig('pop_jump_type') == ''){
                json_fail_admin('请先配置完弹窗所有配置');
            }

            $msg_file_info = (new MemberMessage())->msg_file_info(2);

            $user_ids=$msg_file_info['user_ids'];

            if(count($user_ids)>10000){
                json_fail_admin('一次性发送用户数不能超过1W');
            }

            $error_user_ids = [];
            foreach ($user_ids as $k=>$v){
                if(empty($v)){
                    unset($user_ids[$k]);
                }elseif(!is_numeric($v)){
                    $error_user_ids[]=$v;
//                    json_fail_admin('用户id必须全是数字,请检查文件内容，错误用户id：'.$v);
                }elseif ($v>4294967295){
                    $error_user_ids[]=$v;
//                    json_fail_admin('用户id：'.$v.'不存在');
                }
            }

            if(!empty($error_user_ids)){
                json_fail_admin('请检查文件内容，错误用户id：'.implode(',',$error_user_ids));
            }

            $user_ids = array_unique($user_ids);

            $res=false;
            $chunk_list = array_chunk($user_ids,3000);

            Db::startTrans();

            try {
                $message_pop_id = Db::name('message_pop')->insertGetId([
                    'operator_user_id'=>$this->user['id'],
                    'operator_user_name'=>$this->user['username'],
                    'status'=>CommonConst::ENABLED[0],
                    'last_operator_time'=>date('Y-m-d H:i:s'),
                ]);

                foreach ($chunk_list as $k=>$user_ids){
                    $install_data = [];
                    foreach ($user_ids as $k1=>$v1){

                        $install_data[]=[
                            'message_pop_id' => intval($message_pop_id),
                            'member_id' => intval($v1),
                        ];
                    }

                    if(!empty($install_data)){
                        $res = Db::name('user_answers')->insertAll($install_data);
                    }
                }

                Db::commit();
            }catch (\Throwable $e){
                Db::rollback();
                json_fail_admin('新增失败'.$e->getMessage());
            }

            if($res){
                $this->addlog('发送用户首页弹窗消息(txt文件上传)','新增发送用户');
            }

            $res ? json_success_admin("发送成功") : json_fail_admin("发送失败，或无需要发送的用户");

        }
    }

    /**
     * 新增/编辑配置
     * @return void
     */
    public function config()
    {

        if ($this->request->isGet()) {
            $this->assign([
                'detail' => [
                    'pop_title'=>businessConfig('pop_title'),
                    'pop_content'=>businessConfig('pop_content'),
                    'pop_url'=>businessConfig('pop_url'),
                    'pop_jump_type'=>businessConfig('pop_jump_type'),
                ],
            ]);
            return view();
        }else{
            $data =  $this->request->post();
            businessConfig('pop_title',$data['pop_title']??'');
            businessConfig('pop_content',$data['pop_content']??'');
            businessConfig('pop_url',$data['pop_url']??'');
            businessConfig('pop_jump_type',$data['pop_jump_type']??0);

            $this->addlog('用户弹窗消息配置', json_encode($this->request->post()));
            json_success_admin('配置成功!');
        }

    }


}
