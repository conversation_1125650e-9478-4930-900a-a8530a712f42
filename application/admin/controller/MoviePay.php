<?php
namespace app\admin\controller;

use think\Db;
class MoviePay extends BaseController
{
	public function index()
	{
        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['order_id'] = input('request.order_id');
        $condition['movie_id'] = input('request.movie_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }


        if (!empty($condition['member_id'])) $where[] = ['member_id','=', $condition['member_id']];
        if (!empty($condition['order_id'])) $where[] = ['order_id', '=',$condition['order_id']];
        if (!empty($condition['movie_id'])) $where[] = ['movie_id', '=',$condition['movie_id']];
        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'] . '23:59:59')];


//        Leftjoin('app_movie am', 'mp.movie_id=am.id')->
//        ,am.title title
        $app_movie_list = Db::name('app_movie')->field('id,title')->select();
        $app_movie_column = [];
        foreach ($app_movie_list as $k=>$v){
            $app_movie_column[$v['id']]=$v;
        }

        $list = Db::name("app_pay_movie")->alias('mp')->field('mp.id,mp.order_id,mp.addtime,mp.movie_id,mp.member_id,mp.amount')->where($where)->order("id desc")->paginate(25)->each(function($item, $key)use ($app_movie_column){
            $item['title'] = $app_movie_column[$item['movie_id']]['title']??'';
            return $item;
        });

        $page = $list->render();
        $list = $list->toArray();

        $sum = Db::name("app_pay_movie")->where($where)->sum('amount');
        $count = Db::name("app_pay_movie")->where($where)->count();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => '/admin/movie_pay',
            'page' => $page,
            'sum' => $sum,
            'count' => $count,
        ]);
        return view();
	}
    public function report()
    {
        $where = [];
        $condition['date'] = input('request.date');

        if(input('request.start_enter')!=1){
            if(empty($condition['date']))$condition['date']=date('Y-m-d');
        }

        if (!empty($condition['date'])) $where[] = ['date', '=',$condition['date']];

        $list = Db::name("app_pay_movie_report")->where($where)->order("date desc");

        if(!empty($_GET['is_export']) && $_GET['is_export']==1) {
            $list = $list->select();

            $export_data = [];
            foreach ($list as $item) {
                $export_data[] = [
                    'date' => $item['date'],
                    'movie_num' => $item['movie_num'],
                    'member_num' => $item['member_num'],
                    'amount' => $item['amount'] / 100
                ];
            }

            $title = ['日期','视频售出','购买人次','销售额'];
            $filename = '视频销售报表';
            $this->excel_data($filename,$title,$export_data);
            exit;
        }

        $list = $list->paginate(25);
        $page = $list->render();
        $list = $list->toArray();

        $count = Db::name("app_pay_movie_report")->where($where)->sum('amount');
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => '/admin/movie_pay',
            'page' => $page,
            'count' => $count
        ]);
        return view();
    }
    public function export() {
        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['order_id'] = input('request.order_id');
        $condition['movie_id'] = input('request.movie_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['category_ids'] = input('request.category_ids');

        if (!empty($condition['member_id'])) $where[] = ['member_id','=', $condition['member_id']];
        if (!empty($condition['order_id'])) $where[] = ['order_id', '=',$condition['order_id']];
        if (!empty($condition['movie_id'])) $where[] = ['movie_id', '=',$condition['movie_id']];
        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'] . '23:59:59')];
        if (!empty($condition['category_ids'])) $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$condition['category_ids']}',am.category_ids)")];

        $data = Db::name("app_pay_movie")->alias('mp')->Leftjoin('app_movie am', 'mp.movie_id=am.id')->field('mp.id,mp.order_id,from_unixtime(mp.addtime),mp.movie_id,mp.member_id,mp.amount/100,am.title title,am.category_ids')->where($where)->order("id desc")->select();

        if (! $data) {
            json_fail_admin("无数据可供导出！");
        }
        $cate = Db::name('app_category')->field('id,title')->where('category_type', '=', 'movie')->select();

        foreach ($data as $k=>$v){
            $title ='';
            $category_ids =  explode(',',$v['category_ids']);
            foreach ($category_ids as $v2){
               foreach ($cate as $v3){
                    if($v2 == $v3['id']){
                        $title .= $v3['title'].',';
                    }
               }
            }
            $data[$k]['category_ids'] =$title ;
        }
        /*echo '<pre>';
        print_r($data);exit;*/
        $title = ['ID','订单号', '时间', '视频ID', '购买人','售价','视频标题','视频分类'];
        exportXls($title,$data,$condition['begin_time'],$condition['end_time']);
    }

}
