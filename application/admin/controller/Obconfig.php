<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;
use app\Aes;

class Obconfig extends BaseController
{

    public function system()
    {

        if ($this->request->isGet()) {
            return view();
        }else{    	
            foreach ($this->request->post() as $key => $vo) {
                sysconf($key, $vo);
            }
            json_success_admin('bob/博鱼参数配置成功!');
        }

    }
    public function payconfig()
    {

        if ($this->request->isGet()) {
            return view();
        }else{      
            foreach ($this->request->post() as $key => $vo) {
                sysconf($key, $vo);
            }
            json_success_admin('转账配置成功!');
        }

    }
    //欧宝游戏分类
    public function type_list()
    {
        $list = Db::name('ob_category')->order('sort desc')->paginate(20);     
        $this->assign([
            'list' => $list,
        ]);
        return view();
    }
    //欧宝
    public function edit()
    {
        $id = input('param.id');
        if (!$id) {
            json_fail_admin('参数错误');
        }
        $res = Db::name('ob_category')->where('id', '=', $id)->find();
        if (!$res) {
            json_fail_admin('数据不存在!');
        }

        if (request()->isPost()) {
            $data = input('post.');
            Db::name('ob_category')->strict(false)->update($data);
            $this->addlog('修改欧宝分类',' 数据ID: '.$id);

            redis()->del('home_index_recommend');
            json_success_admin('修改成功!');
        }
        $this->assign('res',$res);
        return view();

    }
    public function del($id)
    {
        $res = Db::name('ob_category')->delete($id);
        $this->addlog('删除欧宝分类',' 数据ID: '.$id);

        $res ? json_success_admin() : json_fail_admin();
    }
    public function game_list()
    {
        $condition = [];
        $condition['game'] = input('request.game');
        $condition['code'] = input('request.code');

        $where = [];
        $whereOr = [];
        if (!empty($condition['game'])) $where[] = ['zhName','like', '%' .$condition['game'] . '%'];
        if (!empty($condition['code'])){
            $where[] = ['ob_type_code', '=', $condition['code']];
            if ($condition['code'] == 'dz') {
                $whereOr[] = ['ob_type_code', '=', 'by'];
            }
        } 

        //所有分类
        $category = Db::name('ob_category')->order('sort desc')->column("code,name"); 
        
        //游戏列表
        $list = Db::name('ob_game')->where($where)->whereOr($whereOr)->order('sort desc')->paginate(20);     
        $this->assign([
            'list' => $list,
            'category' => $category,
            'condition' => $condition
        ]);
        return view();
         
    }

    public function add_game()
    {
        if (request()->isPost()) {
            $data = input('post.');
            if (!$data['ob_type_code']) {
                json_fail_admin('游戏分类不能为空');
            }
            if (!$data['ob_id']) {
                json_fail_admin('请选择子游戏');
            }
            $game = Db::name('ob_game')->where('ob_id',$data['ob_id'])->find();
            if ($game) {
               json_fail_admin('该游戏已存在!'); 
            }
            
            $insert = [];
            // $insert['ob_type_code'] =  $data['ob_type_code'];
            $insert['ob_id'] =  $data['ob_id'];
            $insert['sort'] =  $data['sort'];
            $insert['status'] =  $data['status'];
            $insert['img'] =  $data['img'];

            $list = $this->oblist();

            $game = [];
            foreach ($list as $key => $value) {
                $game = array_merge($game,$value);
            }
            foreach ($game as $key => &$value) {
                $value = (array)$value;
                if ($value['id'] == $data['ob_id']) {

                    $insert['ob_type_code'] = strtolower($value['gameType']);
                    $insert['channelCode'] =  $value['channelCode'];
                    $insert['ob_type_id'] =  $value['category'];
                    $insert['channelName'] =  $value['channelName'];
                    $insert['zhName'] =  $value['zhName'];
                    $insert['enName'] =  $value['enName'];
                    
                    break;
                }
            }
            $re = Db::name('ob_game')->insert($insert);
            if (!$re) {
                json_fail_admin('添加失败,请重试!'); 
            }
            $this->addlog('添加欧宝游戏', $insert['zhName']);

            redis()->del('home_index_recommend');
            json_success_admin('成功!'); 

        }else{
            //所有分类
            $category = Db::name('ob_category')->order('sort desc')->column("code,name"); 
            // //所有游戏
            $list = $this->oblist();
            $dz = array_merge($list['dz'],$list['by']);
            $list['dz'] = $dz;
            unset($list['by']);

            //已经添加的游戏
            $games = Db::name('ob_game')->order('sort desc')->column('ob_id,zhName');

            $this->assign('category',$category);     
            $this->assign('list',$list);     
            $this->assign('games',$games);     
            return view();
        }
    }

    public function edit_game($id)
    {
        $res = Db::name("ob_game")->where('id',$id)->find();
        if (empty($res)) json_fail_admin('游戏不存在!');       

        if (request()->isPost()) {
            $post = input('post.');
            $data = [
                'img' => $post['img'],
                'status' => $post['status'],
                'sort' => $post['sort'],
                'id' => $post['id']
            ];
            $re = Db::name('ob_game')->update($data);
            if (!$re) {
                json_fail_admin('请重试!');
            }
            redis()->del('home_index_recommend');
            $this->addlog('修改欧宝游戏', $res['zhName']);

            json_success_admin('修改成功!');
        }
        $this->assign([
            'res' => $res,
        ]);
        return view();
    }
    public function del_game($id)
    {
        $res = Db::name('ob_game')->delete($id);
        $this->addlog('修改欧宝游戏', '数据ID: '.$id);

        $res ? json_success_admin() : json_fail_admin();
    }

    protected function oblist()
    {
        $list = cacheGet('obgame');
        if (!$list){
            $api = sysconf('apiurl').config('ob.')['gamelist'];
            // $re = makeReq($api);  //查看成功
            $aes = new Aes();
            $data = $aes->encrypt(json_encode([],JSON_FORCE_OBJECT),sysconf('appkey'));
            $req = [];
            $req['reqId'] = com_create_guid(); 
            $req['appId'] = sysconf('appid'); 
            $req['ts'] = getMillisecond();
            $req['token'] = '';
            $req['data'] = $data;
            $req['sign'] = md5(implode('|',$req));
            $req['thUuid'] = "kumi-admin-default-device"; 
            $req['thClientType'] = 'h5';
            $re = http_post_json($api,json_encode($req));
            $re = json_decode($re,true);
            if ($re['code'] != 0) {
                json_fail_admin($re['msg']);  
            }
            $re['data'] = json_decode($aes->decrypt($re['data'],sysconf('appkey')),true);       
            $list = $re['data'];
            cacheSet('obgame',$list,300);
        }
        return $list;
    }
}


