<?php
namespace app\admin\controller;

use think\Db;
class Optools extends BaseController
{
	public function index()
	{       
        $where = [];
        $condition['type'] = input('request.type');
        $condition['title'] = input('request.title');
        $condition['status'] = input('request.status');
        if (!empty($condition['type'])) $where[] = ['type','=', $condition['type']];
        if (!empty($condition['status'])) $where[] = ['status','=', $condition['status']];
        if (!empty($condition['title'])) $where[] = ['title', 'like', '%' . $condition['title'] . '%'];

        $list = Db::name("app_pay_tools")->where($where)->paginate(20);
        $page = $list->render();
        $list = $list->toArray();   
        
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => '/admin/optools',
            'page' => $page,
        ]);
        return view();	    	
	}
    public function add()
    {

        if (request()->isPost()) {
            $data = input('post.');
            if (!$data['title']) {
                json_fail_admin('标题不能为空!');
            }
            if (!check_number($data['amount']) || $data['amount'] <= 0) {
                json_fail_admin('达标充值金额不正确');
            }
            if (!check_number($data['amount_give'])) {
                json_fail_admin('赠送金额不正确');
            }
            if (!check_number($data['vip_give'])) {
                json_fail_admin('VIP月数不正确');
            }

            if (!$data['start_time'] || !$data['end_time'] || (strtotime($data['end_time']) < strtotime($data['start_time']))) {
                json_fail_admin('开始结束时间不正确');
            }

            if ($data['type'] == 2 && !$data['give_time']) {
                json_fail_admin('发放时间不正确');
            }
            if (!in_array($data['status'], [0,1])) {
                json_fail_admin('状态错误');
            }
   
            $insert = [
                'title' => $data['title'],
                'amount' => $data['amount'] * 100,
                'amount_give' => $data['amount_give'] * 100,
                'vip_give' => $data['vip_give'],
                'start_time' => strtotime($data['start_time']),
                'end_time' => strtotime($data['end_time']),
                'status' => $data['status'],
                'type' => $data['type'],
                'opadmin' => $this->user['username'],
                'addtime' => time()
            ];

            if ($data['type'] == 2) {
                $insert['give_time'] = strtotime($data['give_time']);
            }
            $re = Db::name('app_pay_tools')->insert($insert);
            if (!$re) {
                json_fail_admin('添加失败,请重试!');
            }
            $this->addlog('添加运营工具', $insert['title']);

            json_success_admin('添加成功!');  
        }else{
            $type = input('param.type');
            $this->assign('type',$type);     
            return view();
        }
       
    }
    public function edit($id)
    {
        $res = Db::name("app_pay_tools")->where('id',$id)->find();
        if (empty($res)) json_fail_admin('奖励活动不存在');           

        if (request()->isPost()) {
           $data = input('post.');
            if (!$data['title']) {
                json_fail_admin('标题不能为空!');
            }
            if (!check_number($data['amount']) || $data['amount'] <= 0) {
                json_fail_admin('达标充值金额不正确');
            }
            if (!check_number($data['amount_give'])) {
                json_fail_admin('赠送金额不正确');
            }
            if (!check_number($data['vip_give'])) {
                json_fail_admin('VIP月数不正确');
            }

            if (!$data['start_time'] || !$data['end_time'] || (strtotime($data['end_time']) < strtotime($data['start_time']))) {
                json_fail_admin('开始结束时间不正确');
            }

            if ($data['type'] == 2 && !$data['give_time']) {
                json_fail_admin('发放时间不正确');
            }
            if (!in_array($data['status'], [0,1])) {
                json_fail_admin('状态错误');
            }
   
            $update = [
                'title' => $data['title'],
                'amount' => $data['amount'] * 100,
                'amount_give' => $data['amount_give'] * 100,
                'vip_give' => $data['vip_give'],
                'start_time' => strtotime($data['start_time']),
                'end_time' => strtotime($data['end_time']),
                'status' => $data['status'],
                'type' => $data['type'],
                'opadmin' => $this->user['username'],
                'addtime' => time(),
                'id' => $data['id']
            ];
            if ($data['type'] == 2) {
                $insert['give_time'] = strtotime($data['give_time']);
            }
            $re = Db::name('app_pay_tools')->update($update);
            if (!$re) {
                json_fail_admin('修改失败,请重试!');
            }
            $this->addlog('修改运营工具', $update['title']);

            json_success_admin('修改成功!');  

        }
        $res['start_time'] = str_replace(' ', 'T', date('Y-m-d H:i:s',$res['start_time']));
        $res['end_time'] = str_replace(' ', 'T', date('Y-m-d H:i:s',$res['end_time']));
        $res['give_time'] = str_replace(' ', 'T', date('Y-m-d H:i:s',$res['give_time']));
        $this->assign([
            'info' => $res,
        ]);
        return view();
    }
    public function del($id)
    {
        $res = Db::name('app_pay_tools')->delete($id);

        $this->addlog('删除运营工具', '数据ID: '.$id);

        $res ? json_success_admin() : json_fail_admin();
    }
}
