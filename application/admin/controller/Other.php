<?php 
namespace app\admin\controller;

use think\Db;

class Other extends BaseController
{
   public function safe()
   {
   		if ($this->request->isGet()) {
            return view('safe');
        }else{    	
            foreach ($this->request->post() as $key => $vo) {
                sysconf($key, $vo);
            }
            $this->addlog('接口安全', json_encode($this->request->post()));
            json_success_admin('安全参数配置成功!');
        }

   }

   public function reg_sms()
   {
      if ($this->request->isGet()) {
            return view('reg_sms');
        }else{      
            foreach ($this->request->post() as $key => $vo) {
  
                sysconf($key, $vo);
            }
            $this->addlog('注册环节短信验证', json_encode($this->request->post()));
            json_success_admin('配置成功!');
        }

  }
   public function oss()
   {
      if ($this->request->isGet()) {
            return view('oss');
        }else{      
            foreach ($this->request->post() as $key => $vo) {
  
                sysconf($key, $vo);
            }
            $this->addlog('OSS配置', json_encode($this->request->post()));
            json_success_admin('配置成功!');
        }

  }

    /**
     * 支付拉单限制
     * @return \think\response\View|void
     */
    public function pay_limit()
    {
        if ($this->request->isGet()) {
            $this->assign([
                'detail' => json_decode(sysconf("pay_limit_rules"),true),
            ]);
            return view();
        }else{
            $data =  $this->request->post();
            foreach ($data as $k=>$v){
                if(!preg_match("/^[1-9][0-9]*$/" ,$v)){
                    json_fail_admin('请填入正整数');
                }
            }

            sysconf('pay_limit_rules', json_encode([
                'limit_count'=>$data['limit_count']??0,
                'limit_time'=>$data['limit_time']??0,
                'limit_time_count'=>$data['limit_time_count']??0,
            ]));

            $this->addlog('支付拉单限制', json_encode($this->request->post()));
            json_success_admin('配置成功!');
        }

    }




   public function get_sms()
   {
        if ($this->request->isGet()) {
            return view('get_sms');
        }else{      
            $phone = input("param.phone");
            $code = Db::name('member_sms')->where('mobile',$phone)->field('send_time,code')->find();
            if (!$code) {
               json_fail_admin('未找到发送记录!');
            }
            $code['sendtime'] = date('Y-m-d H:i:s',$code['send_time']);
            
            json_success_admin('查询成功!',$code);
        }
      
   }


  public function white_ip()
  {
      if ($this->request->isGet()) {
            return view('white_ip');
        }else{      
            foreach ($this->request->post() as $key => $vo) {
                sysconf($key, $vo);
            }
            $this->addlog('后台白名单设置', json_encode($this->request->post()));
            json_success_admin('后台白名单设置配置成功!');
        }
  }


  //中奖喜报设置

  public function win_report()
  {
    $page  = [
      1 => '首页',
      2 => '我的',
      3 => '发现',
      4 => '短视频',
      5 => '视频播放',
    ];
    if ($this->request->isGet()) {
      $this->assign('page',$page);

      //所有游戏
      $allgame = Db::name('ob_game')->where('status',1)->cache(true,60)->column('id,zhName');
      $this->assign('allgame',$allgame);
      $checkgame = sysconf('win_games');
      $checkgame = explode(',', $checkgame);
      $this->assign('checkgame',$checkgame);
      $win_page = sysconf('win_page');
      $win_page = explode(',', $win_page);
      $this->assign('win_page',$win_page);
      return view('win_report');
    }else{  
        $data = $this->request->post();
        if ($data['win_url_type'] == 2 && !$data['win_url']) {
          json_fail_admin('跳转地址不能为空!');
        }
        if ($data['win_min_money'] > $data['win_max_money']) {
          json_fail_admin('金额区间不正确!');
        }
        foreach ($data as $key => &$vo) {
          if ($key == 'win_games') {
            $vo = implode(',', $vo);
          }
          if ($key == 'win_page') {
            $vo = implode(',', $vo);
          }
            sysconf($key, $vo);
        }
        $this->addlog('中奖喜报配置','');
        json_success_admin('中奖喜报配置成功!');
    }
  }


  //
  public function video_api()
  {

      if ($this->request->isGet()) {
          return view();
      }else{      
          foreach ($this->request->post() as $key => $vo) {
              sysconf($key, $vo);
          }
          json_success_admin('配置成功!');
      }

  }
}