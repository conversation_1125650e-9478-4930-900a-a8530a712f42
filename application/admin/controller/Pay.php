<?php
namespace app\admin\controller;

use app\common\BizConst;
use app\common\Constants\CommonConst;
use app\Helper\CacheHelper;
use app\Helper\MemberInfoHelper;
use app\Service\AiService;
use app\Service\AppPayOrderService;
use app\Service\AppPayService;
use app\Service\GameAmountRecordService;
use app\Service\PayChannelService;
use think\Db;
use think\db\Where;
use think\response\View;

class Pay extends BaseController
{
    /**
     * 获取支付类型列表
     * @return array
     */
    protected static function getpayType(){
        return AppPayOrderService::getConstPluck("PAY_TYPE_");
    }

    /**
     *
     * @param int $is_chess 是否棋牌专属 0否 1是（这里保持兼容旧代码，controller当内部调用方法）
     * @param string $view_path 视图层路径（这里保持兼容旧代码，controller当内部调用方法）
     * @return \think\response\View
     * @throws \think\exception\DbException
     */
    public function index(int $is_chess=0,string $view_path='')
    {
        $request_is_chess = input('request.is_chess');
        $condition['name_code']  = input('request.name_code');
        $condition['status']  = input('request.status');
        if(isset($request_is_chess))json_fail_admin('非法参数');

        $all = PayChannelService::spliceChannel(0);

        $all = array_reverse($all);
        $where=[
            ['is_chess','=',$is_chess]
        ];

        if (!empty($condition['name_code'])) $where[] = ['name_code','=', $condition['name_code']];
        if (isset($condition['status']) && $condition['status']!='') $where[] = ['status','=', $condition['status']];

//        dump($all);die();


        $list = Db::name("app_pay")->where($where)->order("status asc,sort desc,id desc")->paginate(20);

        $page = $list->render();
        $list = $list->toArray();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => self::$current_fix_not_fun_url,
            'page' => $page,
            'all' => $all,
            'pay_type' => self::getpayType(),
        ]);

        return view($view_path);
    }

    /**
     *
     * @param int $is_chess 是否棋牌专属 0否 1是（这里保持兼容旧代码，controller当内部调用方法）
     * @param string $view_path 视图层路径（这里保持兼容旧代码，controller当内部调用方法）
     * @return \think\response\View
     * @throws \think\exception\DbException
     */
    public function add(int $is_chess=0,string $view_path='add')
    {
        $request_is_chess = input('request.is_chess');
        if(isset($request_is_chess))json_fail_admin('非法参数');

        $all = PayChannelService::spliceChannel(0);
        $all = array_reverse($all);
        if (request()->isPost()) {
            $data = input('post.');
            if (!$data['name_code']) {
                json_fail_admin('支付品牌不能为空！');
            }
            if (!$data['channel']) {
                json_fail_admin('支付API不能为空');
            }
            if (!$data['show_name']) {
                json_fail_admin('前台名称不能为空');
            }
            $data['pay_type'] = $data['pay_type'] ?? 0;

            $re = Db::name('app_pay')->where(['name_code'=>$data['name_code'],'channel'=>$data['channel'],'is_chess'=>$is_chess])->find();
            if ($re) {
                json_fail_admin('该支付方式已存在!');
            }

            $insert = [
                'name_code' => $data['name_code'],
                'channel' => $data['channel'],
                'show_name' => $data['show_name'],
                'sort' => $data['sort'],
                'logo' => $data['logo'],
                'status' => $data['status'],
                'type' => 1,
                'pay_type' => $data['pay_type'],
                'is_chess'=>$is_chess,
            ];
            if ($data['name_code'] == 'Proxy') {
                $insert['type'] = 2;
            }
            redis()->del('all_pay');
            $id = Db::name('app_pay')->insertGetId($insert);

            if (!$id) {
                json_fail_admin('添加失败,请重试!');
            }
            CacheHelper::delPayCache($id);

            $this->addlog('添加支付通道','通道名: '.$insert['show_name']);
            json_success_admin('添加成功!');
        }
        $this->assign([
            'all' => $all,
            'pay_type' => self::getpayType()
        ]);
        return view($view_path);

    }

    /**
     * @param int $is_chess 是否棋牌专属 0否 1是（这里保持兼容旧代码，controller当内部调用方法）
     * @param string $view_path 视图层路径（这里保持兼容旧代码，controller当内部调用方法）
     * @return \think\response\View
     * @throws \think\exception\DbException
     */
    public function edit($id,int $is_chess=0,string $view_path='')
    {
        $request_is_chess = input('request.is_chess');
        if(isset($request_is_chess))json_fail_admin('非法参数');

        $where=[
            ['is_chess','=',$is_chess]
        ];
        $res = Db::name("app_pay")->where('id',$id)->where($where)->find();
        if (empty($res)) json_fail_admin('支付方式不存在');
        $all = PayChannelService::spliceChannel(0);
        $all = array_reverse($all);

        if (request()->isPost()) {
            $post = input('post.');
            $data = [
                'show_name' => $post['show_name'],
                'logo' => $post['logo'],
                'status' => $post['status'],
                'sort' => $post['sort'],
                'id' => $post['id'],
                'is_chess'=>$is_chess,
            ];
            if (isset($post['info'])) {
               $data['info'] = $post['info'];
            }
            redis()->del('pay_'.$id);
            $re = Db::name('app_pay')->update($data);
            if (!$re) {
                json_fail_admin('请重试!');
            }
            CacheHelper::delPayCache($id);
            $this->addlog('修改支付通道','通道名: '.$data['show_name']);

            json_success_admin('修改成功!');
        }
        $this->assign([
            'info' => $res,
            'all' => $all,
            'pay_type' => self::getpayType()
        ]);
        return view($view_path);
    }

    /**
     * @param int $is_chess 是否棋牌专属 0否 1是（这里保持兼容旧代码，controller当内部调用方法）
     */
    public function del($id,int $is_chess=0)
    {
        $request_is_chess = input('request.is_chess');
        if(isset($request_is_chess))json_fail_admin('非法参数');
        $where=[
            ['id','=',$id],
            ['is_chess','=',$is_chess]
        ];

        $find = Db::name('app_pay')->where($where)->find();
        if(empty($find)){
            json_fail_admin('该支付方式不存在');
        }

        if(!empty(Db::name('app_pay_order')->where(['pay_id'=>$find['id']])->field('id')->find())){
            json_fail_admin('该支付方式已产生订单，不能删除');
        }


        $res = Db::name('app_pay')->where($where)->delete();
        if (!$res) {
            json_fail_admin();
        }
        Db::name('app_pay_price')->where('pay_id',$id)->delete();
        CacheHelper::delPayCache($id);

        $this->addlog('删除支付通道','通道ID: '.$id);

        json_success_admin();
    }

    /**
     * <AUTHOR>
     * @DateTime 2020-09-17
     * @param int $is_chess 是否棋牌专属 0否 1是（这里保持兼容旧代码，controller当内部调用方法）
     * @param string $view_path 视图层路径（这里保持兼容旧代码，controller当内部调用方法）
     * @return   [type]     [四方支付订单记录]
     */
    public function line(int $is_chess=0,string $view_path='')
    {
        $request_is_chess = input('request.is_chess');
//        if(isset($request_is_chess))json_fail_admin('非法参数');
        $app_pay_where=[
            ['is_chess','=',$is_chess]
        ];


        $all = PayChannelService::spliceChannel(0);
        unset($all['Proxy']);

        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['order_id'] = input('request.order_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['pay'] = input('request.pay');
        $condition['status'] = input('request.status');
        $condition['pay_type'] = input('request.pay_type');
        $condition['pay_channel'] = input('request.pay_channel');
        $condition['channelid'] = input('request.channelid');
        $condition['device_source'] = input('request.device_source');
        $condition['start_enter'] = input('request.start_enter');
        $condition['account'] = trim(input('request.account'));
        $condition['page_source'] = trim(input('request.page_source'));

        $condition['start_price'] = input('request.start_price');
        $condition['end_price'] = input('request.end_price');

        if (!empty($condition['start_price'])) $where[] = ['o.order_amount', '>=', $condition['start_price'].'00'];
        if (!empty($condition['end_price'])) $where[] = ['o.order_amount', '<=', $condition['end_price'].'00'];


        if (!empty($condition['account'])) {
            $member_info_search = Db::name('member_info')->where('account','=',$condition['account'])->field('id')->find();
            $where[] = ['o.member_id','=', $member_info_search['id']??0];
        }

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        if (!empty($condition['member_id'])) $where[] = ['o.member_id','=', $condition['member_id']];
        if (!empty($condition['channelid'])) $where[] = ['o.channelid','=', $condition['channelid']];
        if (!empty($condition['order_id'])) $where[] = ['o.order_id', '=',$condition['order_id']];
        if (!empty($condition['device_source']) || $condition['device_source']===0) $where[] = ['o.device_source', '=',$condition['device_source']];
        if (!in_array($condition['page_source'],['-1',''])) $where[] = ['o.page_source', '=',$condition['page_source']];
        if (!empty($condition['pay'])) $where[] = ['o.pay', '=',$condition['pay']];
        if (!empty($condition['pay_type'])) $where[] = ['o.pay_type', '=',$condition['pay_type']];
        if (!empty($condition['begin_time'])) $where[] = ['o.addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['o.addtime', '<', strtotime($condition['end_time'] . '23:59:59')];
        if ($condition['status'] === '0' || $condition['status'] == 1 || $condition['status'] == 2) {
            $where[] = ['o.status', '=',$condition['status']];
        }
        if (!empty($condition['pay_channel'])){
            $payid=Db::name("app_pay")->where('channel',$condition['pay_channel'])->where($app_pay_where)->where('name_code',$condition['pay'])->value('id');
            if($payid) $where[] = ['o.pay_id', '=',$payid];
        }
//        $where[]=['is_chess','=',$is_chess];
        $app_pay_ids = Db::name('app_pay')->where([['is_chess','=',$is_chess]])->column('id');
        if(!empty($app_pay_ids))$where[]=['o.pay_id','IN',$app_pay_ids];

        $page_source_list = AppPayOrderService::getConstSelect('PAGE_');
        $page_source_column_list = AppPayOrderService::getConstPluck('PAGE_');
        foreach ($page_source_list as $k=>&$v){
            $v['id']=strval($v['id']);
        }

        $list = Db::name("app_pay_order")->alias('o')->leftJoin('app_pay p','o.pay_id = p.id')->field('o.*,p.show_name')->where($where)->order("o.id desc")
        ->paginate(20)->each(function ($item,$key)use ($page_source_column_list){
                $item['device_source_name']=MemberInfoHelper::DEVICE_TYPE[$item['device_source']]??'未知';
                $item['page_source_name']=$page_source_column_list[$item['page_source']]??'未知';

                if(in_array($item['page_source'],[AppPayOrderService::PAGE_SOURCE_AI[0],AppPayOrderService::PAGE_SOURCE_AI_GOLD[0],AppPayOrderService::PAGE_SOURCE_AI_VIP[0]])){
                    $item['page_source_id_name']=!empty($item['page_source_id'])?(AiService::getConstPluck('AI_TYPE_')[$item['page_source_id']]??''):'';
                }else{
                    $item['page_source_id_name']=!empty($item['page_source_id'])?'id:'.$item['page_source_id']:'';
                }

                return $item;
            });
        // ->fetchsql(true)->find();//->paginate(20);
                // var_dump($list);exit();

        $success_order_rate=$wx_success_order_rate=$zfb_success_order_rate=0;
        $app_pay_order_count_list = Db::name('app_pay_order')->alias('o')->field("o.pay_type")->where($where)->group("o.pay_type")->fieldRaw('sum(if(o.status=0,1,0)) success_count, count(*) count,sum(IF( o.status = 0, order_amount, 0 )) success_sum,sum(order_amount) sum_order_amount')->select();

        $member_count_find = Db::name('app_pay_order')->alias('o')->field("o.pay_type")->where($where)->fieldRaw('count(DISTINCT o.member_id) distinct_member_count , count(DISTINCT IF( o.status = 0, o.member_id, null )) success_distinct_member_count')->find();

//        dump($app_pay_order_count_list->getLastSql());die();
        $app_pay_order_count_column = array_column($app_pay_order_count_list,null,'pay_type');

        if(!empty(array_sum(array_column($app_pay_order_count_column,'success_count'))) &&  array_sum(array_column($app_pay_order_count_column,'count')) ){
            $success_order_rate=number_format(array_sum(array_column($app_pay_order_count_column,'success_count'))/array_sum(array_column($app_pay_order_count_column,'count')) * 100,2);
        }


        if(!empty($app_pay_order_count_column[AppPayOrderService::PAY_TYPE_WECHAT[0]]['success_count']) &&  $app_pay_order_count_column[AppPayOrderService::PAY_TYPE_WECHAT[0]]['count'] ){
            $wx_success_order_rate=number_format($app_pay_order_count_column[AppPayOrderService::PAY_TYPE_WECHAT[0]]['success_count']/$app_pay_order_count_column[AppPayOrderService::PAY_TYPE_WECHAT[0]]['count']* 100,2);
        }

        if(!empty($app_pay_order_count_column[AppPayOrderService::PAY_TYPE_ALIPAY[0]]['success_count']) &&  $app_pay_order_count_column[AppPayOrderService::PAY_TYPE_ALIPAY[0]]['count'] ){
            $zfb_success_order_rate=number_format($app_pay_order_count_column[AppPayOrderService::PAY_TYPE_ALIPAY[0]]['success_count']/$app_pay_order_count_column[AppPayOrderService::PAY_TYPE_ALIPAY[0]]['count']* 100,2);
        }

        if($is_chess==0){
            $is_chess_where = [['is_first','IN',[AppPayOrderService::ORDER_TYPE_MOVIE[0],AppPayOrderService::ORDER_TYPE_AI[0]]]];
        }else{
            $is_chess_where = [['is_first','=',AppPayOrderService::ORDER_TYPE_CHESS[0]]];
        }

        $first_member_count_find = Db::name('app_pay_order')->alias('o')->where(array_merge($where,$is_chess_where,[['o.status','=',0]]))->fieldRaw('count(DISTINCT o.member_id) first_distinct_member_count , sum(o.order_amount) first_success_sum')->find();
        $sum=[
            'success_sum'=>array_sum(array_column($app_pay_order_count_column,'success_sum'))??0,
            'sum_order_amount'=>array_sum(array_column($app_pay_order_count_column,'sum_order_amount'))??0,
            'distinct_member_count'=>$member_count_find['distinct_member_count']??0,
            'success_distinct_member_count'=>$member_count_find['success_distinct_member_count']??0,
            'first_distinct_member_count'=>$first_member_count_find['first_distinct_member_count']??0,
            'first_success_sum'=>$first_member_count_find['first_success_sum']??0,
        ];

        $page = $list->render();
        $list = $list->toArray();

        if($is_chess==AppPayService::IS_CHESS_YES[0]){
            if(!empty($list['data'])){
                $member_ids = array_column($list['data'],'member_id');
                $member_info_cloumn = Db::name('member_info')->whereIn('id',$member_ids)->column('account','id');
                foreach ($list['data'] as &$v){
                    $v['account']=$member_info_cloumn[$v['member_id']]??'';
                }
            }
        }


        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => self::$current_fix_url,
            'page' => $page,
            'title' => '四方支付订单记录',
            'all' => $all,
            'sum' => $sum,
            'overtime' => strtotime('-20 day'),
            'role_id' => $this->user['role_id'],
            'pay_type' => self::getpayType(),
            'success_order_rate' => $success_order_rate,
            'wx_success_order_rate' => $wx_success_order_rate,
            'zfb_success_order_rate' => $zfb_success_order_rate,
            'device_type_list' => MemberInfoHelper::DEVICE_TYPE,
            'page_source_list' => $page_source_list,
        ]);
        return view($view_path);
    }
    public function export_line()
    {
        $all = PayChannelService::spliceChannel();
        unset($all['Proxy']);

        $is_chess = $condition['is_chess'] = trim(input('request.is_chess'));
        $app_pay_where=[
            ['is_chess','=',$is_chess]
        ];

        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['order_id'] = input('request.order_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['pay'] = input('request.pay');
        $condition['status'] = input('request.status');
        $condition['pay_type'] = input('request.pay_type');
        $condition['pay_channel'] = input('request.pay_channel');
        $condition['channelid'] = input('request.channelid');
        $condition['device_source'] = input('request.device_source');
        $condition['start_enter'] = input('request.start_enter');
        $condition['account'] = trim(input('request.account'));
        $condition['page_source'] = trim(input('request.page_source'));

        $condition['start_price'] = input('request.start_price');
        $condition['end_price'] = input('request.end_price');

        if (!empty($condition['start_price'])) $where[] = ['o.order_amount', '>=', $condition['start_price'].'00'];
        if (!empty($condition['end_price'])) $where[] = ['o.order_amount', '<=', $condition['end_price'].'00'];


        if (!empty($condition['account'])) {
            $member_info_search = Db::name('member_info')->where('account','=',$condition['account'])->field('id')->find();
            $where[] = ['o.member_id','=', $member_info_search['id']??0];
        }

        if (!empty($condition['member_id'])) $where[] = ['o.member_id','=', $condition['member_id']];
        if (!empty($condition['channelid'])) $where[] = ['o.channelid','=', $condition['channelid']];
        if (!empty($condition['order_id'])) $where[] = ['o.order_id', '=',$condition['order_id']];
        if (!empty($condition['device_source']) || $condition['device_source']===0) $where[] = ['o.device_source', '=',$condition['device_source']];
        if (!empty($condition['page_source'])) $where[] = ['o.page_source', '=',$condition['page_source']];
        if (!empty($condition['pay'])) $where[] = ['o.pay', '=',$condition['pay']];
        if (!empty($condition['pay_type'])) $where[] = ['o.pay_type', '=',$condition['pay_type']];
        if (!empty($condition['begin_time'])) $where[] = ['o.addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['o.addtime', '<', strtotime($condition['end_time'] . '23:59:59')];
        if ($condition['status'] === '0' || $condition['status'] == 1 || $condition['status'] == 2) {
            $where[] = ['o.status', '=',$condition['status']];
        }
        if (!empty($condition['pay_channel'])){
            $payid=Db::name("app_pay")->where('channel',$condition['pay_channel'])->where($app_pay_where)->where('name_code',$condition['pay'])->value('id');
            if($payid) $where[] = ['o.pay_id', '=',$payid];
        }
//        $where[]=['is_chess','=',$is_chess];
        $app_pay_ids = Db::name('app_pay')->where([['is_chess','=',$is_chess]])->column('id');
        if(!empty($app_pay_ids))$where[]=['o.pay_id','IN',$app_pay_ids];

        $page_source_list = AppPayOrderService::getConstSelect('PAGE_');
        $page_source_column_list = AppPayOrderService::getConstPluck('PAGE_');
        foreach ($page_source_list as $k=>&$v){
            $v['id']=strval($v['id']);
        }


        $data = Db::name("app_pay_order")->alias('o')->leftJoin('app_pay p','o.pay_id = p.id')->field('o.*,p.show_name')->where($where)->order("o.id desc")
            ->select();

//        $data = Db::name("app_pay_order")->where($where)->order("id desc")->field('member_id,channelid,order_id,order_amount,addtime,successtime,status,pay,device_source,page_source,page_source_id')->select();
        foreach ($data as $key => &$value) {
            $value['order_amount'] = $value['order_amount'] / 100;

            $value['status'] = AppPayOrderService::getConstPluck('STATUS_')[$value['status']]??'';

            $value['addtime'] = date('Y-m-d H:i:s',$value['addtime']);
            if ($value['successtime']) {
                $value['successtime'] = date('Y-m-d H:i:s',$value['successtime']);
            }
            if (isset($all[$value['pay']])) {
                $value['pay'] = $all[$value['pay']]['name'];
            }else{
                $value['pay'] = '未知';
            }
            $value['device_source_name'] = MemberInfoHelper::DEVICE_TYPE[$value['device_source']]??'未知';
            unset($value['device_source']);

            $value['page_source_name']=$page_source_column_list[$value['page_source']]??'未知';
            if($value['page_source']==AppPayOrderService::PAGE_SOURCE_AI[0]){
                $value['page_source_id_name']=!empty($value['page_source_id'])?(AiService::getConstPluck('AI_TYPE_')[$value['page_source_id']]):'';
            }else{
                $value['page_source_id_name']=!empty($value['page_source_id'])?'id:'.$value['page_source_id']:'';
            }
            if(!empty($value['page_source_id_name'])){
                $value['page_source_name'].="\n".$value['page_source_id_name'];
            }

        }

        $field_list = [
            'member_id'=>'用户id',
            'channelid'=>'渠道uid',
            'order_id'=>'订单号',
            'page_source_name'=>'订单溯源',
            'order_amount'=>'订单金额',
            'addtime'=>'转账时间',
            'successtime'=>'成功时间',
            'status'=>'状态',
            'pay'=>'支付商',
            'device_source_name'=>'订单来源'
        ];
        if($condition['is_chess']==AppPayService::IS_CHESS_YES[0]){
            if(!empty($data)){
                $member_ids = array_column($data,'member_id');
                $member_info_cloumn = Db::name('member_info')->whereIn('id',$member_ids)->column('account','id');
                foreach ($data as &$v){
                    $v['account']=$member_info_cloumn[$v['member_id']]??'';
                }
            }

            $field_list = [
                'member_id'=>'用户id',
                'account'=>'用户名称',
                'channelid'=>'渠道uid',
                'order_id'=>'订单号',
                'order_amount'=>'订单金额',
                'addtime'=>'转账时间',
                'successtime'=>'成功时间',
                'status'=>'状态',
                'pay'=>'支付商',
                'device_source_name'=>'订单来源'
            ];

        }

        $title = array_values($field_list);

        foreach ($data as $k=>$v){
            $temp_detail=$data[$k];
            $data[$k]=[];
            foreach ($field_list as $k1=>$v1){
                $data[$k][$k1]=$temp_detail[$k1]??'';
            }
        }

        //没有数据输出空字符串，前端会判断的
        if(empty($data)){echo '';exit;}

        $filename = '四方充值订单记录';
        $this->excel_data($filename,$title,$data);exit;
    }
    /**
     * <AUTHOR>
     * @DateTime 2020-09-18
     * @param    [type]     $id [description]
     * @return   [type]         [手动回调订单]
     */
    public function lineback($id)
    {
        $re = Db::name('app_pay_order')->where('id',$id)->find();
        if (!$re ||  !in_array($re['status'],[1,2])) {
            json_fail_admin('订单有误!');
        }
        if ($re['addtime'] <  strtotime('-20 day') && $this->user['role_id'] != 1) {
            json_fail_admin('超过二十天的订单只能系统管理员才能手动回调!');
        }
        (new \app\api\model\Base())->paySuccess($re['order_id'],$re['pay_amount'] / 100, $this->user['username'].' - 手动回调订单');


        $this->addlog('手动回调订单','订单ID: '.$id);

        json_success_admin('操作成功!');
    }



    /**
     * @return   [type]     [游戏钱包流水记录]
     */
    public function game_amount_record()
    {
        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['order_id'] = input('request.order_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['type'] = input('request.type');
        $condition['sub_type'] = input('request.sub_type');
        $condition['channelid'] = input('request.channelid');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }


        if (!empty($condition['member_id'])) $where[] = ['member_id','=', $condition['member_id']];
        if (!empty($condition['order_id'])) $where[] = ['order_id', '=',$condition['order_id']];
        if (!empty($condition['type'])) $where[] = ['type', '=',$condition['type']];
        if (!empty($condition['sub_type'])) $where[] = ['sub_type', '=',$condition['sub_type']];
        if (!empty($condition['channelid'])) $where[] = ['channelid', '=',$condition['channelid']];
        if (!empty($condition['begin_time'])) $where[] = ['create_time', '>=', $condition['begin_time'] . ' 00:00:00'];
        if (!empty($condition['end_time'])) $where[] = ['create_time', '<', $condition['end_time'] . ' 23:59:59'];

        $list = Db::name('game_amount_record')->where($where)
            ->order('id desc')->paginate(20);

        $page = $list->render();
        $list = $list->toArray();

        $sum = 0;
        foreach ($list['data'] as $key => &$value) {
            $value['type_str'] = getGameTradTypeStr($value['type'],$value['sub_type']);
            $sum += $value['amount'];
        }
        $ssum = Db::name('game_amount_record')->where($where)->sum('amount');


        $paytype = GameAmountRecordService::TYPES_LIST;

        $sub_type = null;

        if ($condition['type']) {

            foreach ($paytype as $k => $v) {
                if ($v['type'] == $condition['type']) {
                    $sub_type = $v['sub_type'];
                    break;
                }
            }
        }

        $this->assign([
            'list' => $list,
            'page' => $page,
            'condition' => $condition,
            'current_url' => '/admin/pay/game_amount_record',
            'title' => '游戏钱包流水记录',
            'paytype' => $paytype,
            'sub_type' => $sub_type,
            'sum' => $sum,
            'ssum' => $ssum
        ]);
        return view();
    }
    /**
     * <AUTHOR>
     * @DateTime 2020-09-16
     * @return   [type]     [用户总流水]
     */
    public function order()
    {

        $request = request()->get();
        $is_export = $request['is_export']??'';

        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['order_id'] = input('request.order_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['type'] = input('request.type');
        $condition['sub_type'] = input('request.sub_type');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        if (!empty($condition['member_id'])) $where[] = ['member_id','=', $condition['member_id']];
        if (!empty($condition['order_id'])) $where[] = ['order_id', '=',$condition['order_id']];
        if (!empty($condition['type'])) $where[] = ['type', '=',$condition['type']];
        if (!empty($condition['sub_type'])) $where[] = ['sub_type', '=',$condition['sub_type']];
        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'] . '23:59:59')];

        $list = Db::name('app_pay_deal')->where($where)
            ->order('id desc')->paginate($is_export==1?CommonConst::EXPORT_LIMIT+1:20);

        $page = $list->render();
        $list = $list->toArray();

        $sum = 0;
        foreach ($list['data'] as $key => &$value) {
            $value['type_str'] = getTradTypeStr($value['type'],$value['sub_type']);
            $sum += $value['amount'];
        }
        $ssum = Db::name('app_pay_deal')->where($where)->sum('amount');


        $paytype = config('paytype.');

        $sub_type = null;

        if ($condition['type']) {

            foreach ($paytype as $k => $v) {
               if ($v['type'] == $condition['type']) {
                    $sub_type = $v['sub_type'];
                    break;
               }
            }
        }

        if($is_export==1){
            $this->export_excel($list['data']);
        }

        $this->assign([
            'list' => $list,
            'page' => $page,
            'condition' => $condition,
            'current_url' => '/admin/pay/order',
            'title' => '用户流水',
            'paytype' => $paytype,
            'sub_type' => $sub_type,
            'sum' => $sum,
            'ssum' => $ssum
        ]);
        return view();
    }


    public function export_excel($data){

        foreach ($data as $v){
            $export_data[]=[
                'id'=>$v['id'],
                'member_id'=>$v['member_id'],
                'order_id'=>$v['order_id'],
                'amount'=>$v['amount']/100,
                'is_first'=>$v['is_first']==1?'首冲':'否',
                'type_str'=>$v['type_str'],
                'amount_before'=>$v['amount_before']/100,
                'amount_now'=>$v['amount_now']/100,
                'addtime'=>date('Y-m-d H:i:s',$v['addtime']),
            ];
        }
        $title = ['ID','用户ID','订单号','账变金额','是否首冲','账变类型','账变前余额','账变后余额','时间'];

        if(count($data)>CommonConst::EXPORT_LIMIT){
            array_pop($export_data);
            $warn=[];
            foreach ($title as $k=>$v){
                $warn[]='一次最多只能导出'.CommonConst::EXPORT_LIMIT.'条';
            }

            $export_data[]=$warn;

        }
        if(empty($export_data)){echo '';die();}
        $filename = '用户流水记录列表';
        $this->excel_data($filename,$title,$export_data);exit;
    }

    /**
     * @param int $is_chess 是否棋牌专属 0否 1是（这里保持兼容旧代码，controller当内部调用方法）
     * @param string $view_path 视图层路径（这里保持兼容旧代码，controller当内部调用方法）
     * @return \think\response\View
     * @throws \think\exception\DbException
     */
    public function price(int $is_chess=0,string $view_path='')
    {
        $request_is_chess = input('request.is_chess');
        if(isset($request_is_chess))json_fail_admin('非法参数');

        $where = [
            ['p.is_chess','=',$is_chess]
        ];
        $where_not_join = [
            ['is_chess','=',$is_chess]
        ];

        $condition = [];
        $condition['pay_id'] = input('request.pay_id');
        $condition['name_code'] = input('request.name_code');
        $condition['start_price'] = input('request.start_price');
        $condition['end_price'] = input('request.end_price');
        if (!empty($condition['pay_id'])) $where[] = ['a.pay_id','=', $condition['pay_id']];
        if (!empty($condition['name_code'])) $where[] = ['p.name_code','=', $condition['name_code']];
        if (!empty($condition['start_price'])) $where[] = ['a.price', '>=', $condition['start_price'].'00'];
        if (!empty($condition['end_price'])) $where[] = ['a.price', '<=', $condition['end_price'].'00'];

        $list = Db::name('app_pay_price')->alias('a')->join('app_pay p','a.pay_id = p.id','LEFT')->join('agent_info g','a.agent_id = g.id','LEFT')->where($where)
            ->order('a.sort desc')->field('a.id,a.pay_id,a.price,a.price_give,a.price_per,a.agent_id,a.sort,p.show_name,p.channel,p.name_code,g.nick_name')->paginate(20);
//                    dump(Db::name('app_pay_price')->getLastSql());die();
        $all = PayChannelService::spliceChannel(0);  //所有四方商户
        $pays = Db::name('app_pay')->field('id,name_code,show_name,channel')->where($where_not_join)->select();//所有支付方式

        foreach ($pays as $key => &$value) {
            $value['name'] = $all[$value['name_code']]['name']??'';
        }

        $page = $list->render();
        $list = $list->toArray();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => self::$current_fix_not_fun_url,
            'page' => $page,
            'title' => '充值金额配置',
            'all' =>  AppPayService::getPayDiffList($is_chess),
            'pays' => $pays,

        ]);
        return view($view_path);
    }

    /**
     * <AUTHOR>
     * @DateTime 2020-09-18
     * @param int $is_chess 是否棋牌专属 0否 1是（这里保持兼容旧代码，controller当内部调用方法）
     * @param string $view_path 视图层路径（这里保持兼容旧代码，controller当内部调用方法）
     * @return   [type]     [增加金额配置]
     */
    public function addprice(int $is_chess=0,string $view_path='addprice')
    {

        $request_is_chess = input('request.is_chess');
        if(isset($request_is_chess))json_fail_admin('非法参数');

        $where = [
            ['is_chess','=',$is_chess]
        ];

        if (request()->isPost()) {


            $data = input('post.');
            if (!$data['name_code']) {
                json_fail_admin('支付品牌不能为空！');
            }
            if (!$data['pay_id']) {
                json_fail_admin('支付方式不能为空');
            }
            if ($data['name_code'] == 'Proxy' ) {
                if (!$data['agent_id']) {
                    json_fail_admin('代理账号不能为空');
                }
                if (Db::name('app_pay_price')->where(['pay_id'=>$data['pay_id'],'agent_id'=>$data['agent_id']])->find()) {
                    json_fail_admin('代理账号重复');
                }
                $insert = [
                    'pay_id' => $data['pay_id'],
                    'agent_id' => $data['agent_id'],
                    'sort' => $data['sort']
                ];
            }else{

                $detail_app_pay = Db::name('app_pay')->where(['id'=>$data['pay_id']])->find();
                if(empty($detail_app_pay)){
                    json_fail_admin('支付商不存在');
                }

                $pay_config = PayChannelService::spliceChannel(0);
                if(!empty($pay_config[$detail_app_pay['name_code']]['channels'][$detail_app_pay['channel']]) && strpos($pay_config[$detail_app_pay['name_code']]['channels'][$detail_app_pay['channel']], '(海外专用)') !== false ){
                    if (!preg_match('/^[0-9]+(.[0-9]{1,2})?$/' ,$data['price']) ) {
                        json_fail_admin('海外专用充值金额必须大于1，且最多支持小数点后2位');
                    }

                    if ($data['price'] < 1 || $data['price'] > 999999) {
                        json_fail_admin('充值金额1-999999之间，海外专用支持小数点后2位');
                    }
                }else{
                    if (!preg_match("/^[1-9][0-9]*$/" ,$data['price']) ) {
                        json_fail_admin('充值金额不合法');
                    }

                    if ($data['price'] < 10 || $data['price'] > 999999) {
                        json_fail_admin('充值金额10-999999之间');
                    }
                }

                if ($data['price_give'] != 0 && !preg_match("/^[1-9][0-9]*$/" ,$data['price_give'])) {
                    json_fail_admin('赠送金额不合法');
                }
                if (!check_number($data['price_per'])) {
                    json_fail_admin('赠送比例不合法');
                }
                if (Db::name('app_pay_price')->where(['pay_id'=>$data['pay_id'],'price'=>$data['price'] * 100])->find()) {
                    json_fail_admin('充值金额重复');
                }
                $insert = [
                    'pay_id' => $data['pay_id'],
                    'price' => $data['price'] * 100,
                    'price_give' => $data['price_give'] * 100,
                    'price_per' => $data['price_per'],
                    'sort' => $data['sort'],
                ];
            }

            redis()->del('pay_prices_'.$data['pay_id']);
            $re = Db::name('app_pay_price')->insert($insert);
            if (!$re) {
                json_fail_admin('添加失败,请重试!');
            }
            CacheHelper::delPayCache(intval($data['pay_id']));
            $this->addlog('添加支付金额配置',json_encode($insert));
            json_success_admin('添加成功!');
        }else{

            //代理账号
//            $agent = Db::name('agent_info')->where('status',1)->field('id,nick_name')->select();

            $this->assign([
                'all' => AppPayService::getPayDiffList($is_chess),
                'agent' => [],
            ]);
            return view($view_path);
        }
    }
    /**
     * <AUTHOR>
     * @DateTime 2020-09-18
     * @param    [type]     $id [description]
     * @param int $is_chess 是否棋牌专属 0否 1是（这里保持兼容旧代码，controller当内部调用方法）
     * @param string $view_path 视图层路径（这里保持兼容旧代码，controller当内部调用方法）
     * @return   [type]         [修改金额配置]
     */
    public function editprice($id,int $is_chess=0,string $view_path='editprice')
    {

        if (request()->isPost()) {

            $data = input('post.');

            if (!$data['name_code']) {
                json_fail_admin('支付品牌不能为空！');
            }
            if (!$data['pay_id']) {
                json_fail_admin('支付方式不能为空');
            }
            if ($data['name_code'] == 'Proxy' ) {
                if (!$data['agent_id']) {
                    json_fail_admin('代理账号不能为空');
                }
                $up = [
                    'pay_id' => $data['pay_id'],
                    'agent_id' => $data['agent_id'],
                    'sort' => $data['sort'],
                    'id' => $data['id'],
                ];
            }else{
                $detail_app_pay = Db::name('app_pay')->where(['id'=>$data['pay_id']])->find();
                if(empty($detail_app_pay)){
                    json_fail_admin('支付商不存在');
                }

                $pay_config = PayChannelService::spliceChannel(0);
                if(!empty($pay_config[$detail_app_pay['name_code']]['channels'][$detail_app_pay['channel']]) && strpos($pay_config[$detail_app_pay['name_code']]['channels'][$detail_app_pay['channel']], '(海外专用)') !== false ){
                    if (!preg_match('/^[0-9]+(.[0-9]{1,2})?$/' ,$data['price']) ) {
                        json_fail_admin('海外专用充值金额必须大于1，且最多支持小数点后2位');
                    }

                    if ($data['price'] < 1 || $data['price'] > 999999) {
                        json_fail_admin('充值金额1-999999之间，海外专用支持小数点后2位');
                    }
                }else{
                    if (!preg_match("/^[1-9][0-9]*$/" ,$data['price']) ) {
                        json_fail_admin('充值金额不合法');
                    }

                    if ($data['price'] < 10 || $data['price'] > 999999) {
                        json_fail_admin('充值金额10-999999之间');
                    }
                }
                if ($data['price_give'] != 0 && !preg_match("/^[1-9][0-9]*$/" ,$data['price_give'])) {
                    json_fail_admin('赠送金额不合法');
                }
                if (!check_number($data['price_per'])) {
                    json_fail_admin('赠送比例不合法');
                }
                $up = [
                    'pay_id' => $data['pay_id'],
                    'price' => $data['price'] * 100,
                    'price_give' => $data['price_give'] * 100,
                    'price_per' => $data['price_per'],
                    'sort' => $data['sort'],
                    'id' => $data['id'],

                ];
            }

            redis()->del('pay_prices_'.$data['pay_id']);
            $re = Db::name('app_pay_price')->update($up);
            if (!$re) {
                json_fail_admin('修改失败,请重试!');
            }
            CacheHelper::delPayCache(intval($data['pay_id']));
            $this->addlog('修改支付金额配置',json_encode($up));

            json_success_admin('修改成功!');
        }else{

            $price = Db::name('app_pay_price')->alias('a')->where('a.id',$id)->join('app_pay p','a.pay_id = p.id','LEFT')->join('agent_info g','a.agent_id = g.id','LEFT') ->order('a.sort desc')->field('a.id,a.pay_id,a.price,a.price_give,a.price_per,a.agent_id,a.sort,p.show_name,p.name_code,g.nick_name')->find();

            if (!$price) {
                json_fail_admin('数据不存在,请重试!');
            }

            //代理账号
//            $agent = Db::name('agent_info')->where('status',1)->field('id,nick_name')->select();

            $this->assign([
                'all' => AppPayService::getPayDiffList($is_chess),
                'agent' => [],
                'price' => $price
            ]);
            return view($view_path);
        }
    }
    /**
     * <AUTHOR>
     * @DateTime 2020-09-18
     * @param    [type]     $id [description]
     * @return   [type]         [删除金额配置]
     */
    public function delprice($id,int $is_chess=0)
    {
        $pay_id = Db::name('app_pay_price')->where('id',$id)->value('pay_id');
        if (!$pay_id) {
            json_fail_admin();
        }

        if(empty(Db::name('app_pay')->where('id',$pay_id)->where('is_chess',$is_chess)->field('id')->find())){
            json_fail_admin('该配置不存在');
        }

        redis()->del('pay_prices_'.$pay_id);
        $res = Db::name('app_pay_price')->delete($id);
        if (!$res) {
            json_fail_admin();
        }

        CacheHelper::delPayCache(intval($pay_id));
        $this->addlog('删除支付金额配置',$id);

        json_success_admin();
    }


    public function ob_log()
    {
        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['order_id'] = input('request.order_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['status'] = input('request.status');
        $condition['type'] = input('request.type');

        if (!empty($condition['member_id'])) $where[] = ['member_id','=', $condition['member_id']];
        if (!empty($condition['type'])) $where[] = ['type','=', $condition['type']];
        if (!empty($condition['order_id'])) $where[] = ['order_id', '=',$condition['order_id']];
        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'] . '23:59:59')];
        if ($condition['status'] === '0' || $condition['status'] == 1) {
             $where[] = ['status', '=',$condition['status']];
        }

        $list = Db::name("app_pay_ob")->where($where)->order("id desc")->paginate(20);
        $sum = Db::name("app_pay_ob")->where($where)->sum('amount') / 100;
        $page = $list->render();
        $list = $list->toArray();
        foreach ($list['data'] as $key => &$value) {
            $tmp = Db::name('member_ob_info')->where('member_id',$value['member_id'])->field('ob_id,name')->find();
            $value['ob_id'] = $tmp['ob_id'];
            $value['ob_name'] = $tmp['name'];
        }

        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => '/admin/pay/ob_log',
            'page' => $page,
            'sum' => $sum,
            'title' => '欧宝转账订单记录',
        ]);

        return view();
    }

    public function export_ob_log()
    {
        $where = [];
        $condition['member_id'] = input('param.member_id');
        $condition['order_id'] = input('param.order_id');
        $condition['begin_time'] = input('param.begin_time');
        $condition['end_time'] = input('param.end_time');
        $condition['status'] = input('param.status');
        $condition['type'] = input('param.type');

        if (!empty($condition['member_id'])) $where[] = ['o.member_id','=', $condition['member_id']];
        if (!empty($condition['type'])) $where[] = ['o.type','=', $condition['type']];
        if (!empty($condition['order_id'])) $where[] = ['o.order_id', '=',$condition['order_id']];
        if (!empty($condition['begin_time'])) $where[] = ['o.addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['o.addtime', '<', strtotime($condition['end_time'] . '23:59:59')];
        if ($condition['status'] === '0' || $condition['status'] == 1) {
             $where[] = ['o.status', '=',$condition['status']];
        }

        $data = Db::name("app_pay_ob")->alias('o')->join('member_ob_info i','o.member_id = i.member_id','left')->where($where)->order("id desc")->field('o.id,o.order_id,i.ob_id,i.name,o.amount,o.addtime,o.successtime,o.status')->select();
        foreach ($data as $key => &$value) {
            $value['amount'] = $value['amount'] / 100;
            if ($value['status'] == 1) {
                $value['status'] = '等待';
            }else{
                $value['status'] = '成功';
            }
            $value['addtime'] = date('Y-m-d H:i:s',$value['addtime']);
            if ($value['successtime']) {
                $value['successtime'] = date('Y-m-d H:i:s',$value['successtime']);
            }

        }
        $title = ['ID','订单号','欧宝用户ID','欧宝用户名','金额','转账时间','成功时间','状态'];
        $filename = '欧宝转账订单记录';

        $this->excel_data($filename,$title,$data);exit;
    }
    public function payqq()
   {
        if ($this->request->isGet()) {
            $this->assign([
                'current_url' => '/admin/pay/payqq',
            ]);
            return view('payqq');
        }else{
            foreach ($this->request->post() as $key => $vo) {

                sysconf($key, $vo);
            }
            $this->addlog('配置客服QQ', json_encode($this->request->post()));
            json_success_admin('配置成功!');
        }

   }

    public function movie_deal()
    {
        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['order_id'] = input('request.order_id');
        $condition['movie_id'] = input('request.movie_id');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['category_ids'] = input('request.category_ids');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }


        if (!empty($condition['member_id'])) $where[] = ['member_id','=', $condition['member_id']];
        if (!empty($condition['order_id'])) $where[] = ['order_id', '=',$condition['order_id']];
        if (!empty($condition['movie_id'])) $where[] = ['movie_id', '=',$condition['movie_id']];
        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'] . '23:59:59')];
        if (!empty($condition['category_ids'])) $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$condition['category_ids']}',am.category_ids)")];

        $list = Db::name("app_pay_movie")->alias('mp')->Leftjoin('app_movie am', 'mp.movie_id=am.id')->field('mp.id,mp.order_id,mp.addtime,mp.movie_id,mp.member_id,mp.amount,am.title title,am.category_ids')->where($where)->order("id desc")->paginate(20);
        $sum = Db::name("app_pay_movie")->alias('mp')->Leftjoin('app_movie am', 'mp.movie_id=am.id')->field('mp.id,mp.order_id,mp.addtime,mp.movie_id,mp.member_id,mp.amount,am.title title,am.category_ids')->where($where)->sum('amount') / 100;


        $page = $list->render();
        $list = $list->toArray();
        $cate = Db::name('app_category')->field('id,title')->where('category_type', '=', 'movie')->select();

        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => '/admin/pay/movie_deal',
            'page' => $page,
            'cate' => $cate,
            'sum' => $sum
        ]);
        return view();
    }

    //禁用页面
    public function status($id)
    {
        $res = Db::name('app_pay')->where('id',$id)->find();
        if (empty($res)) json_fail_admin('数据不存在');

        $res = Db::name('app_pay')->where('id',$id)->exp('status', 'if(status=1,2,1)')->update();
        if (!$res) {
            json_fail_admin();
        }
        $this->addlog(($res['status']==1?'禁用':'启用').'支付状态','表名: app_pay 数据ID: '.$id);

        json_success_admin();
    }


    /**
     * 支付前台排序
     * @return View|void
     */
    public function pay_sort(){
        if ($this->request->isGet()) {
            $this->assign([
                'current_url' => '/admin/pay/pay_sort',
                'pay_sort' => json_decode(sysconf("pay_sort"),true),
            ]);
            return view();
        }else{
            $data =  $this->request->post();
            foreach ($data as $k=>$v){
                if(!preg_match("/^[1-9][0-9]*$/" ,$v)){
                    json_fail_admin('请填入正整数');
                }
            }
            sysconf('pay_sort', json_encode([
                '2'=>$data['alipay']??0,
                '1'=>$data['wechat']??0,
                '3'=>$data['bank_card']??0,
            ]));
            $this->addlog('支付前台排序', json_encode($this->request->post()));
            json_success_admin('配置成功!');
        }
    }
}
