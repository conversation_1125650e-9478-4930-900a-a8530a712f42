<?php
namespace app\admin\controller;

use app\common\BizConst;
use app\common\Constants\CommonConst;
use app\common\Constants\RedisConst;
use app\Helper\CacheHelper;
use app\Helper\MemberInfoHelper;
use app\Service\AiService;
use app\Service\AppPayOrderService;
use app\Service\AppPayService;
use app\Service\GameAmountRecordService;
use app\Service\PayChannelService;
use think\Db;
use think\db\Where;
use think\response\View;

class PayChannel extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->current_url = "/admin/pay_channel";
    }
    public function _assign()
    {
        $this->assign("current_url", $this->current_url);
    }

    /**
     * 获取支付类型列表
     * @return array
     */
    protected static function getpayType(){
        return AppPayOrderService::getConstPluck("PAY_TYPE_");
    }


    public function index()
    {
        $condition['name_code']  = input('request.name_code');
        $condition['status']  = input('request.status');

        $all = PayChannelService::spliceChannel();
        $all = array_reverse($all);
//        dump($all);die();
        $where=[];

        if (!empty($condition['name_code'])) $where[] = ['name_code','=', $condition['name_code']];
        if (isset($condition['status']) && $condition['status']!='') $where[] = ['status','=', $condition['status']];


        $list = Db::name("pay_channel")->where($where)->order("status asc,id desc")->paginate(20)->each(function ($item,$key)use ($all){
            $item['pay_name']=$all[$item['name_code']]['name']??'';
            return $item;
        });

        $page = $list->render();
        $list = $list->toArray();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'current_url' => self::$current_fix_not_fun_url,
            'page' => $page,
            'all' => $all,
            'pay_type' => self::getpayType(),
        ]);
        $this->_assign();
        return view();
    }


    public function add()
    {
        $all = PayChannelService::spliceChannel();
        $all = array_reverse($all);
        if (request()->isPost()) {
            $data = input('post.');
            if (!$data['name_code']) {
                json_fail_admin('支付品牌不能为空！');
            }
            if (!$data['channel']) {
                json_fail_admin('通道编码不能为空');
            }
            if (!$data['title']) {
                json_fail_admin('通道名称不能为空');
            }

            $re = Db::name('pay_channel')->where(['name_code'=>$data['name_code'],'channel'=>$data['channel']])->find();
            if (!empty($re))json_fail_admin('该通道编码已存在!');

            $insert = [
                'name_code' => $data['name_code'],
                'channel' => $data['channel'],
                'title' => $data['title'],
            ];

            $id = Db::name('pay_channel')->insertGetId($insert);

            if (!$id) {
                json_fail_admin('添加失败,请重试!');
            }
            PayChannelService::delCache();
            $this->addlog('添加通道编码','通道名: '.$insert['title'].',通道编码：'.$data['channel']);
            json_success_admin('添加成功!');
        }
        $this->assign([
            'all' => $all,
            'pay_type' => self::getpayType()
        ]);
        return view();

    }


    public function edit($id)
    {
        $res = Db::name("pay_channel")->where('id',$id)->find();
        if (empty($res)) json_fail_admin('支付通道不存在');
        $all = PayChannelService::spliceChannel();
        $all = array_reverse($all);


        if (request()->isPost()) {
            $post = input('post.');
            if(!in_array($post['status'],[1,2])) json_fail_admin('状态参数不正确');
            $data = [
                'name_code' => $post['name_code'],
                'channel' => $post['channel'],
                'title' => $post['title'],
                'status' => $post['status'],
            ];

            $re = Db::name('pay_channel')->where(['id'=>$res['id']])->update($data);
            if ($re) {
                PayChannelService::delCache();
                $this->addlog('修改支付通道','修改前: '.json_encode($res,JSON_UNESCAPED_UNICODE).',修改后：'.json_encode($data,JSON_UNESCAPED_UNICODE));
            }
            json_success_admin('修改成功!');
        }
        $this->assign([
            'info' => $res,
            'all' => $all,
            'pay_type' => self::getpayType()
        ]);
        return view();
    }

    //禁用页面
    public function status($id)
    {
        $res = Db::name('pay_channel')->where('id',$id)->find();
        if (empty($res)) json_fail_admin('数据不存在');

        $res = Db::name('pay_channel')->where('id',$id)->exp('status', 'if(status=1,2,1)')->update();
        if (!$res) {
            json_fail_admin();
        }
        PayChannelService::delCache();
        $this->addlog(($res['status']==1?'禁用':'启用').'支付状态','支付通道 数据ID: '.$id);

        json_success_admin();
    }

}
