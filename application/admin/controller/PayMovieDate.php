<?php
namespace app\admin\controller;

use app\Service\PrizeService;
use app\Service\TurntableService;
use think\Db;
class PayMovieDate extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '单视频销售记录',
            'name'=>'pay_movie_date',
            'current_url'=>'/admin/pay_movie_date',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['movie_name'] = input('request.movie_name');
        $condition['product_id'] = input('request.product_id');
        $condition['start_time'] = input('request.start_time');
        $condition['end_time'] = input('request.end_time');
        $condition['sort_type'] = input('request.sort_type');
        $condition['movie_id'] = input('request.movie_id');

        if(input('request.start_enter')!=1){
            if(empty($condition['start_time']))$condition['start_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        if (!empty($condition['member_id'])) $where[] = ['a.member_id','=', $condition['member_id']];
        if (!empty($condition['movie_id'])) $where[] = ['a.movie_id','=', $condition['movie_id']];
        if (!empty($condition['start_time'])) $where[] = ['a.create_time', '>=', $condition['start_time'] . ' 00:00:00'];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', $condition['end_time'] . ' 23:59:59'];


        $sort_type='';
        $pay_movie_date_query = Db::name("pay_movie_date");
        if (!empty($condition['sort_type'])) {
            switch ($condition['sort_type']){
                case 1:
                     $sort_type = "a.order_count desc";
                    break;
                case 2:
                    $sort_type = "b.create_time desc";
                    break;
            }

        }

        if(!empty($condition['movie_name'])){
            $movie_ids = Db::name('app_movie')->where([['title','LIKE', '%' .$condition['movie_name'].'%' ]])->column("id");
            if(!empty($movie_ids)){
                $where[] = ['a.movie_id','IN', $movie_ids];
            }else{
                $where[] = ['a.id','=', 0];
            }
        }
        if(!empty($condition['product_id'])){
            $movie_ids = Db::name('app_movie')->where([['product_id','=', $condition['product_id'] ]])->column("id");
            if(!empty($movie_ids)){
                $where[] = ['a.movie_id','IN', $movie_ids];
            }else{
                $where[] = ['a.id','=', 0];
            }
        }

        $app_products = Db::name("app_products")->field('id,title')->order('id ASC')->select();

        $app_product_list = Db::name('app_products')->field("id,title")->select();
        $app_product_column = array_column($app_product_list,null,'id');

//        $app_movie_list = Db::name("app_movie")->field("id,product_id,title,price")->select();
//        $app_movie_column = array_column($app_movie_list,null,'id');

        $rows=20;
        if(!empty($_GET['is_export']) && $_GET['is_export']==1)$rows=99999999;

        if(empty($sort_type))$sort_type="a.id desc";
        $list=$pay_movie_date_query->alias("a")->field('a.*,b.product_id,b.title,b.price,b.create_time movie_create_time')->leftJoin('app_movie b', "a.movie_id=b.id")->where($where)->order($sort_type)
            ->paginate($rows)->each(function($item, $key)use ($app_product_column){
                $item['product_name'] = $app_product_column[$item['product_id']??'']['title']??'';
                $item['movie_name'] = $item['title']??'';
                if(!empty($item['movie_create_time'])){
                    $item['movie_create_time']=date('Y-m-d H:i:s',$item['movie_create_time']);
                }
                $item['price'] = $app_movie_column[$item['movie_id']]['price']??0;
                if($item['amount']>0)$item['amount']=$item['amount']/100;
                return $item;
            });

        $page = $list->render();

        if(!empty($_GET['is_export']) && $_GET['is_export']==1){

            // 日志记录
            $this->addlog('导出单视频销售记录',json_encode(['before_data'=>[],'after_data'=>$where]));

            $list=$list->items();


//            dump($list);die();

            $export_data=[];
            foreach ($list as $v){
                $export_data[]=[
                    'movie_id'=>$v['movie_id'],
                    'movie_name'=>$v['movie_name'],
                    'product_name'=>$v['product_name'],
                    'price'=>$v['price'],
                    'count'=>$v['count'],
                    'amount'=>$v['amount'],
                    'order_count'=>$v['order_count'],
                    'last_buy_time'=>$v['last_buy_time'],
                    'movie_create_time'=>$v['movie_create_time'],
                ];
            }

            $title = ['视频id','视频名称','出品方','售价','购买人数','销售额','拉单人数','最近购买时间','视频创建时间'];
            $filename = '单视频销售记录';
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'app_products'=>$app_products,
        ]);

        return view();
    }


}
