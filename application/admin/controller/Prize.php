<?php
namespace app\admin\controller;

use app\Service\PrizeService;
use think\Db;
class Prize extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '转盘奖品管理',
            'name'=>'prize',
            'current_url'=>'/admin/prize',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];

        $list = Db::name("prize")->alias('o')->field('o.*')->whereNull("delete_time")->where($where)->order("o.id desc")
            ->paginate(20)->each(function($item, $key){
                $item['type_name']=PrizeService::getConstPluck('TYPE_')[$item['type']]??'';
                return $item;
            });
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'page' => $page,
        ]);

        return view();
    }



    public function add(){
        if (!request()->isPost()) {
            $this->assign([
                'type_list' => PrizeService::getConstSelect('TYPE_','id','title'),
            ]);
            return view();
        } else {

            $post = input("post.");
                    
            $post['title'] = trim($post['title']);
            if (empty($post['title'])) {
                json_fail_admin("奖品名称不能为空");
            }

            $post['type'] = trim($post['type']);
            if (empty($post['type'])) {
                json_fail_admin("奖品类型不能为空");
            }

            $post['number'] = trim($post['number']);
            if (empty($post['number']) && in_array($post['type'],[PrizeService::TYPE_GOLD[0],PrizeService::TYPE_VIP[0],PrizeService::TYPE_SUPER_VIP[0]])) {
                json_fail_admin("金币或vip类型时，数量必填");
            }

            if (empty($post['number']) && is_numeric($post['number'])) {
                json_fail_admin("数量必须是数字类型");
            }

            $restar = Db::name("prize")->where('title',$post['title'])->whereNull('delete_time')->find();
            if($restar){
                json_fail_admin("奖品名称不能重复");
            }

            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            $this->addlog('添加转盘奖品','转盘奖品ID:'.$res.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        }
    }


    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->whereNull('delete_time')->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input("post.");

            $post['title'] = trim($post['title']);
            if (empty($post['title'])) {
                json_fail_admin("奖品名称不能为空");
            }

            $post['type'] = trim($post['type']);
            if (empty($post['type'])) {
                json_fail_admin("奖品类型不能为空");
            }

            $post['number'] = trim($post['number']);
            if (empty($post['number']) && in_array($post['type'],[PrizeService::TYPE_GOLD[0],PrizeService::TYPE_VIP[0],PrizeService::TYPE_SUPER_VIP[0]])) {
                json_fail_admin("金币或vip类型时，数量必填");
            }

            if (empty($post['number']) && is_numeric($post['number'])) {
                json_fail_admin("数量必须是数字类型");
            }


            $restar = Db::name("prize")->where('title',$post['title'])->where('id','<>',$id)->whereNull('delete_time')->find();
            if($restar){
                json_fail_admin("奖品名称不能重复");
            }

            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);

            $this->addlog('修改转盘奖品','转盘奖品ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            json_success_admin();
        } else {

            $this->assign([
                'res' => $res,
                'type_list' => PrizeService::getConstSelect('TYPE_','id','title'),
            ]);
            return view();
        }
    }


}