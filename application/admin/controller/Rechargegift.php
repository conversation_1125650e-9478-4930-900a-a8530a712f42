<?php
namespace app\admin\controller;

use think\Db;
class Rechargegift extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '充值赠送设置',
            'name'=>'rechargegift',
            'current_url'=>'/admin/rechargegift',
        ];
        parent::__construct($conf);
    }

    //充值赠送活动列表
    public function index()
    {

        $where = [];
        $condition['min_order_price'] = input('request.min_order_price');
        $condition['max_order_price'] = input('request.max_order_price');
        if (!empty($condition['min_order_price'])) $where[] = ['min_order_price', '>=', $condition['min_order_price']];
        if (!empty($condition['max_order_price'])) $where[] = ['max_order_price', '<=', $condition['max_order_price']];
        if (!empty($condition['status'])) {
            $where[] = ['o.status', '=',$condition['status']];
        }
        $list = Db::name("recharge_gift")->where($where)->paginate(20);
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
        ]);

        return view();
    }

    //编辑充值赠送
    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name("recharge_gift")->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin('记录不存在');
        if (request()->isPost()) {
            $post = input("post.");


            $validate = new \think\Validate();
            $validate->rule([
                'min_order_price' => 'require|number',
                'max_order_price' => 'require|number',
                'min_coin' => 'require|number',
                'max_coin' => 'require|number',
                'coin_probability' => 'require|between:0.1,0.9',
                'vip_days' => 'require|number',
                'vip_probability' => 'require|between:0.1,0.9',
            ]);
            $validate->message([
                'min_order_price.require' => '最小充值不能为空',
                'min_order_price.number' => '最小充值只能是数字',
                'max_order_price.require' => '最大充值不能为空',
                'max_order_price.number' => '最大充值只能是数字',
                'min_coin.require' => '赠送金币不能为空',
                'min_coin.number' => '赠送金币只能是数字',
                'coin_probability.require' => '金币中奖概率不能为空',
                'coin_probability.between' => '金币中奖概率只能在0.1到0.9之间',
                'vip_days.require' => '赠送vip天数不能为空',
                'vip_days.number' => '赠送vip天数只能是数字',
                'vip_probability.require' => ' vip中奖概率不能为空',
                'vip_probability.between' => ' vip中奖概率只能在0.1到0.9之间',
            ]);

            $data = $this->request->param();
            if (!$validate->check($data)) {
                json_fail_admin($validate->getError());
            }
            if($post['coin_probability'] + $post['vip_probability'] != 1){
                json_fail_admin("中奖比例错误");
            }

            //判断区间是否有得复
            $check_result = Db::name("recharge_gift")->where('id', '<>', $id)->select();
            foreach($check_result as $value){
                if($post['min_order_price'] >= $value['min_order_price'] and $post['min_order_price'] <= $value['max_order_price']){
                    json_fail_admin("最小充值有重复区间");
                }
                if($post['max_order_price'] >= $value['min_order_price'] and $post['max_order_price'] <= $value['max_order_price']){
                    json_fail_admin("最大充值有重复区间");
                }
            }

            $res = Db::name("recharge_gift")->where('id', '=', $id)->update($post);

            $this->addlog('修改充值赠送活动','充值赠送活动ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));
            if ($res !== false) {
                json_success_admin('修改成功');
            }else{
                json_fail_admin();
            }
        } else {
            $this->assign([
                'res' => $res,
            ]);
            return view();
        }
    }

    //修改充值赠送信息
    public function editInfo()
    {
        $id = input('param.id');
        if (empty($id)) $id = $this->id;
        $res = Db::name("rechage_info")->where('id', '=', $id)->find();
        if (empty($res)) json_fail_admin($this->title . '记录不存在');
        if (request()->isPost()) {
            $post = input("post.");

            $validate = new \think\Validate();
            $validate->rule([
                'start_time' => 'require|date',
                'end_time' => 'require|date|after:'.$post['start_time'],
                'info' => 'require',
                'images' => 'require',
            ]);
            $validate->message([
                'start_time.require' => '开始时间不能为空',
                'start_time.date' => '开始时间格式错误',
                'end_time.require' => '开始时间不能为空',
                'end_time.date' => '结束时间格式错误',
                'end_time.after' => '结束时间不能小于开始时间',
                'info.require' => '活动说明不能为空',
                'images.require' => '图片不能为空',
            ]);

            $data = $this->request->param();
            if (!$validate->check($data)) {
                json_fail_admin($validate->getError());
            }

            if($res['status'] == 2 && $post['status'] == 1){
                $post['start_time'] = date('Y-m-d H:i:s',time());
            }

            $res = Db::name("rechage_info")->where('id', '=', $id)->update($post);

            $this->addlog('修改充值赠送活动信息', '充值赠送活动信息ID:' . $id . ',post_data:' . json_encode($post, JSON_UNESCAPED_UNICODE));
            if ($res !== false) {
                json_success_admin('修改成功');
            } else {
                json_fail_admin();
            }
        } else {
            $this->assign([
                'res' => $res,
            ]);
            return view();
        }
    }

    //统计报表
    public function report(){
        $where = [];
        $condition['start_time'] = input('request.start_time')??date('Y-m-d',time());
        $condition['end_time'] = input('request.end_time')??date('Y-m-d',time());
        $condition['name'] = input('request.name');
        if (!empty($condition['start_time'])) $where[] = ['re_date', '>=', $condition['start_time']];
        if (!empty($condition['start_time'])) $where[] = ['re_date', '<=', $condition['end_time']];
        if (!empty($condition['status'])) {
            $where[] = ['o.status', '=',$condition['status']];
        }
        $list = Db::name("recharge_statistics")
            ->field('re_date,name,content,sum(all_count) as all_count,sum(code_count) as code_count,sum(r_count) as r_count')
            ->where($where)->order('re_date desc')
            ->group('re_date,content')
            ->paginate(20);
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'title' => '充值赠送报表',
            'name'=>'rechargegift',
            'current_url'=>'/admin/rechargegift/report',
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
        ]);
        return view();
    }

}
