<?php
namespace app\admin\controller;

use app\Helper\CacheHelper;
use think\Db;
/**
 * 短视频机器人
 *
 */
class ShortVideoBot extends BaseController
{
    private $title, $current_url, $name, $error;
    public function initialize()
    {
        parent::initialize();
        $this->name = "member_info";
        $this->title = "机器人列表";
        $this->current_url = "/admin/short_video_bot";
    }
    private function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    /**
     * 列表
     * @return \think\response\View
     */
    public function index() {
        // 设置搜索条件
//        $where = [
//            // 排除待审核和删除及审核不通过
//            ["is_bot", "=", 1],
//            ["is_visitor", "=", 2],
//        ];

//        $query   = Db::connect('movie')->table("hg_member_info")
//                       ->field("*")
//                       ->where($where);
//        $list  = $query->paginate(10);

//        $list = Db::name('member_info')->where($where)->cache(CacheHelper::SHORT_VIDEO_BOT_LIST_KEY,86400*365)->select();
        $list = Db::name('member_bot')->order('id desc')->select();
//        $item  = $list->toArray();

        $this->assign([
//            'list'      => $list,
            'data'      => $list,
            'total'=>count($list),
        ]);
        $this->_assign();
        return view();
    }

    /**
     * 管理员新增短视频
     */
    public function add($id=0)
    {

        if (request()->isPost()) {
            $post = input("post.");
            if(!empty($post['id'])){
                $detail = Db::name('member_bot')->where('id','=',$post['id'])->find();
                if(!empty(Db::name('member_bot')->where('account',$post['nick_name'])->where('id','<>',$post['id'])->find())){
                    json_fail_admin('该账号所属用户已存在1');
                }

                if(!empty(Db::name("member_info")->where('account',$post['nick_name'])->where('id','<>',$detail['member_id'])->find())){
                    json_fail_admin('该账号所属用户已存在');
                }

                try {
                    Db::startTrans();
                    Db::name("member_info")->where('id',$detail['member_id'])->update([
                        'account'=>$post['nick_name'],
                        'nick_name'=>$post['nick_name'],
                        'avatar'=>$post['img'],
                    ]);
                    Db::name("member_bot")->where('id',$post['id'])->update([
                        'account'=>$post['nick_name'],
                        'avatar'=>$post['img'],
                    ]);
                    Db::commit();
                }catch (\Throwable $e){
                    Db::rollback();
                    json_fail_admin('修改失败,error:'.$e->getMessage().$e->getFile().$e->getLine());
                }

            }else{
                try {
                    Db::startTrans();
                    $member_info_detail = (new VideoClip())->_getOfficialUser($post['nick_name'],$post['img']);
                    Db::name("member_bot")->insert([
                        'member_id'=>$member_info_detail['id'],
                        'account'=>$post['nick_name'],
                        'avatar'=>$post['img'],
                    ]);
                    Db::commit();
                }catch (\Throwable $e){
                    Db::rollback();
                    json_fail_admin('创建失败,error:'.$e->getMessage().$e->getFile().$e->getLine());
                }

            }
//            cacheDel(CacheHelper::SHORT_VIDEO_BOT_LIST_KEY);
            json_success_admin();
        }

//        $detail = Db::name('member_info')->where('id', '=',$id)->find();
        $detail = Db::name('member_bot')->where('id', '=',$id)->find();
        if(!empty($detail))$detail['nick_name']=$detail['account'];
        $this->assign([
            "data"  => $detail,
        ]);
        return view('add');
    }



}
