<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class SiteLog extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "site_log_one";
        $this->title = "用户访问记录";
        $this->current_url = "/admin/site_log";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['account'] = input('request.account');
        $condition['member_id'] = input('request.member_id');
        $condition['device_type'] = input('request.device_type');
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');

        if(!isset($condition['is_visitor']))$condition['is_visitor']=2;

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }


//        if (!empty($condition['account'])) $where[] = ['b.member_id', 'like', '%' . $condition['account'] . '%'];
        if (!empty($condition['member_id'])) $where[] = ['a.member_id', '=', $condition['member_id'] ];
        if (!empty($condition['device_type'])) $where[] = ['a.device_type', '=', $condition['device_type']];
        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['begin_time'])) $where[] = ['a.create_time', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.create_time', '<', strtotime($condition['end_time'])+86400];

        $limit=20;
        if(!empty($_GET['is_export']) && $_GET['is_export']==1){
            $limit = 999999;
        }

        $list = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id', 'left')
            ->field('a.*,b.account,b.is_visitor')
            ->where($where)
            ->order('id desc')->paginate($limit);


        if(!empty($_GET['is_export']) && $_GET['is_export']==1){

            if(!empty($list->toArray()['data'])){
                // 日志记录
                $this->addlog('导出用户访问记录',json_encode(['before_data'=>[],'after_data'=>$where]));

                $list=$list->toArray()['data'];

                $export_data=[];
                foreach ($list as $k=>$v){
                    $export_data[]=[
                        'device_type'=>$v['device_type'],
                        'member_id'=>$v['member_id'],
                        'action'=>$v['action'],
                        'create_ip'=>$v['create_ip'],
                        'create_addr'=>$v['create_addr'],
                        'create_time'=>date('Y-m-d H:i:s',$v['create_time']),
                    ];
                }

                $title = ['设备类型','用户id','访问api','访问ip','访问地址','访问时间'];
                $filename = '用户访问记录';
                $this->excel_data($filename,$title,$export_data);exit;
            }
            exit;
        }

        $data = $list->items();
//        if(!empty($data)) {
//            $use_ids = array_column($data, 'member_id');
//            $member_column_list = Db::name('member_info')->whereIn('id', $use_ids)->column('is_visitor,account', 'id');
//
//            foreach ($data as &$item) {
//                $item['is_visitor'] = $member_column_list[$item['member_id']]['is_visitor'] ?? 1;
//                $item['account'] = $member_column_list[$item['member_id']]['account'] ?? '';
//            }
//        }
//dump($data);die();
        $this->assign([
            'list' => $list,
            'data' => $data,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name($this->name)->where($where)->delete();

        $res ? json_success_admin() : json_fail_admin();
    }
}
