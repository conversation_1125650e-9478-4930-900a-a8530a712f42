<?php
namespace app\admin\controller;

use think\Db;
class Skin extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '皮肤管理',
            'name'=>'skin',
            'current_url'=>'/admin/skin',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['module_id'] = input('request.module_id');

        if (!empty($condition['module_id'])) $where[] = ['o.module_id','=', $condition['module_id']];
        $list = Db::name("skin")->alias('o')->field('o.*')->where($where)->order("o.id desc")
            ->paginate(20)->each(function($item, $key){
                $item['module_name']=array_column(config('api.skin_module'),'title','id')[$item['module_id']]??'';
                return $item;
            });
        $page = $list->render();

        $last_release_time =json_decode(sysconf('app_skin')??'[]',true)['last_release_time']??'';

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'last_release_time' => $last_release_time,
        ]);

        return view();
    }



    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input("post.");

            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);

            $this->addlog('修改皮肤','皮肤ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        } else {
            $res['module_name']=array_column(config('api.skin_module'),'title','id')[$res['module_id']]??'';
            $this->assign([
                'res' => $res,
            ]);
            return view();
        }
    }

    /**
     * 皮肤发布
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function release(){

        $where=[
            'status'=>1
        ];
        $list = Db::name($this->name)->field("type,value_data,uniquely_mark")->where($where)->select();
        sysconf('app_skin',json_encode(['last_release_time'=>date('Y-m-d H:i:s'),'data'=>$list],JSON_UNESCAPED_UNICODE));

        json_success_admin();
    }

}