<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class Source extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->title = "下载记录";
        $this->current_url = "/admin/source";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }
    /**
     * 列表
     * @return \think\response\View
     */
    public function index() {
        // 设置搜索条件
        $where = [];

        $condition['name'] =  input('name', '', 'trim');
        $condition['url'] =  input('url', '', 'trim');
        if (!empty($condition['url'])) $where[] = ['url', '=', $condition['url']];
        if (!empty($condition['name'])) $where[] = ['name', 'like', '%' . $condition['name'] . '%'];

        $list = Db::name('source_data')->where($where)->paginate(20);
        $page = $list->render();
        $list = $list->toArray();
        foreach ($list['data'] as $key => &$value) {
            $value['pic'] = explode(',',$value['pic']);
        }
        $this->assign([
            'list' => $list,
            'page' => $page,
            'condition' => $condition

        ]);
        $this->_assign();
        return view();
    }
    /**
     * 新增
     */
    public function add() {

        if (request()->isPost()) {
            $data = input('post.');
            if (!$data['name']) {
                json_fail_admin('资源标题不能为空');
            }
            if (!$data['url']) {
                json_fail_admin('资源路径不能为空');
            }
            $re = Db::name('source_data')->where('url',$data['url'])->select();
            if ($re) {
                json_fail_admin('该资源路径已存在!');
            }
            $data['pic'] = implode(',',array_filter($data['pic']));
            if (!$data['pic']) {
                json_fail_admin('图片路径不能为空');
            }

            $url = 'http://kumilist.ovideo-dev.com:8989/other/downloadVideo';
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $url);
            curl_setopt($ch, CURLOPT_POST, 1 );
            curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            $response = curl_exec($ch);
            curl_close($ch);
            $response = json_decode($response,true);
            if ($response['code'] != 0){
                json_fail_admin($response['message']);
            }
            $data['addtime'] = time();
            $re = Db::name('source_data')->insert($data);
            if (!$re) {
                json_fail_admin('添加远程资源失败,请重试!');
            }
            json_success_admin('添加远程资源成功!');
        }
        return view('form');
    }



    public function config()
    {
        //查询所有配置
        $config = Db::name('source_config')->select();
        $config_column = Db::name('source_config')->column('key_value','key_name');
        if (request()->isPost()) {
            if($this->user['role_id']!=\app\admin\model\AdminRole::ROLE_SUPER_ADMIN ){
                json_fail_admin('只有超级管理员角色才能进行此操作!');
            }
            $update_before_data = [];
            $update_after_data = [];
            $input = input('param.');
            foreach ($input as $key => $value) {
                if(isset($config_column[$key]) && $config_column[$key]!=$value){
                    $update_before_data[$key]=$config_column[$key];
                    $update_after_data[$key]=$value;
                    Db::name('source_config')->where('key_name',$key)->update(['key_value'=>$value]);
                }
            }
            $this->addlog('修改资源配置路径',json_encode(['修改前'=>$update_before_data,'修改后'=>$update_after_data],JSON_UNESCAPED_UNICODE));
            json_success_admin();
        }else{
            $this->assign('config',$config);
            return view('/source/config');
        }

    }

}
