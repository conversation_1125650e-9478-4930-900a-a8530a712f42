<?php
/******************************************************************************
 * Copyright (c) Archer 2023.                                                 *
 ******************************************************************************/

declare(strict_types=1);

namespace app\admin\controller;

use app\common\BizConst;
use app\common\exception\BizException;
use app\Helper\StrHelper;
use app\Service\AppPayService;
use app\Service\BaisonPromoMemberService;
use app\Service\BaisonPromoService;
use app\Service\BaisonTransferService;
use Httpful\Request;
use think\Db;
use think\facade\Validate;

final class Statements extends CommonController
{

    private  $is_baison_export=false;
    private  $is_baisonPromoMembers_export=false;
    public function __construct()
    {
        $conf = [
            'name'        => "baison",
            'title'       => "百胜棋牌账变",
            'current_url' => "/admin/statements/baison",
        ];

        parent::__construct($conf);
        $this->_assign();
    }

    public function _assign(): void
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    /**
     * 百胜导出
     * @return void
     */
    public function baison_export(){
        $this->is_baison_export=true;
        $this->baison();
    }

    public function baison(): \think\response\View
    {
        $queries = input();

        /** @var BaisonTransferService $bts */
        $bts = \app(BaisonTransferService::class);

        $this->assign(
            'cascade_types',
            \json_encode([
                BizConst::TYPE_TRANSFER_TO_BAISON         => [
                    BizConst::BAISON_DEPOSIT_FAILED  => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_DEPOSIT_FAILED],
                    BizConst::BAISON_DEPOSIT_SUCCESS => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_DEPOSIT_SUCCESS],
                    BizConst::BAISON_DEPOSIT_WAITING => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_DEPOSIT_WAITING],
                    BizConst::BAISON_DEPOSIT_REWARDS => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_DEPOSIT_REWARDS],
                ],
                BizConst::TYPE_TRANSFER_BAISON_WITHDRAWAL => [
                    BizConst::BAISON_WITHDRAWAL_WAITING => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_WITHDRAWAL_WAITING],
                    BizConst::BAISON_WITHDRAWAL_FAILED  => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_WITHDRAWAL_FAILED],
                    BizConst::BAISON_WITHDRAWAL_SUCCESS => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_WITHDRAWAL_SUCCESS],
                    BizConst::AUTO_WITHDRAWAL_WAITING   => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::AUTO_WITHDRAWAL_WAITING],
                    BizConst::AUTO_WITHDRAWAL_SUCCESS   => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::AUTO_WITHDRAWAL_SUCCESS],
                    BizConst::AUTO_WITHDRAWAL_FAILED    => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::AUTO_WITHDRAWAL_FAILED],
                ],
            ], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_FORCE_OBJECT,),
        );

        $this->assign('types', BizConst::BAISON_TRANSFER_TYPES_CN);
        $this->assign('status', BizConst::BAISON_TRANSFER_STATUS_CN);

        if (count($queries) > 0) {
            try {
                $limit = BizConst::PAGE_DEFAULT_ROWS;
                if(!empty($_GET['is_export']) && $_GET['is_export']==1){$limit=999999;}

                $paginator = $bts->getPaginator(
                    $this->buildBaisonTransferQueries($queries),
                    (int)max(1, input('page', BizConst::PAGE_DEFAULT_NUM)),
                    (int)max(10, input('limit', $limit)),
                );

                if(!empty($_GET['is_export']) && $_GET['is_export']==1 && $this->is_baison_export){

                    $list = $paginator->toArray()['data'];

                    // 日志记录
                    $this->addlog('导出百胜棋牌账变流水',json_encode(['before_data'=>[],'after_data'=>$queries]));

                    $export_data=[];
                    foreach ($list as $k=>$v) {
                        $export_data[] = [
                            'id' => $v['id'],
                            'account' => $v['account'],
                            'order_id' => $v['order_id'],
                            'ask_amount' => $v['ask_amount'] / 100,
                            'actual_amount' => $v['actual_amount'] / 100,
                            'type' => BizConst::BAISON_TRANSFER_TYPES_CN[$v['type']]??'',
                            'status' => BizConst::BAISON_TRANSFER_STATUS_CN[$v['status']]??'',
                            'created_at' => $v['created_at'],
                            'finish_time' => $v['finish_time'] ?? '',
                        ];
                    }

                    $title = ['ID','用户账号','订单号','账变金额(元)','实际金额(元)','订单类型','状态','创建时间','完成时间'];
                    $filename = '百胜棋牌账变流水';
                    $this->excel_data($filename,$title,$export_data);exit;
                }

                $this->assign('queries', $queries);
                $this->assign('list', $paginator->toArray()['data']);
                $this->assign('pages', StrHelper::genPaginationHtml($paginator));

                return view('baison');
            } catch (BizException $bizException) {
                $this->assign('errorInfo', $bizException->getMessage());
            } catch (\Throwable $e) {
                // logInfo([$e->getMessage()], $e->getFile(), $e->getMessage(), __METHOD__);
                $this->assign('errorInfo', '查询失败，服务器内部错误 1061');
            }
        }

        $paginator = $bts->getPaginator(
            null,
            (int)max(1, input('page', BizConst::PAGE_DEFAULT_NUM)),
            (int)max(10, input('limit', BizConst::PAGE_DEFAULT_ROWS)),
        );
        $this->assign('list', $paginator->toArray()['data']);
        $this->assign('pages', StrHelper::genPaginationHtml($paginator));

        return view('baison');
    }

    public function withdrawalReSend(): void
    {
        $id = (int)\input('id');
        if (empty($id)) {
            json_fail_admin('订单id不正确');
        }

        $order = Db::name('baison_transfer')->where(['id' => $id])->limit(1)->find();
        if (empty($order)) {
            json_fail_admin('订单不存在');
        }

        // if ((int)$order['status'] !== BizConst::BAISON_WITHDRAWAL_SUCCESS) {
        //     json_fail_admin('订单状态不允许重发');
        // }

        $amount  = $order['ask_amount'] / 100;
        $orderId = $order['order_id'];

        /** @var BaisonTransferService $bts */
        $bts = app(BaisonTransferService::class);

        $fee       = \bcmul((string)$amount, BizConst::BAISON_WITHDRAWAL_RATES, 2);
        $actualAmt = \bcsub((string)$amount, $fee, 2);
        $autoOrder = [
            'withdrawNo'      => $orderId,
            'account'         => $order['account'],
            'withdrawDate'    => date('Y-m-d H:i:s'),
            'withdrawAmt'     => $amount,
            'withdrawFee'     => $fee,
            'actualPayment'   => $actualAmt,
            'currencyCode'    => 'CNY',
            'channel'         => 1,
            'bankAccountNo'   => $order['bank_no'],
            'bankAddress'     => $order['bank_branch'],
            'bankAccountName' => $order['real_name'],
            'bankCode'        => $order['bank_name'],
            'channelCode'        => get_order_prefix(),
            'notifyUrl'       => isset($_SERVER['HTTPS']) ?
                'https://'.$_SERVER['HTTP_HOST'].'/api/personal.withdrawal/callback' :
                'http://'.$_SERVER['HTTP_HOST'].'/api/personal.withdrawal/callback',
        ];

        try {
            $resRawBody = '';
            $resp       = Request::post(BizConst::getWithdrawalApiUrl(), $autoOrder)->sendsForm()->expectsJson()
                ->timeout(5)->send();
            $resRawBody = $resp->raw_body;

            logInfo(['info' => 'request_withdrawal', 'data' => $resRawBody],
                __METHOD__,
                '提现返回日志',
                'withdrawal-req');

            if (!$resp) {
                json_fail_admin('返回数据不正确 : '.$resRawBody);
            }

            if ((int)$resp->body->code === 200) {
                // 更新出款状态
                $bts->updateById((int)$order['id'], [
                    'status'        => BizConst::AUTO_WITHDRAWAL_WAITING,
                    'ask_amount'    => StrHelper::toCents((int)$amount),
                    'actual_amount' => (int)bcmul($actualAmt, '100', 0),
                    'fee'           => (int)bcmul($fee, '100', 0),
                ]);
                json_success_admin('重新投递成功，并更新了本地状态');
            } else {
                json_fail_admin($resp->body->msg, $resp->body->data);
            }
        } catch (\Throwable $e) {
            logInfo(['error' => 'request_withdrawal', 'data' => StrHelper::getErrText($e).PHP_EOL.$resRawBody],
                __METHOD__,
                '提现请求报错日志',
                'withdrawal-req-err');

            json_fail_admin('异常：重新投递失败 : '.$e->getMessage());
        }

        logInfo(['info' => 'request_withdrawal', 'data' => $resRawBody],
            __METHOD__,
            '提现code!==200日志',
            'withdrawal-req');
        // 因为不确定其他状态提现服务器是否保存了订单，所以不可以自动加回用户余额
    }

    public function withdrawal(): \think\response\View
    {
        $this->assign("current_url", '/admin/statements/withdrawal?is_search=1');

        if ($this->request->isPost() && input('get.is_search')!=1) {
            $this->withdrawalReSend();
            exit(0);
        }
        $queries         = input();
        $queries['type'] = BizConst::TYPE_TRANSFER_BAISON_WITHDRAWAL;



        /** @var BaisonTransferService $bts */
        $bts = \app(BaisonTransferService::class);

        $this->assign(
            'cascade_types',
            \json_encode([
                BizConst::TYPE_TRANSFER_TO_BAISON         => [],
                BizConst::TYPE_TRANSFER_BAISON_WITHDRAWAL => [
                    BizConst::BAISON_WITHDRAWAL_WAITING => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_WITHDRAWAL_WAITING],
                    BizConst::BAISON_WITHDRAWAL_FAILED  => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_WITHDRAWAL_FAILED],
                    BizConst::BAISON_WITHDRAWAL_SUCCESS => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_WITHDRAWAL_SUCCESS],
                    BizConst::AUTO_WITHDRAWAL_WAITING   => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::AUTO_WITHDRAWAL_WAITING],
                    BizConst::AUTO_WITHDRAWAL_SUCCESS   => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::AUTO_WITHDRAWAL_SUCCESS],
                    BizConst::AUTO_WITHDRAWAL_FAILED    => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::AUTO_WITHDRAWAL_FAILED],
                    BizConst::BAISON_PLATFORM_ADD_SUCCESS    => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::AUTO_WITHDRAWAL_FAILED],
                    BizConst::BAISON_PLATFORM_REDUCE    => BizConst::BAISON_TRANSFER_STATUS_CN[BizConst::BAISON_PLATFORM_REDUCE],
                ],
            ], JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE | JSON_FORCE_OBJECT),
        );

        $this->assign('types', [BizConst::TYPE_TRANSFER_BAISON_WITHDRAWAL => '转出']);
        $status_list = [
            BizConst::BAISON_WITHDRAWAL_SUCCESS => '已下分(待打款)',
            BizConst::BAISON_WITHDRAWAL_FAILED  => '下分失败',
            BizConst::BAISON_WITHDRAWAL_WAITING => '下分中',
            BizConst::AUTO_WITHDRAWAL_WAITING   => '打款审核中',
            BizConst::AUTO_WITHDRAWAL_SUCCESS   => '打款成功',
            BizConst::AUTO_WITHDRAWAL_FAILED    => '打款失败',
        ];
        $this->assign('status', $status_list);

        if (count($queries) > 0) {
            try {

                $limit = BizConst::PAGE_DEFAULT_ROWS;
                if(!empty($_GET['is_export']) && $_GET['is_export']==1){$limit=999999;}

                $paginator = $bts->getPaginator(
                    $this->buildBaisonTransferQueries($queries),
                    (int)max(1, input('page', BizConst::PAGE_DEFAULT_NUM)),
                    (int)max(10, input('limit', $limit)),
                    null,
                    [['status','IN',array_keys($status_list)]]
                );

                if(!empty($_GET['is_export']) && $_GET['is_export']==1){

                    $list = $paginator->toArray()['data'];

                    // 日志记录
                    $this->addlog('导出百胜棋牌提现记录',json_encode(['before_data'=>[],'after_data'=>$queries]));

                    $export_data=[];
                    foreach ($list as $k=>$v) {
                        $export_data[] = [
                            'id' => $v['id'],
                            'account' => $v['account'],
                            'order_id' => $v['order_id'],
                            'ask_amount' => $v['ask_amount'] / 100,
                            'actual_amount' => $v['actual_amount'] / 100,
                            'fee' => $v['fee'] / 100,
                            'rates' => \bcmul((string)$v['rates'],'100',0).'%',
                            'status' => $status_list[$v['status']]??'',
                            'created_at' => $v['created_at'],
                            'finish_time' => $v['finish_time'] ?? '',
                        ];
                    }

                    $title = ['ID','用户账号','订单号','账变金额(元)','实际金额(元)','手续费','手续费费率','提现状态','创建时间','完成时间'];
                    $filename = '百胜棋牌提现记录';
                    $this->excel_data($filename,$title,$export_data);exit;
                }

                $this->assign('queries', $queries);
                $this->assign('list', $paginator->toArray()['data']);
                $this->assign('pages', StrHelper::genPaginationHtml($paginator));

                return view('baison_withdrawal');
            } catch (BizException $bizException) {
                $this->assign('errorInfo', $bizException->getMessage());
            } catch (\Throwable $e) {
                // logInfo([$e->getMessage()], $e->getFile(), $e->getMessage(), __METHOD__);
                $this->assign('errorInfo', '查询失败，服务器内部错误 1061');
            }
        }

        $paginator = $bts->getPaginator(
            null,
            (int)max(1, input('page', BizConst::PAGE_DEFAULT_NUM)),
            (int)max(10, input('limit', BizConst::PAGE_DEFAULT_ROWS)),
        );

        $this->assign('list', $paginator->toArray()['data']);
        $this->assign('pages', StrHelper::genPaginationHtml($paginator));

        return view('baison_withdrawal');
    }

    private function buildBaisonTransferQueries(array $queries): ?array
    {
//        return null;
        $conditions = null;

        if (isset($queries['account']) && !empty($queries['account'])) {
            $conditions['account'] = (string)$queries['account'];
        }

        if (isset($queries['belong_times']) && !empty($queries['belong_times'])) {
            $tsArr                    = StrHelper::parseDateTimeRangeStr($queries['belong_times']);
            $conditions['start_time'] = (string)$tsArr[0];
            $conditions['end_time']   = (string)$tsArr[1];
        }

        if (isset($queries['order_id']) && !empty($queries['order_id'])) {
            $conditions['order_id'] = (string)$queries['order_id'];
        }


        (isset($queries['title']) && !empty($queries['title'])) && $conditions['title'] = (string)$queries['title'];
        (isset($queries['nickname']) && !empty($queries['nickname'])) && $conditions['nickname'] = (string)$queries['nickname'];
        (isset($queries['condition']) && !empty($queries['condition'])) && $conditions['condition'] = (string)$queries['condition'];
        (isset($queries['type']) && array_key_exists(
                $queries['type'],
                BizConst::BAISON_TRANSFER_TYPES_CN
            )) && $conditions['type'] = (int)$queries['type'];

        if (isset($queries['status']) && array_key_exists($queries['status'], BizConst::BAISON_TRANSFER_STATUS_CN)) {
            $conditions['status'] = (int)$queries['status'];
        }

        if (!empty($queries['channelids'])) {
            $conditions['channelids'] = $queries['channelids'];
        }

//        if (isset($conditions['type'], $conditions['status'])) {
//            $allowOut = [
//                BizConst::BAISON_WITHDRAWAL_SUCCESS, BizConst::BAISON_WITHDRAWAL_FAILED,
//                BizConst::BAISON_WITHDRAWAL_WAITING, BizConst::AUTO_WITHDRAWAL_SUCCESS,
//                BizConst::AUTO_WITHDRAWAL_WAITING,
//            ];
//
//            if (($conditions['type'] === BizConst::TYPE_TRANSFER_TO_BAISON)
//                && in_array($conditions['sub_type'], $allowOut, true)) {
//                throw new BizException('转入类型不可以包含转出的订单状态');
//            }
//
//            if (($conditions['type'] === BizConst::TYPE_TRANSFER_BAISON_WITHDRAWAL)
//                && !in_array($conditions['sub_type'], $allowOut, true)) {
//                throw new BizException('转出类型不可以包含转入的订单状态');
//            }
//        }

        return $conditions;
    }

    public function promos()
    {
        /** @var BaisonPromoService $bps */
        $bps       = \app(BaisonPromoService::class);
        $paginator = $bps->getPaginator(
            null,
            (int)max(1, input('page', BizConst::PAGE_DEFAULT_NUM)),
            (int)max(10, input('limit', BizConst::PAGE_DEFAULT_ROWS)),
        );

        $this->assign('list', $paginator->toArray()['data']);
        $this->assign('pages', StrHelper::genPaginationHtml($paginator));

        $this->assign('types', BizConst::BAISON_TRANSFER_TYPES_CN);
        $this->assign('status', BizConst::BAISON_TRANSFER_STATUS_CN);

        $this->assign('condition', []);
        $this->assign('current_url', '/admin/statements/promos');

        return view('statements/promos/list');
    }

    public function promo($id)
    {
        if ($this->request->isPost()) {
            $inputs = \input();
            $v      = Validate::make([
                'title'       => 'require|min:2|max:200',
                'condition'   => 'require|number|min:1|max:100000',
                'amount'      => 'require|number|min:0|max:10000',
                'vip_seconds' => 'require|number|min:0|max:36500',
                'is_enabled'  => 'require|number|between:0,1',
            ], [
                'title.require'       => '活动名字必须填写',
                'title.min'           => '标题最短长度2个字符',
                'title.max'           => '标题最长200个字符以内',
                'condition.require'   => '充值金额必须提供',
                'condition.number'    => '充值金额条件必须是个数字',
                'condition.min'       => '充值金额最少1块钱',
                'condition.max'       => '充值金额最多10万人民币',
                'amount.require'      => '奖励金额必须指定',
                'amount.number'       => '奖励金额条件必须是个数字',
                'amount.min'          => '奖励金额不可以小于0',
                'amount.max'          => '奖励金额最多1万人民币',
                'vip_seconds.require' => '奖励vip时长必须提供',
                'vip_seconds.number'  => '奖励vip时长必须是个数字',
                'vip_seconds.min'     => '奖励vip时长不可以小于0',
                'vip_seconds.max'     => '奖励vip时长最多100年',
                'is_enabled.require'  => '启用状态必须提供',
                'is_enabled.number'   => '启用状态请点击单选',
                'is_enabled.between'  => '启用状态要么为是，要么为否，不可以有其他值',
            ]);

            if (!$v->check($inputs)) {
                json_fail_admin($v->getError());
            }

            if ((int)$inputs['amount'] === (int)$inputs['vip_seconds'] && 0 === (int)$inputs['amount']) {
                json_fail_admin('一个活动必须有赠送的内容，奖励金币，或者奖励vip时长，或者都奖励');
            }

            $id = (int)$inputs['id'];
            unset($inputs['id']);
            if ($id > 0) {
                if ($inputs['vip_seconds'] > 0) {
                    $inputs['vip_seconds'] *= 86400;
                }
                try {
                    if (Db::name('baison_promo')->where(['id' => $id])->update($inputs) > 0) {
                        json_success_admin();
                    }
                    json_fail_admin('数据未更新');
                } catch (\Throwable $e) {
                    if ((int)$e->getCode() === 10501) {
                        json_fail_admin('请注意，所有的活动，充值金额都不可以一样');
                    }
                    json_fail_admin($e->getMessage());
                }
            }

            json_success_admin();
        }

        $promo = Db::name('baison_promo')->where(['id' => (int)$id])->find();
        if (empty($promo)) {
            json_fail_admin('id不正确，找不到合法数据');
        }

        $this->assign('promo', $promo);
        $this->assign('pageTitle', '编辑优惠活动');

        return view('statements/promos/edit');
    }

    public function baisonPromoMembers_export(){
        $this->is_baisonPromoMembers_export=true;
        $this->baisonPromoMembers();
    }

    public function baisonPromoMembers(): \think\response\View
    {
        $queries = \input();

        if(isset($queries['channelids']) && $queries['channelids']=='')unset($queries['channelids']);
        if(isset($queries['belong_times']) && $queries['belong_times']=='')unset($queries['belong_times']);

        $begin_time = input('request.begin_time');
        $end_time = input('request.end_time');
        $channelids = input("request.channelids", null, "trim");

        $where=[];
        if (!empty($begin_time)) $where[] = ['created_at', '>=', $begin_time];
        if (!empty($end_time)) $where[] = ['created_at', '<=', $end_time];
        if(!empty($channelids))$where[] = ['channelid', 'IN', explode(',',$channelids)];

        /** @var BaisonPromoMemberService $bpms */
        $bpms = \app(BaisonPromoMemberService::class);

        // 显示层的数据结构，所以定义到这里 如果项目有biz logic层应该定义到biz logic层
        $this->assign('current_url', '/admin/statements/baisonPromoMembers');


        $limit = BizConst::PAGE_DEFAULT_ROWS;
        if(!empty($_GET['is_export']) && $_GET['is_export']==1){$limit=999999;}

        try {

            if (count($queries) > 0) {
                $paginator = $bpms->getPaginator(
                    $this->buildBaisonTransferQueries($queries),
                    (int)max(1, input('page', BizConst::PAGE_DEFAULT_NUM)),
                    (int)max(10, input('limit', $limit)),
                );
            }else{
                $paginator = $bpms->getPaginator(
                    null,
                    (int)max(1, input('page', BizConst::PAGE_DEFAULT_NUM)),
                    (int)max(10, input('limit', $limit)),
                );
            }

            if(!empty($_GET['is_export']) && $_GET['is_export']==1){

                $list = $paginator->toArray()['data'];

                // 日志记录
                $this->addlog('百胜棋牌导出充值会员记录',json_encode(['before_data'=>[],'after_data'=>$queries]));

                $export_data=[];
                foreach ($list as $k=>$v) {

                    $export_data[] = [
                        'id' => $v['id'],
                        'channelid' => $v['channelid'],
                        'account' => $v['account'],
                        'nickname' => $v['nickname'],
                        'type_name' => $v['type']==1?'百胜首充活动':'',
                        'title' => $v['title'],
                        'condition' => $v['condition'],
                        'vip_prized' => $v['vip_prized']/86400,
                        'amount_prized' => $v['amount_prized'],
                        'created_at' => $v['created_at'],
                        'updated_at' => $v['updated_at'],
                    ];
                }

                $title = ['ID','所属渠道','用户账号','用户昵称','活动分类','活动名字','充值金额条件(元)','赠送VIP时长(元)','赠送金额(元)','创建时间','完成时间'];
                $filename = '百胜棋牌导出充值会员记录';
                $this->excel_data($filename,$title,$export_data);exit;
            }

            $where_find = $queries;
            if(isset($where_find['channelids']))unset($where_find['channelids']);
            if(isset($where_find['belong_times']))unset($where_find['belong_times']);
            if(isset($where_find['condition']))unset($where_find['condition']);
            if(isset($where_find['page']))unset($where_find['page']);
            foreach ($where_find as $k=>$v){
                if(empty($v))unset($where_find[$k]);
            }

            $count_find = Db::name('baison_promo_member')
                ->where($where_find)
                ->where($where)
                ->fieldRaw('count(DISTINCT account) distinct_account_count ,sum(vip_prized) sum_vip_prized,sum(amount_prized) sum_amount_prized')
                ->find();
            $count_find['sum_vip_prized']=$count_find['sum_vip_prized']/86400;
            $this->assign('count_find', $count_find);
            $this->assign('queries', $queries);
            $this->assign('list', $paginator->toArray()['data']);
            $this->assign('pages', StrHelper::genPaginationHtml($paginator));

            return view('statements/promos/member_list');
        } catch (BizException $bizException) {
//            $this->assign('errorInfo', $bizException->getMessage());

            json_fail_admin('errorInfo', '查询失败，服务器内部错误 1060,'.$bizException->getMessage().$e->getFile().$e->getLine());
        } catch (\Throwable $e) {
//            $this->assign('errorInfo', '查询失败，服务器内部错误 1061');
            json_fail_admin('errorInfo', '查询失败，服务器内部错误 1061,'.$e->getMessage().$e->getFile().$e->getLine());
        }

        return view('statements/promos/member_list');
    }

    /**
     * 棋牌支付四方订单列表页
     * @return \think\response\View
     */
    public function line(){
       return  (new Pay())->line(AppPayService::IS_CHESS_YES['0'],'statements/pay/line');
    }

    /**
     * 棋牌支付设置列表页
     * @return \think\response\View
     */
    public function payIndex(){

        return (new Pay())->index(AppPayService::IS_CHESS_YES['0'],'statements/pay/index');
    }

    /**
     * 棋牌支付添加
     * @return \think\response\View
     */
    public function add(){
        return (new Pay())->add(AppPayService::IS_CHESS_YES['0'],'statements/pay/add');
    }

    /**
     * 棋牌支付修改
     * @return \think\response\View
     */
    public function payEdit($id){
        return (new Pay())->edit($id,AppPayService::IS_CHESS_YES['0'],'statements/pay/edit');
    }

    /**
     * 棋牌支付方式删除
     * @return void|null
     */
    public function del($id){
         (new Pay())->del($id,AppPayService::IS_CHESS_YES['0']);
    }

    /**
     * 棋牌支付金额配置
     * @return \think\response\View
     */
    public function price(){
        return (new Pay())->price(AppPayService::IS_CHESS_YES['0'],'statements/pay/price');
    }

    /**
     * 棋牌支付新增金额配置
     * @return \think\response\View|null
     */
    public function addprice(){
        return (new Pay())->addprice(AppPayService::IS_CHESS_YES['0'],'statements/pay/addprice');
    }

    /**
     * 棋牌支付修改金额配置
     * @return \think\response\View|null
     */
    public function editprice($id){
        return (new Pay())->editprice($id,AppPayService::IS_CHESS_YES['0'],'statements/pay/editprice');
    }

    /**
     * 棋牌支付删除金额配置
     * @return void|null
     */
    public function delprice($id){
         (new Pay())->delprice($id,AppPayService::IS_CHESS_YES['0']);
    }

    /**
     * 棋牌支付方式配置启用禁用
     */
    public function status($id){
        $conf = [
            'name'        => "app_pay",
            'title'       => "",
            'current_url' => "/admin/statements/payIndex",
        ];
        parent::__construct($conf);
        parent::status($id);

    }


}
