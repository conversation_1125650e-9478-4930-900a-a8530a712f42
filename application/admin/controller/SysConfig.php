<?php
namespace app\admin\controller;
use think\Db;
class SysConfig extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "sys_config",
            'title' => "APP配置",
            'current_url' => "/admin/sys_config",
        ];
        $this->id = 1;
        $this->is_parent = 0;
        parent::__construct($conf);
    }
    public function edit()
    {
        $id = input('param.id');
        if (empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        if (empty($res)) json_fail_admin($this->title . '不存在');

        if (request()->isPost()) {
            $post = input('post.');
            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if (empty($post[$k])) json_fail_admin($v);
                }
            }
            if (!empty($this->files)) {
                $file = $info = [];
                foreach ($this->files as $v) {
                    $file[$v] = request()->file("file_{$v}");
                }
                foreach ($this->files as $v) {
                    if (!empty($file[$v])) {
                        $info[$v] = $file[$v]->validate(['size' => 1024 * 1024, 'ext' => 'jpg,jpeg,png'])->move(Env::get('app_path') . '../public_html/uploads/' . $this->name);
                        if (!$info[$v]) json_fail_admin($file->getError());
                        $post[$v] = '/uploads/' . $this->name . '/' . $info[$v]->getSaveName();
                    }
                    unset($post["file_{$v}"]);
                }
            }
            $post['update_time'] = time();
            $post['update_user'] = $this->user['id'];
            $res = Db::name($this->name)->where('id', '=', $id)->update($post);
            $this->addlog('修改系统配置',json_encode($post));
            reload_cache_sys_config();
            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if (isset($v['type']) && $v['type'] == 'class_list') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => '分类',
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => $v,
                        ];
                    }
                }
            }
            $this->assign([
                'res' => $arr,
                'is_parent' => $this->is_parent,
            ]);
            return view('/common/edit');
        }
    }
}