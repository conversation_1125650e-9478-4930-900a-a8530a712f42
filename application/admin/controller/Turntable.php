<?php
namespace app\admin\controller;

use app\Helper\CommonConstHelper;
use app\Service\PrizeService;
use app\Service\TurntableService;
use think\Db;
class Turntable extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '转盘活动管理',
            'name'=>'turntable',
            'current_url'=>'/admin/turntable',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['title'] = input('request.title');

        if (!empty($condition['title'])) $where[] = ['o.title','LIKE', '%' .$condition['title'].'%' ];

        $list = Db::name("turntable")->alias('o')->field('o.*')->whereNull("delete_time")->where($where)->order("o.id desc")
            ->paginate(20)->each(function($item, $key){
                return $item;
            });
        $page = $list->render();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
        ]);

        return view();
    }



    public function add(){
        if (!request()->isPost()) {
            $this->assign([
                'prize_list' => Db::name("prize")->where([['status','=',CommonConstHelper::STATUS_OPEN]])->whereNull('delete_time')->field(['id','title'])->select(),
            ]);
            return view();
        } else {
            $post = input("post.");

            self::checkParams($post);

            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            $this->addlog('添加转盘活动','转盘活动ID:'.$res.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        }
    }


    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->whereNull('delete_time')->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input("post.");

            self::checkParams($post);

            Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);

            $this->addlog('修改转盘活动','转盘活动ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            json_success_admin();
        } else {
            $res['prizes'] = json_decode($res['prizes'],true);
            $all_prizes_column = Db::name("prize")->column('title','id');

            foreach ($res['prizes'] as $k=>&$v){
                $v['title']=$all_prizes_column[$v['prize_id']]??'';
            }

            $this->assign([
                'res' => $res,
                'prize_list' => Db::name("prize")->where([['status','=',CommonConstHelper::STATUS_OPEN]])->whereNull('delete_time')->field(['id','title'])->select(),
            ]);

            return view();
        }
    }

    /**
     * 新增或编辑入参校验
     * $params 校验的入参参数
     * @return void
     */
    private static function checkParams(array &$params ){
        $params['title'] = trim($params['title']);
        $params['start_time'] = trim($params['start_time']);
        $params['end_time'] = trim($params['end_time']);
        $params['explan_rule'] = trim($params['explan_rule']);
        if (empty($params['title'])) {
            json_fail_admin("活动标题必填");
        }
        if (empty($params['start_time'])) {
            json_fail_admin("开始时间必填");
        }
        if (empty($params['end_time'])) {
            json_fail_admin("结束时间必填");
        }

        $commom_where=[];
        $id = input('param.id');
        if(!empty($id)){
            $commom_where[]=['id','<>',$id];
        }

        if ($params['start_time']>=$params['end_time']  || strtotime($params['end_time'])<time() ) {
            json_fail_admin("开始时间必须大于结束时间，且结束时间必须大于当前时间");
        }

        if (mb_strlen($params['explan_rule']) >200) {
            json_fail_admin("活动规则最多只能输入200个字");
        }

        if (empty($params['prizes'])) {
            json_fail_admin("奖品必须设置");
        }
        $prizes = $params['prizes'];
        if (count($prizes)>10) {
            json_fail_admin("奖品最多只能设置10个");
        }

        $restar = Db::name("turntable")->where('title',$params['title'])->where($commom_where)->whereNull('delete_time')->find();
        if($restar){
            json_fail_admin("活动标题不能重复");
        }

        $prize_column = Db::name("prize")->where([['status','=',CommonConstHelper::STATUS_OPEN]])->whereNull('delete_time')->column('title','id');

        $all_probability=0;
        foreach ($prizes as $k=>$v){

            if(!is_numeric($v['prize_id']) || !is_numeric($v['probability']) ){
                json_fail_admin("奖品参数非法");
            }

            if($v['prize_id']<1){
                unset($prizes[$k]);
                continue;
            }

            if(empty($prize_column[$v['prize_id']])){
                json_fail_admin("奖品id：".$v['prize_id'].'，不存在或该奖品已被禁用');
            }

            $all_probability=bcadd($all_probability,$v['probability'],3);

            if(!is_numeric($v['probability'])){
                json_fail_admin("中奖概率必须是数字类型，请检查设置！");
            }

            $probability = explode('.',$v['probability']);
            if(count($probability)>1 && !empty($probability[1]) && strlen($probability[1])>3 ){
                json_fail_admin("中奖概率最多只支持小数点后三位");
            }
        }

        $params['prizes']=json_encode($prizes,JSON_UNESCAPED_UNICODE);

        if($all_probability!=100){
            json_fail_admin("总中奖概率必须等于100");
        }

        $where=[
            ['end_time','>=',$params['start_time']],
            ['start_time','<=',$params['end_time']],
            ['status','=',CommonConstHelper::STATUS_OPEN],
        ];

        $turntable_repeat = Db::name("turntable")->whereNull('delete_time')->where($where)->where($commom_where)->field('id')->find();

        if(!empty($turntable_repeat)){
            json_fail_admin("不同活动，活动时间不可重叠");
        }

    }


    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');

        if(strtotime($res['start_time'])<=time() && strtotime($res['end_time'])>=time() && $res['status']==CommonConstHelper::STATUS_OPEN){
            json_fail_admin('进行中的活动不可删除');
        }

        $res =  Db::name($this->name)->where('id', '=', $id)->update(['delete_time'=>date('Y-m-d H:i:s')]);

        $this->addlog('删除转盘活动','活动id:'.$id);

        $res ? json_success_admin() : json_fail_admin();
    }


}