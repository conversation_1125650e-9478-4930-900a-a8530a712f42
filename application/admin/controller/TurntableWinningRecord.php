<?php
namespace app\admin\controller;

use app\Service\PrizeService;
use app\Service\TurntableService;
use think\Db;
class TurntableWinningRecord extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '转盘中奖记录',
            'name'=>'turntable_winning_record',
            'current_url'=>'/admin/turntable_winning_record',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['member_id'] = input('request.member_id');
        $condition['nick_name'] = input('request.nick_name');
        $condition['turntable_id'] = input('request.turntable_id');
        $condition['channel'] = $channel = input("request.channel", null, "trim");

        if (!empty($condition['member_id'])) $where[] = ['member_id','=', $condition['member_id']];
        if (!empty($condition['turntable_id'])) $where[] = ['turntable_id','=', $condition['turntable_id']];
        if (!empty($condition['nick_name'])) $where[] = ['nick_name','LIKE', '%' .$condition['nick_name'].'%' ];
        if(!empty($condition['channel']))$where[] = ['channelid', 'IN', explode(',',$condition['channel'])];


        $turntable_list = Db::name('turntable')->field("id,title")->select();
        $turntable_column = array_column($turntable_list,null,'id');

        $list = Db::name("turntable_winning_record")->field('*');


        if(!empty($_GET['is_export']) && $_GET['is_export']==1){

            // 日志记录
            $this->addlog('导出转盘中奖记录',json_encode(['before_data'=>[],'after_data'=>$where]));

            $list=$list->select();
//            dump($list);die();

            $export_data=[];
            foreach ($list as $k=>$v){
                $export_data[]=[
                    'turntable_title'=>$turntable_column[$v['turntable_id']]['title']??'',
                    'channelid'=>$v['channelid'],
                    'member_id'=>$v['member_id'],
                    'nick_name'=>$v['nick_name'],
                    'prize_title'=>$v['prize_title'],
                    'create_time'=>$v['create_time'],
                    'last_recharge_time'=>$v['last_recharge_time'],
                    'last_vip_time'=>$v['last_vip_time']
                ];
            }

            $title = ['活动名称','所属渠道','用户id','用户昵称','奖品名称','中奖时间','充值时间','开通会员时间'];
            $filename = '转盘中奖记录';
            $this->excel_data($filename,$title,$export_data);exit;
        }else{

            $list=$list->where($where)->order("id desc")
                ->paginate(20)->each(function($item, $key)use ($turntable_column){
                    $item['turntable_title'] = $turntable_column[$item['turntable_id']]['title']??'';
                    return $item;
                });
            $page = $list->render();
        }

        $count_data  = [
            'all_member_count'=>Db::name("turntable_winning_record")->where($where)->group('member_id')->count(),
            'sum_gold'=>Db::name("turntable_winning_record")->where($where)->where(['prize_type'=>PrizeService::TYPE_GOLD[0]])->sum('prize_number'),
            'sum_vip'=>Db::name("turntable_winning_record")->where($where)->where(['prize_type'=>PrizeService::TYPE_VIP[0]])->sum('prize_number'),
            'sum_super_vip'=>Db::name("turntable_winning_record")->where($where)->where(['prize_type'=>PrizeService::TYPE_THANKS[0]])->sum('prize_number'),
        ];

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'turntable_list' => $turntable_list,
            'count_data' => $count_data,
            "channel" => $channel,
            'channelAll'=> Db::name('channel')->distinct(true)->field('channelid')->select(),
        ]);

        return view();
    }


}
