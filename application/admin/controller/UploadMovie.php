<?php

namespace app\admin\controller;

class UploadMovie
{
    public function index()
    {
        set_time_limit(0);   // 设置脚本最大执行时间 为0 永不过期
        header('Content-type:application/json;charset=utf-8');
        $file = request()->file('file');
        if ($file) {
            $info = $file->validate(['size' => 2 * 1024 * 1024 * 1024, 'ext' => 'mp4,MP4,m3u8'])->move(UPLOAD_PATH . UPLOAD_NAME . '/app_movie');
            if (!$info) {
                echo json_encode([
                    'status' => 'error_1',
                    'message' => $file->getError(),
                ]);
                die;
            }
            $filepath = UPLOAD_NAME . '/app_movie/' . $info->getSaveName();
            echo json_encode([
                'status' => 'ok',
                'path' => $filepath,
            ]);
            die;
        } else {
            echo json_encode([
                'status' => 'error_2',
                'message' => '未知错误',
            ]);
            die;
        }
    }
}
