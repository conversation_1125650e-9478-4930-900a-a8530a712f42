<?php
namespace app\admin\controller;

use app\Service\AppMovieService;
use app\Service\ElasticsearchService;
use app\Service\UserBehaviorService;
use think\Db;
class UserBehavior extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '用户行为管理',
            'name'=>'user_behavior',
            'current_url'=>'/admin/user_behavior',
        ];
        parent::__construct($conf);
    }

    public function index(){
        return $this->common_index(ltrim($this->current_url.'/'.__FUNCTION__,'/admin'),1);
    }

    public function user_behavior_video(){
        $this->title = "用户详细播放行为";
        $view = ltrim($this->current_url.'/'.__FUNCTION__,'/admin');
        $this->current_url = '/admin/user_behavior/user_behavior_video';
        return $this->common_index($view,2);
    }

    public function common_index($view, $category)
    {
        $condition = [];
        $condition['userId'] = $userId = input('request.userId');
        $condition['account'] = $account = input('request.account');
        $condition['begin_time'] = $createTimeIntStart = input('request.begin_time');
        $condition['end_time'] = $createTimeIntEnd = input('request.end_time');

        // 将日期字符串转换为时间戳
        $createTimeIntStart = $createTimeIntStart ? strtotime($createTimeIntStart) * 1000 : null;
        $createTimeIntEnd = $createTimeIntEnd ? (strtotime($createTimeIntEnd) + 86399) * 1000 : null;

        $page = input('request.page', 1); // 获取当前页数，默认为第1页
        $size=30; //默认每页查询页数
        if (!empty($_GET['is_export']) && $_GET['is_export'] == 1) $size = 10000;

        $index = ElasticsearchService::INDEX_NAME;
        $type = '_doc'; // 使用 _doc 作为 type 字段

        // 构建查询条件
        $query = [
            'bool' => [
                'must' => [[
                    'term' => ['platformCode.keyword' => get_order_prefix()]
                ]],
                'filter' => []
            ]
        ];

        if (empty($condition['userId']) && empty($condition['account'])) {
            $query['bool']['must'][] = [
                'term' => ['_id' => 0]
            ];
        }

        $query['bool']['must'][] = [
            'term' => ['category' => $category]
        ];

        if ($userId) {
            $userIds = explode(',', $userId);

            $query['bool']['must'][] = [
                'terms' => ['userId' => $userIds]
            ];
        }

        if ($account) {
            $query['bool']['must'][] = [
                'term' => ['account.keyword' => $account]
            ];
        }

        // 构建 createTimeInt 的范围查询条件
        if ($createTimeIntStart || $createTimeIntEnd) {
            $rangeQuery = [];

            if ($createTimeIntStart) {
                $rangeQuery['gte'] = $createTimeIntStart;
            }

            if ($createTimeIntEnd) {
                $rangeQuery['lte'] = $createTimeIntEnd;
            }

            $query['bool']['filter'][] = [
                'range' => [
                    'time' => $rangeQuery
                ]
            ];

            if (empty($condition['userId']) && empty($condition['account'])) {
                echo "<script>alert('请选择用户id或用户名称'); window.history.back(-1);  </script>";
                exit();
            }
        }

        // 添加排序
        $sort = [
            'userId.keyword' => [
                'order' => 'desc'
            ],
            'time' => [
                'order' => 'desc'
            ]
        ];

        // 初始化 scroll 参数
        $scroll = '2m'; // scroll 保持2分钟
        $results = [];
//        $size = 30; // 每次查询1000条
        $from = ($page - 1) * $size;

        if($from!=0)$scroll=0;

        // 第一次查询，初始化 scroll
        $initialResponse = (new ElasticsearchService())->search($index, $scroll, [
            'query' => $query,
            'sort' => [$sort],
            'from'=>$from,
            'size' => $size,
        ]);


        $scrollId = $initialResponse['_scroll_id']??0;
        $hits = $initialResponse['hits']['hits'];
        $results = array_merge($results, $hits);
        $totalFetched = (int)$initialResponse['hits']['total']['value'] ?? 0;

        if (!empty($_GET['is_export']) && $_GET['is_export'] == 1) {
            $totalFetched=(($totalFetched>10000)?10000:$totalFetched);
//            $totalFetched += count($hits);
            // 使用 scroll 继续分页获取数据
            while (!empty($hits) && $totalFetched < 100000) {

                $scrollResponse = (new ElasticsearchService())->scroll(
                    $scroll,
                    $scrollId
                );
                $scrollId = $scrollResponse['_scroll_id'];
                $hits = $scrollResponse['hits']['hits'];
                $results = array_merge($results, $hits);
                $totalFetched += count($hits);
            }
        }

        $list = UserBehaviorService::handleData($results);

        if (!empty($_GET['is_export']) && $_GET['is_export'] == 1) {
            if (!empty($list)) {
                $export_data = [];
                foreach ($list as $k => $v) {
                    $export_data[] = [
                        'userId' => $v['userId'],
                        'account' => $v['account'],
                        'createTime' => $v['createTime'],
                        'uid_name' => $v['uid_name'],
                        'page' => $v['page'],
                        'describe' => $v['describe'],
                    ];
                }
                $title = ['用户id', '用户名称', '时间', '行为类型', '页面或功能', '具体描述'];
                $filename = '用户行为' . ($category == 2 ? '(播放)' : '') . '记录';
                $this->excel_data($filename, $title, $export_data);
                exit;
            }
            exit;
        }

        $total = $totalFetched;

        $page_where = [];
        foreach ($condition as $k => $v) {
            if (!empty($v)) {
                $page_where[$k] = $v;
            }
        }
        $page_where_string = '';
        if (!empty($page_where)) {
            $page_where_string = http_build_query($page_where) . '&';
        }

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'paginationHtml' => paginate($total, $page, $size, $this->current_url . '?' . $page_where_string . 'page='),
        ]);
        return view($view);
    }




}
