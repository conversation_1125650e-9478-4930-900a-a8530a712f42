<?php

namespace app\admin\controller;
use think\Db;


class Video extends BaseController
{
    private $title, $current_url, $name, $error;
    public function initialize()
    {
        parent::initialize();
        $this->name = "video";
        $this->title = "热榜列表";
        $this->current_url = "/admin/video";
    }
    private function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    function index(){
        $condition['id']     = input('request.id/d');
        $condition['order']     = input('request.order/d');
        $condition['video_name']  = trim(input('request.video_name'));
        $condition['status'] = input('request.status');
        $condition['username']   = input('request.username');
        $where = [];
        if (!empty($condition['id'])) $where[] = ['id', '=', $condition['id']];
        if (!empty($condition['video_name'])) $where[] = ['a.video_name', 'like', '%' . $condition['video_name'] . '%'];
        if (!empty($condition['status']) || $condition['status'] === '0') { $where[] = ['a.status', '=', $condition['status']]; }
        if (!empty($condition['username'])) $where[] = ['a.username', 'like', '%' . $condition['username'] . '%'];
        $order = '';
        if (!empty($condition['order'])) {
            switch ($condition['order']) {
                case 1:
                    $order = 'b.video_play_count';
                    break;
                case 2:
                    $order = 'b.like';
                    break;
                case 3:
                    $order = 'b.comment';
                    break;
            }
        }
        $order  .= (($order ? ' desc,' : '') . 'a.sort desc');
        $fields  = "a.*,";
        $fields .= "b.like,b.comment,b.video_play_count";
        $query   = Db::connect('short_video')->table("xg_video100")
                ->alias("a")
                ->leftJoin(["xg_video_heat" => "b"], "a.pool_id=b.video_id")
                ->where($where)
                ->field($fields);
              $list  = $query->order('a.status','desc')->order($order)->paginate(10);
        $item  = $list->toArray();
        $data  = $item["data"];
        $this->assign([
            'list'      => $list,
            'condition' => $condition,
            'data'      => $data
        ]);
        $this->_assign();
        return view();
    }
    function  add(){
        $post = input("post.");
        $no = Db::connect('short_video')->table("xg_video100")->where('pool_id',$post['id'])->find();
        if($no){
            json_fail_admin("视频已经存在热榜");
        }
        $num =   Db::connect('short_video')->table("xg_video100")->where('status',1)->count();
        $status = 1;
        if($num >= 100){
            $status = 0;
        }

        $data=[];
        $data['pool_id']=$post['id'];
        $data['cover_picture']=$post['img'];
        $data['url']=explode('?auth_key=',explode('m3u8raw.m3u8?url=xzKpTMJ8mIj1Fggoaa0KT76oUNDUJe',$post['url'])[1])[0];
        $data['username']=$post['username'];
        $data['user_id']=$post['userid'];
        $data['video_time']=$post['time'];
        $data['sort']=50;
        $data['status'] =  $status;
        $data['video_name'] =  $post['name'];
//        print_r($post);exit;
        $id = Db::connect('short_video')->table("xg_video100")->insertGetId($data);
        if($id && $status){
            $num +=1 ;
            json_success_admin('添加热榜成功并开启！！！'.$num);
        }else{
            json_success_admin('热榜推荐已经开启100个 须手动调整热榜！！！');
        }
    }

    function edit($id)
    {
        $data['status']  = input('post.op');
        $num =   Db::connect('short_video')->table("xg_video100")->where('status',1)->count();
        if($num >= 100   && $data['status'] == 1 ){
            json_fail_admin('热榜推荐已经开启100个 须手动调整热榜！！！');
        }
        Db::connect('short_video')->table("xg_video100")->where("id", $id)->update($data);
        if($data['status'] == 1){
            $num+=1;
            json_success_admin('开启热榜'.$num);
        }else{
            $num-=1;
            json_success_admin('开启热榜'.$num);
        }
    }

    public function del($id) {
        if (!Db::connect('short_video')->table("xg_video100")->where("id", $id)->delete()) {
            json_fail_admin('视频已不再热榜');
        }
        $num =   Db::connect('short_video')->table("xg_video100")->where('status',1)->count();
        json_success_admin('移出热榜成功'.$num);
    }

    function sort(){
        $post = input("post.");
        $id = $post['id'];
        $data['sort'] = $post['sort'];
        Db::connect('short_video')->table("xg_video100")->where("id", $id)->update($data);
        json_success_admin();
    }

    function ad(){
        $list  = Db::connect('short_video')->table("xg_video_ad")->order('status','desc');

        if(!empty($_GET['is_export']) && $_GET['is_export']==1){
            // 日志记录
            $this->addlog('导出用户数据',json_encode(['before_data'=>[],'after_data'=>[]]));
            $rolename=db::name('admin_role')->where('status',1)->where('id',$this->user['role_id'])->value('name');
            if(!in_array($rolename,['系统管理员','管理员'])){
                echo "<script>alert('数据导出中，请稍等'); window.history.back(-1);  </script>";exit();
            }
            $list=$list->select();
            $export_data=[];
            $place_name=['未知','下方/1','下方/2','上方','侧边'];
            foreach ($list as $k=>$v){
                $export_data[]=[
                    'id'=>$v['id'],
                    'place'=>$place_name[$v['place']],
                    'img'=>$v['img'],
                    'advertising'=>$v['advertising'],
                    'instructions'=>$v['instructions'],
                    'link'=>$v['link'],
                    'restriction'=>$v['restriction'],
                    'total'=>$v['total'],
                    'create_time'=>$v['create_time'],
                    'status'=>$v['status'],
                    'remark'=>$v['remark'],
                    'img_url'=>$v['img_url'],
                    'export_time'=>date('Y-m-d H:i:s')
                ];
            }

            $title = ['ID','位置\备注','广告图','广告说明','连接说明','链接网址','隐藏条件','点击次数','发布日期','状态','备注','导出时间'];
            $filename = '短视频广告统计数据';
            $this->excel_data($filename,$title,$export_data);exit;
        }

        $list = $list->paginate(10);
        $item = $list->toArray();
        $data = $item["data"];
        $this->assign([
            'list'      => $list,
            'data'      => $data
        ]);
        $this->_assign();
        return view();
    }

    function adadd(){

        if (request()->isPost()) {
            $post = input("post.");
            if(isset($post['id']) && $post['id']) {
             $id =     $post['id'] ;
             unset($post['id']) ;
                $s = Db::connect('short_video')->table("xg_video_ad")->where("id", $id)->update($post);
                if($s){
                    json_success_admin('修改成功');
                }else{
                    json_fail_admin('修改失败！！！');
                }
            }else{
                $post['status'] = 0;
//            $where = [];
//            $where[] =['place','=',$post['place']];
//            $where[] =['status','=',1];
//            if(Db::connect('short_video')->table("xg_video_ad")->where($where)->find()){
//                json_fail_admin('改位置已有广告');
//            }
                $post['img'] = ltrim(parse_url($post['img'], PHP_URL_PATH));
                $post['img_url'] = ltrim(parse_url($post['img_url'], PHP_URL_PATH));
                $id = Db::connect('short_video')->table("xg_video_ad")->insertGetId($post);
                if ($id) {
                    json_success_admin('添加成功请手动开启！！！');
                } else {
                    json_fail_admin('添加失败！！！');
                }
            }
        }
        $data['img_url'] = '';
        $data['restriction'] = 1;
        $data['place'] = 1;
        $data['img'] = '';
        $data['jump_type'] = 0;
        $this->assign([
            'data'      => $data
        ]);
        $this->_assign();
        return view("adadd");
    }

    function adstatus($id){
        $data['status']  = input('post.op');
        $place = input('post.place');

        if($data['status'] == 1 ){
             if(Db::connect('short_video')->table("xg_video_ad")->where('place',$place)->where('status',1)->find()){
                 json_fail_admin('此位置广告位已经存在！！！');
             }
        }
        Db::connect('short_video')->table("xg_video_ad")->where("id", $id)->update($data);
        if($data['status'] == 1){
            json_success_admin('开启成功');
        }else{
            json_success_admin('关闭成功');
        }
    }
    function addel($id){
        if (!Db::connect('short_video')->table("xg_video_ad")->where("id", $id)->delete()) {
            json_fail_admin('视频已不再热榜');
        }
        json_success_admin('删除成功！！！');
    }

    function adedit($id){
        $data = Db::connect('short_video')->table("xg_video_ad")->where('id',$id)->find() ;
        $this->assign([
            'data'      => $data
        ]);
        $this->_assign();
        return view('adadd');
    }

    function ggtotal(){
        $condition['id']     = input('request.id/d');
        $condition['username']   = input('request.username');
        $condition['type']   = input('request.type');
        $where = [];
        if (!empty($condition['id'])) $where[] = ['marquee_id', '=', $condition['id']];
        if (!empty($condition['username'])) $where[] = ['nick_name', 'like', '%' . $condition['username'] . '%'];
        if (!empty($condition['type'])) $where[] = ['type', 'like', '%' . $condition['type'] . '%'];
        $list = Db::connect('short_video')->where($where)->table("xg_marquee_log")->order('id','desc')->paginate(10);
        $item  = $list->toArray();
        $data  = $item["data"];
        $this->assign([
            'list'      => $list,
            'condition' => $condition,
            'data'      => $data
        ]);
        $this->_assign();

        return view('ggtotal');
    }

    function video_advertisement(){
        $query   = Db::connect('short_video')->table("xg_video_advertisement") ;
               $list  = $query->order('status')->paginate(10);
        $item  = $list->toArray();
        $data  = $item["data"];
        $this->assign([
            'list'      => $list,
            'data'      => $data
        ]);
        $this->_assign();
        return view();
    }
    function video_advertisement_status($id){
        $data['status']  = input('post.op');
        $type = input('post.type');

        if($data['status'] == 1 ){
            if(Db::connect('short_video')->table("xg_video_advertisement")->where('type',$type)->where('status',1)->find()){
                json_fail_admin('请勿开启多个相同类型！！！');
            }
        }
        Db::connect('short_video')->table("xg_video_advertisement")->where("id", $id)->update($data);
        if($data['status'] == 1){
            json_success_admin('开启成功');
        }else{
            json_success_admin('关闭成功');
        }
    }


    function add_video_advertisement(){
        $policy = Db::connect('short_video')->table("xg_update_confing")->find();
        if (! $policy) {
            json_fail_admin("请先进行我“短视频上传规则配置”！");
        }
        if (request()->isPost()) {
            $post = input("post.");
            $op   = isset($post["op"]) ? $post["op"] : "";
            if ($op == "upload") {
                // 获取上传参数
                $config    = source_config();
                if (! key_exists("video_upload", $config)) {
                    // 提示未设置路径
                    json_fail_admin("未设置视频上传地址，请先设置！路径：系统配置-资源路径配置");
                }
                $uploadUrl = strtolower($config["video_upload"]);
                // 将http转换为https
                if (substr($uploadUrl, 0, 5) != "https") {
                    $uploadUrl = "https" . substr($uploadUrl, 4);
                }
                // 是否需要拼接file
                if (substr($uploadUrl, -4) != "file") {
                    if (substr($uploadUrl, -1) != '/') $uploadUrl .= '/';
                    $uploadUrl = $uploadUrl . "file";
                }
                // 设置提交签名参数
                $radmonStr  = implode("", range("a", "z")) . implode("", range(0, 9));
                $radmonStr  = str_shuffle($radmonStr . strtoupper($radmonStr));
                $radmonStr  = substr($radmonStr, 0, mt_rand(10, strlen($radmonStr)));
                $time       = date("YmdHi");
                // 生成加密后的参数
                $arr = [
                    "time"   => $time,
                    "string" => $radmonStr,
                    "token"  => "ENNISAPI"
                ];
                sort($arr, SORT_STRING);
                json_success_admin("success", [
                    "url"       => $uploadUrl,
                    "time"      => $time,
                    "strings"   => $radmonStr,
                    "videosign" => strtoupper(md5(sha1(implode("", $arr))))
                ]);
            }
            // 数据提交
            $data = [];

            // 视频文件，视频时间
            if (! $post["url"]) {
                json_fail_admin("请先上传视频文件！");
            }
            $data["url"] = $post["url"];
            $data["time"] = $post["time"];
            $data["status"] = $post["status"];
            $data["type"] = $post["type"];
            // 数据插入
            if(Db::connect('short_video')->table("xg_video_advertisement")->where('type',$post["type"])->where('status',1)->find()){
                $data["status"] = 0  ;
            }
            $result =  Db::connect('short_video')->table("xg_video_advertisement")->insertGetId($data);
            if (! $result) {
                json_fail_admin("新增视频失败：" . $this->error);
            }
            json_success_admin();
        }
        $this->assign([
            "policy" => $policy
        ]);
        return view();
    }

    function video_advertisement_del($id){
        if (!Db::connect('short_video')->table("xg_video_advertisement")->where("id", $id)->delete()) {
            json_fail_admin('数据不存在！');
        }
        json_success_admin('删除成功！！！');
    }
}
