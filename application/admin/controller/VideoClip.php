<?php
namespace app\admin\controller;

use app\extend\Cdn\Tencent;
use app\Helper\CacheHelper;
use think\Db;
/**
 * 短视频
 *
 */
class VideoClip extends BaseController
{
    private $title, $current_url, $name, $error;
    public function initialize()
    {
        parent::initialize();
        $this->name = "video_clip";
        $this->title = "短视频列表";
        $this->current_url = "/admin/video_clip";
    }
    private function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    /**
     * 列表
     * @return \think\response\View
     */
    public function index() {
        // 设置搜索条件
        $where = [
            // 排除待审核和删除及审核不通过
            ["b.status", "<>", 2]
        ];
        $condition['id']     = input('request.id/d');
        $condition['title']  = trim(input('request.title'));
        $condition['status'] = input('request.status');
        $condition['order']  = input('request.order/d');
        $condition['nick_name']   = input('request.nick_name');
        if (!empty($condition['id'])) $where[] = ['a.id', '=', $condition['id']];
        if (!empty($condition['title'])) $where[] = ['b.video_name', 'like', '%' . $condition['title'] . '%'];
        if (!empty($condition['status']) || $condition['status'] === '0') { $where[] = ['b.status', '=', $condition['status']]; }
        if (!empty($condition['nick_name'])) $where[] = ['a.nick_name', 'like', '%' . $condition['nick_name'] . '%'];
        $condition['id'] = $condition['id'] ? $condition['id'] : '';
        // order
        $order = '';
        if (!empty($condition['order'])) {
            switch ($condition['order']) {
                case 1:
                    $order = 'c.video_play_count';
                    break;
                case 2:
                    $order = 'c.like';
                    break;
                case 3:
                    $order = 'c.comment';
                    break;
                case 4:
                    $order = 'd.share_num';
                    break;
                case 5:
                    $order = "heats";
                case 6:
                    $order = 'b.sort';
                    break;
            }
        }
        $order  .= (($order ? ' desc,' : '') . 'b.create_time desc');
        $fields  = "a.id,a.name,b.video_img,a.url_path,b.video_path,b.sort,a.video_time,a.user_id,a.nick_name,";
        $fields .= "c.video_play_count,c.like,c.comment,d.share_num,a.create_time,a.address,b.status,";
        $fields .= "b.video_class_id,b.referrals_phases,b.start_time,b.end_time,";
        $fields .= "ifnull(c.heats,0) heats,b.tag_ids,b.video_type,";
        $fields .= "e.pool_id";
        $query   = Db::connect('short_video')->table("xg_video_pool")
                       ->alias("b")
                       ->leftJoin(["xg_video_upload_information" => "a"], "a.id=b.video_id")
                       ->leftJoin(["xg_video_heat" => "c"], "b.video_id=c.video_id")
                       ->leftJoin(["xg_share_video" => "d"], "b.video_id=d.video_id")
                       ->leftJoin(["xg_video100" => "e"], "a.id=e.pool_id")
                       ->field($fields)
                       ->where($where)
                       ->where("a.id is not null");
        $list  = $query->order($order)->paginate(10)->each(function ($item, $key){

            $item['video_path'] = replaceCdnUrl($item['video_path']);
            $item['url'] = $item['video_path'];
            $item['url_base64'] = base64_encode($item['url']);
//            dump($item['video_path']);die();
            return $item;
//            dump($item['video_path']);die();
        });
        $item  = $list->toArray();
        $data  = $item["data"];


        $this->assign([
            'list'      => $list,
            'condition' => $condition,
            'data'      => $data
        ]);
        $this->_assign();
        return view();
    }

    /**
     * 管理员新增短视频（新的，在用2021-12-27）
     */
    public function add() {

        // 获取分享信息
        $shareInfo = Db::name('video_config')->field("share_title,share_desc")->where('id', 1)->find();
        if (empty($shareInfo)) {
            $shareInfo["share_title"] = "";
            $shareInfo["share_desc"] = "";
        }
        if (request()->isPost()) {
            $post = input("post.");
            $op   = isset($post["op"]) ? $post["op"] : "";
            if ($op == "upload") {
                // 获取上传参数
                $config    = source_config();
                if (! key_exists("video_upload", $config)) {
                    // 提示未设置路径
                    json_fail_admin("未设置短视频上传地址，请先设置！路径：系统配置-资源路径配置");
                }
                $uploadUrl = strtolower($config["video_upload"]);

                // 设置提交签名参数
                $radmonStr  = implode("", range("a", "z")) . implode("", range(0, 9));
                $radmonStr  = str_shuffle($radmonStr . strtoupper($radmonStr));
                $radmonStr  = substr($radmonStr, 0, mt_rand(10, strlen($radmonStr)));
                $time       = date("YmdHi");
                // 生成加密后的参数
                $arr = [
                    "time"   => $time,
                    "string" => $radmonStr,
                    "token"  => "ENNISAPI"
                ];
                sort($arr, SORT_STRING);
                json_success_admin("success", [
                    "url"       => $uploadUrl,
                    "time"      => $time,
                    "strings"   => $radmonStr,
                    "videosign" => strtoupper(md5(sha1(implode("", $arr))))
                ]);
            }
            // 数据提交
            $data = [];
            $data["sort"] = $post["sort"]??0;
            $post["title"] = trim_space($post["title"]);
            if (mb_strlen($post["title"]) < 4 || mb_strlen($post["title"]) > 50) {
                json_fail_admin("短视频长度为4-50个字符！");
            }
            if (! empty($post["share_title"])) {
                $post["share_title"] = trim_space($post["share_title"]);
                if (mb_strlen($post["share_title"]) < 4 || mb_strlen($post["share_title"]) > 100) {
                    json_fail_admin("分享标题长度为4-100个字符!");
                }
                $data["share_desc"] = $post["share_title"];
            } else {
                $data["share_desc"] = $shareInfo["share_title"];
            }
            if (! empty($post["share_desc"])) {
                $post["share_desc"] = trim_space($post["share_desc"]);
                if (mb_strlen($post["share_desc"]) < 5 || mb_strlen($post["share_desc"]) > 200) {
                    json_fail_admin("分享标题长度为5-200个字符!");
                }
                $data["share_describe"] = $post["share_desc"];
            } else {
                $data["share_describe"] = $shareInfo["share_desc"];
            }
            // 视频文件，视频时间
            if (! $post["url"] || ! $post["time"]) {
                json_fail_admin("请先上传视频文件！");
            }
            $data["url_path"] = $post["url"];


            $parse_url = parse_url($post["m3u8_url"]);
            $data["video_path"] = $parse_url['scheme'].'://'.$parse_url['host'].':'.sysconf('short_video_upload_port').str_replace('index.m3u8','',$parse_url['path']).sysconf('short_video_rake').'/hls/index.m3u8';

            switch (count(explode(':',$post["time"]))){
                case 1:$post["time"] = "00:00".$post["time"];break;
                case 2:$post["time"] = "00:".$post["time"];
            }

            $data["video_time"] = strtotime($post["time"]) - strtotime('today');
            $data["origin_video_time"] = $post["time"];
            $data["name"] = $post["title"];

            if(!empty($post['user_id'])){
                $user_detail = Db::name('member_info')->where('is_bot', 1)->where('id','=',$post['user_id'])->find();
            }else{
                // 获取官方用户信息
                $user_detail = $this->_getOfficialUser();
                if (! $user_detail) {
                    json_fail_admin("获取官方用户信息失败！");
                }
            }

            $data["user_id"] = $user_detail["id"];
            $data["nick_name"] = $user_detail["nick_name"];
            $data["video_type"] = 2;
            $data["status"] = 1;
            $data["video_name"] = $data["name"];
            $data["referrals_phases"] = 999;
            $data["referrals_phases"] = $post["share_title"];

            // 数据插入
            $result = $this->_insertVideoData($data, true);
            if (! $result) {
                json_fail_admin("新增视频失败：" . $this->error);
            }
            json_success_admin();
        }
        // 获取短视频上传规则
        $policy = Db::connect('short_video')->table("xg_update_confing")->find();
        if (! $policy) {
            json_fail_admin("请先进行我“短视频上传规则配置”！");
        }

        $config    = source_config();
        if (! key_exists("video_upload", $config)) {
            // 提示未设置路径
            json_fail_admin("未设置短视频上传地址，请先设置！路径：系统配置-资源路径配置");
        }
        $uploadUrl = strtolower($config["video_upload"]);


        $this->assign([
            "share"  => $shareInfo,
            "policy" => $policy,
            "uploadUrl" => $uploadUrl,
            "short_video_bot_list" => Db::name('member_bot')->field("* ,account as nick_name")->select(),
        ]);
        return view("add_video");
    }

    /**
     * 管理员新增短视频（酷米旧的，留着备份2021-12-27）
     */
    public function addOld() {
        // 获取分享信息
        $shareInfo = Db::name('video_config')->field("share_title,share_desc")->where('id', 1)->find();
        if (empty($shareInfo)) {
            $shareInfo["share_title"] = "";
            $shareInfo["share_desc"] = "";
        }
        if (request()->isPost()) {
            $post = input("post.");
            $op   = isset($post["op"]) ? $post["op"] : "";
            if ($op == "upload") {
                // 获取上传参数
                $config    = source_config();
                if (! key_exists("video_upload", $config)) {
                    // 提示未设置路径
                    json_fail_admin("未设置短视频上传地址，请先设置！路径：系统配置-资源路径配置");
                }
                $uploadUrl = strtolower($config["video_upload"]);
                // 将http转换为https
//                if (substr($uploadUrl, 0, 5) != "https") {
//                    $uploadUrl = "https" . substr($uploadUrl, 4);
//                }
                // 是否需要拼接file
                if (substr($uploadUrl, -4) != "file") {
                    if (substr($uploadUrl, -1) != '/') $uploadUrl .= '/';
                    $uploadUrl = $uploadUrl . "file";
                }
                // 设置提交签名参数
                $radmonStr  = implode("", range("a", "z")) . implode("", range(0, 9));
                $radmonStr  = str_shuffle($radmonStr . strtoupper($radmonStr));
                $radmonStr  = substr($radmonStr, 0, mt_rand(10, strlen($radmonStr)));
                $time       = date("YmdHi");
                // 生成加密后的参数
                $arr = [
                    "time"   => $time,
                    "string" => $radmonStr,
                    "token"  => "ENNISAPI"
                ];
                sort($arr, SORT_STRING);
                json_success_admin("success", [
                    "url"       => $uploadUrl,
                    "time"      => $time,
                    "strings"   => $radmonStr,
                    "videosign" => strtoupper(md5(sha1(implode("", $arr))))
                ]);
            }
            // 数据提交
            $data = [];
            $post["title"] = trim_space($post["title"]);
            if (mb_strlen($post["title"]) < 4 || mb_strlen($post["title"]) > 50) {
                json_fail_admin("短视频长度为4-50个字符！");
            }
            if (! empty($post["share_title"])) {
                $post["share_title"] = trim_space($post["share_title"]);
                if (mb_strlen($post["share_title"]) < 4 || mb_strlen($post["share_title"]) > 100) {
                    json_fail_admin("分享标题长度为4-100个字符!");
                }
                $data["share_desc"] = $post["share_title"];
            } else {
                $data["share_desc"] = $shareInfo["share_title"];
            }
            if (! empty($post["share_desc"])) {
                $post["share_desc"] = trim_space($post["share_desc"]);
                if (mb_strlen($post["share_desc"]) < 5 || mb_strlen($post["share_desc"]) > 200) {
                    json_fail_admin("分享标题长度为5-200个字符!");
                }
                $data["share_describe"] = $post["share_desc"];
            } else {
                $data["share_describe"] = $shareInfo["share_desc"];
            }
            // 视频文件，视频时间
            if (! $post["url"] || ! $post["time"]) {
                json_fail_admin("请先上传视频文件！");
            }
            $data["url_path"] = $post["url"];
            $data["video_time"] = $post["time"];
            $data["name"] = $post["title"];
            $data["video_path"] = $post["m3u8_url"];

            // 获取官方用户信息
            $offical = $this->_getOfficialUser();
            if (! $offical) {
                json_fail_admin("获取官方用户信息失败！");
            }
            $data["user_id"] = $offical["id"];
            $data["nick_name"] = $offical["nick_name"];
            $data["video_type"] = 2;
            $data["status"] = 1;
            $data["video_name"] = $data["name"];
            $data["referrals_phases"] = 999;

            // 数据插入
            $result = $this->_insertVideoData($data, true);
            if (! $result) {
                json_fail_admin("新增视频失败：" . $this->error);
            }
            json_success_admin();
        }
        // 获取短视频上传规则
        $policy = Db::connect('short_video')->table("xg_update_confing")->find();
        if (! $policy) {
            json_fail_admin("请先进行我“短视频上传规则配置”！");
        }
        $this->assign([
            "share"  => $shareInfo,
            "policy" => $policy
        ]);
        return view("add_video");
    }


    /**
     * 短视频编辑
     * @param number $id
     */
    public function edit($id) {
        $data = $this->_getVideoInfo($id);
        if (empty($data)) json_fail_admin('短视频不存在！');
        readBase($data['video_img']);  //提前加载图片
        if (request()->isPost()) {
            if (input('post.op')) {
                if (input('post.op')=='status') {
                    $status['status'] = 1;
                    if ($data['status'] == 1) $status['status'] = 0;

                    if (! $this->_saveVideoData($status, $id)) {
                        json_fail($this->error);
                    }
                    json_success_admin();
                } elseif (input("post.op") == "upload") {
                    // 上传图片
                    (new File())->upload();
                }
                json_fail_admin('请联系管理员！');
            }

            $updateData = $this->getDbData($data);

            $post = input("post.");
            if(!empty($post['user_id'])){
                $user_detail = Db::name('member_info')->where('is_bot', 1)->where('id','=',$post['user_id'])->find();
                $updateData['user_id']=$user_detail['id'];
                $updateData['nick_name']=$user_detail['nick_name'];
            }

            if (! $this->_saveVideoData($updateData, $id)) {
                json_fail($this->error);
            }

            // update_video_clip_weight($id);
            // $this->updateCacheIds();
            json_success_admin();
        }
        $this->assign([
            'data' => $data,
        ]);
        $this->assign('ac','edit');
        $this->assign([
            "short_video_bot_list" => Db::name('member_bot')->field("* ,account as nick_name")->select(),
        ]);

        return view('add');
    }
    /**
     * 详情
     * @param number $id
     * @return \think\response\View
     */
    public function details($id) {
        $data = $this->_getVideoInfo($id);
        if (empty($data)) json_fail_admin('短视频不存在！');
        $this->assign('data', $data);
        return view();
    }

    /**
     * 删除
     * @param number $id
     */
    public function del($id) {
        json_fail_admin('该功能暂时关闭');
        if (! $this->_getVideoInfo($id)) {
            json_fail_admin("短视频不存在！");
        }
        if (! $this->_saveVideoData(["status" => 2], $id)) {
            json_fail_admin($this->error);
        }
        json_success_admin();
    }

    /**
     * 获取短视频信息
     * @param number $id
     * @return array
     */
    private function _getVideoInfo($id) {
        $where = [
            ["a.status", "not in", [0, 2, 4]],
            ["a.id", "=", $id]
        ];
        $fields  = "a.id,a.name,b.video_img,c.like,c.comment,c.video_play_count,b.sort,";
        $fields .= "d.share_num,d.share_desc,d.share_describe,b.status,a.video_time,";
        $fields .= "a.create_time,b.tag_ids,b.video_path,a.url_path,a.user_id,a.nick_name";

        $data    = Db::connect('short_video')->table("xg_video_upload_information")
                       ->alias("a")
                       ->leftJoin(["xg_video_pool" => "b"], "a.id=b.video_id")
                       ->leftJoin(["xg_video_heat" => "c"], "b.video_id=c.video_id")
                       ->leftJoin(["xg_share_video" => "d"], "b.video_id=d.video_id")
                       ->field($fields.', b.user_id  user_id_pool , b.nick_name  nick_name_pool')
                       ->where($where)
                       ->group($fields)
                       ->find();
        return $data;
    }

    /**
     * 上传文件
     * @param string $type
     * @return
     */
    private function upload($type = "video")
    {
        $file = request()->file('upfile');
        $filePath = UPLOAD_PATH . UPLOAD_NAME . '/video_clip';
        $size = 1000 * 1024 * 1024;
        $type = strtolower($type);
        if ($file) {
            // 自定义文件名
            $ext = pathinfo($file->getInfo('name'), PATHINFO_EXTENSION);
            $fileName = md5(time() . mt_rand(1, 1000)) . '.' . $ext;
            if ($type == "video") {
                // 视频
                $mimeType = "mp4,MP4";
            } else {
                // 图片
                $mimeType = "jpg,JPG,jpeg,JPEG,png,PNG,gif,GIF";
            }
            $info = $file->validate(['size' => $size, 'ext' => $mimeType])->move($filePath, $fileName);
            if (! $info) {
                json_fail_admin($file->getError());
            }
            // 生成header参数
            $radmonStr  = implode("", range("a", "z")) . implode("", range(0, 9));
            $radmonStr  = str_shuffle($radmonStr . strtoupper($radmonStr));
            $radmonStr  = substr($radmonStr, 0, mt_rand(10, strlen($radmonStr)));
            $time       = date("YmdHi");
            // 生成加密后的参数
            $arr = [
                "time"   => $time,
                "string" => $radmonStr,
                "token"  => "ENNISAPI"
            ];
            sort($arr, SORT_STRING);
            $sign      = implode("", $arr);
            $sign      = sha1($sign);
            $sign      = md5($sign);
            $videosign = strtoupper($sign);
            // $videosign = strtoupper(md5(sha1(implode("", $arr))));
            // 上传地址
            $uploadUrl = source_config()["video_upload"];
            if (substr($uploadUrl, -1) != '/') $uploadUrl .= '/';
            $uploadUrl .= "file";
            // 上传
            $fileName = realpath($filePath . '/' . $fileName);
            $body = [
                "type" => ($type == "video") ? $type : "imgs",
                "file" => (new \CURLFile($fileName, $info->getMime(), time() . mt_rand(10000, 99999) . '.' . $ext))
            ];
            // 上传
            $header = [
                "time:" . $time,
                "strings:" . $radmonStr,
                "videosign:" . $videosign
            ];
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $uploadUrl);
            curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, $body);
            curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
            curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
            $response = curl_exec($ch);
            $code     = curl_errno($ch);
            $error    = curl_error($ch);
            curl_close($ch);
            // 上传结束删除原文件
            @unlink($fileName);

            if ($code === 0) {
                $data = json_decode($response, true);
                if ($data["code"] == 200) {
                    $res = [
                        "url" => $data["data"]["src"]
                    ];
                    if ('imgs' == $type) {
                        $res["url"] = source_config()["video_cdn"] . $data["data"]["src"];
                    }
                    // 视频增加返回时间
                    if ($type == "video") {
                        $res["time"] = floor($data["data"]["time"]);
                    }
                    json_success_admin("success", $res);
                } else {
                    json_fail_admin("上传至远程服务器失败：" . $data["msg"]);
                }
            } else {
                json_fail_admin("上传至远程服务器出错：" . $error);
            }
        } else {
            json_fail_admin('未找到文件！');
        }
    }

    /**
     * 获取编辑提交数据
     * @param array $oriData
     * @return array
     */
    private function getDbData() {
        $mustKeys = [
            'title' => ['短视频名称','length:4,50'],
            'like_num' => ['点赞次数','number|length:1,10'],
            'comment_num' => ['评论次数','number|length:1,10'],
            'status' => ['状态','number|between:0,1'],
            'img' => ['封面图片','length:20,500'],
            'share_num' => ['分享次数','number|length:1,10'],
            'play_num' => ['播放次数','number|length:1,9'],
            'sort' => ['客户端视频排序','number|min:0|max:2147483647'],
        ];
        $validate = [];
        foreach ($mustKeys as $key => $value) {
            $validate[$key . '|' . $value[0]] = 'require|' . $value[1];
        }

        $role = new \think\Validate($validate,[
            'play_num.length'=>'播放次数不能超过9位数',
        ]);
        $post = input('post.');
        foreach ($post as $key => $val) {
            if ($key == 'tag_ids') continue;
            $post[$key] = trim_space($post[$key]);
        }
        if (! $role->check($post)) json_fail_admin($role->getError());
        // 处理需要返回的数据
        $data = [];
        $data["name"]             = $post["title"];
        $data["video_name"]       = $post["title"];
        $data["status"]           = $post["status"];
        $data["like"]             = $post["like_num"];
        $data["comment"]          = $post["comment_num"];
        $data["video_play_count"] = $post["play_num"];
        $data["share_num"]        = $post["share_num"];
        $data["video_img"]        = $post["img"];
        $data["sort"]        = $post["sort"];

        // 未设置短视频标题及描述将读取系统默认值
        $defaultArr = Db::name('video_config')->where('id', 1)->find();
        $shareTitle = input('post.share_title', '');
        if ($shareTitle) {
            if (mb_strlen($shareTitle) < 4 || mb_strlen($shareTitle) > 100) {
                json_fail_admin('分享标题长度为4-100个字符');
            }
        } else {
            $shareTitle = $defaultArr['share_title'];
        }
        $shareDesc = input('post.share_desc', '');
        if ($shareDesc) {
            if (mb_strlen($shareDesc) < 5 || mb_strlen($shareDesc) > 200) {
                json_fail_admin('分享描述长度为5-200个字符');
            }
        } else {
            $shareDesc = $defaultArr['share_desc'];
        }
        $data["share_desc"]     = $shareTitle;
        $data["share_describe"] = $shareDesc;
        // 标题重复检查待产品确认
        return $data;
    }

    public function wait()
    {

        $condition['id'] = input('request.id');
        $condition['title'] = input('request.title');
        if (!empty($condition['title'])) $where[] = ['name', 'like', '%' . $condition['title'] . '%'];

        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'])+60*60*24];


        $condition['status'] = input('request.status',1);

        if ($condition['status'] != '-1') {
            $where[] = ['status', '=', $condition['status']];
        }
        $where[] = ['type', '=', 2];

        $list = Db::name('slice_data')->where($where)->paginate(10);
        $page = $list->render();
        $list = $list->toArray();
        // foreach ($list['data'] as $key => &$value) {
        //     $value['index'] = array_filter(explode(',',$value['index']));
        //     $value['index3'] = array_filter(explode(',',$value['index3']));
        //     $value['cover'] = [];
        //     foreach ($value['index'] as $k => $v) {
        //         $value['cover'][] = readBase($v);
        //     }
        //     foreach ($value['index3'] as $k => $v) {
        //         $value['cover'][] = readBase($v);
        //     }
        // }

        $this->assign([
            'list' => $list,
            'url' => source_config()['movie_url'],
            'page' => $page,
            'condition' => $condition

        ]);
        $this->_assign();

        return view();
    }
    public function edit_wait($id)
    {
        $data = Db::name('slice_data')->find($id);
        if (!$data) {
            json_fail_admin('数据不存在');
        }
        $data['cover'] = [];
        if ($data['index']) {
            $data['index'] = explode(',',$data['index']);
            foreach ($data['index'] as $key => $value) {
                $data['cover'][$value] = readBase($value);  //提前加载图片
            }
        }
        if ($data['index3']) {
            $data['index3'] = explode(',',$data['index3']);
            foreach ($data['index3'] as $key => $value) {
                $data['cover'][$value] = readBase($value);  //提前加载图片
            }
        }
        $data['cover'] = array_filter($data['cover']); //资源路径不存在时返回的空前段依然有选项
        if (request()->isPost()) {

            $data = [];
            $data['like_num'] = 0;
            $data['play_num'] = 0;
            $data['comment_num'] = 0;
            $data['share_num'] = 0;
            $data['weight_num'] = 0;
            $title = input('post.title');
            if (!$title || mb_strlen($title) < 4 || mb_strlen($title) > 50) {
                json_fail_admin('标题长度为4-50个字符');
            }
            $data['title'] = $title;

            $share_title = input('post.share_title');
            if (!$share_title || mb_strlen($share_title) < 4 || mb_strlen($share_title) > 100) {
                json_fail_admin('分享标题长度为4-100个字符');
            }
            $data['share_title'] = $share_title;

            $share_desc = input('post.share_desc');
            if (!$share_desc || mb_strlen($share_desc) < 5 || mb_strlen($share_desc) > 200) {
                json_fail_admin('分享描述长度为5-200个字符');
            }
            $data['share_desc'] = $share_desc;

            $id = input('post.id');
            $url2 = input('post.url2');

            $data['url2'] = $url2;
            $data['status'] = input('post.status');
            $data['size'] = input('post.size');
            $data['img'] = input('post.img');
            $data['sort'] = 0;
            $data['create_time'] = time();
            $data['duration'] = gmstrftime('%H:%M:%S',input('post.duration'));

            // 修改至新数据表
            if (! key_exists('tag_str', input("post."))) {
                json_fail_admin("设置标签！");
            }
            $tagStr = input('post.tag_str');
            $tagIds = [];
            $tag = array_filter(explode(',', $tagStr));
            if (count($tag) < 1 || count($tag) > 5) {
                json_fail("标签范围为1-5个！");
            }
            foreach ($tag as $key => $value) {
                $tid = Db::name('app_tag')->where('title',trim($value))->value('id');
                if (! $tid) {
                    $tid = Db::name('app_tag')->insertGetId(['title'=>trim($value),'status'=>0]);
                }
                $tagIds[] = $tid;
            }
            $data['tag_ids'] = implode(',', $tagIds);

            // 获取官方用户信息
            $official = $this->_getOfficialUser();
            if (! $official) {
                json_fail_admin($this->error);
            }
            $newData = [
                "name"           => $data["title"],
                "url_path"       => $data["url2"],
                "video_time"     => input('post.duration'),
                "user_id"        => $official["id"],
                "nick_name"      => $official["nick_name"],
                "video_type"     => 2,
                "status"         => $data['status'],
                "tag_ids"        => $data['tag_ids'],
                "video_name"     => $data['title'],
                "share_desc"     => $data['share_title'],
                "share_describe" => $data['share_desc'],
                "video_img"      => $data['img'],
                "video_path"     => $data['url2']
            ];
            $re = $this->_insertVideoData($newData, true);
            if (! $re) {
                json_fail_admin($this->error);
            }
            $this->addlog('添加短视频','视频ID:'.$re);
            Db::name('slice_data')->where('id',$id)->update(['status'=>0]);
            // $this->updateCacheIds();
            json_success_admin();
        }
        $config = Db::name('video_config')->where('id', 1)->find();
        $data['share_title'] = $config['share_title'];
        $data['share_desc'] = $config['share_desc'];
        $allTag = Db::name('app_tag')->field('id,title')->order('id desc')->select();
        $this->assign([
            'data' => $data,
            'allTag' => $allTag
        ]);

        $this->assign('url',source_config()['movie_url']);
        return view();

    }

    public function del_wait($id)
    {
        $re = Db::name('video_clip_wait')->where('id',$id)->delete();
        // $this->updateCacheIds();
        json_success_admin();
    }
    public function move_wait($id)
    {
        $re = Db::name('slice_data')->where('id',$id)->update(['type'=>1]);
        $re ? json_success_admin() : json_fail_admin('请重试!');
    }
    /**
     * 短视频配置
     */
    public function config() {
        $data = Db::name('video_config')->where('id', 1)->find();
        if (empty($data)) json_fail_admin('未找到配置文件！');
        if (request()->isPost()) {
            $post = input('post.');
            foreach ($post as $key => $val) {
                $post[$key] = trim_space($val);
            }
            $update = [];
            $validate = [
                'share_title|默认分享标题' => 'require|length:4,100',
                'share_desc|默认分享描述' => 'require|length:5,200'
            ];
            $role = new \think\Validate($validate);
            if (! $role->check($post)) json_fail_admin($role->getError());
            $update['share_title'] = $post['share_title'];
            $update['share_desc'] = $post['share_desc'];
            $update['update_time'] = time();
            $update['update_user'] = $this->user['id'];
            $res = Db::name('video_config')->where('id', $data['id'])->update($update);
            json_success_admin();
        }
        $this->assign('data', $data);
        return view();
    }
    // 更新短视频缓存
    private function updateCacheIds() {
        $ids = Db::name($this->name)->where('status', '=', 1)->column('id');
        cacheSet('video_clip_ids', $ids);
    }


    /**
     * 用户短视频审核列表
     */
    public function review() {
        // 设置搜索及查询条件
        $where = [
            ["a.status", "<>", 4],
            ["a.video_type", "=", 1]
        ];
        $condition["name"] = input("request.name");
        $condition["start_time"] = input("request.start_time");
        $condition["end_time"] = input("request.end_time");
        $condition["status"] = input("request.status", 2);
        if (! empty($condition["name"])) {
            $where[] = ["a.name", "like", "%" . $condition["name"] . "%"];
        }
        if (! empty($condition["start_time"])) {
            $where[] = ["a.create_time", ">=", $condition["start_time"] . " 00:00:00"];
        }
        if (! empty($condition["end_time"])) {
            $where[] = ["a.create_time", "<=", $condition["end_time"] . " 23:59:59"];
        }
        if (! empty($condition["status"]) || is_numeric($condition["status"])) {
            if ($condition["status"] == 1) { // 1、3均为审核通过
                $where[] = ["a.status", "in", [1, 3]];
            } else {
                $where[] = ["a.status", "=", $condition["status"]];
            }
        }
        // 待审核新增升序排序，其他按修改倒序排序
        if (! empty($condition["status"]) && $condition["status"] == 2) {
            $order = "a.create_time";
        } else {
            $order = "a.update_time desc";
        }
        $field  = "a.id,a.nick_name,a.user_id,a.name,a.url_path,b.video_path,a.video_time,a.create_time,";
        $field .= "b.video_img,a.reject_cause,a.address,(CASE WHEN IFNULL(b.status,5)=0 THEN 5 ELSE a.status END) status";
        $list = Db::connect('short_video')->table("xg_video_upload_information")
                    ->alias("a")
                    ->leftJoin(["xg_video_pool" => "b"], "a.id=b.video_id and a.video_type=b.video_type")
                    ->field($field)
                    ->where($where)
                    ->order($order)
                    ->paginate(20);
        $item  = $list->toArray();
        $data  = $item["data"];
//        // 获取设置推荐等级
//        $levelInfo = Db::connect('short_video')->table("xg_evaluate_class")
//                         ->field("id,video_class,referrals_num")
//                         ->where("status", "=", 1)
//                         ->select();
        $this->assign([
            'list'           => $list,
            'condition'      => $condition,
//            'recommendLevel' => $levelInfo,
            'data'           => $data
        ]);
        return view();
    }

    /**
     * 用户短视频审核
     */
    public function review_check() {
        $id     = input("id", 0, "intval");
        $result = input("result");
        $level  = input("level", 0, "intval");
        $cause  = input("cause", "", "trim");
        $ban    = input("ban", 0, "intval");
        if (! is_numeric($id) || ! is_numeric($result) || ($result != 1 && $result != 0)) {
            json_fail_admin("参数错误！");
        }
        // 查询数据
        $where = [
            ["id", "=", $id],
            ["status", "=", 2]
        ];
        $data = Db::connect('short_video')->table("xg_video_upload_information")->where($where)->find();
        if (empty($data)) {
            json_fail_admin("短视频不存在！");
        }
    /*    // 数据判断
        if ($result == 1) {
            // 获取推荐等级数据
            if (! $level) {
                json_fail_admin("请选择推荐等级");
            }
            $where = [
                "id" => $level,
                "status" => 1
            ];
            if ($level == 999) {
                // 全网推送
                $levelInfo = [
                    "id"            => 999,
                    "referrals_num" => 999
                ];
            } else {
                $levelInfo = Db::connect('short_video')->table("xg_evaluate_class")->field("id,referrals_num")->where($where)->find();
                if (empty($levelInfo)) {
                    json_fail_admin("未找到对应推荐层级数据！");
                }
            }
        }*/

        // 处理
        Db::startTrans();
        try {


            if ($result == 1) {
                // 通过
                // 通过需修改上传表状态为3，插入视频池表状态为3待切片，插入视频热度表、短视频分享表
                // 如需标示上用户上传短视频，将修改用户属性关联表
                // 写入视频池表
                $insert = [
                    "user_id" => $data["user_id"],
                    "nick_name" => $data["nick_name"],
                    "address" => $data["address"],
                    "video_id" => $id,
                    "tag_ids" =>'',
                    "video_name" => $data["name"],
                    "video_time" => $data["video_time"] ? $data["video_time"] : 0,
                    "video_class_id" => $level,
                    "referrals_phases" => 999 , // $levelInfo["referrals_num"],
                    "video_type" => 1,
                    "status" => 3
                ];
                $videoId = Db::connect('short_video')->table("xg_video_pool")->insertGetId($insert);
                if (! $videoId) {
                    throw new \Exception("插入视频池表失败！");
                }
                // 视频热度表
                $insert = [
                    "video_id" => $id,
                    "like"  => 0,
                    "comment" => 0,
                    "video_play_count" => 0,
                    "status" => 0 // 切片中热度为0
                ];
                if (! Db::connect('short_video')->table("xg_video_heat")->insert($insert)) {
                    throw new \Exception("插入视频热度表失败！");
                }
                // 短视频分享表
                $shareInfo = Db::name('video_config')->field("share_title,share_desc")->where('id', 1)->find();
                if (empty($shareInfo)) {
                    $shareInfo["share_title"] = "";
                    $shareInfo["share_desc"] = "";
                }
                $insert = [
                    "share_num" => 0,
                    "share_desc" => $shareInfo["share_title"],
                    "share_describe" => $shareInfo["share_desc"],
                    "video_id" => $id
                ];
                if (! Db::connect('short_video')->table("xg_share_video")->insert($insert)) {
                    throw new \Exception("插入视频分享表失败！");
                }
                // 视频上传表状态修改为3并将拒绝原因清空防止二次操作
                if (! Db::connect('short_video')->table("xg_video_upload_information")->where("id", $id)->update(["status" => 3, "reject_cause" => null])) {
                    throw new \Exception("修改视频上传表失败！");
                }

                // 设置推送信息数据
                $msgContent = "恭喜您，您上传的短视频“" . $data["name"] . "”审核通过。";

            } else {
                // 不通过，直接修改状态
                Db::connect('short_video')->table("xg_video_upload_information")->where("id", $id)->update(["status" => 0, "reject_cause" => $cause]);
                // 设置推送信息数据
                if (! $cause) {
                    $msgContent = "很遗憾，您上传的短视频“" . $data["name"] . "”审核不通过。";
                } else {
                    $msgContent = "很遗憾，您上传的短视频“" . $data["name"] . "”因" . $cause . "。";
                }
            }
            // 是否禁止用户上传短视频
            if ($ban == 1) {
                $isUpload = Db::connect('short_video')->table("xg_user_property")->where("user_id", "=", $data["user_id"])->field("id,is_update")->find();
                if (empty($isUpload)) {
                    $insert = [
                        "user_id" => $data["user_id"],
                        "user_type" => 1,
                        "is_update" => 0
                    ];
                    Db::connect('short_video')->table("xg_user_property")->insert($insert);
                } else {
                    if ($isUpload["is_update"] != 0) {
                        Db::connect('short_video')->table("xg_user_property")->where("id", "=", $isUpload["id"])->update(["is_update" => 0]);
                    }
                }
            }
            // 保存日志
            $this->addlog("用户短视频审核", ($result == 1 ? "通过" : "不通过") . "；用户短视频ID：" . $id);
            // 推送信息
            $message = [
                "member_id"    => $data["user_id"],
                "message_type" => 1,
                "title"        => "短视频审核" . ($result == 1 ? "通过" : "未通过"),
                "content"      => $msgContent,
                "create_time"  => time(),
                "create_user"  => 1,
            ];
            Db::name("member_message")->insert($message);

            Db::commit();
            json_success_admin();
        } catch (\Exception $e) {
            Db::rollback();
            json_fail_admin("审核操作失败：" . $e->getMessage());
        }
    }

    /**
     * 用户短视频下架
     */
    public function review_down() {
        $id = input("id", 0, "intval");
        if (! is_numeric($id) || ! $id) {
            json_fail_admin("参数错误！");
        }
        // 查询数据
        $where = [
            ["id", "=", $id],
            ["status", "in", [1, 3]]
        ];
        $data = Db::table("xg_video_upload_information")->where($where)->find();
        if (empty($data)) {
            json_fail_admin("短视频不存在！");
        }
        // 保证数据一致性
        Db::startTrans();
        try {
            // 视频池表
            $poolInfo = Db::table("xg_video_pool")->where("video_id","=", $id)->field("id")->find();
            if ($poolInfo) {
                // 存在将处理
                Db::table("xg_video_pool")->where("id", "=", $poolInfo["id"])->update(["status" => 0]);
                // 视频热度表
                Db::table("xg_video_heat")->where("video_id", "=", $id)->update(["status" => 0]);
            }

            Db::commit();
            $this->addlog("下架用户短视频", "上传表ID：" . $id);
            json_success_admin();
        } catch (\Exception $e) {
            Db::rollback();
            json_fail_admin("下架失败：" . $e->getMessage());
        }
    }

    /**
     * 推荐等级维护
     */
    public function recommend_level() {
        // 获取当前值
        $temp = Db::table("xg_evaluate_class")
                    ->field("id,video_class name,referrals_num num")
                    ->where("status", "=", 1)
                    ->select();
        // 整理成以ID为KEY
        $initId = 0;
        foreach ($temp as $item) {
            if ($item["id"] > $initId) {
                $initId = $item["id"];
            }
            $levelData[$item["id"]] = [
                "name" => $item["name"],
                "num"  => $item["num"],
            ];
        }
        if (request()->isPost()) {
            // 数据提交
            $data = input("post.data", [], "trim");
            if (empty($data)) {
                json_fail_admin("参数丢失，请重试！");
            }
            $insert = [];
            $update = [];
            foreach ($data as $item) {
                if ($item["num"] == 999) {
                    json_fail_admin("999为保留等级人数，不可输入，请更换！");
                }
                if (empty($item["id"])) {
                    $initId ++;
                    $insert[] = [
                        "id"            => $initId,
                        "video_class"   => $item["name"],
                        "referrals_num" => $item["num"]
                    ];
                } else {
                    if (! key_exists($item["id"], $levelData)) {
                        json_fail_admin("数据有误，请确认！");
                    }
                    $dbItem = $levelData[$item["id"]];
                    if ($dbItem["name"] != $item["name"] || $dbItem["num"] != $item["num"]) {
                        $update[] = [
                            "id" => $item["id"],
                            "data" => [
                                "video_class"   => $item["name"],
                                "referrals_num" => $item["num"]
                            ]
                        ];
                    }
                }
            }
            if (empty($insert) && empty($update)) {
                json_success_admin("保存成功");
            }
            // 修改数据库
            Db::startTrans();
            try {
                $result = true;
                if ($insert) {
                    if (! Db::table("xg_evaluate_class")->insertAll($insert)) {
                        $result = false;
                    }
                }
                if ($update) {
                    foreach ($update as $up) {
                        if (! Db::table("xg_evaluate_class")->where("id", "=", $up["id"])->update($up["data"])) {
                            $result = false;
                        }
                    }
                }
                if (! $result) {
                    Db::rollback();
                    json_fail_admin("保存失败");
                }
                Db::commit();
                json_success_admin("保存成功");
            } catch (\Exception $e) {
                Db::rollback();
                json_fail_admin("保存失败：" . $e->getMessage());
            }

        }
        // 显示页面
        $this->assign([
            "data"  => $temp,
            "count" => count($temp) + 1
        ]);
        return view();
    }

    /**
     * 获取官方账户信息，没有将自动创建
     * @return array
     */
    public function _getOfficialUser($account='km_official',$avatar='') {

        // 查询是否存在
        $where = [
//            "status"    => 1,
            'account' =>$account
        ];
        $userInfo = Db::name("member_info")
        ->alias("a")
        ->where($where)
        ->field("id,nick_name")
        ->find();
        if (empty($userInfo)) {

            if($account=='km_official'){
                $avatar='https://szdhLh.com/images/kumi/2022/03/04/3d92618077ea43a2a88906639aad1410.ced';
            }else{
                if(empty($avatar)){
                    $avatar = Db::name('member_default_avatar')->orderRaw("RAND()")->limit(1)->cache(10)->find();
                    if ($avatar) {
                        $avatar = $avatar['img'];
                    }
                }
            }

            // 没有官方账号将进行创建
            $kmdata['account'] = $account;
            $kmdata['nick_name'] = $account=='km_official'?'陌陌官方':$account;
            $kmdata['password'] = md5(uniqid());
            $kmdata['device_id'] = md5(uniqid());
            $kmdata['avatar'] = $avatar;
            $kmdata['level_id'] = 9;
            $kmdata['day_view_times'] = 0;
            $kmdata['today_view_times'] = 0;
            $kmdata['re_today_view_times'] = 0;
            $kmdata['day_cache_times'] = 0;
            $kmdata['today_cache_times'] = 0;
            $kmdata['re_today_cache_times'] = 0;
            $kmdata['expire_time'] = 0;
            $kmdata['is_visitor'] = 2;
            $kmdata['token'] = create_token();
            $kmdata['create_ip'] = get_client_ip();
            $kmdata['create_addr'] = get_addr($kmdata['create_ip']);
            $kmdata['create_time'] = time();
//            $kmdata['mobile'] = '';
            $kmdata['is_bot'] = 1;


            $id = Db::name("member_info")->insertGetId($kmdata);
            if (! $id) {
                $this->error = "插入用户信息失败！";
                return [];
            }
            $result = Db::connect('short_video')->table("xg_user_property")->insert([
                "user_id" => $id,
                "user_type" => 2,
                "is_update" => 1,
                "is_comment" => 1
            ]);
            if (! $result) {
                $this->error = "插入用户属性失败！";
                return [];
            }
            $userInfo = [
                "id" => $id,
                "nick_name" => $kmdata["nick_name"]
            ];
        }elseif ($account!='km_official'){
            json_success_admin("该账号所属用户已存在");
        }

        // 返回数据
        return $userInfo;
    }



    /**
     * 新增数据
     * @param array $saveData 需插入数据
     * @param boolean $insertUpload 是否插入上传
     * @throws \Exception
     * @return boolean
     */
    private function _insertVideoData($saveData, $insertUpload = false) {
        Db::startTrans();
        try {
//            throw new \Exception("插入上传表失败！".json_encode($saveData));
            if ($insertUpload == true) {
                // 插入视频上传表
                $keys = ["name", "url_path", "video_time", "user_id", "nick_name", "video_type", "status"];
                $data = $this->_getDataByKey($saveData, $keys);
                if (! $data) {
                    throw new \Exception($this->error);
                }
                if ($data["status"] == 0) {
                    // 下架仍然视为正常
                    $data["status"] = 1;
                }
                if (key_exists("address", $saveData)) {
                    $data["address"] = $saveData["address"];
                }
                $saveData["video_id"] = Db::connect('short_video')->table("xg_video_upload_information")->insertGetId($data);
                if (! $saveData["video_id"]) {
                    throw new \Exception("插入上传表失败！");
                }
            }
            // 视频池
            $keys = ["user_id", "nick_name", "video_id", "video_name", "origin_video_time","video_time", "video_type", "status",'sort'];
            $data = $this->_getDataByKey($saveData, $keys);
            if (! $data) {
                throw new \Exception($this->error);
            }
            $keys = ["address", "video_class_id", "video_img", "video_path", "referrals_phases"];
            $othersData = $this->_getDataByKey($saveData, $keys, false);
            $data = array_merge($data, $othersData);

            $videoType = $data["video_type"];
            if ($videoType == 2) {
                // 官方账户固定值
                $data["referrals_phases"] = 999;
                $data["video_class_id"] = 999;
            }
            // 插入值
            $result = Db::connect('short_video')->table("xg_video_pool")->insert($data);
            if (! $result) {
                throw new \Exception("新增视频池失败！");
            }
            // 视频热度
            if (! key_exists("video_id", $saveData)) {
                throw new \Exception("缺少 video_id 字段！");
            }
            $data = [
                "video_id"         => $saveData["video_id"],
                "like"             => 0,
                "comment"          => 0,
                "video_play_count" => 0
            ];
            if ($videoType == 2) {
                // 官网视频热度值为1000
                $data["heats"] = 1000;
            }
            // 切片中状态为下架
            if ($saveData["status"] == 3) {
                $data["status"] = 0;
            } else {
                $data["status"] = 1;
            }
            // 插入值
            $result = Db::connect('short_video')->table("xg_video_heat")->insert($data);
            if (! $result) {
                throw new \Exception("新增视频热度表失败！");
            }
            // 视频分享
            $keys = ["share_desc", "share_describe", "video_id"];
            $data = $this->_getDataByKey($saveData, $keys);
            if (! $data) {
                throw new \Exception($this->error);
            }
            $data["share_num"] = 0;
            $result = Db::connect('short_video')->table("xg_share_video")->insert($data);
            if (! $result) {
                throw new \Exception("新增视频热度表失败！");
            }
            Db::commit();
            return $saveData["video_id"];
        } catch (\Exception $e) {
            Db::rollback();
            $this->error = $e->getMessage();
            return false;
        }
    }

    /**
     * 根据指定key返回数据
     * @param array $checkData          被检查数据
     * @param array $keys               检查key
     * @param boolean $must             是否必须
     * @param boolean $notMustIsReturn  非必须时是否返回
     * @param string|number|null $notMustReturnDefault 非必须时必须返回值
     * @return array
     */
    private function _getDataByKey($checkData, $keys, $must = true, $notMustIsReturn = false, $notMustReturnDefault = null) {
        $data = [];
        foreach ($keys as $key) {
            if (! key_exists($key, $checkData)) {
                if ($must) {
                    // 必须
                    $this->error = "缺少" . $key. "数据！";
                    return [];
                } else {
                    // 可选
                    if ($notMustIsReturn == true) {
                        $data[$key] = $notMustReturnDefault;
                    }
                }
            } else {
                $data[$key] = $checkData[$key];
            }
        }
        return $data;
    }

    /**
     * 修改视频信息
     * @param array $saveData 修改的数据
     * @param number $videoId 视频ID
     * @return boolean
     */
    private function _saveVideoData($saveData, $videoId) {
        Db::startTrans();
        try {
            // 视频上传表
            $data = [];
            if (key_exists("name", $saveData)) {
                $data["name"] = $saveData["name"];
            }
            if (key_exists("user_id", $saveData)) {
                $data["user_id"] = $saveData["user_id"];
            }
            if (key_exists("nick_name", $saveData)) {
                $data["nick_name"] = $saveData["nick_name"];
            }

            if (key_exists("status", $saveData)) {
                if ($saveData["status"] == 2) {
                    // 删除
                    $data["status"] = 4;
                }
            }
            if ($data) {
                Db::connect('short_video')->table("xg_video_upload_information")->where("id", $videoId)->update($data);
            }
            // 视频池
            $keys = ["tag_ids", "video_name", "status", "video_img",'user_id','nick_name','sort'];
            $data = $this->_getDataByKey($saveData, $keys, false);
            if ($data) {
                Db::connect('short_video')->table("xg_video_pool")->where("video_id", $videoId)->update($data);
            }
            // 视频热度表
            $keys = ["like", "comment", "video_play_count", "status"];
            if(!empty($saveData['like_num']))$saveData['like']=$saveData['like_num'];
//            dump($saveData);die();
            $data = $this->_getDataByKey($saveData, $keys, false);
            if (key_exists("status", $data)) {
                if ($data["status"] == 2) {
                    // 删除
                    $data["status"] = 0;
                }
            }
            if ($data) {
                Db::connect('short_video')->table("xg_video_heat")->where("video_id", $videoId)->update($data);
            }

            // 视频分享表
            $keys = ["share_desc", "share_describe", "share_num"];
            $data = $this->_getDataByKey($saveData, $keys, false);
            if ($data) {
                Db::connect('short_video')->table("xg_share_video")->where("video_id", $videoId)->update($data);
            }
            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            $this->error = $e->getMessage();
            return false;
        }
    }

    /**
     * 短视频上传规则配置
     */
    public function upload_policy() {
        // 查询数据
        $data  = Db::connect('short_video')->table("xg_update_confing")->find();
        $isAdd = false;
        if (empty($data)) {
            // 没有值将需要新增
            $data = [
                "video_size_min"  => 1,
                "video_size_max"  => 500,
                "video_title_min" => 4,
                "video_title_max" => 20,
                "video_time_min"  => 10,
                "video_time_max"  => 600,
                "status"          => 1
            ];
            $isAdd = true;
        }
        // 定义标签对应名称
        $label = [
            "video_size_min"  => "文件最小限制（MB）",
            "video_size_max"  => "文件最大限制（MB）",
            "video_title_min" => "短视频名称最小长度",
            "video_title_max" => "短视频名称最大长度",
            "video_time_min"  => "短视频最小时长（秒）",
            "video_time_max"  => "短视频最大时长（秒）"
        ];

        // 数据提交
        if (request()->isPost()) {
            $post = input("post.");
            // 检查数据
            $save = [];
            foreach ($label as $key => $value) {
                $post[$key] = trim($post[$key]);
                if (! key_exists($key, $post)) {
                    json_fail_admin("缺少：“" . $value . "”");
                }
                // 不能为零
                if (! $post[$key] || ! is_numeric($post[$key]) || strpos($post[$key], ".") !== false) {
                    json_fail_admin("“". $value . "”请填写不为零的整数！");
                }
                $save[$key] = $post[$key];
            }
            if (! key_exists("status", $post)) {
                json_fail_admin("缺少：“用户上传功能”");
            }
            // 不能为零
            if ($post["status"] != 1 && $post["status"] != 0) {
                json_fail_admin("“用户上传功能”错误！");
            }
            $save["status"] = $post["status"];
            // 修改或新增数据
            if ($isAdd) {
                Db::connect('short_video')->table("xg_update_confing")->insert($save);
            } else {
                Db::connect('short_video')->table("xg_update_confing")->where("id", $data["id"])->update($save);
            }
            json_success_admin();
        }


        // 显示数据
        $showData = [];
        foreach ($label as $key => $value) {
            $showData[] = [
                "label"  => $value,
                "name"   => $key,
                "value"  => $data[$key]
            ];
        }

        // 显示页面
        $this->assign([
            "config" => $showData,
            "status" => $data["status"]
        ]);
        return view();
    }
}
