<?php
namespace app\admin\controller;

use think\Db;
/**
 * 短视频评论
 *
 */
class VideoClipComment extends BaseController
{
    private $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "video_clip_comment";
        $this->title = "短视频评论记录";
        $this->current_url = "/admin/video_clip_comment";
    }
    private function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    /**
     * 评论列表
     * @return \think\response\View
     */
    public function index() {
        // 设置查询信息
        $field  = "a.id,a.nick_name,a.user_id,a.content,a.create_time,";
        $field .= "b.video_id,b.video_name";
        $where  = [
            ["b.status", "<>", 2],
            ["a.status", "=", 1]
        ];
        // 是否有名称搜索
        $title  = input("request.title", "", "trim");
        $content = input("request.content", "", "trim");
        $account = input("request.account", "", "trim");
        if ($title) {
            $where[] = ["b.video_name", "like", "%" . $title . "%"];
        }
        if (!empty($content)) {
            $where[] = ["a.content", "like", "%" . $content . "%"];
        }

        if(!empty($account)){
            $search_account_detail = Db::name('member_info')->where(['account'=>$account])->field('id')->find();
            if(!empty($search_account_detail['id'])){
                $where[] = ["a.user_id", "=", $search_account_detail['id']];
            }
        }

        // 查询数据
        $list   = Db::connect('short_video')->table("xg_video_comment")
                      ->alias("a")
                      ->leftJoin(["xg_video_pool" => "b"], "a.video_id=b.video_id")
                      ->field($field)
                      ->where($where)
                      ->order("a.id desc")
                      ->paginate(20);
        $page = $list->render();
        $list = $list->toArray();

        if(!empty($list['data'])){
            $user_ids = array_column($list['data'],'user_id');
            $member_info_list = Db::name('member_info')->whereIn('id',$user_ids)->field('id,account')->select();
            $member_info_column = array_column($member_info_list,null,'id');

            foreach ($list['data'] as $k=>&$v){
                $v['account'] = $member_info_column[$v['user_id']]['account']??'';
            }
        }

        $this->assign([
            'list' => $list,
            'page' => $page,
            'condition' => ["title" => $title,'content'=>$content,'account'=>$account]
        ]);
        $this->_assign();
        return view();
    }

    /**
     * 单个视频评论列表
     * @param number $id
     * @return \think\response\View
     */
    public function video($id) {
        // 设置查询信息
        $field  = "a.id,a.nick_name,a.user_id,a.content,a.create_time";
        $where  = [
            ["a.video_id", "=", $id],
            ["b.status", "<>", 2],
            ["a.status", "=", 1]
        ];
        // 查询数据
        $list   = Db::connect('short_video')->table("xg_video_comment")
                      ->alias("a")
                      ->leftJoin(["xg_video_pool" => "b"], "a.video_id=b.video_id")
                      ->field($field)
                      ->where($where)
                      ->order("a.id desc")
                      ->paginate(20);
        $this->assign([
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }

    /**
     * 删除评论
     */
    public function del()
    {
        $id  = input('id');
        $ids = explode(",",$id);

        // 获取评论对应的视频ID
        $videoIds = Db::connect('short_video')->table("xg_video_comment")->where("id", "in", $ids)->column("video_id");
        if (empty($videoIds)) {
            // 没有视频不操作
            json_success("删除成功");
        }
        Db::startTrans();
        try {
            // 遍历扣减评论次数，有可能多个评论指向同一个视频
            foreach ($videoIds as $vId) {
                Db::connect('short_video')->table("xg_video_heat")->where("video_id", $vId)->setDec("comment");
            }
            Db::connect('short_video')->table("xg_video_comment")->where("id", "in", $ids)->update(["status" => 0]);
            Db::commit();
            $this->addlog('删除短视频评论','视频ID:'.$id);
            json_success_admin("删除成功");
        } catch (\Exception $e) {
            Db::rollback();
            json_fail_admin("删除失败：" . $e->getMessage());
        }
    }
}

