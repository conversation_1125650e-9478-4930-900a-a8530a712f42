<?php
namespace app\admin\controller;

use think\Db;
/**
 * 短视频点赞
 *
 */
class VideoClipLike extends BaseController
{
    private $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "video_clip_like";
        $this->title = "短视频点赞记录";
        $this->current_url = "/admin/video_clip_like";
    }
    private function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }
    
    /**
     * 评论列表
     * @return \think\response\View
     */
    public function index() {
        // 设置查询信息
        $field  = "a.id,a.nick_name,a.user_id,a.status,a.create_time,";
        $field .= "a.video_id,b.video_name";
        $where  = [
            ["b.status", "<>", 2]
        ];
        // 是否有名称搜索
        $title = input('request.title', '', 'trim');
        if ($title) {
            $where[] = ["b.video_name", "like", "%" . $title . "%"];
        }
        // 查询
        $list   = Db::connect('short_video')->table("xg_video_like")
                      ->alias("a")
                      ->leftJoin(["xg_video_pool" => "b"], "a.video_id=b.video_id")
                      ->field($field)
                      ->where($where)
                      ->order("a.id desc")
                      ->paginate(20);
        $this->assign([
            'list' => $list,
            'condition' => ["title" => $title]
        ]);
        $this->_assign();
        return view();
    }
    
    /**
     * 单个视频点赞记录
     * @param number $id
     * @return \think\response\View
     */
    public function video($id) {
        // 设置查询信息
        $field  = "a.id,a.nick_name,a.user_id,a.status,a.create_time";
        $where  = [
            ["b.status", "<>", 2],
            ["a.video_id", "=", $id]
        ];
        // 查询
        $list   = Db::connect('short_video')->table("xg_video_like")
                      ->alias("a")
                      ->leftJoin(["xg_video_pool" => "b"], "a.video_id=b.video_id")
                      ->field($field)
                      ->where($where)
                      ->order("a.id desc")
                      ->paginate(20);
        $this->assign([
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }
}

