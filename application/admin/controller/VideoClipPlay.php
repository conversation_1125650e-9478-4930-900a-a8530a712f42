<?php
namespace app\admin\controller;

use think\Db;
/**
 * 短视频播放
 *
 */
class VideoClipPlay extends BaseController
{
    private $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "video_clip_play";
        $this->title = "短视频播放记录";
        $this->current_url = "/admin/video_clip_play";
    }
    private function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }
    
    /**
     * 播放列表
     * @return \think\response\View
     */
    public function index() {
        $title = trim(input('request.title'));
        // 设置查询条件
        $where = [
            ["b.status", "<>", 2]
        ];
        if ($title) {
            $where[] = ["b.video_name", "like", "%" . $title . "%"];
        }
        // 查询
        // 客户端IP及地址暂定，先用null代替
        $field = "a.id,a.user_id,b.video_id,a.nick_name,a.create_time,b.video_name";
        $list  = Db::connect('short_video')->table("xg_video_record")
                     ->alias("a")
                     ->leftJoin(["xg_video_pool" => "b"], "a.video_id=b.video_id")
                     ->field($field)
                     ->where($where)
                     ->order("a.id desc")
                     ->paginate(20);
        $this->assign([
            'list' => $list,
            'condition' => ['title' => $title]
        ]);
       
        $this->_assign();
        return view();
    }
    /**
     * 单个视频播放记录
     * @param number $id
     * @return \think\response\View
     */
    public function video($id) {
        // 客户端IP及地址暂定，先用null代替
        $field = "a.id,a.user_id,a.nick_name,a.create_time";
        // 已删除短视频不显示
        $where = [
            ["a.video_id", "=", $id],
            ["b.status", "<>", 2]
        ];
        $list  = Db::connect('short_video')->table("xg_video_record")
                     ->alias("a")
                     ->leftJoin(["xg_video_pool" => "b"], "a.video_id=b.video_id")
                     ->field($field)
                     ->where($where)
                     ->order("a.id desc")
                     ->paginate(20);
        $this->assign([
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }
}

