<?php
namespace app\admin\controller;

use think\Db;

/**
 * 短视频分享
 *
 */
class VideoClipShare extends BaseController
{
    private $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "video_clip_share";
        $this->title = "短视频分享记录";
        $this->current_url = "/admin/video_clip_share";
    }
    private function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }
    
    /**
     * 分享记录
     * @return \think\response\View
     */
    public function index() {
        // 设置查询信息
        $field  = "a.id,b.video_name,c.share_desc,c.share_describe,a.create_time,";
        $field .= "a.user_id,a.nick_name,b.video_id";
        $where  = [
            ["b.status", "<>", 2]
        ];
        // 是否有搜索查询
        $title  = input("request.title", "", "trim");
        if ($title) {
            $where[] = ["b.video_name", "like", "%" . $title . "%"];
        }
        // 查询
        $list   = Db::connect('short_video')->table("xg_share_video_list")
                      ->alias("a")
                      ->leftJoin(["xg_video_pool" => "b"], "a.video_id=b.video_id")
                      ->leftJoin(["xg_share_video" => "c"], "a.video_id=c.video_id")
                      ->field($field)
                      ->where($where)
                      ->order("a.id desc")
                      ->paginate(20);
        $this->assign([
            'list'      => $list,
            'condition' => ["title" => $title]
        ]);
        $this->_assign();
        return view();
    }
    
    /**
     * 单个视频分享记录
     * @param number $id
     * @return \think\response\View
     */
    public function video($id) {
        // 设置查询信息
        $field  = "a.id,b.video_name,c.share_desc,c.share_describe,a.create_time,";
        $field .= "a.user_id,a.nick_name";
        $where  = [
            ["a.video_id", "=", $id],
            ["b.status", "<>", 2]
        ];
        $list   = Db::table("xg_share_video_list")
                      ->alias("a")
                      ->leftJoin(["xg_video_pool" => "b"], "a.video_id=b.video_id")
                      ->leftJoin(["xg_share_video" => "c"], "a.video_id=c.video_id")
                      ->field($field)
                      ->where($where)
                      ->order("a.id desc")
                      ->paginate(20);
        // 显示页面
        $this->assign([
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }
}

