<?php
namespace app\admin\controller;

use think\Db;
class VideosStatistics extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '单视频排行榜',
            'name'=>'videos_statistics',
            'current_url'=>'/admin/videos_statistics',
        ];
        parent::__construct($conf);
    }

    //充值赠送活动列表
    public function index()
    {
        $order_list = [
            ['key'=>'pay_amount','value'=>'销售总额'],
            ['key'=>'pay_members','value'=>'销售总人数'],
            ['key'=>'history_members','value'=>'播放次数'],
            ['key'=>'member_movie_up_down','value'=>'点赞次数'],
            ['key'=>'member_comment','value'=>'评论次数'],
            ['key'=>'member_folder','value'=>'收藏次数'],
        ];
        $where = [];
        $condition['id'] = input('request.id');
        $condition['title'] = input('request.title');
        $condition['star_ids'] = input('request.star_ids');
        $condition['product_id'] = input('request.product_id');
        $condition['day'] = input('request.day') ?? 1;
        $condition['order_value'] = input('order_value');
        if (!empty($condition['id'])) $where[] = ['m.id', '=', $condition['id']];
        if (!empty($condition['title'])) $where[] = ['m.title', '=', $condition['title']];
        if (!empty($condition['star_ids'])) $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$condition['star_ids']}',star_ids)")];
        if (!empty($condition['product_id'])) $where[] = ['m.product_id', '=', $condition['product_id']];
        //if (!empty($condition['day'])) $where[] = ['s.time', '>=', date("Y-m-d", strtotime("-".$condition['day']." days"))];
        $app_star = Db::name('app_star')->cache(true,3600)->field('id,uname title')->order('pinyin asc')->select();
        $app_theme = Db::name('app_theme')->field('id,title')->order('pinyin asc')->select();
        $app_products = Db::name("app_products")->where('status',1)->field('id,title')->order('id ASC')->select();
        $on_str = "m.id = s.movie_id";
        if (!empty($condition['day'])){
            $on_str .= " and s.time >= '".date("Y-m-d", strtotime("-".$condition['day']." days"))."'";
        }
        if (!empty($condition['order_value'])){
            $order = $condition['order_value'].' desc';
        }else{
            $order = 'm.id desc';
        }

        if(!empty(input('request.day'))){
            $list = Db::name("app_movie")->alias('m')->cache(true,3600)
                ->field('m.id,m.title,m.star_ids,m.product_id,m.create_time,sum(pay_amount) as pay_amount,sum(pay_members) as pay_members,sum(history_members) as history_members,sum(member_movie_up_down) as member_movie_up_down,sum(member_comment) as member_comment,sum(member_folder) as member_folder')
                ->leftJoin('movie_statistics s',$on_str)
                ->group('m.id')
                ->where($where)->order($order)->paginate(20)->each(function($item,$key){
                    if($item['pay_amount'] != 0 && $item['pay_members'] != 0){
                        $item['avg_price'] = round($item['pay_amount']/$item['pay_members'],2);
                    }else{
                        $item['avg_price'] = '0';
                    }
                    return $item;
                });
            //echo Db::name("app_movie")->getLastSql();
            $page = $list->render();
        }


        $this->_assign();
        $this->assign([
            'list' => $list ?? [],
            'condition' => $condition,
            'page' => $page ?? [],
            'app_star' => $app_star,
            'app_theme' => $app_theme,
            'app_products' =>$app_products,
            'order_list' => $order_list,
        ]);

        return view();
    }

    public function consumption(){
        $where = [];
        $condition['is_super'] = input('request.is_super');
        $condition['name'] = input('request.name');
        $condition['day'] = input('request.day') ?? 1;
        $on_str = "v.id=m.vip_id";
        if (!empty($condition['day'])){
            $on_str .= " and m.create_time >= ".strtotime("-".$condition['day']." days");
        }
        if ($condition['is_super'] === "1") $where[] = ['v.is_super', '=', $condition['is_super']];
        if ($condition['is_super'] === "2") $where[] = ['v.is_super', '=', 0];
        if (!empty($condition['name'])) $where[] = ['v.name', '=', $condition['name']];
        //echo date("Y-m-d", strtotime("-1".$condition['day']." days"));
        if(!empty(input('request.day'))){
            $list = Db::name("vip")->alias('v')->cache(true,3600)
                ->field('v.id,v.name,v.is_super,v.price,v.original_price,v.date,count(member_id) as member_num,sum(amount) as amount_num')
                ->leftJoin('member_vip m',$on_str)
                ->group('v.id')
                ->where($where)->paginate(20);
            $page = $list->render();
        }
        //echo Db::name("vip")->getLastSql();
        $this->_assign();
        $this->assign([
            'title' => 'vip套餐销量',
            'list' => $list ?? [],
            'condition' => $condition,
            'page' => $page ?? [],
            'list' => $list ?? [],
            'current_url'=>'/admin/videos_statistics/consumption',
        ]);
        return view();
    }

    public function member(){
        $where = [];
        $condition['id'] = input('request.id');
        $condition['type'] = input('request.type');
        $condition['day'] = input('request.day') ?? 1;

        if (!empty($condition['id'])) $where[] = ['member_id', '=', $condition['id']];
        if($condition['type'] == 1){
            if (!empty($condition['day'])) $where[] = ['addtime', '>=',strtotime("-".$condition['day']." days")];
            //echo date("Y-m-d", strtotime("-1".$condition['day']." days"));
            if(!empty(input('request.day'))){
                $list = Db::name("app_pay_movie")->cache(true,3600)
                    ->field('member_id,sum(amount) as amount_num,count(id) as videos_num')
                    ->group('member_id')
                    ->order('amount_num desc')
                    ->where($where)->paginate(20);
                $page = $list->render();
                $data = $list->toArray();
                foreach($data['data']as $key=>$value){
                        $data['data'][$key]['username'] = Db::name("member_info")->where("id",$value['member_id'])->value("account");
                }
            }
        }

        if($condition['type'] == 2){
            if (!empty($condition['day'])) $where[] = ['create_time', '>=',strtotime("-".$condition['day']." days")];
            if(!empty(input('request.day'))){
                $list = Db::name("member_vip")->cache(true,3600)
                ->field('member_id,sum(amount) as amount_num,count(id) as videos_num')
                    ->group('member_id')
                    ->order('amount_num desc')
                    ->where($where)->paginate(20);
                $page = $list->render();
                $data = $list->toArray();
                foreach($data['data']as $key=>$value){
                    $data['data'][$key]['username'] = Db::name("member_info")->where("id",$value['member_id'])->value("account");
                }
            }
        }

        $this->_assign();
        $this->assign([
            'title' => '付费用户排行',
            'list' => $data['data'] ?? [],
            'condition' => $condition,
            'page' => $page ?? [],
            'current_url'=>'/admin/videos_statistics/member',
        ]);
        return view();
    }

}
