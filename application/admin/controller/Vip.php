<?php
namespace app\admin\controller;

use app\Service\MemberInfoExtraService;
use app\Service\VipService;
use think\Db;
use think\facade\Validate;

class Vip extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "vip",
            'title' => "VIP购买配置",
            'current_url' => "/admin/vip",
        ];
        $this->where = [
            'title' => 'like',
            'day' => '',
            'status' => '',
        ];
//        $this->class_list = [1=>'月卡',2=>'季卡','3'=>'年卡','6' => '荣耀季卡'];
        $this->class_list = [];
        $this->order = 'id asc';
        parent::__construct($conf);
    }
    public function index()
    {
        $list = Db::name($this->name)->order('status desc,show_condition desc,sort desc,id asc')->paginate(20)->each(function ($item,$res){
            $item['update_user'] = Db::name("admin_info")->where('id',$item['update_user'])->value('username');
            $item['create_user'] = Db::name("admin_info")->where('id',$item['create_user'])->value('username');
//            $item['pre'] = Db::name($this->name)->where('type',$item['pre_vip'])->cache(29)->value('name');
            $item['show_condition_name'] = VipService::getConstPluck('SHOW_CONDITION_')[$item['show_condition']]??'';
            $item['hide_condition_name'] = VipService::getConstPluck('HIDE_CONDITION_')[$item['hide_condition']]??'';
            $ai_count_list = json_decode($item['ai_count_list'],true)??[];
            $item['ai_date'] =$ai_count_list['ai_date']??0;
            return $item;
        });
        $this->assign([
            'class_list'=>$this->class_list,
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }

    public function edit1()
    {
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {
            $post = input('post.');

            if (!empty($this->verify)) {
                foreach ($this->verify as $k => $v) {
                    if(is_array($v))
                    {
                        $res = preg_match($v['rule'],$post[$k]);
                        if(!$res) json_fail_admin($v['message']);
                    }else{
                        if (empty($post[$k])) json_fail_admin($v);
                    }
                }
            }
            $post['update_time'] = time();
            $post['update_user'] = $this->user['id'];
            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);

            // Db::name("vip_log")->insert($post);

            redis()->del('vip_price');
            $this->addlog('修改VIP配置',json_encode($post));

            $res ? json_success_admin() : json_fail_admin();
        } else {
            $arr = [];
            $role = config("role.{$this->name}");
            if ((strtolower(request()->controller()) == 'appcategory') && input('param.category_type') != 'movie') {
                unset($role['icon']);
                unset($role['is_pc_top']);
                unset($role['is_pc_middle']);
            }
            if (!empty($role)) {
                foreach ($role as $k => $v) {
                    if (isset($v['type']) && $v['type'] == 'class_list') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => '分类',
                                'value' => $this->class_list,
                            ],
                        ];
                    } elseif (isset($v['type']) && $v['type'] == 'class_list2') {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => [
                                'title' => $v['title'],
                                'value' => $this->class_list2,
                            ],
                        ];
                    } else {
                        $arr[$k] = [
                            'value' => $res[$k],
                            'role' => $v,
                        ];
                    }
                }
            }
            $this->assign([
                'res' => $arr,
                'class_list'=>$this->class_list,
                'is_parent' => $this->is_parent,
            ]);
            return view();
        }
    }
    public function event()
    {
        $list = Db::name('vip_event')->paginate(20);
        $this->assign([
            'class_list'=>$this->class_list,
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }
    //添加活动
    public function event_add()
    {

        if (request()->isPost()) {
            $data = input('post.');
            if (!$data['title']) {
                json_fail_admin('标题不能为空!');
            }
            if (!$data['content']) {
                json_fail_admin('内容不能为空!');
            }
            if (!check_number($data['amount']) || $data['amount'] <= 0) {
                json_fail_admin('赠送金额需为正整数');
            }

            $insert = [
                'title' => $data['title'],
                'amount' => $data['amount'],
                'content' => $data['content'],
                'status' => $data['status'],
                'type' => $data['type']
            ];
            $re = Db::name('vip_event')->where('type',$data['type'])->find();
            if ($re) {
                json_fail_admin('该类型首充活动已存在!');
            }
            $re = Db::name('vip_event')->insert($insert);
            if (!$re) {
                json_fail_admin('添加失败,请重试!');
            }
            $this->addlog('添加VIP首充活动', $insert['title']);
            redis()->del('vip_event_info');
            json_success_admin('添加成功!');

        }else{

            $this->assign([
                'class_list'=>$this->class_list,
            ]);
            return view();

        }

    }
    //修改活动

    public function event_edit()
    {
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name("vip_event")->where('id', '=', $id)->find();

        if(empty($res)) json_fail_admin('数据不存在!');

        if (request()->isPost()) {
            $post = input('post.');
            if ($post['amount'] <= 0) {
               json_fail_admin('金额需大于0!');
            }
            $post['updatetime'] = time();
            $res = Db::name("vip_event")->where('id', '=', $id)->strict(false)->update($post);

            $this->addlog('修改VIP首冲活动','');
            redis()->del('vip_event_info');

            $res ? json_success_admin() : json_fail_admin();
        } else {

            $this->assign([
                'res' => $res,
                'class_list'=>$this->class_list,
            ]);
            return view();
        }
    }
    public function event_status($v)
    {
        sysconf('vip_first_event',$v);
        $info = '关闭';
        if ($v == 1) {
           $info = '开启';
        }
        redis()->del('vip_event_info');

        $this->addlog('VIP首冲活动', $info);
        json_success_admin("{$info}成功!");
    }

    //删除活动
    public function event_del($id)
    {
        $res = Db::name('vip_event')->delete($id);
        $this->addlog('删除数据','表名: vip_event 数据ID: '.$id);
        redis()->del('vip_event_info');

        $res ? json_success_admin() : json_fail_admin();
    }

    //vip 首充活动报表
    public function event_report()
    {
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['member_name'] = input('request.member_name');

        $where = [];
        if (!empty($condition['begin_time'])) $where[] = ['addtime', '>=', strtotime($condition['begin_time'] . '00:00:00')];
        if (!empty($condition['end_time'])) $where[] = ['addtime', '<', strtotime($condition['end_time'] . '23:59:59')];
        if (!empty($condition['member_name'])) $where[] = [ 'member_name','=' ,$condition['member_name']];


        $list = Db::name('vip_event_report')->where($where)->paginate(20);
        $count = Db::name('vip_event_report')->where($where)->sum('amount');
        $count1_s = Db::name('vip_event_report')->where($where)->where('type',1)->sum('amount');
        $count1_c = Db::name('vip_event_report')->where($where)->where('type',1)->count();
        $count2_s = Db::name('vip_event_report')->where($where)->where('type',2)->sum('amount');
        $count2_c = Db::name('vip_event_report')->where($where)->where('type',2)->count();
        $count3_s = Db::name('vip_event_report')->where($where)->where('type',3)->sum('amount');
        $count3_c = Db::name('vip_event_report')->where($where)->where('type',3)->count();
        $this->assign([
            'class_list'=>$this->class_list,
            'list' => $list,
            'condition' => $condition,
            'count' => $count,
            'count1_s' => $count1_s,
            'count1_c' => $count1_c,
            'count2_s' => $count2_s,
            'count2_c' => $count2_c,
            'count3_s' => $count3_s,
            'count3_c' => $count3_c,
        ]);
        $this->_assign();
        return view();
    }


   public function set_expire()
   {
        if ($this->request->isGet()) {
            return view('set_expire');
        }else{
            $phone = input("post.phone");
            $day = input("post.day");
            $svip_day = input("post.svip_day");
            $ai_day = input("post.ai_day");
            $ai_type_remove_clothes = input("post.ai_type_remove_clothes");
            $ai_type_change_face = input("post.ai_type_change_face");
            $ai_type_movie = input("post.ai_type_movie");
            $ai_type_diy_change_face = input("post.ai_type_diy_change_face");
            $ai_type_draw_count = input("post.ai_type_draw_count");

//            if( empty($day) && empty($svip_day) ){
//                json_fail_admin('赠送vip至少填一个!');
//            }

            $inputs = $post = input("post.");

            $v = Validate::make([
                'phone' => 'require',
                'day' => 'number',
                'svip_day' => 'number',
                'ai_day' => 'number',
                'ai_type_remove_clothes' => 'number',
                'ai_type_change_face' => 'number',
                'ai_type_movie' => 'number',
                'ai_type_diy_change_face' => 'number',
                'ai_type_draw_count' => 'number',
            ], [
                'title.phone' => '用户名或手机号码必填',
                'day.number' => '赠送VIP天数必须是正整数',
                'svip_day.number' => '赠送超级VIP天数必须是正整数',
                'ai_day.number' => '赠送ai会员天数必须是正整数',
                'ai_type_remove_clothes.number' => '图片去衣次数必须是正整数',
                'ai_type_change_face.number' => '图片换脸次数必须是正整数',
                'ai_type_movie.number' => '视频换脸次数必须是正整数',
                'ai_type_diy_change_face.number' => '自定义换脸次数必须是正整数',
                'ai_type_draw_count.number' => 'AI绘画次数必须是正整数',
            ]);

            if (!$v->check($inputs)) {
                json_fail_admin($v->getError());
            }

            $minfo = Db::name('member_info')->where('mobile',$phone)->whereOr('account',$phone)->field('id,account,expire_time,super_vip_expire_time')->find();
            if (!$minfo) {
               json_fail_admin('未找到用户记录!');
            }
            $new  = $old =' 无 ';
            if ($minfo['expire_time']) {
                $new =$old = date('Y-m-d H:i:s',$minfo['expire_time']);
            }

            $svip_new = $svip_old = ' 无 ';
            if ($minfo['super_vip_expire_time']) {
                $svip_new  = $svip_old = date('Y-m-d H:i:s',$minfo['super_vip_expire_time']);
            }

            if(!empty($day)){
                if( !is_numeric($day)){
                    json_fail_admin('天数必须是整数类型!');
                }

                (new \app\admin\model\Trad())->viptime($minfo['id'],4,$day,'后台设置赠送普通vip');//增加vip时间并增加记录
                $new = Db::name("member_info")->where('id',$minfo['id'])->value('expire_time');
                $new = date('Y-m-d H:i:s',$new);
                $title = "尊敬的用户 ".$minfo['account']." 您好，现赠送的视频VIP ".$day."天，请注意查收";
                $data        = [
                    'member_id' => $minfo['id'],  'title' => $title,
                    'content'   => $title, 'create_time' => time(), 'create_user' => 1,
                ];

            }

            if(!empty($svip_day)){
                if( !is_numeric($svip_day)){
                    json_fail_admin('天数必须是整数类型!');
                }
                (new \app\admin\model\Trad())->viptime($minfo['id'],4,$svip_day,'后台设置赠送超级vip',true);//增加svip时间并增加记录
                $svip_new = Db::name("member_info")->where('id',$minfo['id'])->value('super_vip_expire_time');
                $svip_new = date('Y-m-d H:i:s',$svip_new);
                $title = "尊敬的用户 ".$minfo['account']." 您好，现赠送的超级视频VIP ".$svip_day."天，请注意查收";
                $data        = [
                    'member_id' => $minfo['id'], 'title' => $title,
                    'content'   => $title, 'create_time' => time(), 'create_user' => 1,
                ];
            }

            if(!empty($ai_day) ){
                $datetime = date('Y-m-d H:i:s');
                $member_info_extra_detail =  MemberInfoExtraService::getDetail(intval($minfo['id']));
                $ai_vip_time_new = date('Y-m-d H:i:s',strtotime((!empty($member_info_extra_detail['ai_vip_time']) && $member_info_extra_detail['ai_vip_time']!='0000-00-00 00:00:00' ?$member_info_extra_detail['ai_vip_time']:$datetime))+($ai_day??0)*86400);
                MemberInfoExtraService::changeValue(intval($minfo['id']),'ai_vip_time',3,$ai_vip_time_new);
            }
            if(!empty($ai_type_remove_clothes) ){
                MemberInfoExtraService::changeValue(intval($minfo['id']),'ai_clothing_count',1,intval($ai_type_remove_clothes));
            }
            if(!empty($ai_type_change_face) ){
                MemberInfoExtraService::changeValue(intval($minfo['id']),'change_face_count',1,intval($ai_type_change_face));
            }
            if(!empty($ai_type_movie) ){
                MemberInfoExtraService::changeValue(intval($minfo['id']),'type_movie_count',1,intval($ai_type_movie));
            }
            if(!empty($ai_type_diy_change_face) ){
                MemberInfoExtraService::changeValue(intval($minfo['id']),'ai_type_diy_change_count',1,intval($ai_type_diy_change_face));
            }

            if(!empty($ai_type_draw_count) ){
                MemberInfoExtraService::changeValue(intval($minfo['id']),'ai_type_draw_count',1,intval($ai_type_draw_count));
            }



            if(!empty($data)){
                Db::name('member_message')->insert(array_merge($data,['message_type' => 1])); //发送用户消息
                Db::name('member_message')->insert(array_merge($data,['message_type' => 3])); //发送用户消息
            }

            $string = '';
            if(!empty($day)){
                $string.='普通vip设置前: <span style="color:red">'.$old. '</span> <br>设置后: <span style="color:red">' . $new.'</span><br>';
            }
            if(!empty($svip_day)){
                $string.='超级vip设置前: <span style="color:red">'.$svip_old. '</span> <br>设置后: <span style="color:red">' . $svip_new.'</span><br>';
            }


            json_success_admin('VIP设置成功!',['post'=>$post,'old'=> $old,'new'=> $new,'svip_old'=> $svip_old,'svip_new'=> $svip_new,'explanation'=>$string]);
        }

   }


    public function set_super_expire()
    {
        if ($this->request->isGet()) {
            return view('set_super_expire');
        }else{
            $phone = input("param.phone");
            $day = input("param.day");
            $minfo = Db::name('member_info')->where('mobile',$phone)->whereOr('account',$phone)->field('id,super_vip_expire_time')->find();
            if (!$minfo) {
                json_fail_admin('未找到用户记录!');
            }
            $old = ' 无 ';
            if ($minfo['super_vip_expire_time']) {
                $old = date('Y-m-d H:i:s',$minfo['super_vip_expire_time']);
            }

            (new \app\admin\model\Trad())->viptime($minfo['id'],4,$day,'',true);//增加vip时间并增加记录
            $new = Db::name("member_info")->where('id',$minfo['id'])->value('super_vip_expire_time');
            $new = date('Y-m-d H:i:s',$new);
            json_success_admin('VIP设置成功!',['old'=> $old,'new'=> $new]);
        }

    }

}
