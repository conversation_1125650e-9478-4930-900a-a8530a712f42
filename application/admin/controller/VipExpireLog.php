<?php
namespace app\admin\controller;

use app\Service\VipExpireLogService;
use think\Db;
use think\facade\Env;

class VipExpireLog extends BaseController
{
    public $title, $current_url, $name,$class_list;
    public function initialize()
    {
        parent::initialize();
        $this->name = "vip_expire_log";
        $this->title = "VIP期限变化";
        $this->current_url = "/admin/vip_expire_log";
        $this->class_list = VipExpireLogService::getConstPluck("TYPE_");
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
        $where = [];
        $condition['account'] = input('request.account');
        $condition['remark'] = input('request.remark');
        $condition['is_visitor'] = input('request.is_visitor');
        $condition['begin_time'] = input('request.begin_time');
        $condition['end_time'] = input('request.end_time');
        $condition['type'] = input('request.type');

        if(input('request.start_enter')!=1){
            if(empty($condition['begin_time']))$condition['begin_time']=date('Y-m-d');
            if(empty($condition['end_time']))$condition['end_time']=date('Y-m-d');
        }

        $movie_id = input('param.movie_id');
        if (!empty($movie_id)) {
            $where[] = ['a.movie_id', '=', $movie_id];
            $this->current_url .= "/index/movie_id/". $movie_id;
        }

        if (!empty($condition['is_visitor'])) $where[] = ['b.is_visitor', '=', $condition['is_visitor']];
        if (!empty($condition['account'])) $where[] = ['b.account', 'like', '%' . $condition['account'] . '%'];
        if (!empty($condition['remark'])) $where[] = ['a.remark', 'like', '%' . $condition['remark'] . '%'];
        if (!empty($condition['begin_time'])) $where[] = ['a.addtime', '>=', strtotime($condition['begin_time'])];
        if (!empty($condition['end_time'])) $where[] = ['a.addtime', '<', strtotime($condition['end_time'])];
        if (!empty($condition['type'])) $where[] = ['a.type', '=', $condition['type']];

        $list = Db::name($this->name)->alias('a')
            ->join('member_info b', 'a.member_id=b.id','left')
            ->where($where)
            ->field('a.*,b.account,b.is_visitor')
            ->order('id desc')->paginate(20);
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'class_list'=>$this->class_list
        ]);
        $this->_assign();
        return view();
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name($this->name)->where($where)->delete();
        $res ? json_success_admin() : json_fail_admin();
    }
}
