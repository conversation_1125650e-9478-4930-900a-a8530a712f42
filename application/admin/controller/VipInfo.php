<?php
namespace app\admin\controller;

use think\Db;
class VipInfo extends CommonController
{
    public function __construct()
    {
        $conf = [
            'name' => "vip_info",
            'title' => "VIP权益配置",
            'current_url' => "/admin/vip_info",
        ];
        $this->order = 'id asc';
        $this->verify =
            [
                'title'=>[
                    'rule'=>'/^[\x{4e00}-\x{9fa5}A-Za-z0-9 ]{1,100}$/u',
                    'message'=>"主标题长度1-100位",
                ],
                'sub_title'=>[
                    'rule'=>'/^[\x{4e00}-\x{9fa5}A-Za-z0-9 ]{1,100}$/u',
                    'message'=>"副标题长度1-100",
                ]
            ];
        parent::__construct($conf);
    }
    public function index()
    {
        $list = Db::name($this->name)->order('id asc')->paginate(20)->each(function ($item,$res){
            if($item['update_user']){
                $item['update_user'] = Db::name("admin_info")->where('id',$item['update_user'])->value('username');
            }else{
                $item['update_user'] = Db::name("admin_info")->where('id',$item['create_user'])->value('username');
            }
            return $item;
        });
        $this->assign([
            'class_list'=>$this->class_list,
            'list' => $list
        ]);
        $this->_assign();
        return view();
    }


}
