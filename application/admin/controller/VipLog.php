<?php
namespace app\admin\controller;

use think\Db;
use think\facade\Env;

class VipLog extends BaseController
{
    public $title, $current_url, $name;
    public function initialize()
    {
        parent::initialize();
        $this->name = "vip_log";
        $this->title = "VIP修改记录";
        $this->current_url = "/admin/vip_log";
    }
    public function _assign()
    {
        $this->assign("title", $this->title);
        $this->assign("current_url", $this->current_url);
    }

    public function index()
    {
                
        $where = [];
        $condition['type'] = input('request.type');

        
        if (!empty($condition['type'])) $where[] = ['type', '=',$condition['type']];
        $list = Db::name($this->name)
            ->where($where)
            ->order('id desc')->paginate(10)->each(function ($item,$res){
                $item['update_user'] = Db::name("admin_info")->where('id',$item['update_user'])->value('username');
                return $item;
            });
        $this->assign([
            'list' => $list,
            'condition' => $condition,
        ]);
        $this->_assign();
        return view();
    }

    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');
        $res = Db::name($this->name)->where($where)->delete();
        $res ? json_success_admin() : json_fail_admin();
    }
}