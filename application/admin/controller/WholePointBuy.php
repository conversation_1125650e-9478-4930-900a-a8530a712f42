<?php
namespace app\admin\controller;

use think\Db;
class WholePointBuy extends CommonController
{
    public function __construct()
    {
        $conf=[
            'title' => '整点秒杀管理',
            'name'=>'whole_point_buy',
            'current_url'=>'/admin/whole_point_buy',
        ];
        parent::__construct($conf);
    }

    public function index()
    {

        $where = [];
        $condition['start_time'] = input('request.start_time');
        $condition['start_hour'] = input('request.start_hour');
        $condition['status'] = input('request.status');

        if (!empty($condition['start_time'])) $where[] = ['a.start_time', 'LIKE', $condition['start_time'].'%'];

        if (!empty($condition['status'])) {
            $where[] = ['a.status', '=',$condition['status']];
        }

        if(!empty($condition['start_time'])){
            $module_list = Db::name("whole_point_buy")->alias('a')->leftJoin("app_movie b","a.movie_id=b.id")->whereNull('a.delete_time')->field("count(a.id) count, a.start_time")->where($where)->order("a.start_time asc")->group("start_time")->select();
            foreach ($module_list as $k=>&$v){
                $v['start_hour']= date('H:i',strtotime($v['start_time']));
            }
        }

        if(!empty($condition['start_hour'])){
            $where[] = ['a.start_time', '=',$condition['start_hour']];
        }


        $list = Db::name("whole_point_buy")->alias('a')->whereNull('a.delete_time')->leftJoin("app_movie b","a.movie_id=b.id")->field("a.*,b.id as movie_id,b.title,b.cover")->where($where)->order("a.id desc")->paginate(20)->each(function($item, $key){
                $item['title_short'] = mb_substr($item['title'],0,100).(strlen($item['title'])>100?'.....':'');
                return $item;
        });

        $page = $list->render();
        $list =$list->toArray();

        $this->_assign();
        $this->assign([
            'list' => $list,
            'condition' => $condition,
            'page' => $page,
            'start_date' => $condition['start_time'],
            'module_list' => $module_list??[],
        ]);

        return view();
    }


    public function add(){
        if (!request()->isPost()) {
            $screenshot_lists=[];
            for ($i=0;$i<10;$i++){
                $screenshot_lists[$i] = '';
            }
            $this->assign([
                'screenshot_lists' => $screenshot_lists,
            ]);

            return view();
        } else {
            $post = input("post.");
                    
            $post['movie_id'] = trim($post['movie_id']);
            $post['start_time'] = trim($post['start_time']);
            if (!$post['movie_id']) {
                json_fail_admin("视频id不能为空");
            }
            $movie_detail = Db::name("app_movie")->where('id',$post['movie_id'])->field('id,type')->find();

            if(empty($movie_detail)){
                json_fail_admin("视频id不存在");
            }
            if(!in_array($movie_detail['type'],[2,5])){
                json_fail_admin("添加的视频必须是付费或活动类型的视频");
            }

            if(empty($post['start_time']) || strtotime($post['start_time'])<time() ){
                json_fail_admin("预设整点时间不能为空且必须大于当前时间");
            }

            $post['start_time'] = date('Y-m-d H:i:',strtotime($post['start_time'])).'00';

            $restar = Db::name($this->name)->where('movie_id',$post['movie_id'])->where('start_time',$post['start_time'])->whereNull('delete_time')->find();

            if($restar){
                json_fail_admin("当前时间整点已添加过该视频");
            }

            if(!empty($post['screenshot_lists'])){
                $post['screenshot_lists']=json_encode($post['screenshot_lists'],JSON_UNESCAPED_UNICODE);
            }

            $res = Db::name($this->name)->strict(false)->insertGetId($post);
            $this->addlog('添加整点秒杀活动','整点秒杀活动ID:'.$res.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();
        }
    }

    public function edit(){
        $id = input('param.id');
        if(empty($id)) $id = $this->id;
        $res = Db::name($this->name)->where('id', '=', $id)->whereNull('delete_time')->find();
        if(empty($res)) json_fail_admin($this->title.'不存在');

        if (request()->isPost()) {

            $post = input("post.");

            $post['movie_id'] = trim($post['movie_id']);
            $post['start_time'] = trim($post['start_time']);
            if (!$post['movie_id']) {
                json_fail_admin("视频id不能为空");
            }
            $movie_detail = Db::name("app_movie")->where('id',$post['movie_id'])->where('id','<>',$id)->field('id,type')->find();

            if(empty($movie_detail)){
                json_fail_admin("视频id不存在");
            }

            if(!in_array($movie_detail['type'],[2,5])){
                json_fail_admin("添加的视频必须是付费或活动类型的视频");
            }


            if(empty($post['start_time']) || strtotime($post['start_time'])<time() ){
                json_fail_admin("预设整点时间不能为空且必须大于当前时间");
            }

            $post['start_time'] = date('Y-m-d H:i:',strtotime($post['start_time'])).'00';

            $restar = Db::name($this->name)->where('movie_id',$post['movie_id'])->whereNull('delete_time')->where('id','<>',$id)->where('start_time',$post['start_time'])->find();

            if(strtotime($res['start_time'])<time()){
                json_fail_admin("过期活动不能编辑更改");
            }

            if($restar){
                json_fail_admin("当前时间整点已添加过该视频");
            }

            if(!empty($post['screenshot_lists'])){
                $post['screenshot_lists']=json_encode($post['screenshot_lists'],JSON_UNESCAPED_UNICODE);
            }

            $res = Db::name($this->name)->where('id', '=', $id)->strict(false)->update($post);
            $this->addlog('修改整点秒杀活动','整点秒杀活动ID:'.$id.',post_data:'.json_encode($post,JSON_UNESCAPED_UNICODE));

            $res ? json_success_admin() : json_fail_admin();

        } else {
            $res['screenshot_lists'] = json_decode($res['screenshot_lists'],true);
            for ($i=0;$i<10;$i++){
                $res['screenshot_lists'][$i] = !empty($res['screenshot_lists'][$i])?$res['screenshot_lists'][$i]:'';
            }
            $this->assign([
                'res' => $res,
                'screenshot_lists' => $res['screenshot_lists'],
            ]);
            return view();
        }
    }


    public function del($id)
    {
        $where[] = ['id', '=', $id];
        $res = Db::name($this->name)->where($where)->find();
        if (empty($res)) json_fail_admin('数据不存在');

        $res =  Db::name($this->name)->where('id', '=', $id)->update(['delete_time'=>date('Y-m-d H:i:s')]);

        $this->addlog('删除整点秒杀活动','活动id:'.$id);

        $res ? json_success_admin() : json_fail_admin();
    }

}