<?php
namespace app\admin\model;
use think\Model;
class AdminNode extends Model
{
    public function getAll($ids='')
    {
        $where = [
            'status'=>1
        ];
        if(!empty($ids)) $this->whereIn('id',$ids);

        $data = $this->where($where)->order('sort desc')->select();
        $data = self::sortOut($data);
        return $data;
    }
    static public function sortOut($cate,$pid=0){
        $tree = array();
        foreach($cate as $k => $v){
            if($v['pid'] == $pid){
                $tree[$k] = $v;
                $s = self::sortOut($cate,$v['id']);
                if(!empty($s)) $tree[$k]['s'] = $s;
            }
        }
        return $tree;
    }
}