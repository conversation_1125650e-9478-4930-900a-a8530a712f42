<?php
namespace app\admin\model;
use app\Service\AppPayOrderService;
use	think\Model;
use think\Db;


class Trad extends Model {
    /**
     * <AUTHOR>
     * @DateTime 2020-09-17
     * @param    [type]     $member_id  [用户id]
     * @param    [type]     $amount   [金额]
     * @param    [type]     $order    [订单号]
     * @param    [type]     $type     [description]
     * @param    [type]     $sub_type [description]
     * @param    string     $remark   [description]
     */
    public function addTrad($member_id,$amount,$order,$type,$sub_type,$remark=""){

    	//查询余额
        $amount_info = Db::name('member_amount')->where('member_id',$member_id)->find();
        if (!$amount_info) {
            Db::name('member_amount')->insert(['member_id' => $member_id]);
            $amount_info = [
                'member_id' => $member_id,
                'amount' => 0,
                'recharge' => 0,
            ];
        }
        //查询用户基本信息
        $memberInfo  = Db::name('member_info')->where('id',$member_id)->field('nick_name,create_time,channelid')->find();
        $member_nick = $memberInfo["nick_name"];


        //写入交易表
        $data = [];

        $data['member_id'] = $member_id;
        $data['member_nick'] = $member_nick;
        $data['order_id'] = $order;

        if ($type == TTYPE_OL_PAY || $type == TTYPE_TZ_GIVE || $type==OTHER_INCOME) {
            $data['amount_now'] = $amount_info['amount'] + $amount;  //充值及赠送 当前余额增加
            $data['amount'] = $amount;
        }else{
            $data['amount_now'] = $amount_info['amount'] - $amount; //当前余额减少
            if ($data['amount_now'] < 0) {
                json_fail_admin('余额不足!');
            }
            $data['amount'] = -$amount;   //流水金额置负
        }
        $data['is_first'] = 0;
        if (!$amount_info['recharge'] && $type == TTYPE_OL_PAY) {   //总充值为0 并且为充值则是首冲
            $data['is_first'] = 1;
        }
        $data['amount_before'] = $amount_info['amount'];
        $data['type'] = $type;
        $data['sub_type'] = $sub_type;
        $data['addtime'] = time();
        $data_id = Db::table('hg_app_pay_deal')->insertGetId($data);

        //改变用户余额 如果是充值则增加总充值

        if ($type == TTYPE_OL_PAY || $type == TTYPE_TZ_GIVE || $type == OTHER_INCOME) {
            Db::name('member_amount')->where('member_id',$member_id)->setInc('amount',$amount);
        }else{
            Db::name('member_amount')->where('member_id',$member_id)->setDec('amount',$amount);
        }

        if ($type == TTYPE_OL_PAY) { //充值增加总充值
            Db::name('member_amount')->where('member_id',$member_id)->setInc("recharge",$amount);

        }

        //赠送增加入款表
        if(in_array($type,[TTYPE_TZ_GIVE,OTHER_INCOME])){
            if($type == TTYPE_TZ_GIVE){
                $app_pay_tmp_type=3;
            }elseif ($type == OTHER_INCOME){
                $app_pay_tmp_type=5;
            }
            if(!empty($app_pay_tmp_type)){
                Db::name('app_pay_tmp')->insert([
                    'order_id' => $order,
                    'member_id' => $member_id,
                    'amount' => $amount,
                    'addtime' => time(),
                    'status' => 0, //状态 赠送默认成功
                    'type' => $app_pay_tmp_type //类型 赠送
                ]);
            }
        }

        $month_first_time = strtotime(date('Y-m-01 00:00:00')); //本月初时间戳
        //渠道统计
        if ($type == TTYPE_OL_PAY) {
            //渠道首充人数与额度
            if ($data['is_first'] == 1) {
                channeldata('first_pay',$memberInfo['channelid'],$amount);

                //特殊情况处理，这里后台的统计数据的首冲需要和外部订单合并统计算
                $outside_pay_order =  Db::name('outside_pay_order')->where([
                    ['member_id','=',$member_id],
                    ['status','=',0],
                ])->field('id')->find();

                if(empty($outside_pay_order)){
                    channeldata('all_first_pay',$memberInfo['channelid'],$amount);
                }
            }

            //渠道今日充值人数
            $pay_member_Ymd_key = 'pay_member_'.date('Ymd').'_'.$memberInfo['channelid'];
            redis()->sadd($pay_member_Ymd_key,$member_id);
            redis()->expire($pay_member_Ymd_key,86500);

            //渠道今日充值人数（特殊处理，这个将会包含ai等外部订单）
            $pay_member_Ymd_key = 'pay_member_all'.date('Ymd').'_'.$memberInfo['channelid'];
            redis()->sadd($pay_member_Ymd_key,$member_id);
            redis()->expire($pay_member_Ymd_key,86500);

            //渠道本月充值人数
            $pay_member_Ym_key = 'pay_member_'.date('Ym').'_'.$memberInfo['channelid'];
            redis()->sadd($pay_member_Ym_key,$member_id);
            redis()->expire($pay_member_Ym_key,86400*31+200);
            //渠道总充值额度
            channeldata('recharge',$memberInfo['channelid'],$amount);

            //渠道今日成功单数
            channeldata('order_count_success',$memberInfo['channelid']);

            if ($memberInfo['create_time'] >= $month_first_time) { //用户当月注册
                //渠道本月注册的用户充值人与额度
                $month_pay_key = 'month_pay_'.date('Ym').'_'.$memberInfo['channelid'];
                redis()->sadd($month_pay_key,$member_id);
                redis()->expire($month_pay_key,86400*31+200);
                channeldata('month_recharge',$memberInfo['channelid'],$amount);
            }
        }

        if (($type == TTYPE_TZ_OK && $sub_type == TTYPE_TZ_OK_VIP) && ($memberInfo['create_time'] >= $month_first_time)) {  //本月注册用户购买vip渠道统计
            $month_vip_key = 'month_vip_'.date('Ym').'_'.$memberInfo['channelid'];
            redis()->sadd($month_vip_key,$member_id);
            redis()->expire($month_vip_key,86400*31+200);
            channeldata('month_vip_recharge',$memberInfo['channelid'],$amount);
        }
        if (($type == TTYPE_TZ_OK && $sub_type == TTYPE_TZ_OK_MOVIE) && ($memberInfo['create_time'] >= $month_first_time)) {  //本月注册用户购买视频渠道统计
            $month_movie_key = 'month_movie_'.date('Ym').'_'.$memberInfo['channelid'];
            redis()->sadd($month_movie_key,$member_id);
            redis()->expire($month_movie_key,86400*31+200);
            channeldata('month_movie_recharge',$memberInfo['channelid'],$amount);
        }
    }


    /**
     * <AUTHOR>
     * @DateTime 2020-09-20
     * @param    [type]     $member_id [description]
     * @param    [type]     $type      [description]  1  购买  2 单次充值奖励  3  累计充值奖励  4 掉单补充 5 其他
     * @param    [type]     $day       [description]
     * @param    string     $remark    [description]
     * @param    string     $is_super    [description] 是否购买超级Vip
     * @return   [type]                [description]
     */
    public function viptime($member_id,$type,$day,$remark = '',$is_super = false)
    {
        list($field , $expire_time_new,$old)=self::getUpdateVipData($member_id,$day,$is_super);
        //改变过期时间
        Db::name('member_info')->where('id',$member_id)->update([$field=>$expire_time_new]);
        //增加记录
        $insert = [
            'member_id' => $member_id,
            'vip_day' => $day,
            'expire_time_old' => $old,
            'expire_time_new' => $expire_time_new,
            'type' => $type,
            'addtime' => time(),
            'remark' => $remark
        ];
        Db::name('vip_expire_log')->insert($insert);

    }

    public static function getUpdateVipData($member_id,$day,$is_super){
        //天数
        $time = $day * 24 * 3600;
        // vip类型对应的字段
        $field  = $is_super ? 'super_vip_expire_time' : 'expire_time';

        //查询会员过期时间
        $expire_time_old = Db::name('member_info')->where('id',$member_id)->value($field);

        $old = $expire_time_old;

        if($expire_time_old <= time()){
            //失效后续费或第一次开启
            $expire_time_old = time();
        }
        $expire_time_new = $expire_time_old + $time;

        return [$field , $expire_time_new , $old];

    }

    //新增推广 按小时送 vip时长
    public function code_viptime($member_id,$type,$day,$remark = '推广赠送单位：小时')
    {
        //天数
        $time = $day * 3600;
        //查询会员过期时间
        $expire_time_old = Db::name('member_info')->where('id',$member_id)->value('expire_time');

        $old = $expire_time_old;

        if($expire_time_old <= time()){
            //失效后续费或第一次开启
            $expire_time_old = time();
        }

        $expire_time_new = $expire_time_old + $time;


        //改变过期时间
        Db::name('member_info')->where('id',$member_id)->update(['expire_time' => $expire_time_new]);
        //增加记录
        $insert = [
            'member_id' => $member_id,
            'vip_day' => $day,
            'expire_time_old' => $old,
            'expire_time_new' => $expire_time_new,
            'type' => $type,
            'addtime' => time(),
            'remark' => $remark
        ];
        Db::name('vip_expire_log')->insert($insert);

    }


    /**
     * 开通会员接口
     * @param $type
     * @param $price
     * @param $day
     * @param $is_super
     * @return bool
     */
    public function openVip($type,$price,$day,$is_super,$user_id,$vip_id){
        $order_no = CreateDingdan($user_id, TTYPE_TZ_OK, TTYPE_TZ_OK_VIP, rand(0, 9));
        Db::startTrans();
        try {
            $log_id = Db::name("member_vip")->insertGetId([
                'member_id'=>$user_id,
                'type'=>$type,
                'vip_id'=>$vip_id,
                'create_time'=>time(),
                'amount'=>$price*100,
                'order_no'=>$order_no
            ]);

            Db::name("member_vip")->where('id',$log_id)->update(['status'=>1]);  //修改状态

            (new \app\admin\model\Trad())->addTrad($user_id,$price * 100,$order_no,TTYPE_TZ_OK,TTYPE_TZ_OK_VIP); //操作余额

            if(!empty($day)){
                (new \app\admin\model\Trad())->viptime($user_id,1,$day,'',$is_super);//增加vip时间并增加记录
            }

            Db::commit();
        }catch (\Exception $e) {
            Db::rollback();
            json_fail("开通会员失败,".$e->getMessage());
        }

        return true;

    }



}
