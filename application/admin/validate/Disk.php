<?php
namespace app\admin\validate;

use think\Validate;
use think\Db;

class Disk extends Validate
{


    protected $rule = [
        'title'  => 'require|max:25|min:2',
        'video_id' => 'require|isVideo',
        'detail'=>'require|max:200|min:2',
        'sort'=>[
            'regex'=>'/^[1-9][0-9]*$/'
        ],
        'status'=>'require|boolean'
    ];

    protected $message = [
        'title.require'=>'标题必须',
        'title.max'=>'标题长度不能大于25',
        'title.min'=>'标题长度不能小于2',
        'video_id.require'=>'最少选择一个视频',
        'detail.require'=>'片单介绍不能为空',
        'detail.max'=>'片单介绍长度最大为200',
        'detail.min'=>'片单介绍长度最小为2',
        'sort.regex'=>'排序参数为正整数',
        'status.require'=>'状态必须',
        'status.boolean'=>'状态参数不正确',
    ];

    /**
     * @param $value  验证数据
     * @param $rule  验证规则
     * @param $data  全部数据（数组）
     * @param $field 字段名
     */
    protected function isVideo($value,$rule,$data,$field)
    {
        $idArr = array_filter(explode(',',$value));
        $res = Db::name('app_movie')->whereIn('id',$value)->count();
        if(count($idArr) != $res)
        {
            return "视频ID不正确";
        }else{
            return true;
        }
    }
}