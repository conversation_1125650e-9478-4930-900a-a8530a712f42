{include file="public/header" /}

<body>
<div class="page-container">
<table class="table table-border table-bordered table-bg">
    <thead>
    </thead>
    <tbody>
        <tr class="text-c">
            <td style="width:20%;">分类</td>
            <td>
                {foreach $app_category as $k=>$v}
                {if condition="in_array($v['id'],explode(',',$res['category_ids']))"}
                <span class="label label-success radius">{$v.title}</span>
                {/if}
                {/foreach}
            </td>
        </tr>
        <tr class="text-c">
            <td>标签</td>
            <td>
                {foreach $app_tag as $k=>$v}
                <span class="label label-success radius" style="background-color:#b66cdc;">{$v.title}</span>
                {/foreach}
            </td>
        </tr>
        <tr class="text-c">
            <td>女优</td>
            <td>
                {foreach $app_star as $k=>$v}
                {if condition="in_array($v['id'],explode(',',$res['star_ids']))"}
                <span class="label label-success radius" style="background-color:#e0748d;">{$v.title}</span>
                {/if}
                {/foreach}
            </td>
        </tr>
        <tr class="text-c">
            <td>专题</td>
            <td>
                {foreach $app_theme as $k=>$v}
                {if condition="in_array($v['id'],explode(',',$res['theme_ids']))"}
                <span class="label label-success radius" style="background-color:#d48059;">{$v.title}</span>
                {/if}
                {/foreach}
            </td>
        </tr>
        <tr class="text-c">
            <td>标题</td>
            <td>{$res.title}</td>
        </tr>
        <tr class="text-c">
            <td>番号</td>
            <td>{$res.number}</td>
        </tr>
        <tr class="text-c">
            <td>出品方</td>
            <td>
                {foreach $app_products as $k=>$v}
                {if condition="$res['product_id'] == $v['id']"}
                {$v.title}
                {/if}
                {/foreach}
            </td>

        </tr>

        <tr class="text-c">
            <td>水平封面</td>
            <td>
                {if $res.cover}
                    {if isbase64($res.cover)}
                        <a href="{$res.cover|get_host}" target="_blank" rel="noopener noreferrer">
                            <img src="{$res.cover|get_host}" style="max-width:300px;max-height:300px;" alt="水平封面">
                        </a>
                    {else}
                        <a href="{$res.cover|readBase}" target="_blank" rel="noopener noreferrer">
                            <img src="{$res.cover|readBase}" style="max-width:300px;max-height:300px;" alt="水平封面">
                        </a>
                    {/if}
                {else}
                    <span style="color:#999;">暂无封面</span>
                {/if}
            </td>
        </tr>
        <tr class="text-c">
            <td>垂直封面</td>
            <td>
                {if $res.cover_vertical}
                    {if isbase64($res.cover_vertical)}
                        <a href="{$res.cover_vertical|get_host}" target="_blank" rel="noopener noreferrer">
                            <img src="{$res.cover_vertical|get_host}" style="max-width:300px;max-height:300px;" alt="垂直封面">
                        </a>
                    {else}
                        <a href="{$res.cover_vertical|readBase}" target="_blank" rel="noopener noreferrer">
                            <img src="{$res.cover_vertical|readBase}" style="max-width:300px;max-height:300px;" alt="垂直封面">
                        </a>
                    {/if}
                {else}
                    <span style="color:#999;">暂无封面</span>
                {/if}
            </td>
        </tr>
        <!-- <tr class="text-c">
            <td>视频</td>
            <td><a href="javascript:;" target="target" onclick="show_video('视频:{$res.title}','{$res.url2}','80%','80%')"><video src="{$res.url2}" style="max-width:300px;max-height:300px;"></video></a></td>
        </tr> -->
        <tr class="text-c">
            <td>详情</td>
            <td>{$res.detail}</td>
        </tr>
        <tr class="text-c">
            <td>评分</td>
            <td>{$res.score}</td>
        </tr>
        <tr class="text-c">
            <td>时长</td>
            <td>{$res.duration}</td>
        </tr>
        <tr class="text-c">
            <td>浏览次数</td>
            <td><a title="浏览记录:{$res.title}" href="javascript:;" onclick="show('浏览记录:{$res.title}','/admin/member_history/index/movie_id/{$res.id}')" class="label label-primary radius">{$res.click_num}</a></td>
        </tr>
        <tr class="text-c">
            <td>喜欢次数</td>
            <td><a title="喜欢记录:{$res.title}" href="javascript:;" onclick="show('喜欢记录:{$res.title}','/admin/member_like/index/movie_id/{$res.id}')" class="label label-primary radius">{$res.like_num}</a></td>
        </tr>
        <tr class="text-c">
            <td>收藏次数</td>
            <td><a title="收藏记录:{$res.title}" href="javascript:;" onclick="show('喜欢记录:{$res.title}','/admin/member_folder/index/movie_id/{$res.id}')" class="label label-primary radius">{$res.folder_num}</a></td>
        </tr>
        <tr class="text-c">
            <td>点赞次数</td>
            <td><a title="点赞记录:{$res.title}" href="javascript:;" onclick="show('点赞记录:{$res.title}','/admin/member_movie_up_down/index/movie_id/{$res.id}/up_down/1')" class="label label-primary radius">{$res.up_num}</a></td>
        </tr>
        <tr class="text-c">
            <td>点踩次数</td>
            <td><a title="点踩记录:{$res.title}" href="javascript:;" onclick="show('点踩记录:{$res.title}','/admin/member_movie_up_down/index/movie_id/{$res.id}/up_down/2')" class="label label-primary radius">{$res.down_num}</a></td>
        </tr>
        <tr class="text-c">
            <td>评论次数</td>
            <td><a title="评论记录:{$res.title}" href="javascript:;" onclick="show('评论记录:{$res.title}','/admin/member_comment/index/movie_id/{$res.id}')" class="label label-primary radius">{$res.comment_num}</a></td>
        </tr>
        <tr class="text-c">
            <td>排序</td>
            <td>{$res.sort}</td>
        </tr>
        <tr class="text-c">
            <td>发布时间</td>
            <td>{$res.create_time|date="Y-m-d H:i:s"}</td>
        </tr>
        <tr class="text-c">
            <td>状态</td>
            <td>{$res.status==1?'<span class="label label-success radius">启用</span>':'<span class="label label-default radius">禁用</span>'}</td>

        </tr>
    </tbody>
</table>
</div>
    {include file="public/footer" /}
    <script type="text/javascript">
        function show_video(title, url, w, h) {
            layer.open({
                type: 2,
                title: title,
                content: url,
                area: [w, h],
                // maxmin: true,           //最大化按钮
                anim: 3,                //动画
                shade: [0.8, '#393D49'],//遮罩层
                end: function () { }
            });
        }
        function show(title, url, w, h) {
            perContent = layer.open({
                type: 2,
                title: title,
                content: url,
                area: ['100%', '100%'],
                maxmin: true,           //最大化按钮
                anim: 3,                //动画
                shade: [0.8, '#393D49'],//遮罩层
                end: function () { }
            });
            layer.full(perContent);
        }
    </script>
</body>

</html>
