{include file="public/header" /}
<link rel="stylesheet" href="/static/lib/jquery_uploader/src/css/bootstrap.min.css?222" integrity="sha384-Zug+QiDoJOrZ5t4lssLdxGhVrurbmBWopoEl+M6BdEfwnCJZtKxi1KgxUyJq13dy" crossorigin="anonymous">
<link rel="stylesheet" href="/static/lib/jquery_uploader/src/css/jquery.dm-uploader.css">

<body>
    <article class="page-container">
        <form action="javascript:;" class="form form-horizontal" id="form-admin">
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">分类</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <div class="skin-minimal">
                        <div id="app_category">
                            {foreach $app_category as $k=>$v}
                                {if condition="in_array($v['id'],explode(',',$res['category_ids']))"}
                                <a src="javascript:;" class="app_category" id="app_category-{$v.id}" onclick="remove(this);">
                                    <input type="hidden" name="category_ids[]" value="{$v.id}">
                                    <span class="label label-success" style="position: relative;padding-right: 20px;">
                                        {$v.title}
                                        <div style="position: absolute;right: 0;top: 0;background-color: #ef6b67;font-size: 14px;width: 18px;height: 100%;text-align: center;line-height: 18px;">  X  76B8CEB7A48F40C8846DCDB064FAB7C</div>
                                    </span>
                                </a>
                                {/if}
                            {/foreach}
                        </div>
                        <a href="javascript:;" onclick="show('选择分类','/admin/app_movie/choose/name/app_category')" class="label label-primary radius">点击选择</a>
                    </div>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">标签</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <div class="skin-minimal">
                        <div id="app_tag">
                            {foreach $app_tag as $k=>$v}
                                <a src="javascript:;" class="app_tag" id="app_tag-{$v.id}" onclick="remove(this);">
                                    <input type="hidden" name="tag_ids[]" value="{$v.id}">
                                    <span class="label label-success" style="position: relative;padding-right: 20px;">
                                        {$v.title}
                                        <div style="position: absolute;right: 0;top: 0;background-color: #ef6b67;font-size: 14px;width: 18px;height: 100%;text-align: center;line-height: 18px;">  X  76B8CEB7A48F40C8846DCDB064FAB7C</div>
                                    </span>
                                </a>
                            {/foreach}
                        </div>
                        <a href="javascript:;" onclick="show('选择标签','/admin/app_movie/choose/name/app_tag','tag')" class="label label-primary radius">点击选择</a>
                    </div>
                    <div style="margin-top: 10px"></div>
                    <input type="text" autocomplete="off" class="input-text" value="" id='tag_str' name="tag_str" style='max-width:400px'>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">女优</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <div class="skin-minimal">
                        <div id="app_star">
                            {foreach $app_star as $k=>$v}
                                {if condition="in_array($v['id'],explode(',',$res['star_ids']))"}
                                <a src="javascript:;" class="app_star" id="app_star-{$v.id}" onclick="remove(this);">
                                    <input type="hidden" name="star_ids[]" value="{$v.id}">
                                    <span class="label label-success" style="position: relative;padding-right: 20px;">
                                        {$v.title}
                                        <div style="position: absolute;right: 0;top: 0;background-color: #ef6b67;font-size: 14px;width: 18px;height: 100%;text-align: center;line-height: 18px;">  X  76B8CEB7A48F40C8846DCDB064FAB7C</div>
                                    </span>
                                </a>
                                {/if}
                            {/foreach}
                        </div>
                        <a href="javascript:;" onclick="show('选择女优','/admin/app_movie/choose/name/app_star','star')" class="label label-primary radius">点击选择</a>
                    </div>
                    <div style="margin-top: 10px"></div>
                    <input type="text" autocomplete="off" class="input-text" value="" id='star_str' name="star_str" style='max-width:400px'>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">专题</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <div class="skin-minimal">
                        <div id="app_theme">
                            {foreach $app_theme as $k=>$v}
                                {if condition="in_array($v['id'],explode(',',$res['theme_ids']))"}
                                <a src="javascript:;" class="app_theme" id="app_theme-{$v.id}" onclick="remove(this);">
                                    <input type="hidden" name="theme_ids[]" value="{$v.id}">
                                    <span class="label label-success" style="position: relative;padding-right: 20px;">
                                        {$v.title}
                                        <div style="position: absolute;right: 0;top: 0;background-color: #ef6b67;font-size: 14px;width: 18px;height: 100%;text-align: center;line-height: 18px;">  X  76B8CEB7A48F40C8846DCDB064FAB7C</div>
                                    </span>
                                </a>
                                {/if}
                            {/foreach}
                        </div>
                        <a href="javascript:;" onclick="show('选择专题','/admin/app_movie/choose/name/app_theme')" class="label label-primary radius">点击选择</a>
                    </div>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">标题</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.title}" name="title">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">番号</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.number}" name="number">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">出品方</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <!-- <div id="demo1">
                        <select name="product_id" size="1" id="product_id">
                            {volist name="app_products" id="vo"}
                        <option value="{$vo.id}" {eq name="vo.id" value="$res.product_id"} selected {/eq}>{$vo.title}</option>
                        {/volist}
                       </select>
                    </div>  -->
                    <select class="select" name="product_id" size="1" id="product_id">
                        {volist name="app_products" id="vo"}
                        <option value="{$vo.id}" {eq name="vo.id" value="$res.product_id"} selected {/eq}>{$vo.title}</option>
                        {/volist}
                    </select>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">水平封面</label>
                <div class="formControls col-xs-8 col-sm-3">
                    {if $res.cover}
                        {if isbase64($res.cover)}
                            <a href="{$res.cover|get_host}" target="target"><img class="file_cover_src" src="{$res.cover|get_host}" height="80" alt=""></a>
                        {else}
                            {if $res.cover}
                            <img  class="file_cover_src" src="{$res.cover|readBase}" height='80'>
                            {/if}

                        {/if}
                    {else}
                        <img  class="file_cover_src" src="" height='80' style="display:none">
                    {/if}
                    <span class="btn-upload form-group">
                        <!-- <input class="input-text upload-url radius" type="text" name="cover" readonly value="{$res.cover}"> -->
                        <a href="javascript:void();" class="btn btn-primary radius">浏览文件</a><!-- <span class="ml-20 label label-warning">800 X 540</span> -->
                        <input type="file" name="file_cover" id='file_cover' class="input-file">
                    </span>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">垂直封面</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <!-- <a href="" class='file_cover_vertical_href' target="target"><img class="file_cover_vertical_src" src="{$res.cover_vertical|default=''}" height="100" alt=""></a> -->
                    {if $res.cover_vertical}
                        {if isbase64($res.cover_vertical)}
                            <a href="{$res.cover|get_host}" target="target"><img class="file_cover_vertical_src" src="{$res.cover_vertical|get_host}" height="80" alt=""></a>
                        {else}
                            {if $res.cover}
                            <img  class="file_cover_vertical_src" src="{$res.cover_vertical|readBase}" height='80'>
                            {/if}
                        {/if}
                    {else}
                        <img  class="file_cover_vertical_src" src="" height='80' style="display:none">
                    {/if}
                    <span class="btn-upload form-group">
                        <!-- <input class="input-text upload-url radius" type="text" name="cover_vertical" readonly value="{$res.cover_vertical}"> -->
                        <a href="javascript:void();" class="btn btn-primary radius">浏览文件</a><!-- <span class="ml-20 label label-warning">380 X 540</span> -->
                        <input type="file" id='file_cover_vertical' name="file_cover_vertical" class="input-file">
                    </span>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">GIF图片</label>
                <div class="formControls col-xs-8 col-sm-3">
                   <img class="file_gif_src" src="{$res.gif|readBase}" height="100" alt="">

                    <span class="btn-upload form-group">
                        <!-- <input class="input-text upload-url radius" type="text" name="cover_vertical" readonly value="{$res.cover_vertical}"> -->
                        <a href="javascript:void();" class="btn btn-primary radius">浏览文件</a><!-- <span class="ml-20 label label-warning">380 X 540</span> -->
                        <input type="file" id='file_gif' name="file_gif" class="input-file">
                    </span>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">视频地址</label>
                <div class="formControls col-xs-8 col-sm-3">

                   <a href="chrome-extension://emnphkkblegpebimobpbekeedfgemhof/player.html#{$res.url}" target="target"><img src="/static/images/tv.png" height="50" alt=""></a>
                   <input class="input-text upload-url radius" type="hidden" name="url" readonly value="{$res.url}">
                </div>
            </div>

<!--             {volist name="app_movie_bt" id="vo"}
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">种子</label>
                <div class="formControls col-xs-8 col-sm-9">
                    <input type="text" autocomplete="off" class="input-text"  name="bt[]" value="{$vo.bt_url}">
                </div>
            </div>
            {/volist}
            <button class="btn btn-default addbt" type="button" style="width: 50%;margin-left: 40%;margin-top: 1%">添加种子</button>
            {volist name="app_movie_hotpoint" id="vo"}
                <div class="row cl">
                    <label class="form-label col-xs-4 col-sm-3">预览图</label>
                    <div class="formControls col-xs-8 col-sm-9">
                        <span class="btn-upload form-group">
                            <input class="input-text upload-url radius" type="text" name="hot_pic[]" readonly value="{$vo.hot_pic}">
                            <a href="javascript:void();" class="btn btn-primary radius">浏览文件</a>
                            <input type="file"  name="file_hot_pic[]" class="input-file">
                        </span>
                        <input type="text" style="width: 30%;" placeholder="HH:MM:SS" autocomplete="off" class="input-text" value="{$vo.hot_time}" name="hot_time[]">

                    </div>
                </div>
            {/volist}
            <button class="btn btn-default add_hp" type="button" style="width: 50%;margin-left: 40%;margin-top: 1%">添加预览</button> -->
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">详情</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <textarea class="textarea" name="detail">{$res.detail}</textarea>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">点击数</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.click_num}" name="click_num">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">收藏数</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.folder_num}" name="folder_num">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">点赞数</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.up_num}" name="up_num">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">点踩数</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.down_num}" name="down_num">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">评分</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.score}" name="score">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">模块排序</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.sort}" name="sort">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">搜索排序</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.search_sort}" name="search_sort">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">售价</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <input type="text" autocomplete="off" class="input-text" value="{$res.price}" name="price">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-4 col-sm-3">权限类型</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <select class="select" name="type" size="1">
                        {volist name="price_type" id="item"}
                         <option value="{$key}" {if $res.type == $key}selected{/if}>{$item}</option>
                        {/volist}
                    </select>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-3">状态</label>
                <div class="formControls col-xs-8 col-sm-3">
                    <div class="skin-minimal">
                        <div class="radio-box">
                            <input type="radio" id="radio-1" name="status" value="1" {eq name="$res.status" value="1"}checked{/eq}>
                            <label for="radio-1">启用</label>
                        </div>
                        <div class="radio-box">
                            <input type="radio" id="radio-2" name="status" value="0" {eq name="$res.status" value="0"}checked{/eq}>
                            <label for="radio-2">禁用</label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row cl">
                <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-3">
                    <input class="btn btn-primary radius" id="submit" type="submit" value="&nbsp;&nbsp;提交&nbsp;&nbsp;" style="border: 1px solid #999;">
                </div>
            </div>
            <video style="display: none;" controls="controls" id="video_test" oncanplaythrough="getLength(this)" src=""></video>
            <input type="hidden" name="cover" value="{$res.cover}" id="cover">
            <input type="hidden" name="gif" value="{$res.gif}" id="gif">
            <input type="hidden" name="cover_vertical" value="{$res.cover_vertical}" id="cover_vertical">
            <input type="hidden" name="duration" id="duration" value="{$res.duration}">
        </form>
    </article>
    {include file="public/footer" /}
    <script src="/static/lib/jquery_uploader/src/js/jquery.dm-uploader.js?123123"></script>
    <script src="/static/lib/jquery_uploader/demo-ui.js"></script>
    <script src="/static/lib/jquery_uploader/demo-config.js?111"></script>
    <script src="/pc/static/js/select.js?0607"></script>
    <script>
        $(function() {
           $('select').comboSelect();
       });
    </script>
    <script type="text/javascript">
        var fileurl = "{$Think.config.file.host}";
        var app_tag = JSON.parse("<?php echo $app_tag1 ?>");
        var app_star = JSON.parse("<?php echo $app_star1 ?>");
        function remove(obj) {
            $(obj).remove();
            strchange('tag');
            strchange('star');
        }
        $('.addbt').click(function () {
            var bt = '<div class="row cl">' +
                '                <label class="form-label col-xs-4 col-sm-3">种子</label>' +
                '                <div class="formControls col-xs-8 col-sm-9">' +
                '                    <input type="text" autocomplete="off" class="input-text" value="" name="bt[]">' +
                '                </div>' +
                '            </div>';
            $('.addbt').before(bt)
        })


        $('.add_hp').click(function () {
            var hp = '' +
                '<div class="row cl">\n' +
                '                <label class="form-label col-xs-4 col-sm-3">预览图</label>\n' +
                '                <div class="formControls col-xs-8 col-sm-9">\n' +
                '                    <span class="btn-upload form-group">\n' +
                '                        <input class="input-text upload-url radius" type="text" name="hot_pic[]" readonly>\n' +
                '                        <a href="javascript:void();" class="btn btn-primary radius">浏览文件</a>\n' +
                '                        <input type="file"  name="file_hot_pic[]" class="input-file">\n' +
                '                    </span>\n' +
                '                    <input type="text" style="width: 30%;" placeholder="HH:MM:SS" autocomplete="off" class="input-text" value="" name="hot_time[]">\n' +
                '\n' +
                '                </div>\n' +
                '            </div>';
            $('.add_hp').before(hp);
        })

        $(function(){

            strchange('tag');
            strchange('star');
            $("#tag_str").bind("input propertychange",function(){
                check('tag',app_tag)
            })
            $("#star_str").bind("input propertychange",function(){
                check('star',app_star)
            })

        });
        function strchange(name)
        {
            var app_category = $('#app_'+name)
            var str = ''
            app_category.find('.app_' + name).each(function () {

                var title = $(this).children("span").text().replace('  X  76B8CEB7A48F40C8846DCDB064FAB7C','').trim()
                if (str) {
                    str += ',' + title
                }else{
                    str += title
                }
            });
            console.log(str)
            $('#'+name+'_str').val(str)
        }

        function check(name,data)
        {

            // var val = $("#"+name).val().replace('，',',');
            // $("#"+name).val(val);

            var val = $("#"+name+"_str").val().replace('，',',');

            var arr = val.split(',');
            var new_arr=[];
            for(var i=0;i<arr.length;i++) {
                var items=arr[i];
               //判断元素是否存在于new_arr中，如果不存在则插入到new_ar中
                if($.inArray(items,new_arr)==-1) {
                    new_arr.push(items);
                }
            }

            // $('#app_'+name).find('.app_' + name).each(function () {

            //     var title = $(this).children("span").text().replace('  X  76B8CEB7A48F40C8846DCDB064FAB7C','').trim()
            //     var v = $.inArray(title, arr);
            //     if (v != -1) {
            //        arr.splice(v,1);
            //     }

            // });
            var arr=$.grep(arr,function(n,i){
                return n;
            },false);
            var val = new_arr.join(',');
            $("#"+name+"_str").val(val);

            var arr = new_arr

            $('#app_'+name).empty()

            for(var i=0;i<arr.length;i++){
                var id = null;
                for(var j=0;j<data.length;j++){
                    if ($.trim(arr[i]) == data[j].title) {
                        id = data[j].id
                        break;
                    }
                }
                if (!id) {
                    var html = '<a src="javascript:;" class="app_' + name  + '" onclick="remove(this);">\
                <span class="label label-success" style="position: relative;padding-right: 20px;">'+$.trim(arr[i])+'<div style = "position: absolute;right: 0;top: 0;background-color: #ef6b67;font-size: 14px;width: 18px;height: 100%;text-align: center;line-height: 18px;" >   X  76B8CEB7A48F40C8846DCDB064FAB7C</div ></span></a>';
                }else{
                    var html = '<a src="javascript:;" data-id="'+id+'" class="app_' + name + '" id="app_' + name + '-' + id + '" onclick="remove(this);"><input type="hidden" name="' + name + '_ids[]" value="' + id +'">\
                            <span class="label label-success" style="position: relative;padding-right: 20px;">'+$.trim(arr[i])+'<div style = "position: absolute;right: 0;top: 0;background-color: #ef6b67;font-size: 14px;width: 18px;height: 100%;text-align: center;line-height: 18px;" >   X  76B8CEB7A48F40C8846DCDB064FAB7C</div ></span></a>';
                }
                $('#app_'+name).append(html)
            }


        }
        function show(title, url,str=null) {
            perContent = layer.open({
                type: 2,
                title: title,
                content: url,
                area: ['60%', '80%'],
                maxmin: true,           //最大化按钮
                anim: 3,                //动画
                shade: [0.8, '#393D49'],//遮罩层
                end: function () {
                    if (str) {
                        strchange(str);
                    }

                }
            });
            // layer.full(perContent);
        }
        function getLength(ele) {
            var hour = parseInt((ele.duration) / 3600);
            if (hour < 10) hour = 0 + '' + hour;
            var minute = parseInt((ele.duration % 3600) / 60);
            if (minute < 10) minute = 0 + '' + minute;
            var second = Math.ceil(ele.duration % 60);
            if (second < 10) second = 0 + '' + second;
            $('#duration').val(hour + ":" + minute + ":" + second)
        }
        $(function () {
            $('.skin-minimal input').iCheck({
                checkboxClass: 'icheckbox-blue',
                radioClass: 'iradio-blue',
                increaseArea: '20%'
            })
        });
        layer.config({ shadeClose: false });
        $("#form-admin").validate({
            onkeyup: false,
            focusCleanup: true,
            success: "valid",
            submitHandler: function (form) {
                var index = layer.load(2);
                $("#submit").attr("disabled","disabled");
                $(form).ajaxSubmit({
                    type: 'post',
                    url: "",
                    dataType: "json",
                    success: function (json) {
                        layer.close(index);
                        if (json.code == 0) {
                            layer.msg(json.msg);
                            setTimeout(function () { window.parent.location.reload(); }, 500);
                        } else {
                            layer.msg(json.msg, function () {
                                $('#submit').attr("disabled",false);
                            });
                        }
                    },
                    error: function (XmlHttpRequest, textStatus, errorThrown) {
                        layer.msg('error!', function () { });
                    }
                });
            }
        });


        // 自定义图片上传函数，支持本地预览
        function uploadImgWithPreview(file, filedom, imgdom) {
            // 先显示本地预览
            var reader = new FileReader();
            reader.onload = function(e) {
                $('.' + imgdom + '_src').attr('src', e.target.result);
                $('.' + imgdom + '_src').show();
            };
            reader.readAsDataURL(file);

            // 然后上传到服务器
            var index = layer.msg('图片上传中..', {
                icon: 16,
                shade: 0.2,
                time: false
            });

            var formData = new FormData();
            formData.append("file", file);

            $.ajax({
                type: "post",
                url: '/admin/file/upload',
                data: formData,
                dataType: 'json',
                processData: false,
                contentType: false,
                success: function(data) {
                    if (data.code == 0) {
                        $("#" + filedom).val(data.data.src);
                        // 不覆盖本地预览图片，保持本地显示
                        if ($('#' + filedom + '_size').length) {
                            $('#' + filedom + '_size').val(data.data.width + ' * ' + data.data.height);
                        }
                        layer.close(index);
                    } else {
                        layer.close(index);
                        layer.msg(data.msg, function() {});
                    }
                }
            });
        }

        $("#file_cover").on('change', function() {
            var file_cover = this.files[0];
            if (file_cover == "") {
                return false;
            }
            uploadImgWithPreview(file_cover, 'cover', 'file_cover');
        });

        $("#file_cover_vertical").on('change', function() {
            var file_cover_vertical = this.files[0];
            if (file_cover_vertical == "") {
                return false;
            }
            uploadImgWithPreview(file_cover_vertical, 'cover_vertical', 'file_cover_vertical');
        });

        $("#file_gif").on('change', function() {
            var file_gif = this.files[0];
            if (file_gif == "") {
                return false;
            }
            uploadImgWithPreview(file_gif, 'gif', 'file_gif');
        });
    </script>
</body>

</html>
