{include file="public/header" /}

<body>
    <div class="page-container">
        <form name="table_form" action="{$current_url}?start_enter=1" method="post">
            <div class="text-c">
                <input type="text" autocomplete="off" class="input-text" style="width:250px" placeholder="视频id" value="{$condition.id}" name="id">
                <input type="text" autocomplete="off" class="input-text" style="width:250px" placeholder="标题" value="{$condition.title}" name="title">
                <input type="text" autocomplete="off" class="input-text" style="width:100px" placeholder="番号" value="{$condition.number}" name="number">
                <select class="btn btn-select" name="movie_type">
                    <option value="">权限类型</option>
                    {volist name="$movie_type_list" id="vo"}
                    <option value="{$vo.id}" {eq name="$vo.id" value="$condition.movie_type" }selected{/eq}>{$vo.title}</option>
                    {/volist}
                </select>
                <select class="btn" name="category_ids">
                    <option value="">全部分类</option>
                    <option value="-1"  {eq name="-1" value="$condition.category_ids" }selected{/eq}>无分类</option>
                    {volist name="$app_category" id="vo"}
                    <option value="{$vo.id}" {eq name="$vo.id" value="$condition.category_ids" }selected{/eq}>&nbsp; {$vo.title}</option>
                    {/volist}
                </select>
                <select class="btn" name="tag_ids">
                    <option value="">全部标签</option>
                    <option value="-1" {eq name="-1" value="$condition.tag_ids" }selected{/eq}>无标签</option>
                    {volist name="$app_tag" id="vo"}
                    <option value="{$vo.id}" {eq name="$vo.id" value="$condition.tag_ids" }selected{/eq}>&nbsp; {$vo.title}</option>
                    {/volist}
                </select>
                <select class="btn" name="star_ids">
                    <option value="">全部女优</option>
                    <option value="-1" {eq name="-1" value="$condition.star_ids" }selected{/eq}>无女优</option>
                    {volist name="$app_star" id="vo"}
                    <option value="{$vo.id}" {eq name="$vo.id" value="$condition.star_ids" }selected{/eq}>&nbsp; {$vo.title}</option>
                    {/volist}
                </select>
                <select class="btn" name="theme_ids">
                    <option value="">全部专题</option>
                    <option value="-1" {eq name="-1" value="$condition.theme_ids" }selected{/eq}>无专题</option>
                    {volist name="$app_theme" id="vo"}
                    <option value="{$vo.id}" {eq name="$vo.id" value="$condition.theme_ids" }selected{/eq}>&nbsp; {$vo.title}</option>
                    {/volist}
                </select>

                <select class="btn" name="product_id">
                    <option value="">全部出品方</option>
                    <option value="-1" {eq name="-1" value="$condition.product_id" }selected{/eq}>无出品方</option>
                    {volist name="$app_products" id="vo"}
                    <option value="{$vo.id}" {eq name="$vo.id" value="$condition.product_id" }selected{/eq}>{$vo.title}</option>
                    {/volist}
                </select>
                <input type="text" autocomplete="off" onfocus="WdatePicker({minDate:'00:00:00',maxDate:'06:00:00',dateFmt: 'HH:mm:ss'})" name="start_duration" value="{$condition.start_duration|default=''}" class="input-text Wdate" style="width:120px;" placeholder="最短时长">
                <input type="text" autocomplete="off" onfocus="WdatePicker({minDate:'00:00:00',maxDate:'06:00:00',dateFmt: 'HH:mm:ss'})" name="end_duration" value="{$condition.end_duration|default=''}" class="input-text Wdate" style="width:120px;" placeholder="最长时长">

                <select class="btn" name="status">
                    <option value="">状态</option>
                    <option value="1" {eq name="$condition.status|default=''" value="1" }selected{/eq}>启用</option>
                    <option value="0" {eq name="$condition.status|default=''" value="0" }selected{/eq}>禁用</option>

                </select>
                <input type="text" autocomplete="off" onfocus="WdatePicker({maxDate:''})" name="begin_time" value="{$condition.begin_time|default=''}" class="input-text Wdate" style="width:120px;" placeholder="开始日期" id="begin_time">
                <input type="text" autocomplete="off" onfocus="WdatePicker({maxDate:''})" name="end_time" value="{$condition.end_time|default=''}" class="input-text Wdate" style="width:120px;" placeholder="结束日期" id="end_time">
                <button type="submit" class="btn btn-success"><i class=" Hui-iconfont">&#xe665;</i>搜索</button>
                <a href="javascript:;" onclick="clearForm(this)" class="btn btn-danger"><i class="Hui-iconfont">&#xe68f;</i>清空重搜</a>
                <input type="hidden" id="current_url" value="{$current_url}">
                <div class="f-r">
                    <a class="btn btn-primary" href="javascript:;" data-url="{$current_url}?start_enter=1&is_export=1" onclick="export_excel(this);"><i class="Hui-iconfont">&#xe644;</i> 导出当前数据</a>
                </div>
            </div>

        </form>
        <div class="cl pd-5 bg-1 bk-gray mt-20" style='padding:10px'>
            <span class="l">
                <a href="javascript:;" onclick="setprice('批量设置售价','{$current_url}/set_price','800px','700px')" style="border:1px solid #5a98de;padding:5px;border-radius: 3px;text-decoration: none;color:#5a98de;background: #eee;margin-right: 10px">
                     批量设置售价
                </a>
                <a href="javascript:;" onclick="setprice('批量设置权限类型','{$current_url}/set_type','800px','700px')" style="border:1px solid #5a98de;padding:5px;border-radius: 3px;text-decoration: none;color:#5a98de;background: #eee">
                     批量设置权限类型
                </a>&nbsp&nbsp
                <a href="javascript:;"  onclick="show_part('批量设置分类','/admin/app_movie/batch_update_choose/name/app_category')"  class="btn btn-danger radius">
                     批量设置分类
                </a>&nbsp&nbsp
                <a href="javascript:;" onclick="show_part('批量设置标签','/admin/app_movie/batch_update_choose/name/app_tag')" class="btn btn-primary radius">
                     批量设置标签
                </a>
                <a href="javascript:;" onclick="show_part('批量设置女优','/admin/app_movie/batch_update_choose/name/app_star')" class="btn btn-warning radius">
                     批量设置女优
                </a>
                <a href="javascript:;" onclick="show_part('批量设置出品方','/admin/app_movie/batch_update_choose/name/app_products')" class="btn btn-success radius">
                     批量设置出品方
                </a>
                <a href="javascript:;" onclick="setprice('批量设置标题前后缀','{$current_url}/set_prefix_suffix','1200px','750px')" class="btn btn-primary radius">
                     批量设置标题前后缀
                </a>
                <!-- <a href="javascript:;" class="btn btn-danger radius" onclick="re_index();">
                    重建索引
                </a> -->
            </span>
            <span class="r">共有数据：<strong>{$list->total()}</strong> 条</span>
        </div>
        <style>
            /* 滚动条凹槽的颜色，还可以设置边框属性 */
            .scroll::-webkit-scrollbar-track-piece {
                background-color:#f8f8f8;
            }
            /* 滚动条的宽度 */
            .scroll::-webkit-scrollbar {
                width:9px;
                height:9px;
            }
            /* 滚动条的设置 */
            .scroll::-webkit-scrollbar-thumb {
                background-color:#dddddd;
                background-clip:padding-box;
                min-height:28px;
            }
            .scroll::-webkit-scrollbar-thumb:hover {
                background-color:#bbb;
            }
            .combo-select  {
                display: inline-block;
                max-width: 150px;
            }
        </style>
        <div id="topScroll" class="scroll" style="width: 100%;overflow:auto;"></div>
        <div id="scroll" class="scroll" style="width:100%;overflow:auto;">
        <table id="scrollTable" class="table table-border table-bordered table-bg  table-hover" style="min-width:200%;">
            <thead>
                <tr>
                    <th scope="col" colspan="30">{$title}</th>
                </tr>
                <tr class="text-c">
                    <th style="width:30px;">全选 <input type="checkbox"></th>
                    <th style="width:100px;">操作</th>
                    <th style="width:50px;">ID</th>
                    <th style="width:50px;">售价</th>
                    <th style="width:50px;">权限类型</th>
                    <th style="width:100px;">分类</th>
                    <th style="width:100px;">标签</th>
                    <th style="width:100px;">女优</th>
                    <th style="width:100px;">专题</th>
                    <th style="width:150px;">标题</th>
                    <th style="width:100px;">番号</th>
                    <th style="width:100px;">出品方</th>
                    <!-- <th style="width:100px;">种子</th> -->
                    <!-- <th style="width:100px;">预览图</th> -->
                    <th style="width:100px;">水平封面</th>
                    <th style="width:100px;">垂直封面</th>
                    <th style="width:100px;">GIF</th>
                    <!-- <th style="width:100px;">视频</th> -->
                    <th style="width:100px;">视频(m3u8)</th>
                    <th style="width:70px;">视频长度</th>
                    <th style="width:40px;">点击数</th>
                    <th style="width:40px;">真实点击数</th>
                    <th style="width:40px;">喜欢数</th>
                    <th style="width:40px;">收藏数</th>
                    <th style="width:40px;">点赞数</th>
                    <th style="width:40px;">点踩数</th>
                    <th style="width:40px;">评论数</th>
                    <th style="width:40px;">评分</th>
                    <th style="width:50px;">排序</th>
                    <!--<th style="width:70px;">采集来源</th>-->
                    <th style="width:150px;">新增时间</th>
<!--                     <th style="width:50px;">最新更新</th>
                    <th style="width:50px;">最多播放</th>
                    <th style="width:50px;">最多喜欢</th> -->
                    <th style="width:50px;">状态</th>
                    <th style="width:50px;">画质</th>

                    <th style="width:100px;">操作</th>
                </tr>
            </thead>
            <tbody>
                {volist name="list" id="vo"}
                <tr class="text-c">
                    <td><input  type="checkbox" value="{$vo.id}" name="menuId"></td>
                    <td class="td-manage">
                        <a title="详情" href="javascript:;" onclick="show2('详情','{$current_url}/details/id/{$vo.id}','800','700')" class="label label-warning radius">详情</a>
                        <a title="编辑" href="javascript:;" onclick="show('编辑','{$current_url}/edit/id/{$vo.id}','800','700')" class="label label-primary radius">编辑</a>
                       <!-- <a title="删除" href="javascript:;" onclick="del(this,{$vo.id})" class="label label-danger radius">删除</a>-->
                    </td>
                    <td>{$vo.id}</td>
                    <td>
                        {if $vo.price > 0}
                            {$vo.price}
                        {/if}
                    </td>
                    <td>
                        {$price_type[$vo.type]}
                    </td>
                    <td>
                        {foreach :explode(',',$vo.category_ids) as $v}
                            {foreach $app_category as $vv}
                                {eq name="$vv.id" value="$v"}<span class="label label-success radius">{$vv.title}</span>{/eq}
                            {/foreach}
                        {/foreach}
                    </td>
                    <td>
                        {foreach :explode(',',$vo.tag_ids) as $v}
                            {foreach $app_tag as $vv}
                                {eq name="$vv.id" value="$v"}<span class="label label-success radius" style="background-color:#b66cdc;">{$vv.title}</span>{/eq}
                            {/foreach}
                        {/foreach}
                    </td>
                    <td>
                        {foreach :explode(',',$vo.star_ids) as $v}
                            {foreach $app_star as $vv}
                                {eq name="$vv.id" value="$v"}<span class="label label-success radius" style="background-color:#e0748d;">{$vv.title}</span>{/eq}
                            {/foreach}
                        {/foreach}
                    </td>
                    <td>
                        {foreach :explode(',',$vo.theme_ids) as $v}
                            {foreach $app_theme as $vv}
                                {eq name="$vv.id" value="$v"}<span class="label label-success radius" style="background-color:#d48059;">{$vv.title}</span>{/eq}
                            {/foreach}
                        {/foreach}
                    </td>
                    <td>{$vo.title}</td>
                    <td>{$vo.number}</td>
                    <td>
                        {foreach $app_products as $vv}
                        {eq name="$vv.id" value="$vo.product_id"}{$vv.title}{/eq}
                        {/foreach}
                    </td>
                    <!-- <td><a href="javascript:;" onclick="show('种子列表','/admin/app_movie_bt/index/movie_id/{$vo.id}','1200','800')">点击查看</a></td> -->
                    <!-- <td><a href="javascript:;" onclick="show('预览图','/admin/app_movie_hotpoint/index/movie_id/{$vo.id}','1200','800')">点击查看</a></td> -->

                    <td>
                        <div class="image-container" style="position:relative;display:inline-block;">
                            <div class="loading-placeholder" style="width:50px;height:50px;background:#f5f5f5;border:1px solid #ddd;display:flex;align-items:center;justify-content:center;font-size:10px;color:#999;">
                                加载中...
                            </div>
                            <a href="javascript:void(0);" style="display:none;">
                                <img class="img-list lazy-img" src="{$vo.cover|readBase}" height='50' onload="hideLoading(this)" onerror="showError(this)">
                            </a>
                        </div>
                    </td>
                    <td>
                        <div class="image-container" style="position:relative;display:inline-block;">
                            <div class="loading-placeholder" style="width:50px;height:50px;background:#f5f5f5;border:1px solid #ddd;display:flex;align-items:center;justify-content:center;font-size:10px;color:#999;">
                                加载中...
                            </div>
                            <a href="javascript:void(0);" style="display:none;">
                                <img class="img-list lazy-img" src="{$vo.cover_vertical|readBase}" height='50' onload="hideLoading(this)" onerror="showError(this)">
                            </a>
                        </div>
                    </td>
                    <td>
                        <div class="image-container" style="position:relative;display:inline-block;">
                            <div class="loading-placeholder" style="width:50px;height:50px;background:#f5f5f5;border:1px solid #ddd;display:flex;align-items:center;justify-content:center;font-size:10px;color:#999;">
                                加载中...
                            </div>
                            <a href="javascript:void(0);" style="display:none;">
                                <img class="img-list lazy-img" src="{$vo.gif|readBase}" height='50' onload="hideLoading(this)" onerror="showError(this)">
                            </a>
                        </div>
                    </td>


                    <td>
                        <a href="javascript:;" onclick="layer_show('视频播放','/admin/app_movie/movie_preview?url={$vo.url_base64}','735','465')" ><img src="/static/images/tv.png" height="50" alt=""></a>
                    </td>
                    <td>{$vo.duration}</td>
                    <td><a href="javascript:;" onclick="show('浏览记录:{$vo.title}','/admin/member_history/index/movie_id/{$vo.id}','1200','800')" class="label label-default radius">{$vo.click_num}</a></td>
                    <td>{$vo.real_click_num}</td>
                    <td><a href="javascript:;" onclick="show('喜欢记录:{$vo.title}','/admin/member_like/index/movie_id/{$vo.id}','1200','800')" class="label label-default radius">{$vo.like_num}</a></td>
                    <td><a href="javascript:;" onclick="show('收藏记录:{$vo.title}','/admin/member_folder/index/movie_id/{$vo.id}','1200','800')" class="label label-default radius">{$vo.folder_num}</a></td>
                    <td><a href="javascript:;" onclick="show('点赞记录:{$vo.title}','/admin/member_movie_up_down/index/movie_id/{$vo.id}/up_down/1','1200','800')" class="label label-default radius">{$vo.up_num}</a></td>
                    <td><a href="javascript:;" onclick="show('点踩记录:{$vo.title}','/admin/member_movie_up_down/index/movie_id/{$vo.id}/up_down/2','1200','800')" class="label label-default radius">{$vo.down_num}</a></td>
                    <td><a href="javascript:;" onclick="show('评论记录:{$vo.title}','/admin/member_comment/index/movie_id/{$vo.id}','1200','800')" class="label label-default radius">{$vo.comment_num}</a></td>
                    <td>{$vo.score}</td>
                    <td>{$vo.sort}</td>
                    <!--<td><span class="label label-success radius" style="background-color: #f702ef94;">{$vo.cj}</span></td>-->
                    <td>{$vo.create_time|date="Y-m-d H:i:s"}</td>
                    <!-- <td><a href="javascript:;" onclick="is_is('设置最新','{$current_url}/is_new',{$vo.id});" class="label label-warning radius">执行</a></td>
                    <td><a href="javascript:;" onclick="is_is('设置最多播放','{$current_url}/is_play',{$vo.id});" class="label label-warning radius">执行</a></td>
                    <td><a href="javascript:;" onclick="is_is('设置最多喜欢','{$current_url}/is_like',{$vo.id});" class="label label-warning radius">执行</a></td> -->
                    <td>
                        {eq name="vo.status" value="1"}
                        <span class="label label-success radius" onclick="status(this,{$vo.id});">已启用</span>
                        {else /}
                        <span class="label label-default radius" onclick="status(this,{$vo.id});">已禁用</span>
                        {/eq}
                    </td>
                    <td>
                    {empty name="vo.sp_reso1"}标清{else/}
						{notempty name="vo.sp_reso1"}标清<br/>{/notempty}
						{notempty name="vo.sp_reso2"}高清<br/>{/notempty}
						{notempty name="vo.sp_reso3"}超清{/notempty}
					{/empty}
                    </td>

                    <td class="td-manage">
                        <a title="详情" href="javascript:;" onclick="show2('详情','{$current_url}/details/id/{$vo.id}','800','700')" class="label label-warning radius">详情</a>
                        <a title="编辑" href="javascript:;" onclick="show('编辑','{$current_url}/edit/id/{$vo.id}','800','700')" class="label label-primary radius">编辑</a>
                        <!--<a title="删除" href="javascript:;" onclick="del(this,{$vo.id})" class="label label-danger radius">删除</a>-->
                    </td>
                </tr>
                {/volist}
            </tbody>
        </table>
        </div>
        {$list->render()|raw}
    </div>
    {include file="public/footer" /}

    <dev id="batch_update_choose" hidden="hidden"></dev>

    <script src="/pc/static/js/select.js?0607"></script>
    <script>
        $(function() {
           $('select').comboSelect();
       });
    </script>

    <script type="text/javascript">


        var par = "/admin/app_products";
        if(parent.location.pathname == par){
            $('.l').hide()
        }
        function is_is(title,url,id){
            layer.confirm('确认要'+title+'吗？', function (index) {
                $.post(url, { 'id': id }, function (json) {
                    if (json.code == 0) {
                        layer.msg(json.msg);
                    } else {
                        layer.msg(json.msg, function () { });
                    }
                }, 'json').error(function () {
                    layer.msg('error!', function () { });
                })
            });
        }
        $(function () {
            $('tr').on('click', function () {
                $('tr').css("background-color", "#FFF");
                $(this).css("background-color", "#f8f8f8");
            })
            $("#topScroll").css("overflow-x", "scroll").height(9);
            $("#topScroll").html($("#scroll").html());
            $("#topScroll").scroll(function () {
                $("#scroll")[0].scrollLeft = $("#topScroll")[0].scrollLeft; //拉动DIV2层滚动条，DIV1层滚动条同步被改变
            });
            $("#scroll").scroll(function () {
                $("#topScroll")[0].scrollLeft = $("#scroll")[0].scrollLeft; //拉动DIV2层滚动条，DIV1层滚动条同步被改变
            });
        });
    layer.config({ shadeClose: false });
        function show2(title, url, w, h) {
            layer_show(title, url, w, h);
        }
        function show(title, url, w, h) {
            perContent = layer.open({
                type: 2,
                title: title,
                content: url,
                area: ['100%', '100%'],
                maxmin: true,           //最大化按钮
                anim: 3,                //动画
                shade: [0.8, '#393D49'],//遮罩层
                end: function () {}
            });
            layer.full(perContent);
        }

        function show_part(title,url){

            var menu_ids = "";
            var arr = document.getElementsByName("menuId");
            var menu_ids_count = arr.length;
            for (var i = 0; i < arr.length; i++) {

                if (menu_ids_count/2<(i+1) && arr[i].checked) {
                    menu_ids += arr[i].value + ",";
                }
            }
            if (menu_ids.length > 0) menu_ids = menu_ids.substring(0, menu_ids.length - 1);
            if (menu_ids == "") {
                layer.msg('请勾选');
                return;
            }

            var nurl = url +"?ids="+menu_ids

            perContent = layer.open({
                type: 2,
                title: title,
                content: nurl,
                area: ['60%', '80%'],
                maxmin: true,           //最大化按钮
                anim: 3,                //动画
                shade: [0.8, '#393D49'],//遮罩层
                end: function () { }
            });
            // layer.full(perContent);
        }

        function del(obj, id) {
            layer.confirm('确认要删除吗？', function (index) {
                $.ajax({
                    type: 'POST',
                    url: '{$current_url}/del',
                    dataType: 'json',
                    data: {
                        'id': id,
                    },
                    success: function (data) {
                        if (data.code === 0) {
                            layer.msg(data.msg);
                            $(obj).parents("tr").remove();
                        } else {
                            layer.msg(data.msg, function () { });
                        }
                    },
                    error: function (data) {
                        layer.msg('error!', function () { });
                    },
                });
            });
        }
        function status(obj, id) {
            var spanhtml = $(obj).html();
            var t1 = (spanhtml == '已启用') ? '禁用' : '启用';
            var c1 = (spanhtml == '已启用') ? 'default' : 'success';

            layer.confirm('确认要' + t1 + '吗？', function (index) {
                $.post('{$current_url}/status', { 'id': id }, function (json) {
                    if (json.code == 0) {
                        $(obj).removeClass('label-success label-default').addClass('label-' + c1).html('已' + t1);
                        layer.msg('已' + t1 + '!');
                    } else {
                        layer.msg(json.msg, function () { });
                    }
                }, 'json').error(function () {
                    layer.msg('error!', function () { });
                })
            });
        }
        function re_index() {
            var t1 = '重建索引';
            layer.confirm('请确保对视频进行过新增/删除操作；<br>并保证当前不处于访问高峰期；', function (index) {
                $.post('{$current_url}/re_index', {}, function (json) {
                    if (json.code == 0) {
                        layer.msg('已' + t1 + '!');
                    } else {
                        layer.msg(json.msg, function () { });
                    }
                }, 'json').error(function () {
                    layer.msg('error!', function () { });
                })
            });
        }
     $(function () {
        $(".text-c th input:checkbox").click(function () {
            $(".text-c td input:checkbox").prop("checked", $(this).prop("checked"));
        });
    });


    function setprice(title,url,w,h){
        var menu_ids = "";
        var arr = document.getElementsByName("menuId");
        var menu_ids_count = arr.length;
        for (var i = 0; i < arr.length; i++) {

            if (menu_ids_count/2<(i+1) && arr[i].checked) {
                menu_ids += arr[i].value + ",";
            }
        }
        if (menu_ids.length > 0) menu_ids = menu_ids.substring(0, menu_ids.length - 1);
        if (menu_ids == "") {
            layer.msg('请勾选');
            return;
        }
        console.log(w,h)
        var nurl = url +"?ids="+menu_ids
        console.log(nurl)
        perContent = layer.open({
            type: 2,
            title: title,
            content: nurl,
            area: [w, h],
            maxmin: true,           //最大化按钮
            anim: 3,                //动画
            shade: [0.8, '#393D49'],//遮罩层
            end: function () {}
        });
        // layer.full(perContent);
    }

    // 图片加载完成后隐藏加载提示
    function hideLoading(img) {
        var container = $(img).closest('.image-container');
        container.find('.loading-placeholder').hide();
        $(img).parent('a').show();
    }

    // 图片加载失败时显示错误提示
    function showError(img) {
        var container = $(img).closest('.image-container');
        var placeholder = container.find('.loading-placeholder');
        placeholder.html('加载失败').css('color', '#ff6b6b');
    }

    // 页面加载完成后开始加载图片
    $(document).ready(function() {
        // 延迟加载图片，避免一次性加载太多图片
        $('.lazy-img').each(function(index) {
            var img = this;
            setTimeout(function() {
                // 触发图片加载
                img.src = img.src;
            }, index * 50); // 每张图片延迟50ms加载
        });
    });


    </script>
</body>

</html>
