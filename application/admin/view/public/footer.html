{if $cache_sys_config.footer_status eq '1'}
<!-- <footer class="footer mt-50">
	<div class="container">
		<p>{$cache_sys_config.footer}</p>
	</div>
</footer> -->
{/if}
<!--_footer 作为公共模版分离出去-->

<script type="text/javascript" src="/static/lib/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript" src="/static/lib/layer/2.4/layer.js"></script>
<script type="text/javascript" src="/static/h-ui/js/H-ui.min.js"></script>
<script type="text/javascript" src="/static/h-ui.admin/js/H-ui.admin.js"></script>

<script type="text/javascript" src="/static/lib/jquery.contextmenu/jquery.contextmenu.r2.js"></script>
<script type="text/javascript" src="/static/lib/jquery.validation/1.14.0/jquery.validate.js"></script>
<script type="text/javascript" src="/static/lib/jquery.validation/1.14.0/validate-methods.js"></script>
<script type="text/javascript" src="/static/lib/jquery.validation/1.14.0/messages_zh.js"></script>
<!--/_footer 作为公共模版分离出去-->
<script type="text/javascript" src="/static/lib/My97DatePicker/4.8/WdatePicker.js"></script>

<script type="text/javascript">
    function uploadImg(file,filedom,imgdom)
    {

        var index = layer.msg('图片上传中..', {
            icon: 16
            ,shade: 0.2
            ,time: false
        });
        console.log(file)
        console.log(filedom)
        console.log(imgdom)
        var formData = new FormData();//这里需要实例化一个FormData来进行文件上传
        formData.append("file", file);//加入文件对象
        console.log('图片上传')
        $.ajax({
            type : "post",
            url : '/admin/file/upload',
            data : formData,
            dataType : 'json', //传递数据的格式
            // async:false, //这是重要的一步，防止重复提交的
            processData : false,
            contentType : false,
            success : function(data){
                console.log(data)

                if (data.code == 0) {
                    $("#"+filedom+"").val(data.data.src);
                    $('.'+imgdom+'_src').attr('src',data.data.base64)
                    $('.'+imgdom+'_src').show()
                    if($('#'+filedom+'_size').length) {
                        $('#'+filedom+'_size').val(data.data.width +' * '+data.data.height);
                    }

                    layer.close(index)
                }else{
                    layer.close(index)

                    layer.msg(data.msg, function () {

                    });

                }
            }
        });
    }

    $("body").on("click",".img-list",function(e){
        layer.photos({
            photos: {"data": [{"src": e.target.src}]},
            shift:0,
            area: 'auto'
        });
    });

    $(document).ready(function() {
        $('form[name="table_form"]').on('submit', function(e) {
            showSpinner(); // 调用 showSpinner 方法
        });
    });

    function export_excel(obj) {
        showSpinner();

        var form = document.forms['table_form'];  // 使用表单的 name 属性来获取表单元素

        if (!(form instanceof HTMLFormElement)) {
            console.error('Form element not found or not an HTMLFormElement');
            hideSpinner();
            return;
        }

        var formData = new FormData(form);  // 创建FormData对象

        var xhr = new XMLHttpRequest();
        xhr.open('POST', obj.getAttribute('data-url'), true);
        xhr.responseType = 'blob';  // 期待从服务器接收的响应类型是Blob

        xhr.onload = function() {
            if (xhr.status === 200) {
                var contentType = xhr.getResponseHeader('Content-Type');
                var blob = xhr.response;

                // 检查 Blob 的大小
                if (blob.size === 0) {
                    alert('无数据导出');
                    hideSpinner();
                    return;
                }

                if (contentType && (contentType.includes('application/json') || contentType.includes('text/html') )) {
                    // 如果返回的是 JSON 格式的错误信息
                    var reader = new FileReader();
                    reader.onload = function(event) {
                        try {
                            var jsonResponse = JSON.parse(event.target.result);
                            if (jsonResponse.code >=0 && jsonResponse.msg) {
                                alert('' + jsonResponse.msg);
                            } else {
                                alert('未知错误');
                            }
                        } catch (e) {
                            console.error('解析 JSON 错误：', e);
                        }
                        hideSpinner();
                    };
                    reader.readAsText(blob);
                } else {
                    // 处理正常的 Excel 文件下载
                    var disposition = xhr.getResponseHeader('Content-Disposition');
                    var filename = disposition ? disposition.match(/filename="(.+)"/)[1] : 'download.xlsx';
                    var link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    link.download = decodeURIComponent(filename);
                    document.body.appendChild(link); // Firefox 需要将链接添加到 DOM 中
                    link.click();
                    document.body.removeChild(link); // 下载后移除链接
                    hideSpinner();
                }
            } else {
                console.error('下载失败：', xhr.statusText);
                hideSpinner();
            }
        };

        xhr.onerror = function() {
            console.error('下载失败：', xhr.statusText);
            hideSpinner();
        };

        xhr.send(formData);
    }


    function clearForm(element) {
        var form = element.closest('form');
        var inputs = form.getElementsByTagName('input');
        var selects = form.getElementsByTagName('select');

        for (var i = 0; i < inputs.length; i++) {
            if (inputs[i].type == 'text') {
                inputs[i].value = '';
            }
        }

        for (var i = 0; i < selects.length; i++) {
            selects[i].selectedIndex = 0;
        }
    }

    function showSpinner() {
        document.getElementById('spinnerOverlay').style.display = 'block';
        return true; // 允许表单提交
    }

    function hideSpinner() {
        document.getElementById('spinnerOverlay').style.display = 'none';
    }


</script>


<iframe id="hidden_iframe" name="hidden_iframe" style="display:none;" onload="hideSpinner();"></iframe>
