<?php
namespace app\agent\controller;

use think\Controller;
use think\Db;

class Base extends Controller
{
    protected $agent = [];
    private $redis = null;
    protected $goUrl = null;
    public function initialize() {
        parent::initialize();
        $this->isLogin();
        $this->isState();
        $this->assign('webTitle', '代理充值平台');
    }
    
    /**
     * 检查用户是否登录
     */
    private function isLogin() {
        $token = cookie('agent_token');
        if (! $token) $this->output('请先登录！', 2);//json_fail_admin('请先登录！', [], 2);
        $agentId = $this->getRedis()->get($token);
        if (! $agentId) {
            $this->setDelUserLoginInfo();
            $this->output('登录信息已过期，请重新登录！', 2);
        }
        $userToken = $this->getRedis()->get('agent_' . $agentId);
        if (! $userToken) {
            $this->setDelUserLoginInfo();
            $this->output('你的登录已失效，请重新登录！', 2);
            json_fail_admin('', [], 2);
        }
        if ($userToken != $token) {
            $this->setDelUserLoginInfo(false);
            $this->output('检测到您已在别处登录，强制下线！', 2);
        }
        $this->agent = ['id' => $agentId];
        // 更新时间
        cookie('agent_token', $token, 20 * 60);
        $this->getRedis()->expire($token, 20 * 60);
        $this->getRedis()->expire('agent_' . $agentId, 20 * 60);
    }
    
    /**
     * 检查用户是否有效
     */
    private function isState() {
        $where = [
            'status' => 1,
            'id' => $this->agent['id']
        ];
        $agentInfo = Db::name('agent_info')->where($where)->field('id,user_name,credit_limit,nick_name,state,login_pwd,recharge_pwd,weixin,weixin_qrcode,qq,qq_qrcode')->find();
        if (! $agentInfo) {
            $this->setDelUserLoginInfo();
            $this->output('身份无效！', 1);
        }
        if ($agentInfo['state'] != 1) {
            $this->setDelUserLoginInfo();
            $this->output('账号被禁用，请联系管理员！', 1);
        }
        $this->agent = $agentInfo;
        $this->assign('agent', $agentInfo);
    }
    
    /**
     * 清空用户登录信息
     * @param boolean $delUser 是否清除用户信息
     */
    protected function setDelUserLoginInfo($delUser = true) {
        $token = cookie('agent_token');
        cookie('agent_token', null);
        $agentId = $this->getRedis()->get($token);
        if ($agentId) {
            $this->getRedis()->del($token);
            if ($this->getRedis()->exists('agent_'.$agentId) && $delUser) {
                $this->getRedis()->del('agent_'.$agentId);
            }
        }
    }
    
    /**
     * 获取redis
     * @return Redis
     */
    protected function getRedis() {
        if (! $this->redis) {
            $this->redis = redis();
        }
        return $this->redis;
    }
    
    /**
     * 输出信息
     * @param string $msg
     * @param number $code
     * @param array $data
     */
    protected function output($msg = '', $code = 0, $data = []) {
        if (! $msg) {
            $msg = $code == 0 ? 'ok' : 'fail';
        }
        if (request()->isAjax()) {
            json_fail_admin($msg, $data, $code);
        }
        $sUrl = strtolower('/agent/' . request()->controller() . '/' . request()->action());
        $this->goUrl = $this->goUrl ? $this->goUrl : '/agent/login/index?s_url=' . $sUrl;
        if ($code !== 0) {
            $this->error($msg, $this->goUrl, $data);
        } else {
            $this->success($msg, $this->goUrl, $data);
        }
    }
}

