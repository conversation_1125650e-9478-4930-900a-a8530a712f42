<?php
namespace app\agent\controller;

class File
{
    public function upload()
    {
        $file = request()->file("file");
        if (!$file) {
            json_fail_admin('请重新上传！');
        }
        $info = $file->rule('uniqid')->move(UPLOAD_PATH . '/uploads/');
        $path_in_folder = UPLOAD_PATH . '/uploads/'.$info->getSaveName();
        $data = uploadImgToOss($path_in_folder);
        $info = getimagesize($path_in_folder);    
        $width = $info[0];
        $height = $info[1];
        @unlink($path_in_folder);        
        if ($data && $data['code'] == 0) {
            echo json_encode(['code'=>0,'msg'=>'success','data'=>['src'=> $data['data'],'base64'=>readBase( $data['data'] ),'width'=>$width,'height' => $height]]);exit;
        }
        echo json_encode(['code'=>1,'msg'=>$data['message'],'data'=>['src'=>'','base64'=>'']]);exit; 
    }
}

