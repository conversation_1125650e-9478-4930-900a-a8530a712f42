<?php
namespace app\agent\controller;
/**
 * 首页操作
 *
 */
class Index extends Base
{
    /**
     * 首页
     */
    public function index() {
        // 左侧菜单
        $menu = [
            'title' => '陌陌视频',
            'list' => [
                [
                    'title' => '充值管理',
                    'fun' => [
                        ['title' => '充值', 'path' => '/recharge/index'],
                        ['title' => '充值记录查询', 'path' => '/recharge/log']
                    ]
                ],
                [
                    'title' => '设置',
                    'fun' => [
                        ['title' => '修改密码', 'path' => '/user/password'],
                        ['title' => '个人信息设置', 'path' => '/user/info']
                    ]
                ],
            ]
        ];
        $this->assign([
            'agent' => $this->agent,
            'menu' => $menu
        ]);
        return view();
    }
    
    /**
     * 首页
     */
    public function welcome() {
        return '欢迎您！【' . $this->agent['nick_name'] . '】';
    }
    
    /**
     * 提示信息页面
     */
    public function message() {
        $type = input('get.type/d') ? input('get.type') : 1;
        $msg = input('get.msg') ? urldecode(input('get.msg')) : '';
        $url = input('get.url') ? urldecode(input('get.url')) : 0;
        $wait = input('get.wait/d');
        $data = input('get.data') ? json_decode(input('get.data')) : [];
        if ($type == 0) {
            $this->success($msg, $url, $data, $wait);
        } else {
            $this->error($msg, $url, $data, $wait);
        }
    }
}

