<?php
namespace app\agent\controller;

use think\captcha\Captcha;
use think\Controller;
use think\Db;

class Login extends Controller {
    /**
     * 代理登陆
     * @return \think\response\View
     */
	public function index() {
		$this->assign([
		    'title' => '陌陌-代理登录'
		]);
		$this->assign('go_url', input('get.s_url') ? input('get.s_url') : '/agent');
		return view();
	}
	public function logout() {
	    $key = cookie('agent_token');
	    if ($key) {
	       cookie('agent_token', null);
	       $redis = redis();
	       $userId = $redis->get($key);
	       if ($userId) {
	           if ($redis->exists('agent_'.$userId)) {
	               $redis->del('agent_' . $userId);
	           }
	           $redis->del($key);
	       }
	    }
		$this->redirect('/agent/login');
	}
	public function checklogin() {

		$post = input('post.');
		if (empty($post['username'])) {
			json_fail_admin('用户名不能为空!');
		}
		if (empty($post['password'])) {
			json_fail_admin('密码不能为空!');
		}
		$captcha = new Captcha();
		if (!$captcha->check($post['verify_code'])) {
			json_fail_admin('验证码错误!');
		}
		$where = [
		    'status' => 1,
		    'user_name' => $post['username']
		];
		$user = Db::name('agent_info')->where($where)->find();
		if (empty($user)) {
			json_fail_admin('账号或密码有误，请检查！');
		}
		if ($user['state'] != 1) {
			json_fail_admin('账号已经被禁用,请联系管理员!');
		}
		if (decode($user['login_pwd']) != $post['password']) {
			json_fail_admin('账号或密码不正确，请检查!');
		}
		$token = 'agent' . create_token();
		cookie('agent_token', $token, 20 * 60);
		$redis = redis();
		$redis->set($token, $user['id']);
		$redis->set('agent_' . $user['id'], $token);
		$redis->expire($token, 20 * 60);
		$redis->expire('agent_' . $user['id'], 20 * 60);
		json_success_admin('登录成功!', '/agent');
	}
	public function verify() {
		$config = array(
			'fontSize' => 30, // 验证码字体大小
			'length' => 4, // 验证码位数
			'useNoise' => false, // 关闭验证码杂点
			'codeSet' => '0123456789',
		);
		ob_clean();
		$captcha = new Captcha($config);
		return $captcha->entry();
	}
}
