<?php
namespace app\agent\controller;

use think\Db;
/**
 * 充值管理
 */
class Recharge extends Base
{
    /**
     * 充值
     */
    public function index() {
        $limit = $this->agent['credit_limit'];
        if (request()->isPost()) {
            $mobile = input('post.mobile');
            $credit = input('post.credit');
            $password = input('post.password');
            $memo = input('post.memo');
            if (! $mobile || ! $credit || ! is_numeric($credit) || $credit <= 0 || ! $password || mb_strlen($memo) > 100) json_fail_admin('参数错误！');
            $pattern = '/^\-?[0-9]{1,14}(\.[0-9]{1,2})?$/';
            if (! preg_match($pattern, $credit)) json_fail_admin('充值金额只支持两位小数！');
            if ($credit > $limit) json_fail_admin('您的授信额度不足，请联系管理员增加授信额度！');
            // 查询用户账号是否存在
            $where = [
                ['status', '=', 1], // 正常
                ['is_visitor', '<>', 1], // 非游客
            ];
            $user = Db::name('member_info')->where($where)->where('mobile',$mobile)->whereOr('account',$mobile)->field('id')->find();
            if (! $user)  json_fail_admin('充值账号不存在，请重新输入！');
            if (decode($this->agent['recharge_pwd']) != $password) json_fail_admin('充值密码错误，请重新输入！');
            Db::startTrans();
            try {
                $where = [
                    'user_id' => $user['id']
                ];

                //用户目前余额
                $amount = Db::name('member_amount')->where('member_id', $user['id'])->value('amount');
                if (!$amount) {
                    $amount = 0;
                }

                //生成订单号
                $order_no = CreateDingdan($user['id'], TTYPE_OL_PAY, TTYPE_OL_PAY_PROXY, rand(0, 9));

                // 代理充值记录
                $log = [
                    'order' => $order_no,
                    'agent_id' => $this->agent['id'],
                    'agent_user' => $this->agent['user_name'],
                    'user_id' => $user['id'],
                    'user_name' => $mobile,
                    'recharge_before' => $limit,
                    'recharge' => $credit,
                    'recharge_after' => $limit - $credit,
                    'recharge_date' => date('Y-m-d H:i:s'),
                    'user_before' => $amount / 100,
                    'user_after' => ($amount / 100 ) + $credit,
                    'memo' => $memo,
                    'create_time' => time(),
                    'create_user' => 0
                ];

                //代理余额修改
                $agentUpdate = [
                    'credit_limit' => $limit - $credit, //余额
                    'update_time' => time(), //时间
                    'update_user' => 0
                ];

                //处理用户流水记录及余额
                (new \app\admin\model\Trad())->addTrad($user['id'], $credit * 100 , $order_no,TTYPE_OL_PAY,TTYPE_OL_PAY_PROXY);

                Db::name('agent_info')->where('id', $this->agent['id'])->update($agentUpdate); //修改代理剩余额度

                Db::name('agent_recharge')->insert($log); //增加充值记录

                //增加充值中间表记录 用于前台用户充值记录
                Db::name('app_pay_tmp')->insert([
                    'order_id' => $order_no,
                    'member_id' => $user['id'],
                    'amount' => (int)($credit * 100),
                    'addtime' => time(),
                    'status' => 0,  //代理充值全部成功状态
                    'type' => 2  //类型 1  四方支付  2 代理充值
                ]);

                Db::commit();

            } catch (\Exception $e) {
                Db::rollback();
                json_fail_admin('充值失败！');
            }

            if (sysconf('telegram_pay_td')) {
                $showamount = $credit;
                $text = "代理充值到账\n\n订单号:{$order_no}\n\n订单金额:{$credit}\n\n用户ID:{$user['id']}\n\n代理账号:{$this->agent['user_name']}\n\n剩余额度: {$agentUpdate['credit_limit']}";
                http_post_json('https://api.telegram.org/bot1791179564:AAGdsrzvHlPzHK6Vd0ElaJPfavw3L4QQInY/sendMessage',json_encode(['chat_id'=>'-434278913','text'=>$text],true));
            }
            json_success_admin();
        }



        $this->assign('limit', $limit);
        return view();
    }

    /**
     * 充值记录
     */
    public function log() {
        $temp = $this->getLogCondition();
        $where = $temp['where'];
        $search = $temp['search'];
        $list = Db::name('agent_recharge')->where($where)->field('user_name,recharge,recharge_after,recharge_date,memo')->order('id desc')->paginate(10);
        $this->assign([
            'list' => $list,
            'search' => $search
        ]);
        return view();
    }

    /**
     * 充值记录导出
     */
    public function log_export() {
        $where = $this->getLogCondition('where');
        $list = Db::name('agent_recharge')->where($where)->field('user_name,recharge,recharge_date,recharge_after,memo')->order('id desc')->select();
        if (empty($list)) json_fail_admin('没有数据可代导出');
        $title = ['充值账号', '充值金额', '充值时间', '授信余额', '备注'];
        $this->exportExcel($list, $title, '充值记录');
    }

    /**
     * 获取记录查询 条件
     * @param string $getType
     * @return array
     */
    private function getLogCondition($getType = 'all') {
        $post = input('request.');
        $search = [
            'begin_time' => key_exists('begin_time', $post) ? $post['begin_time'] : '',
            'end_time' => key_exists('end_time', $post) ? $post['end_time'] : '',
            'user_name' => key_exists('user_name', $post) ? $post['user_name'] : '',
            'recharge' => key_exists('recharge', $post) ? $post['recharge'] : '',
            'memo' => key_exists('memo', $post) ? $post['memo'] : '',
        ];
        $pattern = '/^20[0-9]{2}\-[0|1][0-9]\-[0|1|2|3][0-9]$/';
        $where = [
            ['agent_id', '=', $this->agent['id']]
        ];
        if (preg_match($pattern, $search['begin_time'])) $where[] = ['create_time', '>=', strtotime($search['begin_time'])];
        if (preg_match($pattern, $search['end_time'])) $where[] = ['create_time', '<=', strtotime($search['end_time'] . ' 23:59:59')];
        if (! empty($search['user_name'])) $where[] = ['user_name', 'like', '%'. $search['user_name'] .'%'];
        if (! empty($search['recharge']) && is_numeric($search['recharge'])) $where[] = ['recharge', '=', $search['recharge']];
        if (! empty($search['memo']) && is_numeric($search['memo'])) $where[] = ['memo', 'like', '%' . $search['memo'] . '%'];
        if ($getType == 'all') {
            return [
                'search' => $search,
                'where' => $where
            ];
        } elseif ($getType == 'where') {
            return $where;
        } else {
            return $search;
        }
    }

    /**
     * 导出excel文件
     * @param array $data  需导出的数据
     * @param array $title 文件头，与数据对应
     * @param string $fileName 导出文件名
     */
    private function exportExcel($data, $title, $fileName = null) {
        require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();
        // 表头
        foreach ($title as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
        }
        // 写入内容
        $row = 2;
        foreach ($data as $item) {
            $column = 1;
            foreach ($item as $value) {
                $sheet->setCellValueByColumnAndRow($column, $row, $value);
                $column ++;
            }
            $row ++;
        }
        // 输出 下载内容
        $filename = $fileName ? $fileName : time();
        ob_end_clean();
        ob_start();
        header('Content-Disposition: attachment; filename="' . rawurlencode($filename) . '"');
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet; charset=UTF-8');
        header('Cache-Control: max-age=0');
        // If you're serving to IE 9, then the following may be needed
        header('Cache-Control: max-age=1');
        // If you're serving to IE over SSL, then the following may be needed
        header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
        header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
        header('Pragma: public'); // HTTP/1.0
        $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
        $writer->save('php://output');
        exit;
    }
}

