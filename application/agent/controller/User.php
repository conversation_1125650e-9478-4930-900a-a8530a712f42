<?php
namespace app\agent\controller;

use think\Db;
/**
 * 设置
 */
class User extends Base
{
    /**
     * 修改密码
     */
    public function password() {
        if (request()->isPost()) {
            // 修改密码
            $post = $this->getPasswordData();
            $update = [];
            if ($post['type'] == 1) {
                $update['login_pwd'] = $post['password'];
            } else {
                $update['recharge_pwd'] = $post['password'];
            }
            $update['update_time'] = time();
            $update['update_user'] = $this->agent['id'];
            if (! Db::name('agent_info')->where('id', $this->agent['id'])->update($update)) json_fail_admin('密码修改失败，请稍候重试或联系管理员！');
            if ($post['type'] == 1) {
                $this->setDelUserLoginInfo();
            }
            json_success_admin();
        }
        return view();
    }
    
    /**
     * 修改个人信息
     */
    public function info() {
        if (request()->isPost()) {
            $post = $this->getInfoData();
            $update = [];
            foreach ($post as $key => $value) {
                if (! key_exists($key, $this->agent)) continue;
                if ($value == $this->agent[$key]) continue;
                $update[$key] = $value;
            }
            // 修改代理昵称将检查昵称是否存在
            if (key_exists('nick_name', $update)) {
                $where = [
                    ['id', '<>', $this->agent['id']],
                    ['status', '=', 1],
                    ['nick_name', '=', $update['nick_name']]
                ];
                if (Db::name('agent_info')->where($where)->find()) json_fail_admin('此昵称已经被使用，请更换！');
            }
            if ($update) {
                $update['update_time'] = time();
                $update['update_user'] = $this->agent['id'];
                if (! Db::name('agent_info')->where('id', $this->agent['id'])->update($update)) json_fail_admin('个人信息修改失败！');
            }
            json_success_admin();
        }
        $this->assign('data', $this->agent);
        return view();
    }
    
    /**
     * 获取密码修改数据
     * @return array
     */
    private function getPasswordData() {
        // 获取提交数据
        $post = input('post.');
        $validate = [
            'ori_pwd|旧密码' => 'require|length:6,20',
            'password|新密码' => 'require|length:6,20',
            'type|数据' => 'require|between:1,2'
        ];
        $role = new \think\Validate($validate);
        if (! $role->check($post)) json_fail_admin($role->getError());
        if ($post['ori_pwd'] == $post['password']) json_fail_admin('新密码与旧密码相同！');
        // 检查原密码
        $oriPwd = $post['type'] == 1 ? $this->agent['login_pwd'] : $this->agent['recharge_pwd'];
        if (decode($oriPwd) != $post['ori_pwd']) json_fail_admin('旧密码不正确，请确认！');
        return [
            'type' => $post['type'],
            'password' => encode($post['password'])
        ];
    }
    
    /**
     * 获取修改个人信息提交数据
     * @return array
     */
    private function getInfoData() {
        // 数据检验
        $post = input('post.');
        $validate = [
            'nick_name|代理昵称' => 'require|length:2,12',
            'weixin|微信号' => 'require|/^[a-zA-Z]{1}[a-zA-Z\d_-]{5,19}$/',
            'weixin_qrcode|微信二维码图片' => 'require|length:10,200',
            'qq|QQ号' => 'require|/^[1-9]{1}[0-9]{4,11}$/',
            'qq_qrcode|QQ二维码图片' => 'require|length:5,200'
        ];
        $role = new \think\Validate($validate);
        if (! $role->check($post)) json_fail_admin($role->getError());
        return $post;
    }
    
}

