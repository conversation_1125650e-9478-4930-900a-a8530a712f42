{include file="public/header" /}
<body>
<header class="navbar-wrapper">
<div class="navbar navbar-fixed-top">
	<div class="container-fluid cl"> <a class="logo navbar-logo f-l mr-10 hidden-xs" href="/agent">{$menu.title}</a>
		<span class="logo navbar-slogan f-l mr-10 hidden-xs"><!-- v1.0 --></span>
		<a aria-hidden="false" class="nav-toggle Hui-iconfont visible-xs" href="javascript:;">&#xe667;</a>
		<nav class="nav navbar-nav">
			<ul class="cl">
				<li class="dropDown dropDown_hover">
					<a href="javascript:;" class="dropDown_A">工具 <i class="Hui-iconfont">&#xe6d5;</i></a>
				</li>
			</ul>
		</nav>
		<nav id="Hui-userbar" class="nav navbar-nav navbar-userbar hidden-xs">
			<ul class="cl">
				<li></li>
				<li class="dropDown dropDown_hover">
					<a href="javascript:void(0);" class="dropDown_A">{$agent.nick_name} <i class="Hui-iconfont">&#xe6d5;</i></a>
					<ul class="dropDown-menu menu radius box-shadow">
						<li><a href="/agent/login/logout">退出</a></li>
					</ul>
				</li>
			</ul>
		</nav>
	</div>
</div>
</header><aside class="Hui-aside">
	<div class="menu_dropdown bk_2">
		{volist name="menu.list" id="vo"}
			<dl id="menu-member">
				<dt><i class="Hui-iconfont">&nbsp;</i> {$vo.title}<i class="Hui-iconfont menu_dropdown-arrow">&#xe6d5;</i></dt>
				<dd>
					<ul>
						{if condition="isset($vo['fun'])"}
						{volist name="vo.fun" id="vvo"}
							<li><a data-href="/agent{$vvo.path}" data-title="{$vvo.title}" href="javascript:;">{$vvo.title}</a></li>
						{/volist}
						{/if}
					</ul>
				</dd>
			</dl>
		{/volist}
	</div>
</aside>
<div class="dislpayArrow hidden-xs"><a class="pngfix" href="javascript:void(0);" onClick="displaynavbar(this)"></a></div>
<section class="Hui-article-box">
	<div id="Hui-tabNav" class="Hui-tabNav hidden-xs">
		<div class="Hui-tabNav-wp">
			<ul id="min_title_list" class="acrossTab cl">
				<li class="active">
					<span title="我的桌面" data-href="/agent/index/welcome">我的桌面</span>
					<em></em>
				</li>
			</ul>
		</div>
		<div class="Hui-tabNav-more btn-group">
			<a id="js-tabNav-prev" class="btn radius btn-default size-S" href="javascript:;">
				<i class="Hui-iconfont">&#xe6d4;</i>
			</a>
			<a id="js-tabNav-next" class="btn radius btn-default size-S" href="javascript:;">
				<i class="Hui-iconfont">&#xe6d7;</i>
			</a>
		</div>
	</div>
	<div id="iframe_box" class="Hui-article">
		<div class="show_iframe">
			<div style="display:none" class="loading"></div>
			<iframe id="iframe-welcome" data-scrolltop="0" scrolling="yes" frameborder="0" src="/agent/index/welcome"></iframe>
		</div>
	</div>
</section>

<div class="contextMenu" id="Huiadminmenu">
	<ul>
		<li id="closethis">关闭当前 </li>
		<li id="closeall">关闭全部 </li>
	</ul>
</div>
{include file="public/footer" /}
</body>
</html>
