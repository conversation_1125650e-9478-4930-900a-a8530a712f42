<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8"> 
<meta http-equiv="Pragma" content="no-cache"> 
<meta http-equiv="Cache-Control" content="no-cache"> 
<meta http-equiv="Expires" content="0"> 
<title>{$title}</title> 
<link href="/static/css/login.css" type="text/css" rel="stylesheet"> 
</head>
<body> 
<div class="login">
    <div class="message">{$title}</div>
    <div id="darkbannerwrap"></div>
    <form method="post" action="javascript:;">
		<input name="action" value="login" type="hidden">
		<input name="username" id="username"  placeholder="代理账号"  type="text" autocomplete="off">
		<hr class="hr15">
		<input name="password" id="password"  placeholder="登录密码"  type="password">
		<hr class="hr15">
		<input name="verify_code" id="verify_code"  placeholder="验证码"  type="text" autocomplete="off"style="width:46%;">
		<img id="reload_verify_code" src="/admin/login/verify" style="width:46%;float: right;" onclick="reload_verify();">
		<hr class="hr15">
		<input value="登录" style="width:100%;" type="submit" onclick="check_form();">
		<hr class="hr20">
	</form>
</div>

<script type="text/javascript" src="/static/lib/jquery/1.9.1/jquery.min.js"></script>
<script type="text/javascript" src="/static/lib/layer/2.4/layer.js"></script>
<script>
	var goUrl = '{$go_url}';
	function reload_verify(){
		$("#reload_verify_code").attr("src","/agent/login/verify?rand="+Math.random());
	}
	function check_form(){
		var data = {
			'username':$("#username").val(),
			'password':$("#password").val(),
			'verify_code':$("#verify_code").val(),
		};
		if(!data.username){
			layer.msg("代理账号不能为空",function(){});
			return false;
		}
		if(!data.password){
			layer.msg("登录密码不能为空",function(){});
			return false;
		}
		if(!data.verify_code){
			layer.msg("验证码不能为空",function(){});
			return false;
		}
		var index = layer.load(4);
		$.post("/agent/login/checklogin",data,function(json){
			layer.close(index);
			if(json.code==0) {
				layer.msg(json.msg);
				setTimeout(function(){window.location.href = goUrl;},500);
			}else{
				layer.msg(json.msg,function(){});
				reload_verify();
			}
		},'json').error(function(){
			layer.msg('error!',function(){});
		});
		return false;
	}
</script>
</body>
</html>