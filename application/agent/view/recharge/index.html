{include file="public/header" /}
<body>
    <article class="page-container">
    	<div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-3">
    		<h3>我的授信额度剩余：<span class="c-red" id="limit">{$limit}</span>&nbsp;元</h3>
    	</div>
    	<p>&nbsp;</p>
        <form action="javascript:;" class="form form-horizontal" id="form-admin">
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4"><span class="c-red">*</span> 充值账号：</label>
                <div class="formControls col-xs-4 col-sm-5">
                    <input type="text" placeholder="请输入您的充值的用户账号" autocomplete="off" class="input-text" value="" name="mobile">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4"><span class="c-red">*</span>充值金额：</label>
                <div class="formControls col-xs-4 col-sm-5" style="position:relative;">
                    <input type="text" autocomplete="off" placeholder="请输入需要充值的金额" class="input-text" value="0" name="credit">
                </div>
                <div class="formControls col-xs-1 col-sm-1">元</div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4"><span class="c-red">*</span>充值密码：</label>
                <div class="formControls col-xs-4 col-sm-5" style="position:relative;">
                    <input type="password" placeholder="请输入您的充值密码" autocomplete="off" class="input-text" value="" name="password">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4">备注：</label>
                <div class="formControls col-xs-4 col-sm-5" style="position:relative;">
                	<textarea name="memo" class="textarea"></textarea>
                </div>
            </div>
            <div class="row cl">
                <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-3">
                    <input class="btn btn-success radius" onclick="javascript:form_submit();" id="submit" type="submit" value="&nbsp;&nbsp;提&nbsp;交&nbsp;&nbsp;">
                    <input class="btn btn-danger radius ml-30" type="reset" value="&nbsp;&nbsp;取&nbsp;消&nbsp;&nbsp;">
                </div>
            </div>
        </form>
    </article>
    {include file="public/footer" /}

    <script type="text/javascript">
    	function form_submit() {
    		var mobile = $.trim($("input[name='mobile']").val());
    		var credit = $.trim($("input[name='credit']").val());
    		var password = $("input[name='password']").val();
    		var memo = $.trim($("textarea[name='memo']").val());
    		var limit = parseFloat($("#limit").text());
    		// 判断数据输入
    		if (mobile.length == 0) {
    			layer.alert('充值账号不能为空 ！');
    			return false;
    		}
    		if (isNaN(credit) || credit <= 0) {
    			layer.alert('请输入正确的充值金额金额 ！');
    			return false;
    		}
    		if (credit > limit) {
    			layer.alert('您的授信额度不足，请联系管理员增加授信额度。');
    			return false;
    		}
    		if (password.length < 6 || password.length > 20) {
    			layer.alert('充值密码不正确！');
    			return false;
    		}
    		if (memo.length > 100) {
    			layer.alert('备注最多只能100个字符 ！');
    			return false;
    		}
    		// 数据提交
    		var index = layer.msg('正在提交中..', {
                icon: 16
                ,shade: 0.2
                ,time: false
                ,shadeClose: false
            }); 
            $("#submit").attr("disabled","disabled");
    		$.ajax({
    			url:'',
    			type:'post',
   			    data: {
   			    	'mobile' 	: mobile,
   			    	'credit' 	: credit,
   			    	'password' 	: password,
   			    	'memo' 		: memo
   			    },
   			    dataType:'json',
   		        success:function(res) {
   		        	layer.close(index);
   		            if(res.code == 0){
   		                layer.msg('充值成功！');
   		             	setTimeout(function () { window.location.reload(); }, 500);
   		            } else if (res.code == 2) {
   		            	layer.alert(res.msg, function() {
   		            		window.parent.location.reload();
   		            	});
   		            }else{
                        $('#submit').attr("disabled",false);
   		                layer.alert(res.msg);
   		            }
   		        },
   		        error:function() {
   		        	layer.close(index);
   		        	$('#submit').attr("disabled",false);
   		        	layer.alert('操作失败！');
   		        }
    		});
    		return false;
    	}
    	
    </script>
</body>

</html>