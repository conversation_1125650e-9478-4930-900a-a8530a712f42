{include file="public/header" /}
<body>
    <div class="page-container">
        <form action="/agent/recharge/log" method="post">
            <div class="text-c">
            	<span class="mr-5">充值时间</span>
            	<input type="text" autocomplete="off" onfocus="WdatePicker({maxDate:''})" name="begin_time" value="{$search.begin_time|default=''}" class="input-text Wdate" style="width:100px;" placeholder="开始时间" readonly>
            	<input type="hidden" name="begin_time_old" value="{$search.begin_time|default=''}">
                <span class="ml-5 mr-5">至</span>
                <input type="text" autocomplete="off" onfocus="WdatePicker({maxDate:''})" name="end_time" value="{$search.end_time|default=''}" class="input-text Wdate" style="width:100px;" placeholder="结束时间" readonly>
                <input type="hidden" name="end_time_old" value="{$search.end_time|default=''}">
                <span class="ml-20 mr-5">代理账号</span>
                <input type="text" autocomplete="off" class="input-text" style="width:100px" placeholder="充值账号" value="{$search.user_name|default=''}" name="user_name">
                <input type="hidden" name="user_name_old" value="{$search.user_name|default=''}">
                <span class="ml-20 mr-5">备注关键词</span>
                <input type="text" autocomplete="off" class="input-text" style="width:100px" placeholder="备注关键词" value="{$search.memo|default=''}" name="memo">
                <input type="hidden" name="memo_old" value="{$search.memo|default=''}">
                <span class="ml-20 mr-5">充值金额</span>
                <input type="text" autocomplete="off" class="input-text" style="width:100px" placeholder="充值金额" value="{$search.recharge|default=''}" name="recharge">
                <input type="hidden" name="recharge_old" value="{$search.recharge|default=''}">
                <button type="submit" class="btn btn-success ml-20" onclick="return search();"><i class="Hui-iconfont">&#xe665;</i> 查询</button>
                <a class="btn btn-primary" href="javascript:;" data-url="/agent/recharge/log_export" onclick="export_excel(this);"><i class="Hui-iconfont">&#xe644;</i> 导出</a>
                <a href="/agent/recharge/log" class="btn btn-danger"><i class="Hui-iconfont">&#xe68f;</i>清空</a>
            </div>
        </form>
        <div class="cl pd-5 bg-1 bk-gray mt-20">
            <span class="r">共有数据：<strong id="dataTotal">{$list->total()}</strong> 条</span>
        </div>
        <style>
            /* 滚动条凹槽的颜色，还可以设置边框属性 */
            .scroll::-webkit-scrollbar-track-piece {
                background-color:#f8f8f8;
            }
            /* 滚动条的宽度 */
            .scroll::-webkit-scrollbar {
                width:9px;
                height:9px;
            }
            /* 滚动条的设置 */
            .scroll::-webkit-scrollbar-thumb {
                background-color:#dddddd;
                background-clip:padding-box;
                min-height:28px;
            }
            .scroll::-webkit-scrollbar-thumb:hover {
                background-color:#bbb;
            }
        </style>
        <div id="topScroll" class="scroll" style="width: 100%;overflow:auto;"></div>
        <div id="scroll" class="scroll" style="width:100%;overflow:auto;">
        <table id="scrollTable" class="table table-border table-bordered table-bg  table-hover" style="min-width:100%;">
            <thead>
                <tr class="text-c">
                	<th>充值账号</th>
                	<th>充值金额</th>
                	<th>充值时间</th>
                	<th>授信余额</th>
                	<th>备注</th>
                </tr>
            </thead>
            <tbody>
                {volist name="list" id="vo"}
                <tr class="text-c">
                    <td>{$vo.user_name}</td>
                    <td>{$vo.recharge}</td>
                    <td>{$vo.recharge_date}</td>
                    <td>{$vo.recharge_after}</td>
                    <td>{$vo.memo}</td>
                </tr>
                {/volist}
            </tbody>
        </table>
        </div>
        {$list->render()|raw}
    </div>
    {include file="public/footer" /}
    <script type="text/javascript">
	    layer.config({ shadeClose: false });
	    function search() {
	    	var beginTime = $.trim($("input[name='begin_time']").val());
	    	var beginTimeOld = $.trim($("input[name='begin_time_old']").val());
	    	var endTime = $.trim($("input[name='end_time']").val());
	    	var endTimeOld = $.trim($("input[name='end_time_old']").val());
	    	var userName = $.trim($("input[name='user_name']").val());
	    	var UserNameOld = $.trim($("input[name='user_name_old']").val());
	    	var memo = $.trim($("input[name='memo']").val());
	    	var memoOld = $.trim($("input[name='memo_old']").val());
	    	var recharge = $.trim($("input[name='recharge']").val());
	    	var rechargeOld = $.trim($("input[name='recharge_old']").val());
// 	    	if (beginTime==beginTimeOld && endTime==endTimeOld && memo==memoOld && userName==UserNameOld && recharge==rechargeOld) {
// 	    		return false;
// 	    	}
	    	var reg = /^20[0-9]{2}\-[0|1][0-9]\-[0|1|2|3][0-9]$/;
	    	if (beginTime) {
	    		if (! reg.test(beginTime)) {
	    			layer.alert('充值开始时间非法！');
	    			return false;
	    		}
	    	}
	    	if (endTime) {
	    		if (! reg.test(endTime)) {
	    			layer.alert('充值结束时间非法！');
	    			return false;
	    		}
	    	}
	    	if (credit) {
	    		if (isNaN(credit)) {
	    			layer.alert('充值金额不正确！');
	    			return false;
	    		}
	    	}
	    	return true;
	    }
	    function export_excel(obj) {
	    	var data = parseInt($('#dataTotal').text());
	    	if (data <= 0) {
	    		layer.msg('无数据可供导出');
	    		return false;
	    	}
	    	var url = $(obj).attr('data-url');
	    	var beginTime = $.trim($("input[name='begin_time_old']").val());
	    	var endTime = $.trim($("input[name='end_time_old']").val());
	    	var memo = $.trim($("input[name='memo_old']").val());
	    	var userName = $.trim($("input[name='user_name_old']").val());
	    	var recharge = $.trim($("input[name='recharge_old']").val());
	    	url = url + '?begin_time='+beginTime+'&end_time='+endTime+'&memo='+memo+'&user_name='+userName+'&recharge='+recharge;
	    	window.location.href=url;
	    }
	    
    </script>
</body>

</html>