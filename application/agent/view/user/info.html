{include file="public/header" /}
<body>
    <article class="page-container">
        <form action="javascript:;" class="form form-horizontal" id="form-admin">
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4">昵称</label>
                <div class="formControls col-xs-5 col-sm-6">
                    <input type="text" placeholder="请输入昵称" autocomplete="off" class="input-text" value="{$data.nick_name|default=''}" name="nick_name">
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4">微信号</label>
                <div class="formControls col-xs-5 col-sm-6">
                    <input type="text" placeholder="请输入微信号" autocomplete="off" class="input-text" value="{$data.weixin|default=''}" name="weixin">
                	 <p class="c-999">6-20位的字母、数字、下划线或中划线且只能以字母开头 </p>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4">微信二维码</label>
                <div class="formControls col-xs-5 col-sm-6">
                	<img class="weixin_img_src" src="{$data.weixin_qrcode|readBase}" height="100" alt="">
                    <span class="btn-upload">
                        <a href="javascript:void();" class="btn btn-primary radius"><i class="Hui-iconfont">&#xe642;</i> 上传微信二维码图片</a><span class="ml-20">仅支持jpg、png格式</span>
                        <input type="file" name="weixin_file" class="input-file" accept=".jpg,.jpeg,.png">
                    </span>
                    <input type="hidden" id="weixin_img" value="{$data.weixin_qrcode|default=''}" >
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4">QQ号</label>
                <div class="formControls col-xs-5 col-sm-6">
                    <input type="text" placeholder="请输入QQ号" autocomplete="off" class="input-text" value="{$data.qq|default=''}" name="qq">
                    <p class="c-999">5-12位数字 </p>
                </div>
            </div>
            <div class="row cl">
                <label class="form-label col-xs-5 col-sm-4">QQ二维码</label>
                <div class="formControls col-xs-5 col-sm-6">
                	<img class="qq_img_src" src="{$data.qq_qrcode|readBase}" height="100" alt="">
                    <span class="btn-upload">
                        <a href="javascript:void();" class="btn btn-primary radius"><i class="Hui-iconfont">&#xe642;</i> 上传QQ二维码图片</a><span class="ml-20">仅支持jpg、png格式</span>
                        <input type="file" name="qq_file" class="input-file" accept=".jpg,.jpeg,.png" >
                    </span>
                    <input type="hidden" id="qq_img"  value="{$data.qq_qrcode|default=''}" >
                </div>
            </div>
            <div class="row cl">
                <div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-3">
                    <input class="btn btn-success radius" data-op="" data-commit="1" onclick="javascript:form_submit();" id="submit" type="submit" value="&nbsp;&nbsp;提&nbsp;交&nbsp;&nbsp;">
                </div>
            </div>
        </form>
    </article>
    {include file="public/footer" /}

    <script type="text/javascript">
    	// var fileurl = "{$Think.config.file.host}";
    	$("input[name='weixin_file']").on('change', function() {
            var file = this.files[0];
            if (! file) {
                return false;
            }
            var ext=$(this).val().substr($(this).val().lastIndexOf(".")).toLowerCase();
            if (ext != '.jpg' && ext != '.jpeg' && ext != '.png') {
            	layer.alert('微信二维码仅支持JPG、PNG格式图片！');
            	return false;
            }
            uploadImg(file, 'weixin_img', 'weixin_img');
        });
    	$("input[name='qq_file']").on('change', function() {
            var file = this.files[0];
            if (! file) {
                return false;
            }
            var ext=$(this).val().substr($(this).val().lastIndexOf(".")).toLowerCase();
            if (ext != '.jpg' && ext != '.jpeg' && ext != '.png') {
            	layer.alert('QQ二维码仅支持JPG、PNG格式图片！');
            	return false;
            }
            uploadImg(file, 'qq_img', 'qq_img');
        });
    	function form_submit() {
    		var nickName = $.trim($("input[name='nick_name']").val());
    		var weixin = $.trim($("input[name='weixin']").val());
    		var weixinQrcode = $("#weixin_img").val();
    		var qq = $.trim($("input[name='qq']").val());
    		var qqQrcode = $("#qq_img").val();
    		// 判断数据输入
    		if (check_input(nickName, 2, 12, '代理昵称', true, true) == false) return false;
    		reg = /^[a-zA-Z]{1}[a-zA-Z\d_-]{5,19}$/;
    		if (! reg.test(weixin)) {
    			layer.alert('微信号不符合规则 ！');
    			return false;
    		}
    		reg = /^[1-9]{1}[0-9]{4,11}$/;
    		if (! reg.test(qq)) {
    			layer.alert('QQ号不符合规则 ！');
    			return false;
    		}
    		// 选择文件
    		if (! weixinQrcode) {
    			layer.alert('请上传微信二维码图片！');
    			return false;
    		}
    		if (! qqQrcode) {
    			layer.alert('请上传QQ二维码图片！');
    			return false;
    		}
    		// 数据提交
    		var index = layer.msg('提交中..', {
                icon: 16
                ,shade: 0.2
                ,time: false
                ,shadeClose: false
            }); 
            $("#submit").attr("disabled","disabled");
    		$.ajax({
    			url:'',
    			type:'post',
   			    data: {
   			    	'nick_name' 	: nickName,
   			    	'weixin' 		: weixin,
   			    	'weixin_qrcode' : weixinQrcode,
   			    	'qq' 			: qq,
   			    	'qq_qrcode' 	: qqQrcode,
   			    },
   			    dataType:'json',
   		        success:function(res) {
   		        	layer.close(index);
   		            if(res.code == 0){
   		                layer.msg('修改成功！');
   		             	window.location.reload();
   		            } else if (res.code == 2) {
   		            	layer.alert(res.msg);
   		            	window.location.href = '/agent/login';
   		            } else {
                        $('#submit').attr("disabled",false);
   		                layer.alert(res.msg);
   		            }
   		        },
   		        error:function() {
   		        	layer.close(index);
   		        	$('#submit').attr("disabled",false);
   		        	layer.alert('修改失败，请稍候重试！');
   		        }
    		});
    		return false;
    	}
    	
    	// 检查输入
    	function check_input(str, min, max, name, checkSpace, checkSpecial) {
    		// 长度是否匹配
    		if (str.length < min || str.length > max) {
    			layer.alert(name + '长度为' + min + '至' + max + '个字符！');
    			return false;
    		}
    		// 是否含有空格
    		if (checkSpace) {
    			var reg1 = /\s/, reg2 = /[\u3000]/;
	    		if (reg1.test(str) || reg2.test(str)) {
	    			layer.alert(name + '中不能包含空格');
	    			return false;
	    		}
    		}
    		// 特殊字符
    		if (checkSpecial) {
    			var reg = /[!@#$%^&*！@#￥%……&~·`]/;
    			if (reg.test(str)) {
    				layer.alert(name + '中不能含有特殊字符');
    				return false;
    			}
    			
    		}
    		return true;
    	}
    </script>
</body>
</html>