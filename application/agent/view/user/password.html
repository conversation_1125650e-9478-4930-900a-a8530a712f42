{include file="public/header" /}
<body>
<article class="page-container">
	<form class="form form-horizontal" onsubmit="return false">
	<h3>登录密码</h3>
	<div class="row cl">
		<label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>旧密码</label>
		<div class="formControls col-xs-8 col-sm-9">
			<input type="password" autocomplete="off" class="input-text" value="" name="login_ori_pwd" id="login_ori_pwd">
		</div>
	</div>
	<div class="row cl">
		<label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>新密码</label>
		<div class="formControls col-xs-8 col-sm-9">
			<input type="password" class="input-text" autocomplete="off" value="" id="login_pwd" name="login_pwd">
			<p class="c-999">6-20位至少包含字母和数字的字符，不能含有空格</p>
		</div>
	</div>
	<div class="row cl">
		<label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>确认新密码</label>
		<div class="formControls col-xs-8 col-sm-9">
			<input type="password" class="input-text" autocomplete="off" value="" id="login_pwd2" name="login_pwd2">
		</div>
	</div>
	<div class="row cl mb-30">
		<div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-3">
			<input class="btn btn-success radius" onclick="password_submit(this, 1)" type="submit" value="&nbsp;&nbsp;提交&nbsp;&nbsp;">
		</div>
	</div>
	
	<hr />
	<h3>充值密码</h3>
	<div class="row cl">
		<label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>旧密码</label>
		<div class="formControls col-xs-8 col-sm-9">
			<input type="password" autocomplete="off" class="input-text" value="" name="recharge_ori_pwd" id="recharge_ori_pwd">
		</div>
	</div>
	<div class="row cl">
		<label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>新密码</label>
		<div class="formControls col-xs-8 col-sm-9">
			<input type="password" class="input-text" autocomplete="off" value="" id="recharge_pwd" name="recharge_pwd">
			<p class="c-999">6-20位的字符，不能含有空格</p>
		</div>
	</div>
	<div class="row cl">
		<label class="form-label col-xs-4 col-sm-3"><span class="c-red">*</span>确认新密码</label>
		<div class="formControls col-xs-8 col-sm-9">
			<input type="password" class="input-text" autocomplete="off" value="" id="recharge_pwd2" name="recharge_pwd2">
		</div>
	</div>
	<div class="row cl mb-30">
		<div class="col-xs-8 col-sm-9 col-xs-offset-4 col-sm-offset-3">
			<input class="btn btn-success radius" onclick="password_submit(this, 2)" type="submit" value="&nbsp;&nbsp;提交&nbsp;&nbsp;">
		</div>
	</div>
	</form>
</article>
{include file="public/footer" /}
<script type="text/javascript">
// 提交
function password_submit(obj, type) {
	var name, oriPwd, pwd, pwd2;
	if (type == 1) {
		name = '登录密码';
		var oriPwd = $('#login_ori_pwd').val();
		var pwd = $('#login_pwd').val();
		var pwd2 = $('#login_pwd2').val();
	} else if (tpye == 2) {
		name = '充值密码';
		var oriPwd = $('#recharge_ori_pwd').val();
		var pwd = $('#recharge_pwd').val();
		var pwd2 = $('#recharge_pwd2').val();
	}
	if (! check_password(oriPwd, 1, name + '旧密码')) return false;
	if (! check_password(pwd, 1, name + '新密码')) return false;
	if (pwd != pwd2) {
		layer.alert(name + '新密码两次输入不一致！');
		return false;
	}
	if (oriPwd == pwd) {
		layer.alert(name + '新密码与旧密码相同！');
		return false;
	}
	// 数据提交
	var index = layer.msg('提交中...', {
        icon: 16
        ,shade: 0.2
        ,time: false
        ,shadeClose: false
    }); 
    $(obj).attr("disabled","disabled");
	$.ajax({
		url:'',
		type:'post',
		    data: {
		    	'ori_pwd' 	: oriPwd,
		    	'password' 	: pwd,
		    	'type' 		: type
		    },
		    dataType:'json',
	        success:function(res) {
	        	layer.close(index);
	            if(res.code == 0){
	                if (type == 1) {
	                	layer.msg(name + '修改成功，请重新登录！', {}, function() {
	                		window.location.href= '/agent/login';
	                	});
	                } else {
	                	layer.msg(name + '修改成功！');
	                	window.location.reload();
	                }
	            } else if (res.code == 2) {
	            	layer.alert(res.msg);
	            	window.location.href = '/agent/login';
	            }else{
                	$(obj).attr("disabled", false);
	                layer.alert(res.msg);
	            }
	        },
	        error:function() {
	        	layer.close(index);
	        	$(obj).attr("disabled",false);
	        	layer.alert(name + '修改失败！');
	        }
	});
}

//检查密码
function check_password(pwd, type, name) {
	if (pwd.length == 0) {
		layer.alert(name + '不能为空！');
		return false;
	}
	// 长度是否匹配
	if (pwd.length < 6 || pwd.length > 20) {
		layer.alert(name + '长度为6-20个字符！');
		return false;
	}
	// 是否含有空格
	var reg1 = /\s/, reg2 = /[\u3000]/;
	if (reg1.test(pwd) || reg2.test(pwd)) {
		layer.alert(name + '中不能包含空格');
		return false;
	}
	// 登录密码至少包含字母和数字
	if (type == 1) {
		var reg3 = /[0-9]/, reg4 = /[a-zA-Z]/;
		if (! reg3.test(pwd) || ! reg4.test(pwd)) {
			layer.alert(name + '至少包含字母和数字！');
			return false;
		}
	}	
	return true;
}
</script> 
<!--/请在上方写此页面业务相关的脚本-->
</body>
</html>