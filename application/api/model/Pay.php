<?php
namespace app\api\model;

use app\common\Constants\LogConst;
use app\common\Constants\RedisConst;
use app\extend\Telegram\TelegramBot;
use app\Helper\AppDiffHelper;
use app\Helper\CodeHelper;
use app\Helper\CommonHelper;
use app\Helper\MemberInfoHelper;
use app\Service\AppPayOrderService;
use app\Service\PayChannelService;
use \think\Db;
use think\Log;

/**
 * 支付
 */
class Pay
{
	public $pays = [];

	function __construct()
	{
		$this->pays  = config('pay.');
	}
	/**
	 * <AUTHOR>
	 * @DateTime 2020-09-16
	 * @param    [type]     $payConfig[支付方式id]
	 * @param    [type]     $orderId [订单号]
	 * @return   [type]              [description]
	 */
	public function pay($payconfig, $order)
	{
		//增加入款表
		Db::name('app_pay_tmp')->insert([
			'order_id' => $order['order_id'],
			'member_id' => $order['member_id'],
			'amount' => $order['order_amount'],
			'addtime' => $order['addtime'],
			'status' => 1, //状态 等待
			'type' => 1 //类型 四方支付
		]);

		$order['order_amount']  = $order['order_amount'] / 100;
		include $payconfig["name_code"].".php";

		$payModel = new $payconfig["name_code"]($payconfig,$order);

        try {
            return $payModel->payment();
        }catch (\Throwable $e){
            commonLog(LogConst::PAY_ERROR[1],['channel'=>$this->pays[$payconfig['name_code']]['name'].'-'.$payconfig['channel'],'error'=>$e->getMessage().$e->getFile().$e->getLine()],LogConst::PAY_ERROR[0]);
            if(!AppDiffHelper::checkLimitAPP(AppDiffHelper::FUN_PAY_WAY[0])){
                CommonHelper::$return_is_exit=true;
                json_fail($e->getMessage().' '.remove_app_path($e->getFile()).$e->getLine());
            }
        }

        if(AppDiffHelper::checkLimitAPP(AppDiffHelper::FUN_PAY_WAY[0])){
            $where=[
                ['status','=',1],
                ['pay_type','=',$payconfig['pay_type']],
                ['is_chess','=',$payconfig['is_chess']],
                ['id','<>',$payconfig['id']],
            ];
            $app_pay_list = Db::name('app_pay')->where($where)->cache(60)->order('sort','desc')->select();

            $is_fail=true;
            foreach ($app_pay_list as $k=>$v){
                unset($payModel);
                include $v["name_code"].".php";
                $payModel = new $v["name_code"]($v,$order);
                try {
                    $res = $payModel->payment();
                    $is_fail=false;
                    $uporder['pay'] = $v['name_code'];
                    $uporder['pay_id'] = $v['id'];
                    $uporder['pay_type'] = $v['pay_type'];
                    $uporder['addtime'] = time();
                    $uporder['info'] = $this->pays[$v['name_code']]['name'] . ' - ' . $v['show_name'];
                    Db::name("app_pay_order")->where('order_id',$order["order_id"])->update($uporder);
                    return $res;
                }catch (\Throwable $e){
                    commonLog(LogConst::PAY_ERROR[1],['channel'=>$this->pays[$v['name_code']]['name'].'-'.$v['channel'],'error'=>$e->getMessage().$e->getFile().$e->getLine()],LogConst::PAY_ERROR[0]);
                }
            }
            CommonHelper::$return_is_exit=true;
        }

        json_fail('该支付方式维护中，换个付款方式试试看呢!!');;
	}


    /**
     * 支付结果telegram通知
     */
    public function sendTelegramNotice($order_id)
    {
        $detail = Db::name('app_pay_order')->where('order_id',$order_id)->find();
        if(!empty($detail)){
            $member_info_detail = Db::name('member_info')->where('id',$detail['member_id'])->find();
        }else{
            $member_info_detail = [];
        }

        $app_pay_detail = Db::name('app_pay')->where('id',$detail['pay_id'])->find();

        $pay_config = PayChannelService::spliceChannel();
//        dump($pay_config[$app_pay_detail['name_code']]);die();
//        $pay_config[$app_pay_detail['name_code']]['channels'][$app_pay_detail['channel']];
        $detail['payer_info'] = ($pay_config[$app_pay_detail['name_code']]['name']??'') .' \\- '. ($pay_config[$app_pay_detail['name_code']]['channels'][$app_pay_detail['channel']]??'');
        $message = self::makeOrderBody($detail,$member_info_detail);
        $chat_id = sysconf('order_notice_chat_id');
        TelegramBot::sendMessage($chat_id,$message);

    }


    /**
     *
     * 生成订单Body
     *
     * @param array $params
     * @return string
     *
     */
    public static function makeOrderBody(array $order_detail,array $member_info_detail): string
    {
        $order_status = [
            1=> '未支付',
            0=> '成功',
            2=> '支付失败',
        ];

        $add_time = date('Y\\\-m\\\-d H:i:s',$order_detail['addtime']);
        $is_first = $order_detail['is_first']==0?'否':'是';
        $order_status_name = $order_status[$order_detail['status']]??'';
        $pay_amount = $order_detail['pay_amount']/100;
        $member_info_detail['nick_name'] = str_replace('_','\_',$member_info_detail['nick_name']);
        $nick_name = $member_info_detail['nick_name'].'  \('.$member_info_detail['id'].'\)';
        if($order_detail['status']==0){
            $order_status_name="*[{$order_status_name}](www.收钱成功！.com) *";
        }

        $device_type_name = MemberInfoHelper::DEVICE_TYPE[$member_info_detail['device_type']]??'未知';

        $order_type_name = AppPayOrderService::getConstPluck('ORDER_TYPE_')[$order_detail['order_type']]??'未知';



        return <<<text
订单号：{$order_detail['order_id']}
时间：{$add_time}
状态：{$order_status_name}
金额：{$pay_amount}
订单类型：{$order_type_name}
是否首冲：{$is_first}
支付渠道：{$order_detail['payer_info']}
用户昵称：{$nick_name}
用户渠道：{$member_info_detail['channelid']}
用户来源：{$device_type_name}
text;

    }



	public function notify($name_code)
	{

		if (!isset($this->pays[$name_code])) {
			json_fail('非法请求!');
		}
		include $name_code.".php";


		$payModel = new $name_code();
		if($name_code=='JvDing'){
	        $tmp = file_get_contents("php://input");
	        payLog('回调数据-pay',[$tmp],'JvDing');
			$re = $payModel->notify($tmp);
		}else{
			$re = $payModel->notify();
		}
		return $re;

	}


}
