<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;


class redisuser extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('redisuser')->setDescription('清理缓存中的已被清理的数据');
    }

//    protected function execute(Input $input, Output $output)
//    {
//        Db::name('rds_del')->field('device_id,id')->chunk(30000,function ($list){
//            $r=redis();
//            $delkeys=array_column($list,'device_id');
//            $r->del($delkeys);
//        });
//    }
}
