<?php

namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

class upgradeDB extends Command
{
    protected function configure()
    {
        // 指令配置
        $this->setName('upgrade');
        // 设置参数

    }

//    protected function execute(Input $input, Output $output)
//    {
//        $sql="ALTER table hg_equity_basic ADD column day_preview_times TINYINT(1) default(0) comment '每日试看'";
//        $a=Db::execute($sql);
//        var_dump($a);
//    }
}
