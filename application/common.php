<?php

use app\common\Constants\RedisConst;
use app\common\exception\InsideBusinessException;
use app\extend\Telegram\TelegramBot;
use app\Helper\JaegerHelper;
use GeoIp2\Database\Reader;
use think\Db;
use app\EasyAes;
use OSS\OssClient;
use OSS\Core\OssException;
use app\Helper\StaticHelper;
use think\facade\Log;

const XOR_KEY='hOhJqJZRyBa5'; //异或运算加密密钥

const CDN_URL_TAG = '******************************'; //cdn域名临时替换的标签

function uploadImgToOss($path){
    $content = fileToBase64($path);
    $accessKeyId = sysconf('oos_ak') ?: "LTAI4G8MYMvPMNUtV8DCKHWg";
    $accessKeySecret = sysconf('oos_sk') ?: "******************************";
    $endpoint = sysconf('oos_point') ?: "http://oss-cn-hongkong.aliyuncs.com";
    $bucket= sysconf('oos_bucket') ?: "video-kumi";
    $object = "kmupload/".date("Ymd").'/'.date('His').randomkeys(8);
    try{
        $ossClient = new OssClient($accessKeyId, $accessKeySecret, $endpoint);

        $ossClient->putObject($bucket, $object, $content);
    } catch(OssException $e) {
        return [
            'code' => 1,
            'message' => $e->getMessage()
        ];
    }
    return [
        'code' => 0,
        'message' => '上传成功!',
        'data' => trim(source_config()['img_url'],'/').'/'.$object
    ];
}

/** php 发送流文件
 * @param String $file 要发送的文件
 * @return array
 */

function uploadImgServer(string $file){

    try{

        $aHeader = [];
        $file_upload_url = sysconf('file_upload_url') ?: "";

        $ch = curl_init($file_upload_url);

        // 创建一个 CURLFile 对象
        $cfile = curl_file_create($file);

        //设置请求头数据
        curl_setopt($ch, CURLOPT_HTTPHEADER, $aHeader);
        // 设置 POST 数据
        $data = array('file' => $cfile,'url_type' =>2,'img_type' =>1);
        curl_setopt($ch, CURLOPT_POST,1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);

        curl_setopt($ch, CURLOPT_RETURNTRANSFER,true);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, '0');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, '0');
        $result_orgin = curl_exec($ch);

        commonLog("uploadImgServer",[$result_orgin],"uploadImgServer");
        $result = json_decode($result_orgin,true);

    } catch(Throwable $e) {
        commonLog("uploadImgServer:error:",['error'=>$e->getMessage().$e->getFile().$e->getLine()],"uploadImgServer");
        return [
            'code' => 1,
            'message' => $e->getMessage()
        ];
    }

//    dump($result_orgin);die();
    $img_url = $result['data']['obj_access_url']??'';
    if(!empty($img_url)){
        $img_url = replaceCdnUrlImg($img_url);
    }else{
        return [
            'code' => -1,
            'message' => '上传失败!',
            'data' => json_encode($result_orgin,JSON_UNESCAPED_UNICODE),
        ];
    }
    return [
        'code' => 0,
        'message' => '上传成功!',
        'data' => $img_url,
    ];

}

function uploadImgToMinio($path){
//    dump($path);die();
    $content = fileToBase64($path);
    $path_url = UPLOAD_PATH . '/uploads/'.uniqid().'.ced';
    file_put_contents($path_url,$content);
    $file_upload_url = sysconf('file_upload_url') ?: "";
    $file_upload_token = sysconf('file_upload_token') ?: "";

    try{
        $result = fileRequestPost($file_upload_url,$path_url,['Accept-Token:'.$file_upload_token]);
        $result = json_decode($result,true);
    } catch(Throwable $e) {
        return [
            'code' => 1,
            'message' => $e->getMessage()
        ];
    }


    return [
        'code' => 0,
        'message' => '上传成功!',
        'data' => $result['data']['obj_access_url'],
    ];
}

/**
 * 文件上传新方法
 * @param string $url
 * @param string $filePath
 * @return bool|string
 */
function fileRequestPost(string $url, string $filePath, array $header=[]){
    $ch = curl_init();
    $data     = array('file' => new \CURLFile(realpath($filePath)));

//    dump([$data,$filePath,$header]);die();

    if(!empty($header)){
        curl_setopt($ch, CURLOPT_HTTPHEADER, $header);
    }
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1); // 获取的信息以文件流的形式返回

    $result = curl_exec($ch);

    commonLog("fileRequestPost",[$result],"fileRequestPost");

    return $result;
}

function randomkeys($length)
{
   $pattern = '1234567890abcdefghijklmnopqrstuvwxyz
               ABCDEFGHIJKLOMNOPQRSTUVWXYZ';
    $key = '';
    for($i=0;$i<$length;$i++)
    {
        $key .= $pattern{mt_rand(0,35)};
    }
    return $key;
}
function fileToBase64($file){
    $base64_file = '';
    if(file_exists($file)){
        $base64_file = base64_encode(file_get_contents($file));
    }
    return $base64_file;
}
//点击广告统计
function advclick($area,$uid){

    $month = date('Ym');
    $ckey = 'advclick_'.$area;
    $redis = redis();
    $advclick = cacheGet($ckey);
    if (!$advclick) {
        if (!Db::name('app_adv_click')->where('area',$area)->where('month',$month)->find()) {
            $insert = [
                'area' => $area,
                'month' => $month,
            ];
            Db::name('app_adv_click')->insert($insert);
        }
        $expire = strtotime('+1 month', strtotime(date('Y-m', time()) . '-01 00:00:00')) - time();
        cacheSet($ckey,1,$expire);
    }
    $redis_key = 'advclick_'.$area.'_'.$month;
    $redis->pfadd($redis_key,[$uid]);
    $redis->expire($redis_key,86400*31+200);
    $click = $redis->pfcount('advclick_'.$area.'_'.$month); //区域月点击总数
    $sql = Db::name('app_adv_click')->where('area',$area)->where('month',$month)->fetchSql()->update(['click'=>$click]);  // 区域月点击总数
    pushSql($sql);
}
//视频分类播放统计
function category_click($cate_id,$uid){

    $month = date('Ym');
    $ckey = 'category_click_'.$cate_id;
    $redis = redis();
    $advclick = cacheGet($ckey);
    if (!$advclick) {
        if (!Db::name('app_category_click')->where('cate_id',$cate_id)->where('month',$month)->find()) {
            $insert = [
                'cate_id' => $cate_id,
                'month' => $month,
            ];
            Db::name('app_category_click')->insert($insert);
        }
        $expire = strtotime('+1 month', strtotime(date('Y-m', time()) . '-01 00:00:00')) - time();
        cacheSet($ckey,1,$expire);
    }
    //播放总数加1
    $sql = Db::name('app_category_click')->where('cate_id',$cate_id)->where('month',$month)->fetchSql()->setInc('num_play');
    pushSql($sql);

    $category_click_key = 'category_click:'.$cate_id.':'.$month;
    $category_click_lock_key = $category_click_key.':'.$uid;
    //播放人数
    $redis->pfadd($category_click_key,[$uid]);
    $click = $redis->pfcount('category_click:'.$cate_id.':'.$month); //分类点击人数
    if($redis->setnx($category_click_lock_key,1)){
        $redis->expire($category_click_lock_key,2764800); //有效期32天
        $sql = Db::name('app_category_click')->where('cate_id',$cate_id)->where('month',$month)->fetchSql()->update(['num_member'=>$click]);  // 分类月播放人总数
        pushSql($sql);
    }


}

/**
 * 渠道视频分类统计方法
 * $type_name 统计类型 category_click 分类点击 preview 试看播放 free_play 免费播放 play 视频播放
 * @return void
 * @throws RedisException
 * @throws \think\Exception
 * @throws \think\db\exception\DataNotFoundException
 * @throws \think\db\exception\ModelNotFoundException
 * @throws \think\exception\DbException
 * @throws \think\exception\PDOException
 */
function channel_classify_count($type_name,$cate_id,$uid,$channel_id)
{
    if(!empty($channel_id)){

        $date = date('Y-m-d');

        $where = [
            'category_id'=>$cate_id,
            'channel_id'=>$channel_id,
            'date'=>$date,
        ];
        $redis = redis();
        $redis_key= 'channel_classify_count_'.md5(json_encode($where));
        $advclick = cacheGet($redis_key);
        if (!$advclick) {
            if (!Db::name('channel_classify')->where($where)->find()) {
                $insert = $where;
                Db::name('channel_classify')->insert($insert);
            }
            cacheSet($redis_key,1,86410);
        }

        //播放总数加1
        $sql = Db::name('channel_classify')->where($where)->fetchSql()->setInc($type_name.'_pv');
        pushSql($sql);

        $category_click_lock_key = $redis_key.':'.$uid;
        //播放人数
        $redis->sadd($redis_key,$uid);
        $click = $redis->scard($redis_key);
        if($redis->setnx($category_click_lock_key,1)){
            $redis->expire($category_click_lock_key,86410);
            $sql = Db::name('channel_classify')->where($where)->fetchSql()->update([$type_name.'uv'=>$click]);
            pushSql($sql);
        }
    }
}



//推送渠道数据方法
function channeldata($type = 'install',$channelid , $amount = 0,$time=0){

    if(!empty($time)){
        $date = date('Ymd',$time);
        $month = date('Ym',$time);
    }else{
        $date = date('Ymd');
        $month = date('Ym');
    }

    //初始化渠道信息及今日数据
    $ckey = 'channel_'.$date.'_'.$channelid;

    $channel = cacheGet($ckey);

    if (!$channel) {

        //key 过期时间为24点
        $expire = strtotime(date("Y-m-d",strtotime("+1 day"))." 00:00:00") - time();
        cacheSet($ckey,1,$expire);

        try {
            //删除昨日redis数据
            $yesterday = date('Ymd',strtotime('-1 day'));
            redis()->del("up_member_{$yesterday}_{$channelid}");
            redis()->del("comment_member_{$yesterday}_{$channelid}");
            redis()->del("active_{$yesterday}_{$channelid}");
            redis()->del("active_{$yesterday}_{$channelid}_visitor");
            redis()->del("click_ob_{$yesterday}_{$channelid}");
            redis()->del("pay_member_{$yesterday}_{$channelid}");
            if (!Db::name('channel')->where('channelid',$channelid)->where('month',$month)->find()) {
                $insert = [
                    'channelid' => $channelid,
                    'month' => $month,
                ];
                Db::name('channel')->insert($insert);
            }
            if (!Db::name('channel_day')->where('date',date('Ymd'))->where('channelid',$channelid)->find()) {
                $total_install = Db::name('channel_day')->where('channelid',$channelid)->where('date',date("Ymd",strtotime("-1 day")))->value('total_install');   //取昨天的总安装
                if (!$total_install) {
                    $total_install = 0;
                }

                $total_reg = Db::name('channel_day')->where('channelid',$channelid)->where('date',date("Ymd",strtotime("-1 day")))->value('total_reg');   //取昨天的总安装
                if (!$total_reg) {
                    $total_reg = 0;
                }
                $insert = [
                    'date' => date('Ymd'),
                    'channelid' => $channelid,
                    'total_install' => $total_install,
                    'total_reg' => $total_reg,
                ];
                $re = Db::name('channel_day')->insert($insert);
            }
        }catch (\Throwable $e){
            redis()->del($ckey);
        }

    }


    $condition = [
        'date' => $date,
        'channelid' => $channelid
    ];

    switch ($type) {
        case 'install':   //初始化游客 (安装)
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('install'); // 安装量加1
            pushSql($sql);

//            $device_type = request()->header('device_type');
//            if ($device_type == 'A') {
//                $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('install_'.strtolower($device_type)); // 指定设备类型安装量加1
//                pushSql($sql);
//            }

            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('total_install'); // 安装量加1
            pushSql($sql);
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->setInc('install'); // 渠道总安装量加1
            pushSql($sql);

            //代理系统下载量统计
            \think\Queue::push('app\job\AgentDownloadCountJob', ['channelid' => $channelid, 'created_at' => date('Y-m-d H:i:s')], 'AgentDownloadCountJob');

            break;
        case 'reg':  //注册量
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('reg'); // 注册量加1
            pushSql($sql);
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('total_reg'); // 注册量加1
            pushSql($sql);
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->setInc('reg'); // 渠道总注册量加1
            pushSql($sql);

            $device_type = get_header_device_type();
            if ($device_type == 'A') {
                $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('reg_'.strtolower($device_type)); // 指定设备类型注册量加1
                pushSql($sql);
            }

            break;
        case 'order_count':  //订单总数
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('order_count'); // 订单总数
            pushSql($sql);
            break;
        case 'order_count_success':  //成功订单总数
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('order_count_success'); // 成功订单总数
            pushSql($sql);
            break;
        case 'recharge': //充值总额
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('recharge',$amount); // 日充值总额
            pushSql($sql);
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('all_recharge',$amount); // 日充值总额(总和)
            pushSql($sql);
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->setInc('recharge',$amount); // 月充值总额
            pushSql($sql);

            $all_pay = redis()->scard('pay_member_'.$date.'_'.$channelid);
            $all_all_pay = $all_pay+(redis()->scard('pay_member_all'.$date.'_'.$channelid));
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->update(['all_pay'=>$all_pay,'all_all_pay'=>$all_pay]); // 日充值人数
            pushSql($sql);
            $pay_member = redis()->scard('pay_member_'.$month.'_'.$channelid);
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->update(['recharge_member'=>$pay_member]); // 月充值人数
            pushSql($sql);

            break;
        case 'all_recharge': //充值总额(包括点播和ai以及未来其它的总和)
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('all_recharge',$amount); // 日充值总额(总和)
            pushSql($sql);
            $all_all_pay = redis()->scard('pay_member_all'.$date.'_'.$channelid);
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->update(['all_all_pay'=>$all_all_pay]); // 日充值人数
            pushSql($sql);
            break;

        case 'first_pay': //首充

            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('first_recharge',$amount); // 今日首充总额
            pushSql($sql);
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('first_pay'); //  今日首充人数加一
            pushSql($sql);


            //月首充总额
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->setInc('first_recharge',$amount);
            pushSql($sql);
            //月首充人数加一
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->setInc('first_recharge_member');
            pushSql($sql);
            break;
        // case 'all_pay': //充值人数
        //     $all_pay = redis()->scard('pay_member_'.$date.'_'.$channelid);
        //     $sql = Db::name('channel_day')->where($condition)->fetchSql()->update(['all_pay'=>$all_pay]); // 日充值人数
        //     pushSql($sql);

        //     $pay_member = redis()->scard('pay_member_'.$month.'_'.$channelid);
        //     $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->update(['recharge_member'=>$pay_member]); // 月充值人数
        //     pushSql($sql);
        case 'all_first_pay': //首充(包括点播和ai以及未来其它的总和)
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('all_first_recharge',$amount); // 今日首充总额
            pushSql($sql);
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->setInc('all_first_pay'); //  今日首充人数加一
            pushSql($sql);
        case 'click_ob':  //点击欧宝
            $click_ob = redis()->scard('click_ob_'.$date.'_'.$channelid);
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->update(['click_ob'=>$click_ob]); // 点击欧宝人数修改
            pushSql($sql);
            break;
        case 'up_member':  //点赞人数
            $up_member = redis()->scard('up_member_'.$date.'_'.$channelid);
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->update(['up_member'=>$up_member]); // 点赞人数
            pushSql($sql);
            $up_member = redis()->scard('up_member_'.$month.'_'.$channelid);
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->update(['up'=>$up_member]);  // 月点赞人数
            pushSql($sql);
            break;
        case 'comment_member':  //评论人数
            $comment_member = redis()->scard('comment_member_'.$date.'_'.$channelid);
            $sql = Db::name('channel_day')->where($condition)->fetchSql()->update(['comment_member'=>$comment_member]); // 日评论人数修改
            pushSql($sql);

            $comment_member = redis()->scard('comment_member_'.$month.'_'.$channelid); //月评论人数
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->update(['comment'=>$comment_member]);  // 月评论人数
            pushSql($sql);
            break;
        case 'active':
            $active = redis()->scard('active_'.$date.'_'.$channelid); //日活跃
            $active_visitor = redis()->scard('active_'.$date.'_'.$channelid.'_visitor'); //日活跃游客

            $sql = Db::name('channel_day')->where($condition)->fetchSql()->update(['active'=>$active,'active_visitor'=>$active_visitor]); // 日活跃修改

            pushSql($sql);
            $active = redis()->pfcount('active_'.$month.'_'.$channelid);  //月活跃人数
            $active_visitor = redis()->pfcount('active_'.$month.'_'.$channelid.'_visitor');  //月活跃游客
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->update(['active'=>$active,'active_visitor'=>$active_visitor]);  // 月活跃人数
            pushSql($sql);
            break;
        case 'month_recharge': //用注册用户充值人数与金额
            $month_pay = redis()->scard('month_pay_'.$month.'_'.$channelid); //本月注册充值人数
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->update(['month_pay'=>$month_pay]); // 本月注册充值人数
            pushSql($sql);

            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->setInc('month_recharge',$amount); // 本月注册充值总额
            pushSql($sql);
            break;
        case 'month_vip_recharge':  //月注册用户购买vip人数与金额
            $month_vip = redis()->scard('month_vip_'.$month.'_'.$channelid); //本月注册购买vip人数
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->update(['month_vip'=>$month_vip]); //本月注册购买vip人数
            pushSql($sql);

            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->setInc('month_vip_recharge',$amount); // 月注册用户购买vip金额
            pushSql($sql);
            break;
        case 'month_movie_recharge':  //月注册用户购买视频人数与金额
            $month_movie = redis()->scard('month_movie_'.$month.'_'.$channelid); //本月注册购买视频人数
            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->update(['month_movie'=>$month_movie]); //本月注册购买视频人数
            pushSql($sql);

            $sql = Db::name('channel')->where('channelid',$channelid)->where('month',$month)->fetchSql()->setInc('month_movie_recharge',$amount); // 月注册用户购买vip金额
            pushSql($sql);
            break;
        default:
            # code...
            break;
    }

}


function random_split($total_num,$total_copies){
    $result = []; //结果
    for($i=$total_copies;$i>0;$i--){
        $ls_num=0;
        $num = 0;
        if($total_num > 0){
            if($i==1){
                $num += $total_num;
            }else{
                $max_num = floor($total_num/$i);
                $ls_num = mt_rand(1,$max_num);
                $num += $ls_num;
            }
        }
        $result[] = $num;
        $total_num -= $ls_num;
    }
    shuffle($result); //打乱数组
    return $result;
}


function reqHost(){

    $fixed_domain = sysconf('api_url');
    if(!empty($fixed_domain)){
        return $fixed_domain;
    }
//    if(!empty(env('KUMI_BACKEND_HOST'))){
//        return env('KUMI_BACKEND_HOST');
//    }
    $http_type = ((isset($_SERVER['HTTPS']) && strtolower($_SERVER['HTTPS']) == 'on') || (isset($_SERVER['HTTP_X_FORWARDED_PROTO']) && strtolower($_SERVER['HTTP_X_FORWARDED_PROTO']) == 'https')) ? 'https://' : 'http://';
    return $http_type . $_SERVER['HTTP_HOST'];
}
// 获取当前页面的URL
function curPageURL() {

    if (empty($_SERVER['HTTP_CF_VISITOR'])) {
        $pageURL = 'http';
    }else {
        $schema = json_decode($_SERVER['HTTP_CF_VISITOR'], true);
        $pageURL = $schema['scheme'];
    }

  	$pageURL .= "://";
    $_SERVER["SERVER_PORT"]=$_SERVER["SERVER_PORT"]??'';
    $_SERVER["REQUEST_URI"]=$_SERVER["REQUEST_URI"]??'';
    $_SERVER["HTTP_HOST"]=$_SERVER["HTTP_HOST"]??'';
  	if ($_SERVER["SERVER_PORT"] != "80") {
    	$pageURL .= $_SERVER["HTTP_HOST"] . ":" . $_SERVER["SERVER_PORT"] . $_SERVER["REQUEST_URI"];
  	} else {
	    $pageURL .= $_SERVER["HTTP_HOST"] . $_SERVER["REQUEST_URI"];
	}
  	return $pageURL;
}

function isaccount($str) {
 if (preg_match('/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,11}$/',$str)){
  return true;
 }else {
  return false;
 }
}



function limitApi($Uid = '', $ApiName = '',$Time = '',$ApiLimit = ''){
    $key = $ApiName.$Uid;
    $redis = redis();
    $check = $redis->exists($key);
    if ($check) {
        $redis->incr($key);
        $count = $redis->get($key);
        if ($count > $ApiLimit) {
            return false;
        }
    }else {
        $redis->incr($key);

        $redis->expire($key,$Time);
    }
    return true;
}

function add_querystring_var($url, $key, $value) {
	 $url=preg_replace('/(.*)(?|&)'.$key.'=[^&]+?(&)(.*)/i','$1$2$4',$url.'&');
	 $url=substr($url,0,-1);
	 if(strpos($url,'?') === false){
	  return ($url.'?'.$key.'='.$value);
	 } else {
	  return ($url.'&'.$key.'='.$value);
	 }
}

function check_phone_num($phone_num) {
    if (preg_match("/^1((34[0-8]\d{7})|((3[0-3|5-9])|(4[5-7|9])|(5[0-3|5-9])|(6[5|6])|(7[2-3|5-8])|(8[0-9])|(9[1|8|9|5]))\d{8})$/", $phone_num)) {
        return true;
    } else {
        return false;
    }
}

function pushSql($sql,$key = 'sql')
{

//    switch ($key){
//        case 'sql':
//            Db::execute($sql);
//            break;
//        default:
            $redis = redis();
            $redis->rpush($key,$sql);
//    }

}

function gsettot($key){
    $redis = redis();
    $num = 1  ;
    $key='gsettot:'.$key;
    if($redis->get($key)){
        $num = $redis->incrBy($key,1);
        if($num > 20){
            $redis->del($key);
        }
    }else{
        $redis->set($key,1);
    }
    return $num ;
}



function sysconf($key, $value = null,$tag = null) {

    if ($value !== null) {
        $redis = redis();
        $redis->del(config("cache.prefix").'sysconf');
        if (Db::name('system_config')->where('key',$key)->cache(60)->find()) {
            return Db::name('system_config')->where('key',$key)->update(['value'=>$value]);
        }else{
            return Db::name('system_config')->insert(['key'=>$key,'value'=>$value]);
        }
    }

    if(!empty(StaticHelper::$system_config['data']) && StaticHelper::$system_config['time']>=(time()-300)){
        $config = StaticHelper::$system_config['data'];
    }else{
        $config = cacheGet('sysconf');
        if (!$config) {
            $config = Db::name('system_config')->column('key,value'); //以key为key 以value 为value
            cacheSet('sysconf',$config);
        }

        StaticHelper::$system_config=[
            'data'=>$config,
            'time'=>time(),
        ];
    }

    if ($key == 'apiurl' && !$value && !$tag && isset($config[$key])) {
        $apiurl = array_filter(explode(';',$config[$key]));
        $rand = array_rand($apiurl);
        return $apiurl[$rand];
    }
    if ($key == 'ob_white_ip' && !$value && !$tag && isset($config[$key])) {
        $ob_white_ip = array_filter(explode(';',$config[$key]));

        if (!$ob_white_ip) {
            return '**************';
        }
        $rand = array_rand($ob_white_ip);
        return $ob_white_ip[$rand];
    }
    return isset($config[$key]) ? $config[$key] : '';
}


/**
 * 业务信息配置拓展表
 * @param $key
 * @param $value
 * @param $tag
 * @return int|mixed|string
 */
function businessConfig($key, $value = null) {

    if ($value !== null) {
        $redis = redis();
        $redis->del(config("cache.prefix").'businessConfig');
        if (Db::name('business_config')->where('business_key',$key)->cache(60)->find()) {
            return Db::name('business_config')->where('business_key',$key)->update(['business_value'=>$value]);
        }else{
            return Db::name('business_config')->insert(['business_key'=>$key,'business_value'=>$value]);
        }
    }

    if(!empty(StaticHelper::$business_config['data']) && StaticHelper::$business_config['time']>=(time()-300)){
        $config = StaticHelper::$business_config['data'];
    }else{
        $config = cacheGet('businessConfig');
        if (!$config) {
            $config = Db::name('business_config')->column('business_key,business_value'); //以key为key 以value 为value
            cacheSet('businessConfig',$config);
        }

        StaticHelper::$business_config=[
            'data'=>$config,
            'time'=>time(),
        ];
    }

    return $config[$key] ?? '';
}

function com_create_guid() {
  return sprintf( '%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
    mt_rand( 0, 0xffff ), mt_rand( 0, 0xffff ),
    mt_rand( 0, 0xffff ),
    mt_rand( 0, 0x0fff ) | 0x4000,
    mt_rand( 0, 0x3fff ) | 0x8000,
    mt_rand( 0, 0xffff ), mt_rand( 0, 0xffff ), mt_rand( 0, 0xffff )
  );
}
#直播post
function live_curl($url, array $post = [], int $timeout = 3 , array $header = []){
    debugLive('直播', $post);
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    //模拟用户信息
    curl_setopt($curl, CURLOPT_USERAGENT, 'Mozilla/5.0 (compatible; MSIE 10.0; Windows NT 6.1; Trident/6.0)');
    //跟踪重定向页面
    curl_setopt($curl, CURLOPT_FOLLOWLOCATION, 1);
    //自动设置referer
    curl_setopt($curl, CURLOPT_AUTOREFERER, 1);
    if ($post) {
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, http_build_query($post));
    }
    if ($header) {
        curl_setopt($curl, CURLOPT_HTTPHEADER, $header);
    }
    //文件流形式返回
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($curl, CURLOPT_TIMEOUT, $timeout);
    $data = curl_exec($curl);
    if (curl_errno($curl)) {
        return false;
    }
    curl_close($curl);
    return json_decode($data,true);
}
function live_sign($uname, $ruleid,$time,$changeid){
    $origin = sprintf("changeid=%s&uname=%s&time=%s&ruleid=%s", $changeid,$uname,$time,$ruleid);
    return md5($origin . config('live.liveServer'));
}
function live_changeid(){
    //订购日期
    $order_date = date('Y-m-d');
    //订单号码主体（YYYYMMDDHHIISSNNNNNNNN）
    $order_id_main = date('YmdHis') . rand(10000000,99999999);
    //订单号码主体长度
    $order_id_len = strlen($order_id_main);
    $order_id_sum = 0;
    for($i=0; $i<$order_id_len; $i++){
        $order_id_sum += (int)(substr($order_id_main,$i,1));
    }
    //唯一订单号码（YYYYMMDDHHIISSNNNNNNNNCC）
   return $order_id = $order_id_main . str_pad((100 - $order_id_sum % 100) % 100,2,'0',STR_PAD_LEFT);
}


function http_post_json($url, $jsonStr)
{
    $ip = sysconf('ob_white_ip');
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonStr);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3);  //超时时间
    curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json; charset=utf-8',
            'Content-Length: ' . strlen($jsonStr),
            'CLIENT-IP: ' . request()->ip(),
            'X-FORWARDED-FOR: '.$ip
        )
    );
    $response = curl_exec($ch);
    // $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    return $response;
}
//创建订单
function CreateDingdan($accid, $type = 1, $subtype = 1, $i = 1) {
    return env('ORDER_PREFIX','').sprintf("%s%02d%02d%04X%04X", date("ymdHis"), $type, $subtype, $accid, $i);
}

//创建游戏订单
function CreateGameDingdan($accid, $type = 1, $subtype = 1, $i = 1) {
    return env('ORDER_PREFIX','').sprintf("%s%02d%02d%04X%04X", date("ymdHis"), $type, $subtype, $accid, $i);
}

/**
 * 获取通用订单号
 * @return string
 * @throws RedisException
 */
function getOrderNo() {
    $redis = redis();
    $inc_num = $redis->incr(\app\common\Constants\RedisConst::GET_ORDER_NO_NUM_KEY);
    if($inc_num>99999){
        $inc_num=1;
        $redis->setex(\app\common\Constants\RedisConst::GET_ORDER_NO_NUM_KEY,60,$inc_num);
    }
    if($inc_num==2)$redis->expire(\app\common\Constants\RedisConst::GET_ORDER_NO_NUM_KEY,60);
    return getMillisecond().sprintf("%04d", $inc_num);
}

//随机字符
function alnum($len = 6)
{
	$pool = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
	return substr(str_shuffle(str_repeat($pool, ceil($len / strlen($pool)))), 0, $len);
}

/** 旧日志方式，一弃用，以后打日志不要用这个方法 */
function logInfo(array $data,string $fileName,string $msg, $folder)
{
    $data['log_classification']='logInfo';
    commonLog($msg,$data,$fileName);
}

/** 旧日志方式，一弃用，以后打日志不要用这个方法 */
function debug($msg = "", $data = []) {
    if(is_array($data)){
        $data['log_classification']='debug';
    }elseif (is_string($data)){
        $data.=',log_classification:debug';
        $data=[$data];
    }

    commonLog($msg,$data);
}

/** 旧日志方式，一弃用，以后打日志不要用这个方法 */
function payLog($msg = "", $data = [],$pay_tag='') {
    $data['log_classification']='payLog';
    commonLog($msg,$data,$pay_tag);
}

/** 暂时先统一使用这个方法打日志 */
function commonLog($msg = "", array $data = [],  $pay_tag='') {
    $log_level = 'info';
    if(!empty($data['error'])){
        $log_level = 'error';
    }
    Log::write(array_merge($data,[
        \app\common\Constants\LogConst::LOG_LEVEL[$log_level]??'',
        $pay_tag=>$msg
    ]),$log_level,true);
}

//function commonLog($msg = "", $data = [],$pay_tag='') {
//    $path = LOGS_PATH  . '/common/'.date("Ym").'/';
//
//    $file = $path .'-'. date("d") . ".log";
//
//    if (!file_exists($path)) {
//        mkdir($path, 0777, true);
//    }
//    $time = date("H:i:s");
//    $a = str_replace("\\/", "/", json_encode($data, JSON_UNESCAPED_UNICODE));
//    @file_put_contents($file, $time . " " . $msg. $pay_tag . " => " . $a . "\r\n", FILE_APPEND);
//}


function debugLive($msg = "", $data = []) {
    $path =  LOGS_PATH  . '/debugLive/' ;
    $file = $path. date("Y-m-d") . ".log";
    if (!file_exists($path)) {
        mkdir($path, 0777, true);
    }
    $time = date("Y-m-d H:i:s");
    $a = str_replace("\\/", "/", json_encode($data, JSON_UNESCAPED_UNICODE));
    @file_put_contents($file, $time . ":" . $msg . " => " . $a . "\r\n", FILE_APPEND);
}
function debug1($msg = "", $data = []) {
    $file = LOGS_PATH  . '/debug/token.log';
    $time = date("Y-m-d H:i:s");
    $data['msg'] = $msg;
    $data['time'] = $time;
    $a = str_replace("\\/", "/", json_encode($data, JSON_UNESCAPED_UNICODE));
    @file_put_contents($file,$a . "\r\n", FILE_APPEND);
}

//金额类型
function getTradTypeStr($type, $subtype = 1) {
	$tradType = config("paytype.");
	$type_str = '';
	$subtype_str = '';
	foreach($tradType as $key => $val) {
		if ($val['type'] == $type) {
			$type_str = $val['name'];
			foreach($val['sub_type'] as $k => $v) {
				if ($v['type'] == $subtype) {
					$subtype_str = $v['name'];
				}
			}
		}
	}

	if ($subtype_str == '') {
		if ($type_str == '') {
			return "未知";
		}else{
			return $type_str;
		}
	}
	return $type_str." - ".$subtype_str;
}

//金额类型(游戏钱包)
function getGameTradTypeStr($type, $subtype = 1) {
    $tradType = \app\Service\GameAmountRecordService::TYPES_LIST;
    $type_str = '';
    $subtype_str = '';
    foreach($tradType as $key => $val) {
        if ($val['type'] == $type) {
            $type_str = $val['name'];
            foreach($val['sub_type'] as $k => $v) {
                if ($v['type'] == $subtype) {
                    $subtype_str = $v['name'];
                }
            }
        }
    }

    if ($subtype_str == '') {
        if ($type_str == '') {
            return "未知";
        }else{
            return $type_str;
        }
    }
    return $type_str." - ".$subtype_str;
}

function check_num($number,$type = 1){
	if(!preg_match("/^[".$type."-9][0-9]*$/",$number)){
		return false;
	}
	if($number < 0){
		return false;
	}
	return true;
}

function timestr($time)
{
	$rule = [
		'i',
		'j',
		'k',
		'l',
		'm',
		'n',
		'o',
		'p',
		'q',
		'r'
	];

	$arr = str_split($time);
	foreach ($arr as $key => &$value) {
		foreach ($rule as $k => $v) {
			if ($value == $v) {
				$value = $k;
				break;
			}
		}
	}
	return implode('', $arr);
}
function redis()
{

    static $redis;
    $cache = config('cache.');
    $_link=$cache['host']. $cache['port']. $cache['expire'];
    $_link=md5($_link);
    if(!isset($redis[$_link])){

        if(!empty(JaegerHelper::$tracer)){
            $jaegerHelper = JaegerHelper::getClass();
            $jaegerHelper->startActiveSpan(JaegerHelper::ACTIVE_SPANS['redis.connect']);
        }

        $redis[$_link] = new Redis();
        $redis[$_link]->pconnect($cache['host'], $cache['port'], $cache['expire']);
        if (isset($cache['password'])) {
            $redis[$_link]->auth($cache['password']);
        }
        $redis[$_link]->select($cache['select']);

        if(!empty(JaegerHelper::$tracer)){
            $jaegerHelper = JaegerHelper::getClass();
            $jaegerHelper->close();
        }
    }

    return $redis[$_link];
}

/**
 * 设置缓存（字符串或数字类型的）
 * @param $key
 * @param $value
 * @param int $expire
 * @return void
 * @throws RedisException
 */
function cacheStringSet($key, $value, int $expire = 60)
{
     cacheSet($key,$value,$expire,false);
}

/**
 * 查询缓存（字符串或数字类型的）
 * @param $key
 * @return void
 * @throws RedisException
 */
function cacheStringGet($key)
{
    return cacheGet($key,false);
}

/**
 * 设置缓存（任何类型）
 * @param $key
 * @param $value
 * @param $expire
 * @param bool $is_serialize
 * @return void
 * @throws RedisException
 */
function cacheSet($key, $value, $expire = 60, bool $is_serialize=true)
{

    static $redis;
    if (!$redis) {
        $redis = redis();
    }

    if($is_serialize)$value = serialize($value);
    $original_value = $value ;
    $key=config("cache.prefix").$key;

    if(!empty(JaegerHelper::$tracer)){
        $jaegerHelper = JaegerHelper::getClass();
        $jaegerHelper->startActiveSpan(JaegerHelper::ACTIVE_SPANS['redis.set']);
    }

    $redis->set($key,$value,$expire);

    if(!empty(JaegerHelper::$tracer)){
        $jaegerHelper = JaegerHelper::getClass();
        $jaegerHelper->log(['set_key'=>$key,'value'=>$original_value]);
        $jaegerHelper->close();
    }

    return;
}


/**
 * 获取缓存（任何类型）
 * @param $key
 * @param $is_serialize
 * @return false|mixed|Redis|string
 * @throws RedisException
 */
function cacheGet($key,$is_serialize=true)
{
    static $redis;
    if (!$redis) {
        $redis = redis();
    }
    $key=config("cache.prefix").$key;

    if(!empty(JaegerHelper::$tracer)){
        $jaegerHelper = JaegerHelper::getClass();
        $jaegerHelper->startActiveSpan(JaegerHelper::ACTIVE_SPANS['redis.get']);
    }
    $value = $redis->get($key);

    if(!empty(JaegerHelper::$tracer)){
        $jaegerHelper = JaegerHelper::getClass();
        $jaegerHelper->log(['get_key'=>$key]);
        $jaegerHelper->close();
    }
    if($is_serialize)$value = unserialize($value);
    return $value;
}
function cacheDel($key)
{
    static $redis;
    if (!$redis) {
        $redis = redis();
    }
    $key=config("cache.prefix").$key;

    if(!empty(JaegerHelper::$tracer)){
        $jaegerHelper = JaegerHelper::getClass();
        $jaegerHelper->startActiveSpan(JaegerHelper::ACTIVE_SPANS['redis.del']);
    }

    $redis->del($key);

    if(!empty(JaegerHelper::$tracer)){
        $jaegerHelper = JaegerHelper::getClass();
        $jaegerHelper->log(['del_key'=>$key]);
        $jaegerHelper->close();
    }
}

function uploadFile($file)
{
	$host = source_config();
	$url = 'http://kumilist.ovideo-dev.com/other/uploadCover';
	if (isset($host['img_upload']) && $host['img_upload']) {
		$url = $host['img_upload'];
	}
    $appid = '207';
    $timestamp = getMillisecond();
    $videoId = '00000';
    $sign = strtoupper(md5('appid='.$appid.'&timestamp='.$timestamp.'&videoId='.$videoId.'2sUT5rzJIw'));

    $param = [
        'file' => $file,
        'appid' => $appid,
        'timestamp' => $timestamp,
        'videoId' => $videoId,
        'sign' => $sign
    ];
    debug('图片上传',array_merge($param,['url'=>$url]));
    $ch = curl_init();

    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, 1 );
    curl_setopt($ch, CURLOPT_POSTFIELDS, $param);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
    $response = curl_exec($ch);
    curl_close($ch);
    $data = json_decode($response,true);
    return $data;
}
//HTTP请求（支持HTTP/HTTPS，支持GET/POST）
function http_request($url, $data = null)
{
    $curl = curl_init();
    curl_setopt($curl, CURLOPT_URL, $url);
    curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, FALSE);
    curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, FALSE);
    if (!empty($data)){
        curl_setopt($curl, CURLOPT_POST, 1);
        curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
    }
    curl_setopt($curl, CURLOPT_RETURNTRANSFER, TRUE);
    $output = curl_exec($curl);
    curl_close($curl);
    return $output;
}


// 发送HTTP请求
function sendRequest($url, $accessKeyId, $accessKeySecret, $params) {
    // 生成签名
    $params['Action'] = 'SendSms';
    $params['Version'] = '2017-05-25';
    $params['RegionId'] = 'cn-hangzhou';
    $params['Format'] = 'JSON';
    $params['AccessKeyId'] = $accessKeyId;
    $params['SignatureMethod'] = 'HMAC-SHA1';
    $params['SignatureVersion'] = '1.0';
    $params['SignatureNonce'] = uniqid();
    $params['Timestamp'] = gmdate('Y-m-d\TH:i:s\Z');
    ksort($params);
    $canonicalizedQueryString = '';
    foreach ($params as $key => $value) {
        $canonicalizedQueryString .= '&' . urlencode($key) . '=' . urlencode($value);
    }
    $canonicalizedQueryString = substr($canonicalizedQueryString, 1);
    $stringToSign = 'GET&%2F&' . urlencode($canonicalizedQueryString);
    $signature = base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret . '&', true));

    // 构造请求URL
    $requestUrl = $url . '?' . $canonicalizedQueryString . '&Signature=' . urlencode($signature);

    // 发送HTTP请求
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $requestUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);
    curl_close($ch);

    return $response;
}


function sendSmsRequest($accessKeyId, $params) {
    $url = "https://dysmsapi.aliyuncs.com/?";
    $url .= http_build_query($params);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_HEADER, false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    $response = curl_exec($ch);
    curl_close($ch);
    return json_decode($response, true);
}

function sendInfo(string $mobile, string $code){

    \app\Helper\CacheHelper::limitRate(\app\common\Constants\RedisConst::SEND_SMS_LIMIT_RATE_KEY.$mobile,'',60);

//    $statusStr = array(
//        "0" => "短信发送成功",
//        "-1" => "参数不全",
//        "-2" => "服务器空间不支持,请确认支持curl或者fsocket，联系您的空间商解决或者更换空间！",
//        "30" => "密码错误",
//        "40" => "账号不存在",
//        "41" => "余额不足",
//        "42" => "帐户已过期",
//        "43" => "IP地址限制",
//        "50" => "内容含有敏感词"
//    );

    $smsapi = "http://api.smsbao.com/";
    $user = sysconf('smsbao_user_id');
    $pass = sysconf('smsbao_pass');
    $template = sysconf('smsbao_template');
    if(empty($template))$template="【尊敬的会员】您的注册验证码是：{code}，5分钟内有效，请勿泄露给他人。";
    $content = str_replace('{code}', $code, $template);
    $sendurl = $smsapi."sms?u=".$user."&p=".$pass."&m=".$mobile."&c=".urlencode($content);
    $result =file_get_contents($sendurl) ;
    if($result=="0"){
        return json_encode(['msg_id'=>1]);
    }else{
        commonLog(\app\common\Constants\LogConst::SEND_INFO_KEY_LOG[1],['result'=>$result, 'mobile'=>$mobile,'content'=>$content],\app\common\Constants\LogConst::SEND_INFO_KEY_LOG[0]);
        if ($result=='41'){
            if (!\app\Helper\CacheHelper::limitRate(\app\common\Constants\RedisConst::SMS_ALARM_KEY,'',21600,false)){
                TelegramBot::sendMessageText(sysconf('business_note_chat_id'),"短信宝短信余量已耗尽，请速速充值");
            }
        }
    }
}

function sendInfoAli(string $mobile, string $code){

    \app\Helper\CacheHelper::limitRate(\app\common\Constants\RedisConst::SEND_SMS_LIMIT_RATE_KEY.$mobile,'',60);

    $accessKeyId = sysconf('ali_sms_access_key_id');
    $accessKeySecret = sysconf('ali_sms_access_key_secret');

    $signName = '武汉市领曼盾数字科技';
    $templateCode = 'SMS_323655052';

    $phoneNumber = $mobile;

// 构造请求参数
    $params = array(
        'PhoneNumbers' => $phoneNumber,
        'SignName' => $signName,
        'TemplateCode' => $templateCode,
        'TemplateParam' => json_encode(array('code' => $code)), // 替换为你的模板参数
    );

// 发送请求
    $response = sendRequest('https://dysmsapi.aliyuncs.com/', $accessKeyId, $accessKeySecret, $params);
    $response = json_decode($response,true);

    if(!empty($response['Message']) && $response['Message']=='OK' ){
        return json_encode(['msg_id'=>1]);
    }
//    dump($response);die();
//    return $response;
}


function sendInfoJiGuang(string $mobile, string $code){

    \app\Helper\CacheHelper::limitRate(\app\common\Constants\RedisConst::SEND_SMS_LIMIT_RATE_KEY.$mobile,'',60);

//    $url = 'https://api.sms.jpush.cn/v1/codes';
//    $account = 'OA00143';
//    $password = strtoupper(md5('123456'));
//    $content = '你本次的验证码为'.$code. ' 【比优比yh】';

//    $body=array(
//		'action'=>'send',
//		'userid'=>'',
//		'account'=>$account,
//		'password'=>$password,
//		'mobile'=>$mobile,
//		'extno'=>'',
//		'content'=>$content,
//		'sendtime'=>''
//	);
    $body=[
        'temp_id'=>1,
        'mobile'=>$mobile,
//        'temp_para'  => ['code'=>$code],
    ];

    $ch=curl_init();
	curl_setopt($ch, CURLOPT_URL, \app\common\BizConst::JPUSH_SEND_CODE_URI);
	curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body,JSON_UNESCAPED_UNICODE));
	curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,false);
	curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,false);
	curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);

    curl_setopt($ch, CURLOPT_HTTPHEADER,  ['Content-Type:application/json', "Authorization: Basic ".base64_encode(sysconf('jiguan_app_key').":".sysconf('jiguan_master_secret'))]);

	$result = curl_exec($ch);
	curl_close($ch);
    dump($result);die();
	return $result;

}

//function check_sms_code($mobile, $code)
//{
//    //关闭短信验证
//    if ($code == '318819') {
//        return true;
//    }
//
//    $cache_sms_code = $mobile . "_sms_code";
//    $sms_id = cacheGet($cache_sms_code);
//    if ($sms_id) {
//        $url = "https://api.sms.jpush.cn/v1/codes/{$sms_id}/valid";
//        $body=[
//            'code'=>$code,
//        ];
//        $ch=curl_init();
//        curl_setopt($ch, CURLOPT_URL, $url);
//        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($body,JSON_UNESCAPED_UNICODE));
//        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,false);
//        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,false);
//        curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
//        curl_setopt($ch, CURLOPT_HTTPHEADER,  ['Content-Type:application/json', "Authorization: Basic ".base64_encode(sysconf('jiguan_app_key').":".sysconf('jiguan_master_secret'))]);
//        $result = curl_exec($ch);
//
////        smsLog("sms_id:".$sms_id.',cache_sms_code:'.$cache_sms_code,$result);
//
//        curl_close($ch);
//        return json_decode($result,true)['is_valid']??false;
//    } else {
//        return false;
//    }
//}



function my_file_exits($url){
    $head=@get_headers($url);
    if ($head[0] == 'HTTP/1.1 200 OK') {
        return true;
    }
    return false;
}
function readBase($file,$ext='png',$type = 1)
{
    $host = source_config();
    $url='';
    if(!empty($file)){

        if(filter_var($file, FILTER_VALIDATE_URL)){
            $img_url = replace_url($file,$host['img_url']);
        }else{
            $img_url = $host['img_url'].$file;
        }
        $url = sysconf("img_decrypt_url")."?url=".changceImgVersion($img_url,'1.5.0');
    }

    return $url;
//	if (strpos($file, 'http') !== false) {
//
//        $file_name = get_filename($file);
//
//		if (!$file) {
//			return '';
//		}
//		if($type == 1){
//            $file = get_host($file);
//        }
// 		$cacheFileName = UPLOAD_PATH.'/uploads/cache/'.$file_name.'.'.$ext;
//		if( !file_exists( $cacheFileName ) ) {
//	     	$dir = dirname( $cacheFileName ) ;
//	    	if (!is_dir($dir)) {
//	    		@mkdir( $dir, '0777',true);
//	    	}
//
////            $cacheContent = '';
////            if(file_exists($file)){
//                $cacheContent = @file_get_contents($file);
////            }
//
//		    if ($cacheContent) {
//		    	file_put_contents($cacheFileName, base64_decode($cacheContent));
//		    }else{
//		    	return '';
//		    }
//	    }
//	    $imgsrc = get_host('/uploads/cache/'.md5($file).'.'.$ext);
//    }else{
//    	$imgsrc = get_host($file);
//    }
//    return $imgsrc;
//    return  !empty($file)? sysconf("img_decrypt_url")."?url=".$file:'';
//	if (strpos($file, 'http') !== false) {
//
//        $file_name = get_filename($file);
//
//		if (!$file) {
//			return '';
//		}
//		if($type == 1){
//            $file = get_host($file);
//        }
// 		$cacheFileName = UPLOAD_PATH.'/uploads/cache/'.$file_name.'.'.$ext;
//		if( !file_exists( $cacheFileName ) ) {
//	     	$dir = dirname( $cacheFileName ) ;
//	    	if (!is_dir($dir)) {
//	    		@mkdir( $dir, '0777',true);
//	    	}
//
////            $cacheContent = '';
////            if(file_exists($file)){
//                $cacheContent = @file_get_contents($file);
////            }
//
//		    if ($cacheContent) {
//		    	file_put_contents($cacheFileName, base64_decode($cacheContent));
//		    }else{
//		    	return '';
//		    }
//	    }
//	    $imgsrc = get_host('/uploads/cache/'.md5($file).'.'.$ext);
//    }else{
//    	$imgsrc = get_host($file);
//    }
//    return $imgsrc;
}

/**
 * @param string $filepath 图片路径
 * @return string $fr 图片名
 */
function get_filename($filepath){
    return md5($filepath);

//    $fr=explode("/",$filepath);
//    $count=count($fr)-1;
//    return $fr[$count];
}


function getMillisecond() {
    list($t1, $t2) = explode(' ', microtime());
    return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);

}
function isbase64($url)
{
    return false;
//	preg_match("/(\.(\w+)\?)|(\.(\w+)$)/",$url,$str);
//
//	if (!$str) {
//		return false;
//	}
//	return true;
}

function get_device_type()
{
	$agent = strtolower($_SERVER['HTTP_USER_AGENT']??'');
	//return $agent;
	$type = '其他';
	if(strpos($agent, 'iphone') || strpos($agent, 'ipad'))
	{
		$type = 'IOS';
	}
	if(strpos($agent, 'android') || strpos($agent, 'khttp'))
	{
		$type = 'ANDROID';
	}
	return $type;
}

/**
 * 将xml转换为数组
 * @param string $xml:xml文件或字符串
 * @return array
 */
function xmlToArray($xml)
{
    //考虑到xml文档中可能会包含<![CDATA[]]>标签，第三个参数设置为LIBXML_NOCDATA
    if (file_exists($xml)) {
        libxml_disable_entity_loader(false);
        $xml_string = simplexml_load_file($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
    } else {
        libxml_disable_entity_loader(true);
        $xml_string = simplexml_load_string($xml, 'SimpleXMLElement', LIBXML_NOCDATA);
    }
    $result = json_decode(json_encode($xml_string), true);
    return $result;
}
function re_connect()
{
	Db::query('select 1');
}
function trans_byte($byte)
{
    $KB = 1024;
    $MB = 1024 * $KB;
    $GB = 1024 * $MB;
    $TB = 1024 * $GB;
    if ($byte < $KB) {
        return $byte . "B";
    } elseif ($byte < $MB) {
        return round($byte / $KB, 1) . "KB";
    } elseif ($byte < $GB) {
        return round($byte / $MB, 1) . "MB";
    } elseif ($byte < $TB) {
        return round($byte / $GB, 1) . "GB";
    } else {
        return round($byte / $TB, 1) . "TB";
    }
}
function httpStatus($num){
        static $http = array (
            100 => "HTTP/1.1 100 Continue",
            101 => "HTTP/1.1 101 Switching Protocols",
            200 => "HTTP/1.1 200 OK",
            201 => "HTTP/1.1 201 Created",
            202 => "HTTP/1.1 202 Accepted",
            203 => "HTTP/1.1 203 Non-Authoritative Information",
            204 => "HTTP/1.1 204 No Content",
            205 => "HTTP/1.1 205 Reset Content",
            206 => "HTTP/1.1 206 Partial Content",
            300 => "HTTP/1.1 300 Multiple Choices",
            301 => "HTTP/1.1 301 Moved Permanently",
            302 => "HTTP/1.1 302 Found",
            303 => "HTTP/1.1 303 See Other",
            304 => "HTTP/1.1 304 Not Modified",
            305 => "HTTP/1.1 305 Use Proxy",
            307 => "HTTP/1.1 307 Temporary Redirect",
            400 => "HTTP/1.1 400 Bad Request",
            401 => "HTTP/1.1 401 Unauthorized",
            402 => "HTTP/1.1 402 Payment Required",
            403 => "HTTP/1.1 403 Forbidden",
            404 => "HTTP/1.1 404 Not Found",
            405 => "HTTP/1.1 405 Method Not Allowed",
            406 => "HTTP/1.1 406 Not Acceptable",
            407 => "HTTP/1.1 407 Proxy Authentication Required",
            408 => "HTTP/1.1 408 Request Time-out",
            409 => "HTTP/1.1 409 Conflict",
            410 => "HTTP/1.1 410 Gone",
            411 => "HTTP/1.1 411 Length Required",
            412 => "HTTP/1.1 412 Precondition Failed",
            413 => "HTTP/1.1 413 Request Entity Too Large",
            414 => "HTTP/1.1 414 Request-URI Too Large",
            415 => "HTTP/1.1 415 Unsupported Media Type",
            416 => "HTTP/1.1 416 Requested range not satisfiable",
            417 => "HTTP/1.1 417 Expectation Failed",
            500 => "HTTP/1.1 500 Internal Server Error",
            501 => "HTTP/1.1 501 Not Implemented",
            502 => "HTTP/1.1 502 Bad Gateway",
            503 => "HTTP/1.1 503 Service Unavailable",
            504 => "HTTP/1.1 504 Gateway Time-out"
        );
        header($http[$num]);
        exit();
}

function secure_encode($url,$uri=false){
	if(!$uri) $uri = $url;
	$secret = config('api.secure_pass');
	$expire = time()+300;
	$md5 = base64_encode(md5($secret.$uri.$expire, true));
	$md5 = strtr($md5, '+/', '-_');
	$md5 = str_replace('=', '', $md5);
	$url = $url."?st={$md5}&e={$expire}";
	return $url;
}

function mb_substr_replace($string, $replacement, $start, $length = null, $encoding = 'utf-8'){
	if (extension_loaded('mbstring') === true)
        {
            $string_length = (is_null($encoding) === true) ? mb_strlen($string) : mb_strlen($string, $encoding);

            if ($start < 0)
            {
                $start = max(0, $string_length + $start);
            }

            else if ($start > $string_length)
            {
                $start = $string_length;
            }

            if ($length < 0)
            {
                $length = max(0, $string_length - $start + $length);
            }

            else if ((is_null($length) === true) || ($length > $string_length))
            {
                $length = $string_length;
            }

            if (($start + $length) > $string_length)
            {
                $length = $string_length - $start;
            }

            if (is_null($encoding) === true)
            {
                return mb_substr($string, 0, $start) . $replacement . mb_substr($string, $start + $length, $string_length - $start - $length);
            }

            return mb_substr($string, 0, $start, $encoding) . $replacement . mb_substr($string, $start + $length, $string_length - $start - $length, $encoding);
        }

        return (is_null($length) === true) ? substr_replace($string, $replacement, $start) : substr_replace($string, $replacement, $start, $length);
}
function check_chinese($str){
	if(preg_match('/[\x{4e00}-\x{9fa5}]/u', $str)>0) {
		return true;
	} else {
		return false;
	}
}


function fanyi($query){

	if(check_chinese($query)) return $query;

	$url = "http://fanyi-api.baidu.com/api/trans/vip/translate";
	$appid = "20190131000260877";
	$key = "Rtg0wNuHyYGJO0rnLi6T";
	$data['q'] = $query;
	$data['from'] = "auto";
	$data['to'] = "zh";
	$data['appid'] = $appid;
	$data['salt'] =  time() ;

	$str = $appid . $data['q'] . $data['salt'] .$key ;
	$sign=md5($str);

	$data['sign'] = $sign;
	$result = curl_request($url,$data,1);
	if(!$result) return $query;
	$ret = json_decode($result, true);
	if(!is_array($ret)) return $query;
	if(!isset($ret['trans_result'])) return $query;
	if(!isset($ret['trans_result'][0])) return $query;
	if(!isset($ret['trans_result'][0]['dst'])) return $query;
	return $ret['trans_result'][0]['dst'];
}
function convertUrlArray($query)
{
	$params = array();
	if($query){
		$queryParts = explode('&', $query);
		if(is_array($queryParts)){
			 foreach ($queryParts as $param) {
				$item = explode('=', $param);
				$params[$item[0]] = $item[1];
			}
		}
	}
    return $params;
}
function rsa_decode($encryptData,$passphrase){
	$privateKeyFilePath = KEY_PATH.'/pri.pem';
	$privateKey = openssl_pkey_get_private(file_get_contents($privateKeyFilePath),$passphrase);
	$decryptData ='';
	if(openssl_private_decrypt(base64_decode($encryptData), $decryptData, $privateKey)){
		return $decryptData;
	}
	return "";
}
function rsa_encode($originalData){
	$publicKeyFilePath = KEY_PATH.'/pub.pem';
	$publicKey = openssl_pkey_get_public(file_get_contents($publicKeyFilePath));
	$encryptData = '';
	if(openssl_public_encrypt($originalData, $encryptData, $publicKey)){
		return base64_encode($encryptData);
	}
	return "";
}
function mp4_m3u8($url)
{
	$mp4 = UPLOAD_PATH . $url;
	$ext = substr($url, strrpos($url, '.') + 1);
	$filename = str_replace('.' . $ext, '', substr($url, strrpos($url, '/') + 1));

	// $pathStr = UPLOAD_PATH . UPLOAD_NAME . '/m3u8_app_movie/' . date('/Y/m/d/') . $filename;
	$pathStr = UPLOAD_PATH . UPLOAD_NAME . '/app_movie' . date('/Y/m/d/') . $filename;
	if (strtolower($ext) == 'mp4') {
		if (strrchr($pathStr, "/") != "/") $pathStr .= "/";
		if (!file_exists($pathStr)) {
			if (!mkdir($pathStr, 0777, true)) {
				return false;
			}
		}
		$new_name = random();
		$ts = $pathStr . $new_name . '.ts';
		$m3u8 = $pathStr . $new_name . '.m3u8';
		$m3u8_s = $pathStr . $new_name;

		shell_exec('/usr/bin/ffmpeg -y -i ' . $mp4 . ' -vcodec copy -acodec copy -vbsf h264_mp4toannexb ' . $ts);
		shell_exec('/usr/bin/ffmpeg -i ' . $ts . ' -c copy -map 0 -f segment -segment_list ' . $m3u8 . ' -segment_time 10 ' . $m3u8_s . '-%03d.ts');

		//shell_exec('ffmpeg -y -i  ' . $ts .' -hls_time 10 -hls_key_info_file /data/movie_com/public_html/enc.keyinfo -hls_playlist_type vod  -hls_segment_filename '.$m3u8_s . '-%03d.ts ' .$m3u8);

		@unlink($ts);
		$m3u8 = str_replace(UPLOAD_PATH, '', $m3u8);
		return $m3u8;
	}else{
		return $url;
	}
}
function file_save($url,$filename='',$pathname='')
{
    $state = @file_get_contents($url,0,null,0,1);//获取网络资源的字符内容
    if($state){
		$filepath = UPLOAD_NAME . '/' . $pathname . date('/Y/m/d/');
		$rootpath = PUBLIC_PATH.$filepath;
		if (!is_dir($rootpath)) {
			mkdir($rootpath, 0777, true);
		}
		if(!$filename || $filename==''){
			$filename = time().strtolower(random(16)).'.'.get_ext($url);
		}
		$filepath = $filepath.$filename;
		$filename = $rootpath.$filename;//文件名称生成
		//echo $filename;
        ob_start();//打开输出
        readfile($url);//输出图片文件
        $img = ob_get_contents();//得到浏览器输出
        ob_end_clean();//清除输出并关闭
        $size = strlen($img);//得到图片大小
        $fp2 = @fopen($filename, "a");
        fwrite($fp2, $img);//向当前目录写入图片文件，并重新命名
        fclose($fp2);
        return $filepath;
    }
    else{
        return 0;
    }
}
function formatDate($time){
	$rtime = date("Y-m-d H:i",$time);
	$htime = date("H:i",$time);
	$time = time()-$time;

	if ($time < 60) {
		$str = '刚刚';
	} elseif ($time < 60 * 60) {
		$min = floor ( $time / 60 );
		$str = $min . '分钟前';
	} elseif ($time < 60 * 60 * 24) {
		$h = floor ( $time / (60 * 60) );
		$str = $h . '小时前 ' . $htime;
	} elseif ($time < 60 * 60 * 24 * 3) {
		$d = floor ( $time / (60 * 60 * 24) );
		if ($d == 1)
			//$str = '昨天 ' . $rtime;
			$str = '昨天';
		else
			//$str = '前天 ' . $rtime;
			$str = '前天';
	}else {
		$str = $rtime;
	}
	return $str;
}
function mk_qrcode($data, $filename = 'qrcode',$promo_url="")
{
	$app_config = get_config();
	$promo_url = $promo_url?$promo_url."?code=":$app_config['promo_url'];
	if(!$promo_url) "http://www.k3289.com?code=";
	$data = $promo_url.$data;
	$options = new \chillerlan\QRCode\QROptions([
		'version' => 5,
		'outputType'   => \chillerlan\QRCode\QRCode::OUTPUT_IMAGE_JPG,
		'eccLevel' => \chillerlan\QRCode\QRCode::ECC_L,
	]);

	$qrcode = new \chillerlan\QRCode\QRCode($options);
	$file_name = UPLOAD_NAME . '/' . $filename . date('/Y/m/d/') . date('His') . '_' . random() . '.jpg';
	$qrcode->render($data, PUBLIC_PATH . $file_name);
	return $file_name;
}
function fileupload($name, $filename, $m = '')
{
	$start_str = 'data:image';
	$end_str = ';base64';
	if (strpos($name, $start_str) !== false) {
		$start_index = strlen($start_str) + 1;
		$end_index = strpos($name, $end_str) - $start_index;
		$img_type = substr($name, $start_index, $end_index);
		$base_img = substr($name, strpos($name, $end_str) + strlen($end_str) + 1);
		$new_name = date('His') . '_' . random() . '_' . $m . '.' . $img_type;
		$path = UPLOAD_PATH.'/' . $filename . date('/Y/m/d/');
		if (!is_dir($path)) {
			mkdir($path, 0777, true);
		}
		$img_path = $path . $new_name;
		$ifp = fopen($img_path, "wb");
		fwrite($ifp, base64_decode($base_img));
		fclose($ifp);
		$img_path = str_replace(Env::get('root_path') . '/public_html', '', $img_path);
		return $img_path;
	}
	return null;
}
function reload_cache_app_status()
{
	$cache_code = "app_status";
	$res = Db::name('app_config')->where(array(['id', '=', '1']))->value($cache_code);
	if ($res) {
		cache($cache_code,$res);
	}
}
function cache_app_status()
{
	$cache_code = "app_status";
	$return_data = Cache::get($cache_code);
	if (!$return_data) {
		$res = Db::name('app_config')->where(array(['id', '=', '1']))->value($cache_code);
		if ($res) {
			$return_data = $res;
			Cache::set($cache_code, $return_data);
		}
	}
	return $return_data;
}
function reload_cache_sys_config()
{
	$cache_code = "api_sys_config";
	$res = Db::name('sys_config')->where(array(['id', '=', '1']))->find();
	if ($res) {
		cache($cache_code,$res);
	}
}
function cache_sys_config()
{
//	$cache_code = "api_sys_config";
//	$return_data = Cache::get($cache_code);
//	if (!$return_data) {
		$res = Db::name('sys_config')->where(array(['id', '=', '1']))->find();
		if ($res) {
			$return_data = $res;
//			Cache::set($cache_code, $return_data);
		}
//	}
	return $return_data??[];
}
function data_success($msg = "success", $data = "")
{
	return data_msg(0, $msg, $data);
}
function data_fail($msg = "fail", $data = "", $state = 1)
{
	return data_msg($state, $msg, $data);
}
function data_msg($state, $msg, $data)
{
	$list['code'] = (int)$state;
	$list['msg'] = $msg;
	$list['data'] = $data;
	return $list;
}
//后台成功返回
function json_success_admin($msg = "success", $data = [])
{
	echo json_msg(0, $msg, $data);
	exit;
}
//后台失败返回
function json_fail_admin($msg = "fail", $data = [], $state = 1)
{
	echo json_msg($state, $msg, $data);
	exit;
}

function json_ok($data = [], $msg = "success", bool $aes = true)
{
    $respJson = static function (int $state, $msg, $data): string {
        $list['newcode'] = 0;
        $list['code']    = $state;
        $list['msg']     = $msg;
        $list['data']    = $data;
        return str_replace("\\/", "/", json_encode($list, JSON_UNESCAPED_UNICODE));
    };

    if ($aes) {
        static $aes;
        if (!$aes) {
            $aes = new EasyAes("eDWowITDNUKgND6PfQfA3OMTJ0nm640L", 128, "################");
        }

        $aesreturn = sysconf('aesreturn');
    }

    if (isset($aesreturn) && $aesreturn && \app\api\controller\LinePay::$is_encrypt == 0) {
        $data     = $respJson(0, $msg, $data);
        $newdata  = $aes->encrypt($data);
        $contents = json_encode(['newdata' => $newdata, 'newcode' => $aesreturn]);
    } else {
        $contents = $respJson(0, $msg, $data);
    }

    \response()->header('Content-Type', 'application/json')->content($contents)->send();
    exit(0);
}

function json_success($msg = "success", $data = [])
{

    retrun_before($data);

	static $aes;
	if (!$aes) {
		$aes = new EasyAes("eDWowITDNUKgND6PfQfA3OMTJ0nm640L", 128, "################");
	}
	$aesreturn = sysconf('aesreturn');
    if ($aesreturn && \app\api\controller\LinePay::$is_encrypt==0) {
		$data = json_msg(0, $msg, $data);
		$newdata = $aes->encrypt($data);
		echo json_encode(['newdata' => $newdata, 'newcode' => $aesreturn]);exit;
	}

	echo json_msg(0, $msg, $data);
	exit;
}

function retrun_m3u8_data($connet)
{
    $standard_version ='1.5.5';
    $device_type = get_header_device_type();
    $app_version = get_header_app_version();
    if($device_type != 'A'){
        $app_version=$standard_version;
    }

//    static $aes;
//    if (!$aes) {
//        $aes = new EasyAes("eDWowITDNUKgND6PfQfA3OMTJ0nm640L", 128, "################");
//    }
//    if($app_version>=$standard_version)$connet=$aes->encrypt($connet);

    if(sysconf('is_open_m3u8_encrypt')==1){
        if($app_version>=$standard_version){
            header('Encrypt-Content-Type: video/cipher');
            $connet=xor_crypt($connet);
        }
    }

    return  $connet;
}

/**
 * 检查当前请求是否是对应版本以上(当前请求的版本大于或等于指定版本，返回true，否则返回false，默认除安卓外其他设备都返回true)
 */
function checkVersion(string $compared_version)
{
    $device_type = get_header_device_type();
    $app_version = get_header_app_version();
    if($device_type != 'A'){
        $app_version=$compared_version;
    }
    if($app_version>=$compared_version){
        return true;
    }
    return false;
}



/** api数据返回前处理 */
function retrun_before(&$data)
{
    try {

        //由于旧代码缓存太多，根本无法处理，直接在接口返回前做处理
        list($movie_url,$img_url,$app_version,$ai_movie_url,$ai_img_url) = get_url_change_data();
        if(!empty($img_url)){
            if(is_array($data)){
                foreach ($data as &$v){

                    //这里出于太多接口缓存，以及后面新增功能的缓存可能漏，所以统一返回给前端前处理，避免h5和安卓的cdn缓存串的可能性！
                    if(is_string($v)){
                        if(strpos($v, '.ai.m3u8') ){
                            $v =  str_replace(CDN_URL_TAG,$ai_movie_url,$v);
                        }elseif (strpos($v, '.m3u8') ) {
                            $v =  str_replace(CDN_URL_TAG,$movie_url,$v);
                        }elseif (strpos($v, '.ai.ceb') ){
                            $v =  str_replace(CDN_URL_TAG,$ai_img_url,$v);
                        } elseif ((strpos($v, '.ced') || strpos($v, '.ceb'))  ){
                            $v =  str_replace(CDN_URL_TAG,$img_url,$v);
                            $v=changceImgVersion($v,$app_version);
                            $parse_url = parse_url($v);
//                        if(!empty($parse_url['path'])){
                            $v = $img_url.$parse_url['path'].'?'.(new \app\extend\Cdn\Tencent())->getSignKeyValue($parse_url['path']);
//                        }

                        }
                    }elseif(is_array($v) || is_object($v)){
                        retrun_before($v);
                    }

                }
            }
        }
    }catch (Throwable $e){
        commonLog(\app\common\Constants\LogConst::RETURN_BEFORE_LOG[1],['error'=>$e->getTraceAsString()],\app\common\Constants\LogConst::RETURN_BEFORE_LOG[0]);
    }

}

function json_fail($msg = "fail", $data = [], $state = 1)
{
	static $aes;
	if (!$aes) {
		$aes = new EasyAes("eDWowITDNUKgND6PfQfA3OMTJ0nm640L", 128, "################");
	}
	$aesreturn = sysconf('aesreturn');

    if ($aesreturn && \app\api\controller\LinePay::$is_encrypt==0 && \app\Helper\CommonHelper::$return_is_exit) {
		$data = json_msg($state, $msg, $data);
		$newdata = $aes->encrypt($data);
		echo json_encode(['newdata' => $newdata, 'newcode' => $aesreturn]);exit;
	}

    if(\app\Helper\CommonHelper::$return_is_exit){
        echo json_msg($state, $msg, $data);
        exit;
    }else{
        throw new InsideBusinessException($msg,\app\Helper\CodeHelper::CODE_INSIDE_EXCEPTION[0]);
//        return json_msg($state, $msg, $data);
    }

}
function json_msg($state, $msg, $data)
{
	if(!$data) {$data = (object)[];}
	$list['newcode'] = 0;
	$list['code'] = (int)$state;
	$list['msg'] = $msg;
	$list['data'] = $data;
	return str_replace("\\/", "/", json_encode($list, JSON_UNESCAPED_UNICODE));
}
function get_ip()
{
	if (getenv("http_client_ip") && strcasecmp(getenv("http_client_ip"), "unknown"))
		$ip = getenv("http_client_ip");
	else if (getenv("http_x_forwarded_for") && strcasecmp(getenv("http_x_forwared_for"), "unknown"))
		$ip = getenv("http_x_forwarded_for");
	else if (getenv("remote_addr") && strcasecmp(getenv("remote_addr"), "unknown"))
		$ip = getenv("remote_addr");
	else if (isset($_SERVER["REMOTE_ADDR"]) && $_SERVER["REMOTE_ADDR"] && strcasecmp($_SERVER["REMOTE_ADDR"], "unknown"))
		$ip = $_SERVER["REMOTE_ADDR"];
	else
		$ip = "unknown";
	return ($ip);
}
function get_avatar($url)
{

    return $url;

//	if($url) {
//		static $host;
//        if (!$host) {
//           $host = source_config();
//        }
//		//有http的话替换域名为配置
//		if (strpos($url, 'http') !== false) {
//			if (strpos($url, 'm3u8')) {
//				return replace_url($url,$host['movie_url']);
//			}else{
//				return replace_url($url,$host['img_url']);
//			}
//		}else{
//			return get_domain() . $url;
//		}
//		//无http则获取当前host添加路径
//	}
//	return get_domain() . "/static/image/default_avatar.png";

}
// function get_host($url)
// {
// 	if ($url) {
// 		if (strpos($url, 'http') !== false) {
// 			return $url;
// 		} else {
// 			if(checkMp4($url)){
//                 $mp4Domain = get_random_host();
//                 if (strpos($url, 'https') == false){
//                     $mp4Domain = str_replace('http','https',$mp4Domain);
//                 }
// 				//return get_random_host() . $url;
//                 return $mp4Domain . $url;
// 			}
// //			$host = get_domain();
//             $host = get_random_host();
// 			return $host.$url;
// 		}
// 	}
// 	return $url;
// }
function exportXls($title,$data,$statr='',$end_time=''){
    ini_set('max_execution_time', 600);// 设置PHP超时时间
    $statr   = $statr ? $statr : '全部';
    require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
    $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
    $sheet = $spreadsheet->getActiveSheet();
    // 表头
    foreach ($title as $key => $value) {
        $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
    }
    // 写入内容
    $row = 2;
    foreach ($data as $item) {
        $column = 1;
        foreach ($item as $value) {
            $sheet->setCellValueByColumnAndRow($column, $row, $value);
            $column ++;
        }
        $row ++;
    }
    // 输出 下载内容
    $filename = rawurlencode('数据统计('.$statr . '至' .  $end_time . ')');
    ob_end_clean();
    ob_start();
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
    header('Cache-Control: max-age=0');
    // If you're serving to IE 9, then the following may be needed
    header('Cache-Control: max-age=1');
    // If you're serving to IE over SSL, then the following may be needed
    header('Expires: Mon, 26 Jul 1997 05:00:00 GMT'); // Date in the past
    header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT'); // always modified
    header('Cache-Control: cache, must-revalidate'); // HTTP/1.1
    header('Pragma: public'); // HTTP/1.0
    $writer = \PhpOffice\PhpSpreadsheet\IOFactory::createWriter($spreadsheet, 'Xlsx');
    $writer->save('php://output');
    exit;
}
function get_host($url)
{
    if(!empty($url)) {

        if (strpos($url, CDN_URL_TAG) !== false) {
            return $url;
        }

        if (strpos($url, 'http') !== false) {
            $url = parse_url($url,PHP_URL_PATH);
        }
        $path=ltrim($url,'/');
        if(!empty($path)){
            if(strpos($path, '.ai.ceb') ){
                $url = implode(",", array_map(function($a)  {
                    return CDN_URL_TAG.'/'.$a.'?'.(new \app\extend\Cdn\Tencent())->getAiSignKeyValue('/'.$a);
                }, explode(",", $url)));
            }elseif ( strpos($path, '.ceb')  || strpos($path, '.ced') ){
                $url = CDN_URL_TAG.'/'.$path.'?'.(new \app\extend\Cdn\Tencent())->getSignKeyValue($path);
            }
        }
    }
    return $url;
}


function get_url_change_data(){
    $host = source_config();

    $movie_url = $host['h5_movie_url'];
    $img_url = $host['h5_img_url'];

    $ai_movie_url = $host['h5_ai_movie_url']??'';
    $ai_img_url = $host['h5_ai_img_url']??'';


    $device_type = get_header_device_type();
    $app_version = get_header_app_version();
    if($device_type == 'A'){
        if(!empty($host['movie_url']))$movie_url=$host['movie_url'];
        if(!empty($host['img_url']))$img_url=$host['img_url'];
        if(!empty($host['ai_movie_url']))$ai_movie_url=$host['ai_movie_url'];
        if(!empty($host['ai_img_url']))$ai_img_url=$host['ai_img_url'];
    }else{
        $app_version='1.5.0';
    }

    return [$movie_url,$img_url,$app_version, $ai_movie_url, $ai_img_url];
}


function replace_url($path,$reg)
{
    return preg_replace('/(http|https):\/\/([^\/]+)/i', $reg, $path);
}
function source_config(){

    if(!empty(StaticHelper::$source_config['data']) && StaticHelper::$source_config['time']>=(time()-300)){
        return StaticHelper::$source_config['data'];
    }

	$cache_code = "source_config";
	$return_data = cacheGet($cache_code);
	if(!$return_data){
		$return_data = db("source_config")->column('key_value','key_name');
		cacheSet($cache_code,$return_data,60);
	}
    StaticHelper::$source_config=[
        'data'=>$return_data,
        'time'=>time(),
    ];
/*	$data = config('black.');
	if ($data) {
		$return_data['movie_url'] = $data['movie_url'];
		$return_data['img_url'] = $data['img_url'];
	}*/
	return $return_data ;
}


function get_config(){
	$cache_code = "app_config";
	$return_data = cache($cache_code);
	if(!$return_data){
		$data = db("app_config")->find(1);
		if($data){
			$return_data = $data ;
			cache($cache_code,$return_data);
		}
	}
	return $return_data ;
}
function get_random_host(){
	$cdn_list = get_cdn_list();
	$host = get_domain();
	if($cdn_list && count($cdn_list)>0){
		$app_config = get_config();
		$cdn_type = $app_config['cdn_type'];
//		if($cdn_type==1){
			$host = $cdn_list[0]['host'];
//		}else{
//			$host = $cdn_list[array_rand($cdn_list,1)]['host'];
//		}
	}
	return $host;
}
function get_cdn_list(){
	$cache_code = "app_cdn";
	$return_data = cache($cache_code);
	if(!$return_data){
		$return_data = [];
		$condition = array();
		$condition[] = array('status','=','1');
		$fields = "host";
		$data = db('app_cdn')->where($condition)->field($fields)->select();
		if($data){
			$return_data = $data ;
		}
		cache($cache_code,$return_data);
	}
	return $return_data ;
}

function get_domain()
{
	$sys_protocal = isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == '443' ? 'https://' : 'http://';
	$url = $sys_protocal . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '');
	return $url;
}

function get_url()
{
	$sys_protocal = isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == '443' ? 'https://' : 'http://';
	$php_self = $_SERVER['PHP_SELF'] ? $_SERVER['PHP_SELF'] : $_SERVER['SCRIPT_NAME'];
	$path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
	$relate_url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : $php_self . (isset($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : $path_info);
	$url = $sys_protocal . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '') . $relate_url;
	return $url;
}
function get_uri()
{
	$php_self = $_SERVER['PHP_SELF'] ? $_SERVER['PHP_SELF'] : $_SERVER['SCRIPT_NAME'];
	$path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
	$relate_url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : $php_self . (isset($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : $path_info);
	$uri = $relate_url;
	return $uri;
}
function check_letter($str)
{
	$regex = '/^[a-z]*$/i';
	if (preg_match($regex, $str)) {
		return true;
	} else {
		return false;
	}
}

function check_number($str)
{
	$regex = '/^[0-9]*$/i';
	if (preg_match($regex, $str)) {
		return true;
	} else {
		return false;
	}
}

function check_number_letter($str)
{
	$str = strtolower($str);
	$regex = '/^[0-9a-z]*$/i';
	if (preg_match($regex, $str)) {
		return true;
	} else {
		return false;
	}
}

/**
 * 验证用户名是否规范
 * @param string $account
 * @return bool
 */
function check_member_name(string $account): bool
{
    //验证字符串是否只包含数字和字母以及下划线
    if (preg_match("/^[a-zA-Z0-9_]+$/",$account)){
        return true;
    } else {
        return false;
    }
}




function check_length($str, $min_length, $max_length = '')
{
	if ($max_length) {
		if (strlen($str) >= intval($min_length) && mb_strlen($str,'utf-8') <= intval($max_length)) {
			return true;
		} else {
			return false;
		}
	} else {
		if (strlen($str) == intval($min_length)) {
			return true;
		} else {
			return false;
		}
	}

}

function check_str($str)
{
	$str = trim($str);
	if ($str != '' && strlen($str) > 0) {
		return true;
	} else {
		return false;
	}
}

function check_list($list)
{
	if ($list && !empty($list) && count($list) > 0) {
		return true;
	} else {
		return false;
	}
}

function create_token()
{
	return random(32);
}
function get_ext($url){
	return strtolower( substr($url,strrpos($url,'.')+1) );
}
function checkImg($url){
	$ext = get_ext($url) ;
	$flag = false;
	if( $ext && ( $ext=='jpg' || $ext=='jpeg' || $ext=='png' ) ){
		$flag = true;
	}
	return $flag;
}
function checkMp4($url){
	$ext = get_ext($url) ;
	$flag = false;
	if( $ext && ( $ext=='mp4' ) ){
		$flag = true;
	}
	return $flag;
}

function delPathList($list){
	if($list && is_array($list) && count($list)>0){
		foreach($list as $path){
			if($path && strpos($path,'.')!==false){
				if(strpos($path,PUBLIC_PATH)===false) $path = PUBLIC_PATH.$path;
				if (file_exists($path)) {
					$ext = get_ext($path) ;
					if($ext=='jpg' || $ext=='png' || $ext=='jpeg' || $ext=='mp4'){
						@unlink($path);
					}elseif($ext=='m3u8'){
						$dir = substr($path,0,strrpos($path,'/'));
						$handle = @opendir($dir);
						while (false !== ($file = readdir($handle))) {
							if ($file !== "." && $file !== "..") {
								$file = $dir . '/' . $file;
								if (is_file($file)) {
									@unlink($file);
								}
							}
						}
						@rmdir($dir);
					}
				}
			}
		}
	}
}

function random($length = 6, $type = 'string')
{

	$config = array(
		'number' => '1234567890',
		'lower' => 'abcdefghijklmnopqrstuvwxyz',
		'upper' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ',
		'upper_number' => 'ABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890',
		'letter' => 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ',
		'string' => 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890',
		'lowernumber' => 'abcdefghijklmnopqrstuvwxyz1234567890'
	);

	if (!isset($config[$type])) $type = 'string';
	$string = $config[$type];
	$code = '';
	$strlen = strlen($string) - 1;
	for ($i = 0; $i < $length; $i++) {
		$code .= $string {
			mt_rand(0, $strlen)};
	}

	return $code;
}

function check_mobile($mobile)
{
	if (preg_match("/^1[3456789]\d{9}$/", $mobile)) {
		return true;
	} else {
		return false;
	}
}

function get_client_ip($type = 0, $adv = false)
{
	// $type = $type ? 1 : 0;
	// static $ip = null;
	// if ($ip !== null) return $ip[$type];
	// if ($adv) {
	// 	if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
	// 		$arr = explode(',', $_SERVER['HTTP_X_FORWARDED_FOR']);
	// 		$pos = array_search('unknown', $arr);
	// 		if (false !== $pos) unset($arr[$pos]);
	// 		$ip = trim($arr[0]);
	// 	} elseif (isset($_SERVER['HTTP_CLIENT_IP'])) {
	// 		$ip = $_SERVER['HTTP_CLIENT_IP'];
	// 	} elseif (isset($_SERVER['REMOTE_ADDR'])) {
	// 		$ip = $_SERVER['REMOTE_ADDR'];
	// 	}
	// } elseif (isset($_SERVER['REMOTE_ADDR'])) {
	// 	$ip = $_SERVER['REMOTE_ADDR'];
	// }
	// $long = sprintf("%u", ip2long($ip));
	// $ip = $long ? array($ip, $long) : array('0.0.0.0', 0);
	// return $ip[$type];
	return request()->ip();
}

function get_addr($ip = null)
{
    if (!$ip) {
        $ip = request()->ip();
    }
    if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
        $IpLocation = new \net\IpLocation();
        $addr = $IpLocation->getlocation($ip);
        return icon2utf8($addr['country'] . $addr['area']);
    }
    return "未知";
    // if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_IPV4)) {
    //     $IpLocation = new \net\IpLocation();
    //     $addr = $IpLocation->getlocation($ip);
    //     return icon2utf8($addr['country'] . $addr['area']);
    // }else{
    //     $result = \Ritaswc\ZxIPAddress\IPv6Tool::query($ip);
    //     $addr = '未知';
    //     if (isset($result['addr']) && isset($result['addr'][0]) && isset($result['addr'][1])) {
    //         $addr = $result['addr'][0].' '.mb_substr($result['addr'][1],2,2,"UTF-8");
    //     }
    //     return $addr;
    // }
	// $IpLocation = new \net\IpLocation();
	// $addr = $IpLocation->getlocation($ip);
	// return icon2utf8($addr['country'] . $addr['area']);
}
function icon2utf8($str)
{
	return iconv("gbk", "utf-8//IGNORE", $str);
}
 function curlRequest($url, $postData = null) {
	$ch = curl_init();
	curl_setopt ($ch, CURLOPT_URL, $url);
	curl_setopt ($ch, CURLOPT_RETURNTRANSFER, true);
	curl_setopt ($ch, CURLOPT_SSL_VERIFYPEER, false);
	if ($postData) {
		curl_setopt($ch, CURLOPT_POST, true);
		curl_setopt($ch, CURLOPT_POSTFIELDS, $postData);
	}
	$curlResponse = curl_exec($ch);
	$curlErrno = curl_errno($ch);
	if ($curlErrno) {
		$curlError = curl_error($ch);
		//throw new Exception($curlError);
	}
	curl_close($ch);
	return $curlResponse;
}
function curl_request($url, $data = null, $post = 0)
{
	if (!$url) {
		return false;
	}
	$ch = curl_init();
	curl_setopt($ch, CURLOPT_URL, $url);
	curl_setopt($ch, CURLOPT_HEADER, false);
	if ($post) {
		if (!$data) return false;
		curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
		curl_setopt($ch, CURLOPT_POST, 1);
	}
	curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
	curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 60);
	$output = curl_exec($ch);
	return $output;
}
function encode($string, $key = "")
{
	$src = array("/", "+", "=");
	$dist = array("_a", "_b", "_c");
	$str = authcode($string, "ENCODE", $key);
	$new = str_replace($src, $dist, $str);
	return $new;
}

function decode($string, $key = "")
{
	$src = array("_a", "_b", "_c");
	$dist = array("/", "+", "=");
	$string = str_replace($src, $dist, $string);
	return authcode($string, "DECODE", $key);
}


function authcode($string, $operation = 'DECODE', $key = '', $expiry = 0)
{
	$ckey_length = 4;
	$auth_key = config("auth_key");
	$key = md5(empty($key) ? $auth_key : $key);
	$keya = md5(substr($key, 0, 16));
	$keyb = md5(substr($key, 16, 16));
	$keyc = $ckey_length ? ($operation == 'DECODE' ? substr($string, 0, $ckey_length) : substr(md5(microtime()), -$ckey_length)) : '';
	$cryptkey = $keya . md5($keya . $keyc);
	$key_length = strlen($cryptkey);
	$string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) :
		sprintf('%010d', $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
	$string_length = strlen($string);
	$result = '';
	$box = range(0, 255);
	$rndkey = array();
	for ($i = 0; $i <= 255; $i++) {
		$rndkey[$i] = ord($cryptkey[$i % $key_length]);
	}
	for ($j = $i = 0; $i < 256; $i++) {
		$j = ($j + $box[$i] + $rndkey[$i]) % 256;
		$tmp = $box[$i];
		$box[$i] = $box[$j];
		$box[$j] = $tmp;
	}
	for ($a = $j = $i = 0; $i < $string_length; $i++) {
		$a = ($a + 1) % 256;
		$j = ($j + $box[$a]) % 256;
		$tmp = $box[$a];
		$box[$a] = $box[$j];
		$box[$j] = $tmp;
		$result .= chr(ord($string[$i]) ^ ($box[($box[$a] + $box[$j]) % 256]));
	}
	if ($operation == 'DECODE') {
		if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) &&
			substr($result, 10, 16) == substr(md5(substr($result, 26) . $keyb), 0, 16)) {
			return substr($result, 26);
		} else {
			return '';
		}
	} else {
		return $keyc . str_replace('=', '', base64_encode($result));
	}
}




//对emoji表情转义
function emoji_encode($str){
    $strEncode = '';

    $length = mb_strlen($str,'utf-8');

    for ($i=0; $i < $length; $i++) {
        $_tmpStr = mb_substr($str,$i,1,'utf-8');
        if(strlen($_tmpStr) >= 4){
            $strEncode .= '[[EMOJI:'.rawurlencode($_tmpStr).']]';
        }else{
            $strEncode .= $_tmpStr;
        }
    }

    return $strEncode;
}
//对emoji表情转反义
function emoji_decode($str){
    $strDecode = preg_replace_callback('|\[\[EMOJI:(.*?)\]\]|', function($matches){
        return rawurldecode($matches[1]);
    }, $str);

    return $strDecode;
}
// 过滤所有换行和空格及tab键
function trim_space($str, $trimEnter = true, $trimTab = true) {
    if (! is_string($str)) return $str;
    $search = [" ","　"];
    $replace = ["",""];
    if ($trimEnter) {
        $search[] = "\n";
        $search[] = "\r";
        $replace[] = "";
        $replace[] = "";
    }
    if ($trimTab) {
        $search[] = "\t";
        $replace[] = "";
    }
    return str_replace($search, $replace, $str);
}
// 更新短短视频权重
function update_video_clip_weight($id) {
    // $res = Db::name('video_clip')->where('id','=', $id)->exp('weight_num','like_num*3+comment_num*6+share_num*10')->update();
    $sql = Db::name('video_clip')->where('id','=', $id)->exp('weight_num','like_num*3+comment_num*6+share_num*10')->fetchSql()->update();
    pushSql($sql);

    return;
}
// 获取短视频观影次数配置信息
function get_video_clip_config_times() {
    $times = cacheGet("visitor_video_clip_times");
    // $times = \Cache::get('visitor_video_clip_times');
    if (! $times) {
        $times = Db::name('equity_basic')->where('id','=',1)->value('visitor_d_s_view_times');
        // \Cache::set('visitor_video_clip_times', ($times ? $times : 0));
        cacheSet("visitor_video_clip_times", ($times ? $times : 0));
    }
    return $times ? $times : 0;
}

// 获取长视频观影次数配置信息(包括额外观影次数)
function get_visitor_long_view_times($user_id) {
    $times = cacheGet("get_visitor_long_view_times");
    // $times = \Cache::get('visitor_video_clip_times');
    if (! $times) {
        $times = Db::name('equity_basic')->where('id','=',1)->value('visitor_long_view_times');
        // \Cache::set('visitor_video_clip_times', ($times ? $times : 0));
        cacheSet("get_visitor_long_view_times", ($times ? $times : 0),111);
    }
    $times += (Db::name('member_info')->where('id','=',$user_id)->cache(77)->value('extra_everyday_count')??0);
//    $times += (Db::name('member_info')->where('id','=',$user_id)->cache(5)->field("extra_everyday_count,is_update_reg_count,reg_frequency")->find();
    return $times ? $times : 0;
}

// 注册用户赠送VIP(天)配置信息
function get_register_send_vip_times() {
    $times = cacheGet("register_send_vip_times");
    // $times = \Cache::get('visitor_video_clip_times');
    if (! $times) {
        $times = Db::name('equity_basic')->where('id','=',1)->value('register_send_vip_times');
        // \Cache::set('visitor_video_clip_times', ($times ? $times : 0));
        cacheSet("register_send_vip_times", ($times ? $times : 0));
    }
    return $times ? $times : 0;
}

/**
 * 将秒转换成时：分：秒格式（不满足一小时不返回时）
 * @param number $second
 * @return string
 */
function get_second_to_time($second) {
    // 顺序为 秒、分、时
    $time = [0, 0, 0];
    // 小于60秒
    if ($second < 60) {
        $time[0] = $second;
    } elseif ($second < 3600) {
        // 不满足1小时
        $minute  = floor($second / 60);
        $time[0] = $second - $minute * 60;
        $time[1] = $minute;
    } else {
        // 一小时以上
        $minute = floor($second / 60);
        $hour   = floor($minute / 60);
        $time[0] = $second - $minute * 60;
        $time[1] = $second - $hour * 3600;
        $time[2] = $hour;
    }
    // 整理返回的数据
    $str = "";
    for ($i = 2; $i >= 0; $i --) {
        if (($i == 2 && $time[$i] > 0) || $i < 2) {
            $format = str_pad($time[$i], 2, "0", STR_PAD_LEFT);
            $str .= $str ? ":" . $format : $format;
        }
    }
    // 返回数据
    return $str;
}

/**
 * 获取短视频播放地址
 * @param string $m3u8
 * @param string $mp4
 * @param string $img
 * @return string
 */
function get_video_play_url($m3u8, $mp4, $img) {
    // 获取域名
    $config = source_config();
    $url = $config["video_cdn"];
    if (substr($url, -1) != '/') $url .= '/';
    // 优先播放m3u8
    if ($m3u8) {
        if(! preg_match("/^[http|https]/", $m3u8)) {
            $m3u8 = $url . $m3u8;
        }
        $playUrl = "chrome-extension://emnphkkblegpebimobpbekeedfgemhof/player.html#" . $m3u8;
    } else {
        $playUrl  = url('admin/app_movie/play') . '?jpg=';
        if ($img) {
            $img = get_video_cover_img($img);
            $playUrl .= urlencode($img);
        }
        if(! preg_match("/^[http|https]/", $mp4)) {
            $uploadUrl = strtolower($config["video_upload"]);
            // 将http转换为https
            if (substr($uploadUrl, 0, 5) != "https") {
                $uploadUrl = "https" . substr($uploadUrl, 4);
            }
            $mp4 = $uploadUrl . $mp4;
        }
        return $playUrl . "&addr=" . urlencode($mp4);
    }
    return $playUrl;
}
function video_advertisement_mp4($m3u8){
    $config = source_config();
    $url = $config["video_upload"];
    if (substr($url, -1) != '/') $url .= '/';
    $m3u8 = $url . $m3u8;
  //  return   "chrome-extension://emnphkkblegpebimobpbekeedfgemhof/player.html#" . $m3u8;
    return  $m3u8;
}

/**
 * 获取短视频封面图片地址
 */
function get_video_cover_img($imgPath) {
    return readBase($imgPath,'png',2);
}

/**
 * 获取短视频封面图片地址
 */
function get_video_cover_imgOld($imgPath) {
    $type = 1 ;
    // 获取域名
    $config = source_config();
    $url = $config["video_cdn"];
    // 拼接域名
    if (substr($url, -1) != '/') $url .= '/';
    if(! preg_match("/^[http|https]/", $imgPath)) {
        $type = 2 ;
        $imgPath = $url . $imgPath;
    }
    if (isbase64($imgPath)) {
        return $imgPath;
    }
    return readBase($imgPath,'png',$type);
}

function getMapServers()
{
    $servers = Db::name('static_servers')->order('sort', 'DESC')->field('id,flag,url_prefix,api_domain,root_path,source_url')->cache(10)->select();
    $mapServers = [];
    if (!empty($servers)) {
        foreach ($servers as $v) {
            $mapServers[$v['flag']] = $v;
        }
    }

    return $mapServers;
}

/**
 * 替换成源站ip
 */
function replaceSourceUrl(string $url)
{
    $list_column =  Db::name('static_servers')->field(['url_prefix','source_url'])->cache(5)->select();

    foreach ($list_column as $k=>$v){
        $url = str_replace($v['url_prefix'],$v['source_url'],$url);
    }
    return $url;

}

function replaceCdnUrlApi(string $url,$is_old=false,bool $is_check_ip=false)
{
    return replaceCdnUrl($url,'api',$is_old,$is_check_ip);
}

/**
 * 替换成cdn域名(图片,后台内部使用，请勿用于api接口)
 */
function replaceCdnUrlImg(string $url)
{
    $list_column =  Db::name('static_servers')->field(['url_prefix','source_url','old_cdn_url'])->select();

    foreach ($list_column as $v){
        $url = str_replace($v['source_url'],$v['url_prefix'],$url);

        $url = str_replace($v['old_cdn_url'],$v['url_prefix'],$url);
    }
    $parse_url = parse_url($url);
    if(!empty($parse_url['path'])){
        $url = $parse_url['scheme'].'://'.$parse_url['host'].$parse_url['path'];
    }
    return $url;

}

/**
 * 替换成cdn域名(视频cdn)
 * $module api:对外api接口  auto:内部接口
 * $is_old 目前此参数暂时废弃，直接默认即可
 * $is_check_ip 播放时是否需要检查ip
 */
function replaceCdnUrl(string $url,$module = 'admin',$is_old=false,bool $is_check_ip=false)
{

    $parse_url = parse_url($url);
    if(!empty($parse_url['path'])){
        if(strpos($url, '.ai.m3u8')!==false){
            $authkey = (new \app\extend\Cdn\Tencent())->getAiSignKeyValue($parse_url['path']);
        }else{
            $authkey = (new \app\extend\Cdn\Tencent())->getSignKeyValue($parse_url['path']);
        }
        $url = CDN_URL_TAG.$parse_url['path'].'?'.$authkey;

        $url = ($module!='admin'?env('REQUEST_SCHEME','http'):'http').'://'.$_SERVER['HTTP_HOST'].'/'.$module.'/movie/m3u8raw.m3u8?url='.$url;

        if(!empty($parse_url['query'])){
            parse_str($parse_url['query'],$query_params);

            $second_auth_data = ['type'=>\app\Service\VideoService::TYPE_LONG_VIDEO,'id'=>strval($query_params['second_auth']),'unique'=>uniqid().'-'.rand(1,999)];
//            if($is_check_ip){
                $second_auth_data['ip']=get_client_ip();
//            }

            if(!empty($query_params['second_auth'])){
                $url.='&second_auth='.second_auth_encrypt($second_auth_data);
            }

            if(!empty($query_params['source_type'])){
                $url.='&source_type='.$query_params['source_type'];
            }
        }
    }

    return $url;

}



function requestLimit($uid,string $part = 'pay',int $limitNum = 6, int $expire = 60)
{
    $key = "LIMIT:limit_{$part}_user_" . $uid;
    $banKey = "LIMIT:limit_{$part}_ban_" . $uid;
    $nowTime  = time();

    $redis = redis();
    $redis->watch($key);

    if ($redis->exists($banKey)) {
        return false;
    }

    $limitVal = $redis->get($key);
    if ($limitVal) {
        $limitVal = json_decode($limitVal, true);
        $num  = min($limitNum, ($limitVal['num']) + (int)(($limitNum / $expire) * ($nowTime - $limitVal['time'])));
        if ($num <= 0) {
            $redis->setex($banKey, 60,$uid);
            return false;
        }
    } else {
        $num = $limitNum;
    }
    $redisVal = json_encode(['num' => $num-1, 'time' => $nowTime]);

    $redis->multi();
    $redis->set($key, $redisVal);
    $redis->expire($key, $expire);
    $robResult = $redis->exec();
    if (!$robResult) {
        return false;
    }
    return true;
}


//根据ip获取对应的国家
function is_china($ip){

    try {
        $reader =   new Reader(PUBLIC_PATH.'/GeoIP/GeoLite2-Country.mmdb');
        $record = $reader->country($ip);
        if ($record->country->names['zh-CN'] == '中国'){
            return true;
        }else{
            return false;
//        echo '对应的IP地址所在的地区是国外,国家名称是： '.$record->country->names['zh-CN'];
        }
    }catch (\Throwable $ex){
        commonLog(\app\common\Constants\LogConst::INIT_IP_CHECK_LOG[1],['error'=>$ex->getMessage().$ex->getFile().$ex->getLine()],\app\common\Constants\LogConst::INIT_IP_CHECK_LOG[0]);
        //异常一般是ip库ip池不够的问题，这里直接允许通过
        return true;
    }

}

function init_limit_userinfo(string $channelid='', string $app_version='',string $device_id='',string $device_type='',int $time=0, string $sign=''){

    $redis=redis();

    $false_data = [
        'user_id'=>random(6,'number'),
        'token'=>random(32),
    ];
//dump($device_type);die();
    $create_sign = md5($app_version.$device_id.$device_type.md5(env("INIT_LIMIT_USERINFO_KEY_NEW").$device_id.$time));
    $create_sign_old = md5(env("INIT_LIMIT_USERINFO_KEY_OLD").$device_id.$time);

    // 测试环境不校验
    if (env('APP_ENV')=='dev') {
        return true;
    }

    if(!in_array($device_type,['A','IH','H'])){
//        json_fail('非法请求!');
        json_success('success', $false_data);
    }


    $no_check=false;
    if($device_type=='A'){
        switch (env('CACHE_PREFIX')){
            case 'MOMO':
                if($app_version<='1.0.8')$create_sign=$create_sign_old;
                if($app_version=='1.0.8')$no_check=true;
                break;
            case 'MOMO_TWO': if($app_version<='1.0.7')$create_sign=$create_sign_old;
                if($app_version=='1.0.7')$no_check=true;
                break;
            case 'MOMO_THIRD': if($app_version<='1.0.6')$create_sign=$create_sign_old;
                break;
            case 'MOMO4':
            case 'MOMO5': if($app_version<='1.0.4')$create_sign=$create_sign_old;
                if($app_version=='1.0.4')$no_check=true;
                break;
            case 'MOMO6': if($app_version<='1.0.0')$create_sign=$create_sign_old;
                if($app_version=='1.0.0')$no_check=true;
                break;
        }
    }



    //兼容h5给个过渡期2022-07-18
    if($sign!=$create_sign && !$no_check  && ($device_type == 'A' || env('APP_ENV')=='dev')){
        commonLog("刷安装量：鉴权失败:".$channelid.":".get_client_ip(),['app_version'=>$app_version,'device_id'=>$device_id,'device_type'=>$device_type,'time'=>$time,'sign'=>$sign,'create_sign'=>$create_sign,'create_sign_old'=>$create_sign_old],'init_limit_userinfo_server');
        json_success('success', $false_data);
//        json_fail('非法请求!!');
    }

    $redis_key="init_userinfo_ip_limit:".$channelid.':';

    $init_userinfo_ip_limit_count = sysconf('init_userinfo_ip_limit_count');
    if($redis->get($redis_key.get_client_ip())>=(!empty($init_userinfo_ip_limit_count)?$init_userinfo_ip_limit_count:10)){
//        commonLog("刷安装量：同ip刷量:".$channelid.":".get_client_ip(),[],'init_limit_userinfo');
        commonLog("刷安装量：同ip刷量:".$channelid.":".get_client_ip(),[$_SERVER,$_REQUEST],'init_limit_userinfo_server');
//        json_fail("再瞎鸡巴搞，阉了你");
        json_success('success', $false_data);
    }

    $redis->incr($redis_key.get_client_ip());
    $redis->expire($redis_key.get_client_ip(),3600);


    $redis_foreign_ip_limit_key='foreign_ip_limit:'.$channelid.':';
    $foreign_init_limit_key='foreign_init_limit:'.$channelid.':';
    if(!is_china(get_client_ip())){

        $count = $redis->get($redis_foreign_ip_limit_key)??0;

        if($count>$init_userinfo_ip_limit_count || $redis->get($foreign_init_limit_key)){
            $redis->set($foreign_init_limit_key,1);
            $redis->expire($foreign_init_limit_key,600);
//            commonLog("刷安装量：国外ip刷量:".$channelid.":".get_client_ip(),[],'init_limit_userinfo');
            commonLog("刷安装量：国外ip刷量:".$channelid.":".get_client_ip(),[$_SERVER,$_REQUEST],'init_limit_userinfo_server');
            json_success('success', $false_data);
//            json_fail("再瞎鸡巴搞，阉了你!");
        }

        if(empty($count)){
            $redis->incr($redis_foreign_ip_limit_key);
            $redis->expire($redis_foreign_ip_limit_key,60);
        }

//        commonLog("正常的量:".$channelid.":".get_client_ip(),$_SERVER,'init_limit_userinfo_server');

        $redis->incr($redis_foreign_ip_limit_key);
    }

    if(!empty($_SERVER['HTTP_CLUSTER_CLIENT_IP'])){
        commonLog("刷安装量：国内ip刷量:".$channelid.":".get_client_ip(),[],'init_limit_userinfo');
        json_success('success', $false_data);
//        json_fail("success!！");
    }
}

function generateSignature($parameters, $accessKeySecret) {
    ksort($parameters);
    $canonicalizedQueryString = '';

    foreach ($parameters as $key => $value) {
        $canonicalizedQueryString .= '&' . percentEncode($key) . '=' . percentEncode($value);
    }

    $stringToSign = 'GET&%2F&' . percentEncode(substr($canonicalizedQueryString, 1));
    $signature = base64_encode(hash_hmac('sha1', $stringToSign, $accessKeySecret . '&', true));

    return $signature;
}

function percentEncode($str) {
    $res = urlencode($str);
    $res = str_replace('+', '%20', $res);
    $res = str_replace('*', '%2A', $res);
    $res = str_replace('%7E', '~', $res);

    return $res;
}

/**
 * 获取账户余额（阿里云）
 * @return bool
 */
function getSmsAccountsAli() {
    $accessKeyId = sysconf('ali_sms_access_key_id');
    $accessKeySecret = sysconf('ali_sms_access_key_secret');
    $params = [
        'Action' => 'QueryAccountBalance',
        'Format' => 'JSON',
        'Version' => '2017-12-14',
        'AccessKeyId' => $accessKeyId,
        'SignatureMethod' => 'HMAC-SHA1',
        'Timestamp' => gmdate('Y-m-d\TH:i:s\Z'),
        'SignatureVersion' => '1.0',
        'SignatureNonce' => uniqid(),
    ];

    $params['Signature'] = generateSignature($params, $accessKeySecret);

    $url = 'https://business.aliyuncs.com/?' . http_build_query($params);

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    $response = curl_exec($ch);

    if (curl_errno($ch)) {
        return curl_error($ch);
    } else {
        return json_decode($response, true);
    }

    curl_close($ch);
}


/**
 * 获取短信余量
 * @return bool
 */
function getSmsAccounts()
{

//    base64(devKey:apiDevSecret)
    $url = "https://api.sms.jpush.cn/v1/accounts/dev";
    $ch=curl_init();
    curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'GET');
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER,false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST,false);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER,1);
    curl_setopt($ch, CURLOPT_HTTPHEADER,  [ "Authorization: Basic ".base64_encode("680fd2fd65e04eb4c2a6078d:e829113760d7505a09a0a8aa")]);
    $result = curl_exec($ch);

    curl_close($ch);
    return json_decode($result,true);
}


/**
 * 服务：将时间段按天进行分割
 * @param string $start_date @起始日期('Y-m-d H:i:s')
 * @param string $end_date @结束日期('Y-m-d H:i:s')
 * @return array $mix_time_data=array(
 * 'start_date'=>array([N]'Y-m-d H:i:s'),
 * 'end_date'=>array([N]'Y-m-d H:i:s'),
 * 'days_list'=>array([N]'Y-m-d'),
 * 'days_inline'=>array([N]'Y-m-d H:i:s'),
 * 'times_inline'=>array([N]'time()')
 * )
 */
function date_segmentation(string $start_date, string $end_date): array
{
    /******************************* 时间分割 ***************************/

    //如果为空，则从今天的0点为开始时间
    if (!empty($start_date))
        $start_date = date('Y-m-d H:i:s', strtotime($start_date));
    else
        $start_date = date('Y-m-d 00:00:00', time());


    //如果为空，则以明天的0点为结束时间（不存在24:00:00，只会有00:00:00）
    if (!empty($end_date))
        $end_date = date('Y-m-d H:i:s', strtotime($end_date));
    else
        $end_date = date('Y-m-d 00:00:00', strtotime('+1 day'));


    //between 查询 要求必须是从低到高
    if ($start_date > $end_date) {
        $ttt = $start_date;
        $start_date = $end_date;
        $end_date = $ttt;
    } elseif ($start_date == $end_date) {
        echo '时间输入错误';
        die;
    }


    $time_s = strtotime($start_date);
    $time_e = strtotime($end_date);
    $seconds_in_a_day = 86400;

    //生成中间时间点数组（时间戳格式、日期时间格式、日期序列）
    $days_inline_array = array();
    $times_inline_array = array();

    //日期序列
    $days_list = array();
    //判断开始和结束时间是不是在同一天
    $days_inline_array[0] = $start_date;  //初始化第一个时间点
    $times_inline_array[0] = $time_s;     //初始化第一个时间点
    $days_list[] = date('Y-m-d', $time_s);//初始化第一天
    if (
        date('Y-m-d', $time_s)
        == date('Y-m-d', $time_e)
    ) {
        $days_inline_array[1] = $end_date;
        $times_inline_array[1] = $time_e;
    } else {
        /**
         * A.取开始时间的第二天凌晨0点
         * B.用结束时间减去A
         * C.用B除86400取商，取余
         * D.用A按C的商循环+86400，取得分割时间点，如果C没有余数，则最后一个时间点 与 循环最后一个时间点一致
         */
        $A_temp = date('Y-m-d 00:00:00', $time_s + $seconds_in_a_day);
        $A = strtotime($A_temp);
        $B = $time_e - $A;
        $C_quotient = floor($B / $seconds_in_a_day);    //商舍去法取整
        $C_remainder = fmod($B, $seconds_in_a_day);               //余数

        $days_inline_array[1] = $A_temp;
        $times_inline_array[1] = $A;
        $days_list[] = date('Y-m-d', $A);              //第二天
        for ($increase_time = $A, $c_count_t = 1; $c_count_t <= $C_quotient; $c_count_t++) {
            $increase_time += $seconds_in_a_day;
            $days_inline_array[] = date('Y-m-d H:i:s', $increase_time);
            $times_inline_array[] = $increase_time;
            $days_list[] = date('Y-m-d', $increase_time);
        }
        $days_inline_array[] = $end_date;
        $times_inline_array[] = $time_e;
    }

    return array(
        'start_date' => $start_date,
        'end_date' => $end_date,
        'days_list' => $days_list,
        'days_inline' => $days_inline_array,
        'times_inline' => $times_inline_array
    );
}



/**
 * 数据脱敏
 * @param string $string 需要脱敏值
 * @param int $start 开始
 * @param int $length 结束
 * @param string $re 脱敏替代符号
 * @return bool|string
 * 例子:
 * dataDesensitization('18811113683', 3, 4); //188****3683
 * dataDesensitization('乐杨俊', 0, -1); //**俊
 */
function dataDesensitization(string $string, int $start = 0, int $length = 0, string $re = '*')
{
    if (empty($string)){
        return $string;
    }
    $strarr = array();
    $mb_strlen = mb_strlen($string);
    while ($mb_strlen) {//循环把字符串变为数组
        $strarr[] = mb_substr($string, 0, 1, 'utf8');
        $string = mb_substr($string, 1, $mb_strlen, 'utf8');
        $mb_strlen = mb_strlen($string);
    }
    $strlen = count($strarr);
    $begin = $start >= 0 ? $start : ($strlen - abs($start));
    $end = $last = $strlen - 1;
    if ($length > 0) {
        $end = $begin + $length - 1;
    } elseif ($length < 0) {
        $end -= abs($length);
    }
    for ($i = $begin; $i <= $end; $i++) {
        $strarr[$i] = $re;
    }
    if ($begin >= $end || $begin >= $last || $end > $last) return $string;
    return implode('', $strarr);
}


/**
 * 简单版生成uuid
 * @param string $prefix
 * @return string
 */
function uuid(string $prefix = ''): string
{
    $chars = md5(uniqid(mt_rand(), true));
    $uuid = substr($chars, 0, 8) . '-';
    $uuid .= substr($chars, 8, 4) . '-';
    $uuid .= substr($chars, 12, 4) . '-';
    $uuid .= substr($chars, 16, 4) . '-';
    $uuid .= substr($chars, 20, 12);
    return $prefix . $uuid;
}


/**
 * 获取随机用户名列表
 * @param int $num
 * @return array
 */
function getRandomNameList(int $num=10): array
{
    $name_list = [];
    for($i=0;$i<=$num;$i++){
        $name_list[] = str_replace('_','',env('USER_NAME_PREFIX')).substr(uniqid(strval(rand(0,10000))),-7);
    }
    return $name_list;
}

/**
 * 获取完整范围url
 * @return string
 */
function getFullUrl(){

    $sys_protocal = isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == '443' ? 'https://' : 'http://';
    $php_self = $_SERVER['PHP_SELF'] ? $_SERVER['PHP_SELF'] : $_SERVER['SCRIPT_NAME'];
    $path_info = isset($_SERVER['PATH_INFO']) ? $_SERVER['PATH_INFO'] : '';
    $relate_url = isset($_SERVER['REQUEST_URI']) ? $_SERVER['REQUEST_URI'] : $php_self . (isset($_SERVER['QUERY_STRING']) ? '?' . $_SERVER['QUERY_STRING'] : $path_info);
    return $sys_protocal . (isset($_SERVER['HTTP_HOST']) ? $_SERVER['HTTP_HOST'] : '') . $relate_url;
}

/**
 * 视频播放二次验证加密
 * $data 加密参数
 * @return string
 */
function second_auth_encrypt(array $data){

    static $second_auth_encrypt;
    if (!$second_auth_encrypt) {
        $second_auth_asc_key = sysconf('second_auth_asc_key');
        $second_auth_encrypt = new EasyAes(!empty($second_auth_asc_key)?$second_auth_asc_key:"pP3Ug1Tx8qqT6H0VSpOdIT5d", 128, "################");
    }

    return base64_encode($second_auth_encrypt->encrypt(json_encode($data)));
}

/**
 * 视频播放二次验证加密
 * $second_auth 解密密文
 * @return array
 */
function second_auth_decrypt(string $second_auth){

    static $second_auth_decrypt;
    if (!$second_auth_decrypt) {
        $second_auth_asc_key = sysconf('second_auth_asc_key');
        $second_auth_decrypt = new EasyAes(!empty($second_auth_asc_key)?$second_auth_asc_key:"pP3Ug1Tx8qqT6H0VSpOdIT5d", 128, "################");
    }
    return json_decode($second_auth_decrypt->decrypt(base64_decode($second_auth)),true);
}

/**
 * 获取app对应的名称
 * @return void
 */
function get_app_name(){
    $app_tag = env('CACHE_PREFIX');
    switch ($app_tag){ //兼容生产以前遗留的问题
        case 'MOMO':$app_tag="momo1";break;
        case 'MOMO_TWO':$app_tag="momo2";break;
        case 'MOMO_THIRD':$app_tag="momo3";break;
    }

    return strtolower($app_tag);
}

/**
 * 获取app对应的订单前缀
 * @return string
 */
function get_order_prefix(){
    return get_app_name().'m';
}

/**
 * 去掉项目绝对路径
 * @return array|string|string[]
 */
function remove_app_path(string $path){

    return preg_replace('#/var/www/.*kumi_backend/#','',$path) ;

}

//加盐加密
if(!function_exists('encrypt_password')){

    /**
     * 这里简单处理，不加盐，减少线上变动
     * @param string $password
     * @return string
     */
    function encrypt_password(string $password , string $salt): string
    {
        return md5(base64_encode(md5(trim($password)).$salt));
    }
}

/**
 * 获取随机字符串
 * @param int $length
 * @return string
 */
function getRandStr(int $length=8): string
{
    //字符组合
    $str = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $len = strlen($str)-1;
    $randstr = '';
    for ($i=0;$i<$length;$i++) {
        $num=mt_rand(0,$len);
        $randstr .= $str[$num];
    }
    return $randstr;
}

/**
 * 获取用户推广码
 * @return string
 */
function getPromoCode():string
{
    return random(10, 'upper_number');
}

//参数1是原字符串，参数2是子字符串（区分大小写）
function endWith($str, $suffix): bool
{
    $length = strlen($suffix);
    if ($length == 0) {
        return true;
    }
    return (substr($str, -$length) === $suffix);
}


/**
 * 图片ced一键切换ceb，同时处理旧版本问题
 * @param $url
 * @param $app_version
 * @param $is_version_if 是否需要版本判断
 * @return array|mixed|string|string[]
 */
function changceImgVersion($url,$app_version,$is_version_if=true)
{

    if(sysconf('is_open_ceb')==1){
        if(!$is_version_if){
            return str_replace('.ced','.ceb',$url);
        }else{
            if(version_compare($app_version,'1.5.0','>=')){
                return str_replace('.ced','.ceb',$url);
            }else{
                return str_replace('.ceb','.ced',$url);
            }
        }
    }
    return $url;

}


// 亦或加解密
function xor_crypt(string $data,string $key=XOR_KEY) {
    $result = '';
    for ($i = 0; $i < strlen($data); $i++) {
        $char = substr($data, $i, 1);
        $keyChar = substr($key, $i % strlen($key), 1);
        $asciiCode = ord($char) ^ ord($keyChar);
        $result .= chr($asciiCode);
    }
    return $result;
}



/**
 * 获取header的device-type字段信息(统一方法，以后所有获取header的device-type字段都用这个方法)
 * @return void
 */
function get_header_device_type(){
    $data = request()->header('device-type') ?? "";
    if(empty($data)){
        $data = request()->header('device_type') ?? "";
    }
    return $data;
}


/**
 * 获取header的app-version字段信息(统一方法，以后所有获取header的device-type字段都用这个方法)
 * @return void
 */
function get_header_app_version(){
    $data = request()->header('app-version') ?? "";
    if(empty($data)){
        $data = request()->header('app_version') ?? "";
    }
    return $data;
}

/**
 * 获取header的channelid字段信息(统一方法，以后所有获取header的channelid字段都用这个方法)
 * @return string
 */
function get_header_channelid(){
    return request()->header("channelid") ?? "NONE";
}




/**
 * 自定义转换url规则（目前ai签名使用）
 * @param $data
 * @param $prefix
 * @return array
 */
function custom_http_build_query($data, $prefix = '') {
    $result = array();
    foreach ($data as $key => $value) {
        if(is_array($data)){
            $newKey = $prefix ? "{$prefix}[{$key}]" : "{$key}";
        }else{
            $newKey = $prefix ? "{$prefix}.{$key}" : $key;
        }
        if (is_array($value)) {
            $result = array_merge($result, custom_http_build_query($value, $newKey));
        } elseif (is_object($value)) {
            $result = array_merge($result, custom_http_build_query($value, $newKey));
        } else {
            $result[$newKey] = $value;
        }
    }
    return $result;
}

/**
 * 获取url中路由部分
 * @param $url
 * @return string
 */
function get_url_path($url)
{
    return ltrim(parse_url($url, PHP_URL_PATH),'/');
}

//获取下个月的日期
function get_next_month_days($date)
{
    $timestamp = strtotime($date);
    $arr = getdate($timestamp);
    if ($arr['mon'] == 12) {
        $year = $arr['year'] + 1;
        $month = $arr['mon'] - 11;
        $firstday = $year . '-0' . $month . '-01';
        $lastday = date('Y-m-d', strtotime("$firstday+1month-1day"));
    } else {
        $firstday = date('Y-m-01', strtotime(date('Y', $timestamp) . '-' . (date('m', $timestamp) + 1) . '-01'));
        $lastday = date('Y-m-d', strtotime("$firstday+1month-1day"));
    }
    return array($firstday, $lastday);
}

/**
 * 将第三方http图片上传到自己到服务器桑拿
 * @param string $url
 * @return array|false|int|string|string[]
 */
function uploadImg(string $url)
{

    $cover_url='';
    if(!empty($url) && filter_var($url, FILTER_VALIDATE_URL) !== false ){
        $path_in_folder = UPLOAD_PATH . '/uploads/'.uniqid();
        try {
            $file_get_contents = file_get_contents($url);
            @file_put_contents($path_in_folder,$file_get_contents);
            if(file_exists($path_in_folder) && filesize($path_in_folder)>1024*10){
                $data = uploadImgServer($path_in_folder);
                if ($data && $data['code'] == 0) {
                    if(!empty($data['data']) && filter_var($data['data'], FILTER_VALIDATE_URL) !== false){
                        $cover_url = str_replace('.ced','.ceb',parse_url($data['data'], PHP_URL_PATH));
                    }
                }
            }
        }catch (\Throwable $e){

        }
    }

    return $cover_url;
}


/**
 * 判断是否是内网
 * @param $ip
 * @return bool
 */
function isInternalIP($ip): bool
{
    $internalRanges = [
        '10.0.0.0/8',
        '**********/12',
        '***********/16',
    ];

    foreach ($internalRanges as $range) {
        list($subnet, $bits) = explode('/', $range);
        $subnet = ip2long($subnet);
        $mask = -1 << (32 - $bits);
        $network = $subnet & $mask;

        $ipLong = ip2long($ip);
        if (($ipLong & $mask) == $network) {
            return true;
        }
    }

    return false;
}


/**
 * 判断2个ip前2段是否相同
 * @param string $ip1
 * @param string $ip2
 * @return bool
 */
function areFirstTwoSegmentsEqual(string $ip1, string $ip2) {
    // 拆分 IP 地址
    $ip1_segments = explode('.', $ip1);
    $ip2_segments = explode('.', $ip2);

    // 比较前两个段
    if ($ip1_segments[0] == $ip2_segments[0] && $ip1_segments[1] == $ip2_segments[1]) {
        return true;
    } else {
        return false;
    }
}


/**
 * 自定义前端分页代码
 * @param $totalItems
 * @param int $currentPage
 * @param int $pageSize
 * @param string $url
 * @return string
 */
function paginate($totalItems, $currentPage = 1, $pageSize = 10, $url = '?page=', $pageRange = 8) {
    $totalPages = ceil($totalItems / $pageSize);
    $pagination = '<ul class="pagination">';

    // 生成上一页链接
    if ($currentPage > 1) {
        $pagination .= '<li><a href="' . $url . ($currentPage - 1) . '">&laquo;</a></li>';
    }

    // 计算页码范围
    $startPage = max(1, $currentPage - floor($pageRange / 2));
    $endPage = min($totalPages, $startPage + $pageRange - 1);

    // 调整起始页码以确保显示足够的页码
    if ($endPage - $startPage + 1 < $pageRange) {
        $startPage = max(1, $endPage - $pageRange + 1);
    }

    // 生成页码链接
    for ($page = $startPage; $page <= $endPage; $page++) {
        $activeClass = $page == $currentPage ? 'class="active"' : '';
        $pagination .= '<li><a ' . $activeClass . ' href="' . $url . $page . '">' . $page . '</a></li>';
    }

    // 生成下一页链接
    if ($currentPage < $totalPages) {
        $pagination .= '<li><a href="' . $url . ($currentPage + 1) . '">&raquo;</a></li>';
    }

    $pagination .= '</ul>';
    return $pagination;
}


/**
 * 获取指定日期范围内每天的开始时间和结束时间
 * @param string $startDate 开始指定范围
 * @param string $endDate  结束指定范围
 * @return array
 */
function getDateRangeStartAndEnd(string $startDate, string $endDate) {
    // 将日期转换为时间戳
    $startTimestamp = strtotime($startDate);
    $endTimestamp = strtotime($endDate);

    // 日期区间数组
    $dateRanges = [];

    // 循环从开始日期到结束日期
    for ($currentTimestamp = $startTimestamp; $currentTimestamp <= $endTimestamp; $currentTimestamp += 86400) {
        // 获取当前日期的开始时间和结束时间
        $currentDate = date('Y-m-d', $currentTimestamp);
        $dayStart = $currentDate . ' 00:00:00';
        $dayEnd = $currentDate . ' 23:59:59';

        // 添加到结果数组
        $dateRanges[] = ['start' => $dayStart, 'end' => $dayEnd];
    }

    return $dateRanges;
}


/**
 * php判断浮点数后面小数点为0时，不显示小数点
 * @param $num
 * @return int|mixed
 */
function formatNumber($num) {
    return ($num == intval($num)) ? intval($num) : $num;
}



function setRealChannel()
{
    try {
        $request = request();
        $ip = request()->ip();
        $redis = redis();
        $key = RedisConst::CHANNEL_BIND_KEY. $ip;
        $channelId = $redis->exists($key)?$redis->get($key):$request->header('channelid');

        $ref = new \ReflectionClass($request);
        $prop = $ref->getProperty('header');
        $prop->setAccessible(true);
        $headers = $prop->getValue($request);
        $headers['channelid'] = $channelId;
        $prop->setValue($request, $headers);
    }catch (\Throwable $e){}
}
