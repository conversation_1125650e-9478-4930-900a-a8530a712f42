<?php
/******************************************************************************
 * Copyright (c) Archer 2022.                                                 *
 ******************************************************************************/

declare(strict_types=1);

namespace app\common;

final class BizConst
{
    // pages const
    public const  PAGE_DEFAULT_ROWS = 10;
    public const  PAGE_DEFAULT_NUM  = 1;

    // user account
    public const IS_VISITOR  = 1; //游客
    public const IS_REGISTER = 2; //用户

    public const ENABLED  = 1;
    public const DISABLED = 0;

    //是否需要登录 0不需要 1需要
    const IS_NEED_LOGIN_YES  = 1;
    const IS_NEED_LOGIN_NO = 0;

    // 都是老的博鱼钱包时代的设计
    // public const DEPOSIT_GOLD_REWARD = 1;
    // public const JOIN_VIP_REWARD     = 2;
    // // public const TRANSFER_INTO_BOYU     = 3;
    // public const BALANCE_TO_BAISON              = 4;
    // public const BALANCE_WITHDRAWAL_FROM_BAISON = 5;
    //
    // // game amount records 表子类型与对应描述
    // public const STATEMENTS_SUB_TYPES = [
    //     self::DEPOSIT_GOLD_REWARD            => '充值金币赠送',
    //     self::JOIN_VIP_REWARD                => '开通VIP赠送',
    //     // self::TRANSFER_INTO_BOYU     => '划转博鱼账号',
    //     self::BALANCE_TO_BAISON              => '转出到百胜棋牌',
    //     self::BALANCE_WITHDRAWAL_FROM_BAISON => '从百胜直接提现',// 产品没有下分
    // ];

    // game_amount_record status
    // public const TRANSFER_SUCCESSFUL = 2;
    // public const TRANSFER_FAILED     = 3;
    // public const TRANSFER_WAITING    = 1;
    // public const TRANSFER_STATUS     = [
    //     self::TRANSFER_SUCCESSFUL => '成功',
    //     self::TRANSFER_FAILED     => '失败',
    //     self::TRANSFER_WAITING    => '等待',
    // ];

    public const AUTO_WITHDRAWAL_API_URI = 'uni-finance/payWithdraw/v1/getWithdrawOrder';

    // baison start ============================

    public const BAISON_PROMO_FIRST_DEPOSIT_TYPE = 1;

    // 百胜钱包流水表 类型
    public const TYPE_TRANSFER_TO_BAISON         = 1;
    public const TYPE_TRANSFER_BAISON_WITHDRAWAL = 2;
    public const BAISON_TRANSFER_TYPES_CN        = [
        self::TYPE_TRANSFER_TO_BAISON         => '转入',
        self::TYPE_TRANSFER_BAISON_WITHDRAWAL => '转出',
    ];
    // 提款限额
//    public const BAISON_WITHDRAWAL_RATES = '0.03';
    public const BAISON_WITHDRAWAL_RATES = '0'; //最新需求，手续费不让用户承担2023-02-22
    public const BAISON_WITHDRAWAL_MIN   = 150;
    public const BAISON_WITHDRAWAL_MAX   = 50000;
    // 充值限额
    public const BAISON_DEPOSIT_MIN = 100;
    public const BAISON_DEPOSIT_MAX = 50000;
    // 充值状态
    public const BAISON_DEPOSIT_SUCCESS = 11;
    public const BAISON_DEPOSIT_FAILED  = 12;
    public const BAISON_DEPOSIT_WAITING = 13;
    public const BAISON_DEPOSIT_REWARDS = 14;

    public const BAISON_DEPOSIT_REFUND  = 15;

    public const BAISON_PLATFORM_ADD_SUCCESS  = 16;
    public const BAISON_PLATFORM_REDUCE  = 17;

    public const BAISON_PLATFORM_ACTIVE = 18;


    // 提款状态
    public const BAISON_WITHDRAWAL_SUCCESS = 1;
    public const BAISON_WITHDRAWAL_FAILED  = 2;
    public const BAISON_WITHDRAWAL_WAITING = 3;
    // 自动提款
    public const AUTO_WITHDRAWAL_WAITING = 4;
    public const AUTO_WITHDRAWAL_SUCCESS = 5;
    public const AUTO_WITHDRAWAL_FAILED  = 6;
    // pending状态
    public const BAISON_WITHDRAWAL_HOLDING_STATUS = [
        self::BAISON_WITHDRAWAL_WAITING,
        self::AUTO_WITHDRAWAL_WAITING,
    ];
    // 中文描述
    public const BAISON_TRANSFER_STATUS_CN = [
        self::BAISON_WITHDRAWAL_SUCCESS => '已下分(待打款)',
        self::BAISON_WITHDRAWAL_FAILED  => '下分失败',
        self::BAISON_WITHDRAWAL_WAITING => '下分中',
        self::AUTO_WITHDRAWAL_WAITING   => '打款审核中',
        self::AUTO_WITHDRAWAL_SUCCESS   => '打款成功',
        self::AUTO_WITHDRAWAL_FAILED    => '打款失败',
        self::BAISON_DEPOSIT_REWARDS    => '充值奖励',
        self::BAISON_DEPOSIT_WAITING    => '充值中',
        self::BAISON_DEPOSIT_FAILED     => '充值失败',
        self::BAISON_DEPOSIT_SUCCESS    => '充值成功',
        self::BAISON_DEPOSIT_REFUND     => '提现失败退款',
        self::BAISON_PLATFORM_ADD_SUCCESS=>'平台上分成功',
        self::BAISON_PLATFORM_REDUCE    => '平台下分成功',
        self::BAISON_PLATFORM_ACTIVE    => '投注返现',
    ];

    // baison end ============================

    //银行卡相关
    public const BANK_CARD_BIND_MAX = 5;

    // 极光推送  短信相关
    public const SMS_TOTAL_COUNT     = 'sms:code:count';
    public const SMS_MOBILE_PREFIX   = 'sms:code:';
    public const JPUSH_SEND_CODE_URI = 'https://api.sms.jpush.cn/v1/codes';

    public static function getJPushBasicAuth(): string
    {
        // 从数据库读 这个从数据库读为jami大佬所设计 改为和大佬一样
        //jami:此读方式非本ja设计，代码拿过来就有，楼上的那位同事没搞清楚乱说
        return 'Basic '.\base64_encode(sysconf('jiguan_app_key').":".sysconf('jiguan_master_secret'));
    }

    public static function getChessConfig(): array
    {
        // 按照jami大佬要求，所有棋牌配置写进数据库里
        //jami：本ja从未说过这个要求，楼上的那位同事乱说，顺便问下楼上那位不写数据库配置又打算写哪里？
        return [
            'channel_id'     => sysconf('baison_channel_id'),
            'aesKey'         => sysconf('baison_aes_key'),
            'md5Key'         => sysconf('baison_md5_key'),
            'apiUrl'         => sysconf('baison_api_url'),
            'recordUrl'      => sysconf('baison_record_url'),
            'sub_channel_id' => sysconf('baison_sub_channel_id'),
        ];
    }

    /**
     * 获取调用出款系统的出款接口地址
     * 代码原本是写死常量的，不清楚代码逻辑，暂时不换成数据库配置，直接写个环境判断即可（已换成数据库配置2023-02-25）
     * @return string
     */
    public static function getWithdrawalApiUrl(){

//        $host = 'http://finance-api.91momo50.vip';
//        if(env('APP_ENV')=='pro'){
//            $host = 'http://finance-api.vsxqrhvt.com';
//        }

         return sysconf('finance_api_domain').'/' . self::AUTO_WITHDRAWAL_API_URI;
    }
}
