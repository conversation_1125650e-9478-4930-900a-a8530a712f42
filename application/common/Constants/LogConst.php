<?php
/**
 * 自定义日志
 *
 * <AUTHOR>
 */

namespace app\common\Constants;


class LogConst
{
    /** @var string 日志等级，由于暂时没法往json日志插入level字段，所以只能插入一个搜索关键词暂时代替下 */
    const LOG_LEVEL = [
        'info'=>'log_info:',
        'error'=>'log_error:',
        'http_error'=>'log_http_error:',
    ];

    /**
     * 注：
     * 日志整个项目使用下面的方式打日志
     * commonLog(LogConst::SQL_MONITOR[1],".json_encode(['error'=>$e->getMessage().$e->getFile().$e->getLine()]),LogConst::SQL_MONITOR[0]);
     *
     */

//    const SQL_MONITOR = ['sql_monitor_tag','指定高危表sql全局监听日志'];
    const PAY_ERROR = ['pay_error','支付接口公共部分的异常日志'];

    const PAY_LIMIT = ['pay_limit','拉单限制日志'];

    const AUTO_REGISTER_LIMIT = ['auto_register_limit','自动注册用户限制日志'];
    const BAISON_CALLBACK_DATA = ['baison_callback_data','百胜风控回调数据日志'];
    const NETEASE_LOG = ['netease_log','网易网盾日志'];
    const ALL_REQUEST_LOG = ['all_request_log','所有api请求日志'];
    const CHANGE_MOIVE_LOG = ['change_moive_log','后台待编辑视频转上架视频异常日志'];

    const INTEGAL_CHANGE_LOG = ['integral_change_log','积分变更记录异常日志'];
    const AUTO_REGISTER_LOG = ['auto_register_log','自动注册异常日志'];

    const RECHARGE_GIFT_LOG = ['recharge_gift_log','充值赠送红包异常日志'];
    const INIT_IP_CHECK_LOG = ['init_ip_check_log','初始化游客接口ip检测异常日志'];
    const RETURN_BEFORE_LOG = ['return_before_log','接口返回前置处理异常日志'];
    const AI_LOG = ['ai_log','ai创作异常日志'];
    const MEMORY_MONITOR_LOG = ['memory_monitor_log','异常接口内存监控日志'];
    const GAME_LOG = ['game_log','游戏异常日志'];
    const MESSAGE_POP_CALLBACK_LOG = ['message_pop_callback_log','用户消息弹窗回调日志'];
    const CENTRAL_WALLET_LOG = ['central_wallet_log','用户中心钱包转账异常日志'];

    const ERROR_REPORT_LOG = ['error_report_log','前端异常上报日志'];
    const USER_LOGIN_LOG = ['user_login_log','更新用户登录日志'];
    const ADMIN_VIDEO_RECEIVE_LOG = ['admin_video_receive_log','后台视频接收日志'];
    const SIGN_LIMIT_KEY_LOG = ['sign_limit_key_log','签到限制日志'];
    const SEND_INFO_KEY_LOG = ['send_info_key_log','发送短信异常日志'];





}
