<?php

namespace app\common\Constants;


class RedisConst
{


    /**
     * redis涉及到数组数据缓存的优先使用以下方法
     * cacheSet() , cacheGet(), cacheDel()
     * 如 cacheSet(RedisConst::BATH_UPDATE_USERNAME_KEY,['key'=>'value'],60);
     *
     * 上面方法不能满足的使用 redis()
     * 如
     * $redis = redis();
     * $userinfo = $redis->get(RedisConst::BATH_UPDATE_USERNAME_KEY);
     *
     * 限制api访问频率的统一调用CacheHelper::limitRate()方法
     */

    /*****************API接口端的redis常量********************************/

    /** @var string 获取指定金额所有可用支付方式  */
    const GET_PAY_WAY_FOR_AMOUNT_KEY = 'get_pay_way_for_amount_key:';

    /** @var string 获取指定支付方式所有可用金额  */
    const GET_AMOUNT_FOR_PAY_TYPE_KEY = 'get_amount_for_pay_type_key:';

    /** @var string 发送短信key  */
    const SEND_SMS_KEY = 'send_sms_key:';

    /** @var string 发送短信限制频率key */
    const SEND_SMS_LIMIT_RATE_KEY = 'send_sms_limit_rate_key:';

    /** @var string 发送短信告警限制频率key */
    const SMS_ALARM_KEY = 'sms_alarm_key';

    /** @var string 限制风控时间key */
    const PAY_WIND_CONTROL_TIME_KEY = 'pay_wind_control_time_key:';

    /** @var string 支付拉单首次限制key */
    const PAY_FIRST_LIMIT_KEY = 'pay_first_limit_key:';

    /** @var string 支付风控拉单限制key */
    const PAY_WIND_CONTROL_LIMIT_KEY = 'pay_wind_control_limit_key:';

    /** @var string 广告点击日统计插入数据限制key */
    const ADV_CLICK_DATE_INSERT_KEY = 'adv_click_date_insert_key:';

    /** @var string 百胜回调风控接口限制key */
    const BAISON_FINANCE_CALLBACK_KEY = 'baison_finance_callback_key:';

    /** @var string 用户当日播放次数key */
    const MEMBER_PLAY_COUNT_KEY = 'member_play_count_key:';

    /** @var string 获取订单号自增尾数key */
    const GET_ORDER_NO_NUM_KEY = 'get_order_no_num_key';

    /** @var string 获取最新用户信息缓存key */
    const MEMBER_INFO_NEW_KEY = 'member_info_new_key:';

    /** @var string 百胜提现限制key */
    const BAISON_REMOTE_WITHDRAWAL_LIMIT_KEY = 'baison_remote_withdrawal_limit_key:';

    /** @var string 定时传片任务限制key */
    const SLICE_DATA_CRON_LOCK_KEY = 'slice_data_cron_lock_key';

    /** @var string 获取用户的有效观影券兑换影片key */
    const EXCHANGE_PLAY_FROM_TICKET_KEY = 'exchange_play_from_ticket_key:';

    /** @var string 获取用户拓展表指定用户详细信息key */
    const MEMBER_INFO_EXTRA_DETAIL_KEY = 'member_info_extra_detail_key:';

    /** @var string 获取用户积分钱包表指定用户详细信息key */
    const INTEGRAL_MEMBER_AMOUNT_DETAIL_KEY = 'integral_member_amount_detail_key:';

    /** @var string 百胜用户唯一限制key */
    const BAISON_MEMBER_UNIQUE_KEY = 'baison_member_unique_key:';

    const BAISON_MEMBER_ACTIVE_UNIQUE_KEY = 'baison_member_active_unique_key:';

    /** @var string 转盘活动是否开启缓存 */
    const IS_OPEN_TURNTABLE_KEY = 'is_open_turntable_key:';

    /** @var string 获取ai创作的token */
    const AI_TOKEN_KEY = 'ai_token_key:';

    /** @var string ai创作限制下单key */
    const AI_LIMIT_PAY_KEY = 'ai_limit_pay_key:';

    /** @var string 用户限制频繁手机号创建账号key */
    const LOGIN_MOBILE_INSERTGETID_KEY = 'login_mobile_insertgetid_key:';

    /** @var string m3u8文件缓存key */
    const M3U8_CACHE_KEY = 'm3u8_cache_key:';

    /** @var string 9游用户token缓存key */
    const JYOU_USER_TOKEN_KEY = 'jyou_user_token_key:';

    /** @var string 跑马灯列表数据key */
    const MARQUEE_API_LIST_KEY = 'marquee_api_list_key:';

    /** @var string 订单回调通订单限制key */
    const ORDER_CALLBACK_LIMIT_KEY = 'order_callback_limit_key:';

    /** @var string 重新钱包余额转出限制频率key */
    const MEMBER_AMOUNT_OUT_KEY = 'member_amount_out_key:';

    /** @var string 重新钱包余额转入限制频率key */
    const MEMBER_AMOUNT_INTO_KEY = 'member_amount_into_key:';

    /** @var string 获取ai域名配置key */
    const AI_GET_API_URLS_KEY = 'ai_get_api_urls_key:';

    /** @var string 用户行为获取token授权key */
    const USER_BEHAVIOR_TOKENS_KEY = 'user_behavior_tokens_key:';

    /** @var string 分类视频统计(点击数)key */
    const CHANNEL_CLASSIFY_CLICK_KEY = 'channel_classify_click_key:';

    /** @var string 分类视频统计(免费视频)key */
    const CHANNEL_CLASSIFY_FREE_KEY = 'channel_classify_free_key:';
    /** @var string 分类视频统计(试看视频)key */
    const CHANNEL_CLASSIFY_PREVIEW_KEY = 'channel_classify_preview_key:';
    /** @var string 分类视频统计(播放数)key */
    const CHANNEL_CLASSIFY_PLAY_KEY = 'channel_classify_play_key:';
    /** @var string ai会员转移锁key */
    const AI_TRANSFER_VIP_KEY = 'ai_transfer_vip_key:';

    /** @var string ai订单回调锁锁key */
    const AI_CHANNEL_ORDER_CALLBACK_KEY = 'ai_channel_order_callback_key:';

    /** @var string 用户最后登录更新锁key */
    const USER_UPDATE_LOGIN_KEY = 'user_update_login_key:';

    /** @var string 绑定渠道码key */
    const CHANNEL_BIND_KEY = 'channel_bind_key:';

    /*****************admin后台端的redis常量********************************/

    /** @var string 获取支付通道编码列表key  */
    const GET_PAY_CHANNEL_KEY = 'get_pay_channel_key:';

    /** @var string 获取后台批量设置记忆key  */
    const GET_BATCH_MEMORY_KEY = 'get_batch_memory_key:';

    /** @var string 接收外部视频限制key  */
    const GET_OUTSIDE_VIDEO_KEY = 'get_outside_video_key:';

    /** @var string 检测视频播放失败任务限制key */
    const CHECK_VIDEO_LOCK_KEY = 'check_video_lock_key';

}
