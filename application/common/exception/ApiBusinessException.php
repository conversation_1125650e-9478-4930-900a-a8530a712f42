<?php
namespace app\common\exception;


use Exception;

class ApiBusinessException extends Exception
{

    public function __construct(string $message = null,$errorCode = 0, \Throwable $previous = null)
    {
        $code = $errorCode;
        if (is_array($code)){
            $code = $errorCode[0];
            if ($message == null) {
                $message = $errorCode[1];
            }
        }

        parent::__construct($message, $code, $previous);
    }
}