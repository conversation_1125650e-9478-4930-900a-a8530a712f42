<?php
namespace app\common\exception;

use app\admin\controller\AppInfo;
use app\common\Constants\LogConst;
use app\Helper\JaegerHelper;
use Exception;
use think\exception\Handle;
use think\exception\HttpException;
use think\exception\ValidateException;
use think\facade\Log;


class Http extends Handle
{

    public function report(Exception $e)
    {
        if (!($e instanceof ApiBusinessException)) {
            Log::write(
                [
                    'error'=>$e->getMessage().' '.remove_app_path($e->getFile()).$e->getLine(),
                    'trace'=>$e->getTrace()
                ],LogConst::LOG_LEVEL['http_error'],true);
        }
    }

    public function render(Exception $e)
    {
//        // 参数验证错误


        if ($e instanceof ApiBusinessException) {

            json_fail($e->getMessage(),[], $e->getCode());
        }


//
//        // 请求异常
//        if ($e instanceof HttpException && request()->isAjax()) {
//            return response($e->getMessage(), $e->getStatusCode());
//        }



        if(!empty(JaegerHelper::$tracer)){
            JaegerHelper::$is_error=true;
            $jaegerHelper = JaegerHelper::getClass();
            $jaegerHelper->startActiveSpan(JaegerHelper::ACTIVE_SPANS['error']);
            $jaegerHelper->setTags(['http.status_code'=>$e->getCode(),'error'=>true]);
            $jaegerHelper->log(['code'=>$e->getCode(),'error'=>$e->getMessage().$e->getFile().$e->getLine()]);
        }


        return json(
            [
                'code'=>500,
                'msg'=>$e->getMessage().' '.remove_app_path($e->getFile()).$e->getLine(),
//                'data'=>$e->getTrace(),
            ]
        );

        // 其他错误交给系统处理
//        return parent::render($e);

    }

}
