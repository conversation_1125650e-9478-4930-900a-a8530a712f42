<?php
namespace app\common\exception;


use Exception;

/**
 * 内部捕获专用异常类（仅代码内部捕获使用，不提供给api接口使用）
 */
class InsideBusinessException extends Exception
{

    public function __construct(string $message = null,$errorCode = 0, \Throwable $previous = null)
    {
        $code = $errorCode;
        if (is_array($code)){
            $code = $errorCode[0];
            if ($message == null) {
                $message = $errorCode[1];
            }
        }

        parent::__construct($message, $code, $previous);
    }
}