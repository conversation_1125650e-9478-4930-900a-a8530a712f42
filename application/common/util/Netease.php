<?php

namespace app\common\util;

use app\common\Constants\LogConst;
use app\common\exception\ApiBusinessException;

/**
 * 网易网盾api类
 */
class Netease{

    /** @var string 查询检查接口 */
    const URL_CHECK = 'https://ac.dun.163.com/v3/common/check';

    /** @var int 第三方返回状态码 200：正常，其他值：调用出错 https://support.dun.163.com/documents/15588071870066688?docId=705184381644980224 */
    const CODE_SUCCESS=200;

    /** @var int check接口的 检测结果：0 正常（放行），10 正常（观察），20 致命（拦截） */
    const ACTION_NORMAL=0;
    const ACTION_NORMAL_OBSERVE=10;
    const ACTION_HEAD_OFF=20;


    /** @var array 活动的唯一标识列表 */
    const ACTIVITY_ID_AUTO_REGISTER =[1,'自动注册'];
    const ACTIVITY_ID_HAND_REGISTER =[2,'手动注册'];
    const ACTIVITY_ID_LIST=[
        self::ACTIVITY_ID_AUTO_REGISTER[0]=>self::ACTIVITY_ID_AUTO_REGISTER[1],
        self::ACTIVITY_ID_HAND_REGISTER[0]=>self::ACTIVITY_ID_HAND_REGISTER[1],
    ];

    /**
     * 检查是否作弊请求接口
     * $type 场景 1普通场景
     * @return int
     */
    public function request(array $params,int $type=1)
    {

        $log_msg = LogConst::NETEASE_LOG[1].':'.__FUNCTION__.':'.$type;
        $check_is_ok=0;
        try {
            $data = array_merge($params,[
                'businessId'=>sysconf('netease_business_id'),
                'version'=>300,
                'nonce'=>md5(uniqid().json_encode($params)),
                'timestamp'=>self::getMillisecond(),
                'secretId'=>sysconf('netease_secret_id'),
            ]);

            $data = self::convert_null($data);
            $data = self::toUtf8($data);
            $data["signature"] = self::genSignature(sysconf('netease_secret_key'), $data);
            // print_r($params);

            $options = array(
                'http' => array(
                    'header'  => "Content-type: application/x-www-form-urlencoded",
                    'method'  => 'POST',
                    'timeout' => 3, // read timeout in seconds
                    'content' => http_build_query($data),
                ),
            );
            $context  = stream_context_create($options);
            $result = file_get_contents(self::URL_CHECK, false, $context);

            $response_data = json_decode($result, true);

            $request_data_log=$data;
            $request_data_log['secretId'] = $request_data_log['businessId']='';//日志数据脱敏处理
            commonLog($log_msg,['msg'=>'请求结果','request_data'=>$request_data_log??[],'response_data'=>$response_data],LogConst::NETEASE_LOG[0]);//测试和生产开始上线用的，以后稳定了要关掉这个日志

            if($response_data['code']==self::CODE_SUCCESS){
                if(isset($response_data['result']['action']) && in_array($response_data['result']['action'],[self::ACTION_NORMAL,self::ACTION_NORMAL_OBSERVE])){
                    $check_is_ok=1;
                }
            }else{
                commonLog($log_msg,['msg'=>'第三方请求失败异常','request_data'=>$request_data_log??[],'response_data'=>$response_data],LogConst::NETEASE_LOG[0]);
                throw new ApiBusinessException('第三方请求失败异常',500);
            }

        }catch (\Throwable $e){
            if (!($e instanceof ApiBusinessException)) {
                commonLog($log_msg,['msg'=>'请求失败异常','request_data'=>$request_data_log??[],'error'=>$e->getMessage().$e->getFile().$e->getLine()],LogConst::NETEASE_LOG[0]);
            }

            throw new ApiBusinessException($e->getMessage(),500);
        }

        return $check_is_ok;

    }

    /**
     * 计算参数签名
     * $params 请求参数
     * $secretKey secretKey
     */
    private static function genSignature(string $secretKey, array $params){
        ksort($params);
        $buff="";
        foreach($params as $key=>$value){
            $buff .=$key;
            $buff .=$value;
        }
        $buff .= $secretKey;
        return md5($buff);
    }

    /**
     * 将输入数据的编码统一转换成utf8
     * @params 输入的参数
     * @inCharset 输入参数对象的编码
     */
    private static function toUtf8($params){
        $utf8s = array();
        foreach ($params as $key => $value) {
            $utf8s[$key] = is_string($value) ? mb_convert_encoding($value, "utf8",'auto') : $value;
        }
        return $utf8s;
    }

    private static function convert_null($params) {
        $new_params = array();
        foreach ($params as $key => $value) {
            $new_params[$key] = is_null($value) ? '' : $value;
        }
        return $new_params;
    }

    /**
     * 获取毫秒数
     * @return float
     */
    public static function getMillisecond(){
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
    }



}