<?php

namespace app\crontab;

use app\api\model\Pay;
use app\Service\AiOrderService;
use app\Service\AiService;
use app\Service\AppPayOrderService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class AiAnalysis extends Command
{
    private static $date;

    protected function configure()
    {
        $this->setName('AiAnalysis')
            ->addArgument('date', Argument::OPTIONAL, "具体日期")
            ->setDescription('定时计划：ai数据分析统计');
    }
    protected function execute(Input $input, Output $output)
    {
        self::$date = $input->getArgument('date')??0;
        $this->start();
    }
    public function start(){

        $redis = redis();

        $redis_key="ai_analysis_crontab";

        if(!empty(self::$date)){
            $start_time_format = self::$date.' 00:00:00';
            $end_time_format = self::$date.' 23:59:59';
        }else{
            $start_time_format = date('Y-m-d 00:00:00');
            $end_time_format = date('Y-m-d 23:59:59');
        }

        $start_time = strtotime($start_time_format);
        $end_time = strtotime($end_time_format);

        if($redis->setnx($redis_key,1)){
            $redis->expire($redis_key,60*30);
            $pay_order_where = [
                ['status','=',AppPayOrderService::STATUS_SUCCESS[0]],
                ['order_type','=',AppPayOrderService::ORDER_TYPE_AI[0]],
                ['successtime','between',[$start_time,$end_time]],
            ];


            $install_data = [];

            try{
                 $app_pay_order = Db::name('app_pay_order')->where($pay_order_where)->where(['is_first'=>3])->group('channelid')->fieldRaw("channelid,count(DISTINCT member_id) member_count ,sum(pay_amount) all_pay_amount")->select();
                 foreach ($app_pay_order as $k=>$v){
                     $install_data[$v['channelid']]=[
                         'channelid'=>$v['channelid'],
                         'first_pay_count'=>$v['member_count'],
                         'first_recharge'=>$v['all_pay_amount'],
                     ];
                 }

                $app_pay_order = Db::name('app_pay_order')->where($pay_order_where)->where([['vip_id','=',0]])->group('channelid')->fieldRaw("channelid,count(DISTINCT member_id) member_count , sum(pay_amount) all_pay_amount")->select();
                foreach ($app_pay_order as $k=>$v){
                    $install_data[$v['channelid']]['gold_pay_count']=$v['member_count'];
                    $install_data[$v['channelid']]['gold_recharge']=$v['all_pay_amount'];
                }

                $app_pay_order = Db::name('app_pay_order')->where($pay_order_where)->where([['vip_id','>',0]])->group('channelid')->fieldRaw("channelid,count(DISTINCT member_id) member_count , sum(pay_amount) all_pay_amount")->select();
                foreach ($app_pay_order as $k=>$v){
                    $install_data[$v['channelid']]['vip_pay_count']=$v['member_count'];
                    $install_data[$v['channelid']]['vip_recharge']=$v['all_pay_amount'];
                }


                $app_pay_order = Db::name('app_pay_order')->where($pay_order_where)->group('channelid')->fieldRaw("channelid,count( id) all_count,count(DISTINCT member_id) member_count ,sum(pay_amount) all_pay_amount")->select();
                foreach ($app_pay_order as $k=>$v){
                    $install_data[$v['channelid']]['all_pay_count']=$v['member_count'];
                    $install_data[$v['channelid']]['recharge']=$v['all_pay_amount'];
                }

                $ai_order = Db::name('ai_order')->where([
                    ['consumption_type','=',AiOrderService::CONSUMPTION_TYPE_PAY[0]],
                    ['status','=',AiOrderService::STATUS_SUCCESS[0]],
                    ['create_time','between',[$start_time_format,$end_time_format]],
                ])->group('channelid,ai_type')->fieldRaw("channelid,count(DISTINCT member_id) member_count ,sum(price) all_price,ai_type")->select();
                foreach ($ai_order as $k=>$v){
                    switch ($v['ai_type']){
                        case AiService::AI_TYPE_REMOVE_CLOTHES[0];
                            if(!isset($install_data[$v['channelid']]['gold_consume_remove_clother']))$install_data[$v['channelid']]['gold_consume_remove_clother']=0;
                            $install_data[$v['channelid']]['gold_consume_remove_clother']+=$v['all_price']*100;
                            break;
                        case AiService::AI_TYPE_CHANGE_FACE[0];
                        case AiService::AI_TYPE_MOVIE[0];
                        case AiService::AI_TYPE_DIY_CHANGE_FACE[0];
                            if(!isset($install_data[$v['channelid']]['gold_consume_change_face']))$install_data[$v['channelid']]['gold_consume_change_face']=0;
                            $install_data[$v['channelid']]['gold_consume_change_face']+=$v['all_price']*100;
                            break;
                        case AiService::AI_TYPE_DRAW[0];
                            if(!isset($install_data[$v['channelid']]['gold_consume_draw']))$install_data[$v['channelid']]['gold_consume_draw']=0;
                            $install_data[$v['channelid']]['gold_consume_draw']+=$v['all_price']*100;
                            break;
                    }
                }

                $ai_order = Db::name('ai_order')->where([
                    ['status','=',AiOrderService::STATUS_SUCCESS[0]],
                    ['create_time','between',[$start_time_format,$end_time_format]],
                ])->group('channelid,ai_type')->fieldRaw("channelid,count( id) all_count,ai_type")->select();

                foreach ($ai_order as $k=>$v){

                    switch ($v['ai_type']){
                        case AiService::AI_TYPE_REMOVE_CLOTHES[0];
                            if(!isset($install_data[$v['channelid']]['remove_clother_count']))$install_data[$v['channelid']]['remove_clother_count']=0;
                            $install_data[$v['channelid']]['remove_clother_count']+=$v['all_count'];
                            break;
                        case AiService::AI_TYPE_CHANGE_FACE[0];
                        case AiService::AI_TYPE_MOVIE[0];
                        case AiService::AI_TYPE_DIY_CHANGE_FACE[0];
                        if(!isset($install_data[$v['channelid']]['change_face_count']))$install_data[$v['channelid']]['change_face_count']=0;
                            $install_data[$v['channelid']]['change_face_count']+=$v['all_count'];
                            break;
                        case AiService::AI_TYPE_DRAW[0];
                            if(!isset($install_data[$v['channelid']]['draw_count']))$install_data[$v['channelid']]['draw_count']=0;
                            $install_data[$v['channelid']]['draw_count']+=$v['all_count'];
                            break;
                    }
                }

                $ai_order = Db::name('ai_order')->where([
                    ['status','=',AiOrderService::STATUS_SUCCESS[0]],
                    ['create_time','between',[$start_time_format,$end_time_format]],
                ])->group('channelid')->fieldRaw("channelid,count( DISTINCT member_id) member_count")->select();

                foreach ($ai_order as $k=>$v){
                    if(!isset($install_data[$v['channelid']]['order_count']))$install_data[$v['channelid']]['order_count']=0;
                    $install_data[$v['channelid']]['order_count']+=$v['member_count'];
                }

                $member_promo = Db::name('member_promo')->where([
                    ['promo_source','=',2],
                    ['create_time','between',[$start_time,$end_time]]
                ])->group('member_channelid')->fieldRaw("member_channelid,count( id) all_count")->select();
                foreach ($member_promo as $k=>$v){
                    $install_data[$v['member_channelid']]['invite_count']=$v['all_count'];
                }

                if(isset($install_data[""]))unset($install_data[""]);
                foreach ($install_data as $k=>$v){
                    if(!isset($v['channelid']))$v['channelid']=$k;
                    $v['date']=date('Ymd',$start_time);
                    Db::name('ai_analysis')->insert($v,true);
                }

            }catch (\Throwable $e){
                commonLog('AiAnalysisError', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'AiAnalysisError');
                dump($e->getMessage().$e->getFile().$e->getLine());die();
            }finally{
                $redis->del($redis_key);
            }

        }

    }

}
