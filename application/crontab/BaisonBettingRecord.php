<?php

namespace app\crontab;
use app\api\logic\chess\BaisonLogic;
use app\common\exception\BizException;
use app\extend\Telegram\TelegramBot;
use GuzzleHttp\Client;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\facade\Log;
use app\common\BizConst;

class BaisonBettingRecord extends Command
{
    public $chessConfig = '';
    public $logTtile = "同步百胜投注记录";
    public $logfile = "BaisonBettingRecord";
    protected $httpClient = null;

    protected function configure()
    {
        $this->setName('BaisonBettingRecord')
            ->setDescription($this->logTtile);
    }

    protected function execute(Input $input, Output $output)
    {
//        $this->start();
    }

    public function start(){
        $time = date("Y-m-d H:i:s",time());
        echo "进入成功：".$time;
        $redis = redis();
        commonLog($this->logTtile.':'.$time,["[开始]同步百胜投注记录--》开始"],$this->logfile);
        $this->httpClient = app(Client::class);
        $chessConfig = BizConst::getChessConfig();
        if (empty($chessConfig)) {
            commonLog($this->logTtile.':'.$time,["[异常]同步百胜投注记录--》同步百胜投注记录初始化api接口配置出错"],$this->logfile);
            TelegramBot::sendMessageText(sysconf('order_notice_chat_id'),"[异常]同步百胜投注记录--》同步百胜投注记录初始化api接口配置出错");
            throw new \LogicException('同步百胜投注记录初始化api接口配置出错');
        }

        //print_r($chessConfig);exit;
        $page = 1;
        while(true){
            $this->chessConfig = $chessConfig;
            $this->chessConfig['moneyType'] = 'RMB';
            $queries = [
                'account'     => 11,
                'money_type'    => $this->chessConfig['moneyType'],
                'start_date'    => date("Y-m-d", strtotime("-1 day")),
                'end_date' => date("Y-m-d", strtotime("-1 day")),
                'page' => $page,
                'game_dimension' => 1, // 游戏维度或不传时按游戏类型查询，1按游戏id查询
            ];
            $page++;
            $uri = $this->buildApiUrl($this->buildQueryString($queries, 11));

            if (!$contents = $this->httpGet($uri)) {
                commonLog($this->logTtile.':'.$time,["[异常]同步百胜投注记录--》接口不能通信"],$this->logfile);
                TelegramBot::sendMessageText(sysconf('order_notice_chat_id'),"[异常]同步百胜投注记录--》接口不能通信");
                throw new BizException('同步百胜投注记录--》接口不能通信');
            }
            $contents = json_decode($contents,true);
            if($contents['code'] != 0 ){
                commonLog($this->logTtile.':'.$time,["[异常]同步百胜投注记录--》".$contents['msg']],$this->logfile);
                TelegramBot::sendMessageText(sysconf('order_notice_chat_id'),"[异常]同步百胜投注记录--》".$contents['msg']);
                throw new BizException($contents['msg']);
            }
            //结束死循环
            if(empty($contents['result']['records']) || !isset($contents['result']['records']) ){
                commonLog($this->logTtile.':'.$time,["[结束]同步百胜投注记录--》结束"],$this->logfile);
                echo "结束成功：".$time;
                //同步游戏列表到redis列表里，给后台使用
                $queries = [
                    'account'     => 17,
                ];
                $uri = $this->buildApiUrl($this->buildQueryString($queries, 17));
                $game_list = $this->httpGet($uri);
                $game_list_array = json_decode($game_list,true);
                if( isset($game_list_array['code']) && $game_list_array['code'] == 0 && !empty($game_list_array['result']['records'])){
                    $redis->set('baisongamelist',json_encode($game_list_array['result']['records']));
                }
                break;
            }
            commonLog($this->logTtile.':'.$time, ["[成功]同步百胜投注记录--》成功当前为".$page."页"],$this->logfile);
            $this->insert_data($contents);
        }

    }

    //处理数据
    private function insert_data($contents){
        if(isset($contents['result']['records']) && !empty($contents['result']['records'])){
            foreach($contents['result']['records'] as $key=>$value){
                //user_id:1351_test_test_touzhu1
                try {
                    if(isset($value['user_id']) && !empty($value['user_id'])){
                        $user_name = explode(sysconf('baison_channel_id')."_".sysconf('baison_account_prefix'),$value['user_id']);
                        $value['user_id'] = end($user_name);
                    }
                    $user_info = Db::name('baison_member')->field("baison_group,channelid")->where('account', $value['user_id'])->find();
                    if(!$user_info){
                        continue;
                    }
                    $data = [
                        'game_type' => $value['game_type'],
                        'game_id' => $value['game_id'],
                        'game_name' => $value['game_name'],
                        'user_id' => $value['user_id'],
                        'channelid' => $user_info['channelid'],
                        'all_bet' => $value['all_bet'],
                        'avail_bet' => $value['avail_bet'],
                        'bet_preferential' => 0,
                        'date_label' => $value['date_label'],
                        'create_time' => time(),
                    ];
                    //计算返水金额
                    if(empty($group_id)) $group_id = 1;
                    $configuration = Db::name('baison_bet_active')->where('id',$user_info['baison_group'])->value("configuration");
                    //echo Db::name('baison_bet_active')->getLastSql()."\n";
                    $configuration = json_decode($configuration,true);
                    foreach($configuration as $value){
                        if($data['game_id'] == $value['game_id']){
                            $data['bet_preferential'] = bcmul((float)$data['avail_bet'],$value['coefficient'],2);
                            //echo $data['bet_preferential']."--".$value['game_id']."---".$data['avail_bet']."*".$value['coefficient']."\n";
                        }
                    }
                    $insert = Db::name('baison_betting')->insert($data);
                    if($insert){
                        Db::name('baison_member')->where('account', $data['user_id'])->inc('avail_bet',$data['avail_bet'])->update();
                        Db::name('baison_member')->where('account', $data['user_id'])->inc('bet_preferential',$data['bet_preferential'])->update();
                        //echo Db::name('baison_member')->getLastSql()."\n";
                    }
                }catch (\Throwable $e) {
                    commonLog($this->logTtile.':', ["写入数据失败==>DATA:".json_encode($data??[])."-->info:".$e->getMessage().$e->getFile().$e->getLine()], $this->logfile);
                }
            }
        }
    }

    private function buildApiUrl(string $queryString): string
    {
        $ts = $this->getTimestamp();
        $q  = http_build_query([
            'channel_id' =>  $this->chessConfig['channel_id'],
            'timestamp'  => $ts,
            'param'      => $this->encodeQueryString($queryString),
            'key'        => md5($this->chessConfig['channel_id'].$ts.$this->chessConfig['md5Key'],),
        ]);

        return $this->chessConfig['apiUrl'].'?'.$q;
    }

    private function buildQueryString(array $queries, int $action): string
    {
        $q = array_merge($queries, [
            'action'         => $action,
            'money_type'     => $this->chessConfig['moneyType'],
            'sub_channel_id' => $this->chessConfig['sub_channel_id'],
            'lang'           => 'zh-CN',
        ],);

        return http_build_query($q);
    }

    private function getTimestamp(): int
    {
        return (int)floor(microtime(true) * 1000);
    }

    protected function httpGet(string $uri): ?string
    {
        try {
            $response = $this->httpClient->request('GET', $uri, ['timeout' => 5.0]);
            if (200 === $response->getStatusCode()) {
                return $response->getBody()->getContents();
            }
        } catch (Throwable $e) {
            Log::alert([__METHOD__, $e->getMessage(), $e->getTraceAsString(),],);
        }

        return null;
    }

    private function encodeQueryString(string $queryString): string
    {
        // 由于php版本问题，所以这里与百胜示例代码不可以一致
        return \base64_encode(
            \openssl_encrypt(
                $this->pkcs5_pad(trim($queryString), 16),
                'aes-128-ecb',
                $this->chessConfig['aesKey'],
                OPENSSL_RAW_DATA | OPENSSL_ZERO_PADDING,
            ),
        );
    }

    private function pkcs5_pad($text, $blocksize)
    {
        $pad = $blocksize - (strlen($text) % $blocksize);

        return $text.str_repeat(chr($pad), $pad);
    }
}
