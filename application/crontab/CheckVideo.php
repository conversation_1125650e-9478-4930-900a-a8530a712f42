<?php

namespace app\crontab;

use app\api\model\Pay;
use app\common\Constants\RedisConst;
use app\extend\Telegram\TelegramBot;
use app\Helper\VideoHelper;
use app\Service\SliceDataService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class CheckVideo extends Command
{
    protected function configure()
    {
        $this->setName('CheckVideo')
            ->setDescription('定时计划：检测无法播放的视频');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){


        $start_date = date("Y-m-d H:i:s");
        echo "进入成功：".$start_date."\n";

        $redis = redis();

        $redis_key=RedisConst::CHECK_VIDEO_LOCK_KEY;
//        $redis->del($redis_key);
        if($redis->setnx($redis_key,1)){
            $redis->expire($redis_key,2000);
            Db::name('check_video_log')->where([['id','>',0]])->delete();//先清空记录表
            try{
                Db::name('app_movie')->where([['status','=',1]])->chunk(1000,function ($list){
                    foreach ($list as $v){
                        if(!empty($v['sp_url1'])  ){
                            try{
                                $parse_url = parse_url($v['sp_url1']);
                                $m3u8 =  (new VideoHelper())->rebuildM3u8RawLinks(0, $parse_url['path'], $parse_url['scheme'].'://'.$parse_url['host']);
                                echo '长视频:'.$v['id'].','.$v['title'].",检测结果：".(!empty($m3u8)?'资源存在':'资源不存在')."\n";
                                if(empty($m3u8)){
//                                    sleep(1);
                                    //这里给个机会再检测一次，减少网络波动原因导致检测失败
                                    $m3u8 =  (new VideoHelper())->rebuildM3u8RawLinks(0, $parse_url['path'], $parse_url['scheme'].'://'.$parse_url['host']);
                                    if(empty($m3u8)){
                                        Db::name('check_video_log')->insert(
                                            [
                                                'video_id'=>$v['id'],
                                                'title'=>$v['title'],
                                                'type'=>1,
                                            ]
                                        );
                                    }
                                }
                            }catch (\Throwable $e){
                                commonLog('CheckVideoError', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'CheckVideoError');
                            }

                        }
                    }
                });

                Db::connect('short_video')->table('xg_video_pool')->where([['status','=',1]])->chunk(1000,function ($list){
                    foreach ($list as $v){
                        if(!empty($v['video_path'])  ){
                            try{
                                $parse_url = parse_url($v['video_path']);

                                $m3u8 =  (new VideoHelper())->rebuildM3u8RawLinks(0, $parse_url['path'], $parse_url['scheme'].'://'.$parse_url['host']);
                                echo '短视频:'.$v['id'].','.$v['video_name'].",检测结果：".(!empty($m3u8)?'资源存在':'资源不存在')."\n";

                                if(empty($m3u8)){
//                                    sleep(1);
                                    //这里给个机会再检测一次，减少网络波动原因导致检测失败
                                    $m3u8 =  (new VideoHelper())->rebuildM3u8RawLinks(0, $parse_url['path'], $parse_url['scheme'].'://'.$parse_url['host']);
                                    if(empty($m3u8)){
                                        Db::name('check_video_log')->insert(
                                            [
                                                'video_id'=>$v['id'],
                                                'title'=>$v['video_name'],
                                                'type'=>2,
                                            ]
                                        );
                                    }
                                }
                            }catch (\Throwable $e){
                                commonLog('CheckVideoError', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'CheckVideoError');
                            }

                        }
                    }
                });
            }catch (\Throwable $e){
                commonLog('CheckVideoError', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'CheckVideoError');
            }finally{
                $redis->del($redis_key);
            }

        }

        echo "脚本执行完毕：".date('Y-m-d H:i:s');
        $chat_id=sysconf('timing_data_push_chat_id');
        TelegramBot::sendMessageText($chat_id,get_app_name().$start_date.'-'.date('Y-m-d H:i:s').",视频检测结束");

    }

}
