<?php

namespace app\crontab;

use app\api\model\Pay;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class Count extends Command
{
    protected function configure()
    {
        $this->setName('Count')
            ->setDescription('数据统计');
    }

    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }

    public function start()
    {
        $date = date('Ymd');
        $month = date('Ym');

        // 获取所有渠道
        $channelArray = Db::name('member_info')->group('channelid')->where('is_bot',0)
            ->column('channelid');

        if ($channelArray) {
            foreach ($channelArray as $channelId) {
                if (!$channelId) {
                    // 为什么会有空
                    continue;
                }

                $ckey = 'channel_' . $date . '_' . $channelId;
                $channel = cacheGet($ckey);
                if (!$channel) {
                    continue;
                }

                //key 过期时间为24点
                $expire = strtotime(date("Y-m-d",strtotime("+1 day"))." 00:00:00") - time();
                cacheSet($ckey,1,$expire);

                try {
                    //删除昨日redis数据
                    $yesterday = date('Ymd',strtotime('-1 day'));
                    redis()->del("up_member_{$yesterday}_{$channelId}");
                    redis()->del("comment_member_{$yesterday}_{$channelId}");
                    redis()->del("active_{$yesterday}_{$channelId}");
                    redis()->del("active_{$yesterday}_{$channelId}_visitor");
                    redis()->del("click_ob_{$yesterday}_{$channelId}");
                    redis()->del("pay_member_{$yesterday}_{$channelId}");
                    if (!Db::name('channel')->where('channelid',$channelId)->where('month',$month)->find()) {
                        $insert = [
                            'channelid' => $channelId,
                            'month' => $month,
                        ];
                        Db::name('channel')->insert($insert);
                    }

                    if (!Db::name('channel_day')->where('date',date('Ymd',strtotime("-1 day")))->where('channelid',$channelId)->find()) {
                        $total_install = Db::name('channel_day')->where('channelid',$channelId)->where('date',date("Ymd",strtotime("-1 day")))->value('total_install');   //取昨天的总安装
                        if (!$total_install) {
                            $total_install = 0;
                        }

                        $total_reg = Db::name('channel_day')->where('channelid',$channelId)->where('date',date("Ymd",strtotime("-1 day")))->value('total_reg');   //取昨天的总安装
                        if (!$total_reg) {
                            $total_reg = 0;
                        }

                        $insert = [
                            'date' => date('Ymd'),
                            'channelid' => $channelId,
                            'total_install' => $total_install,
                            'total_reg' => $total_reg,
                        ];

                        Db::name('channel_day')->insert($insert);
                    }
                }catch (\Throwable $e){
                    redis()->del($ckey);
                }
            }
        }

        return true;
    }
}