<?php

namespace app\crontab;

use app\api\model\Pay;
use app\Service\AppPayOrderService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class FailOrder extends Command
{
    protected function configure()
    {
        $this->setName('FailOrder')
            ->setDescription('定时计划：失败订单处理');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){

//        echo "进入成功：".date('Y-m-d H:i:s');


        $redis = redis();

        $redis_key="fail_order_lock";

        if($redis->setnx($redis_key,1)){
            $redis->expire($redis_key,2000);

            try{
                Db::name('app_pay_order')->where(['status'=>1])->where('addtime',"<=",time()-60*10)->chunk(1000,function ($list){
                    foreach ($list as $v){
                        echo date('Y-m-d H:i:s').' ,订单id '. $v['order_id']."\n";
                        Db::name('app_pay_order')->where('id',$v['id'])->update([
                            'status'=>2,
                            'is_first'=>AppPayOrderService::checkFirstOrder(intval($v['order_type']),intval($v['member_id'])),
                            'fail_info'=>'超过规定时间，执行时间：'.date('Y-m-d H:i:s')
                        ]);
                       // (new Pay())->sendTelegramNotice($v['order_id']);
                        Queue::push('app\job\AgentOrderIncJob', ['order_id'=>$v['order_id']], 'AgentOrderIncJob');
                    }
                });
            }catch (\Throwable $e){
                commonLog('FailOrder', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'FailOrder');
            }finally{
                $redis->del($redis_key);
            }

        }

    }

}
