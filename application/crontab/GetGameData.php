<?php

namespace app\crontab;

use app\api\model\Pay;
use app\Service\GameService;
use app\Service\JYouGameService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class GetGameData extends Command
{
    protected function configure()
    {
        $this->setName('GetGameData')
            ->setDescription('定时计划：获取游戏数据');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }

    public function start(){

        try{
//            $this->jyou();
        }catch (\Throwable $e){
//            ,'trace'=>$e->getTrace()
            $error = ['error'=>$e->getMessage().$e->getFile().$e->getLine()];
            commonLog('GetGameDataError', $error, 'GetGameDataError');
            dump($error);
        }

    }

    public function jyou(){

        $JYouGameService = (new JYouGameService([
            'id'=>1,
            'device_type'=>'A',
            'is_visitor'=>2,
        ]));


        $category_type_list = Db::name('category_type_list')->select();
        //获取并更新场馆游戏
        $list = $JYouGameService->commomRequest(JYouGameService::ROUTE_LIST['queryGameByType'],[]);
//        echo json_encode($list,JSON_UNESCAPED_UNICODE);die();
        if(!empty($list)){
            $category_type_list_column = array_column($category_type_list,'id','alias_name');
            foreach ($list as $k=>$v){
                foreach ($v as $k1=>$v1){

                    if(!empty($category_type_list_column[$k])){
                        $where = [
                            'belong_third'=>GameService::BELONG_THIRD_JYOU[0],
                            'third_id'=>$v1['id']??'',
                        ];
                        if(empty(Db::name('game_category_list')->where($where)->field('id')->find())){

                            $insert_data = [
                                'title'=>$v1['zhName'],
                                'en_name'=>$v1['enName'],
                                'third_id'=>$v1['id'],
                                'belong_third'=>GameService::BELONG_THIRD_JYOU[0],
                                'category_type_id'=>$category_type_list_column[$k],
                                'is_maintain'=>$v1['isDisplay'],
                            ];

                            $cover_url = uploadImg($v1['url']);
                            if(!empty($cover_url))$insert_data['cover_url'] = $cover_url;

                            Db::name('game_category_list')->insert($insert_data);
                        }else{
                            $update_data = [
                                'is_maintain'=>$v1['isDisplay'],
//                                'status'=>$v1['status']==0?1:2,
                            ];
                            $cover_url = uploadImg($v1['url']);
                            if(!empty($cover_url))$update_data['cover_url'] = $cover_url;

                            Db::name('game_category_list')->where($where)->update($update_data);
                        }
                    }
                }
            }
        }

        $game_category_list = Db::name('game_category_list')->select();
//        $game_category_list=[['en_name'=>'OBHX']];
        //获取并更新电子游戏
        foreach ($game_category_list as $k=>$category_detail){
            if(!empty($category_detail['en_name'])){
                $list = $JYouGameService->commomRequest(JYouGameService::ROUTE_LIST['getElectronicGameList'],[
                    'apiName'=>$category_detail['en_name'],
                    'pageNumber'=>1,
                    'pageSize'=>100000,
                ]);
//            echo json_encode($list,JSON_UNESCAPED_UNICODE);die();
                if(!empty($list['list'])){
                    foreach ($list['list'] as $k=>$v){
                        $where=[
                            'belong_third'=>GameService::BELONG_THIRD_JYOU[0],
                            'third_id'=>$v['code']??'',
                            'game_category_id'=>$category_detail['id'],
                        ];
                        if(empty(Db::name('game_list')->where($where)->field('id')->find())){
                            $insert_data = [
                                'title'=>$v['zhName'],
                                'en_name'=>$v['enName'],
                                'third_id'=>$v['code'],
                                'belong_third'=>GameService::BELONG_THIRD_JYOU[0],
                                'category_type_id'=>$v['venueType'],
                                'is_maintain'=>$v['isDisplay'],
                                'game_category_id'=>$category_detail['id'],
                            ];

                            $cover_url = uploadImg($v['h5Url']);
                            if(!empty($cover_url))$insert_data['cover_url'] = $cover_url;

                            Db::name('game_list')->insert($insert_data);
                        }else{
                            $update_data = [
                                'is_maintain'=>$v['isDisplay'],
//                                'status'=>$v['status']==0?1:2,
                            ];
                            $cover_url = uploadImg($v['h5Url']);
                            if(!empty($cover_url))$update_data['cover_url'] = $cover_url;

                            Db::name('game_list')->where($where)->update($update_data);
                        }
                    }
                }
            }

        }



    }

}
