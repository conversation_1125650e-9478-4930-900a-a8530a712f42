<?php

namespace app\crontab;

use app\common\Constants\RedisConst;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;

class MemberAdv extends Command
{
    protected function configure()
    {
        $this->setName('adv')
            ->setDescription('定时计划：广告点击');
    }

    protected function execute(Input $input, Output $output)
    {
        $lastTime = $this->msectime() - 1000;
        while (true) {
            $len =   redis()->lLen('click_adv') ;
            if (($this->msectime() - $lastTime < 800 && $len < 1000) || $len < 1) {
                sleep(5);
                continue;
            }

            $sqls =redis()->lRange('click_adv',0,2000);
            $insert_array = [];

            foreach ($sqls as $v){
                $detail = json_decode($v,true);
                $insert_array[]=$detail;

                Db('member_adv_total')->insert($detail);

                $where=[
                    'adv_id'=>$detail['adv_id'],
                    'date'=>date('Y-m-d',$detail['create_time']),
                ];
                if(Db('adv_click_date')->where($where)->cache(RedisConst::ADV_CLICK_DATE_INSERT_KEY.md5(json_encode($where)),86411)->field('id')->find()){
                    Db('adv_click_date')->where($where)->update(['click_count'=> Db::raw("click_count + 1")]);
                }else{
                    Db('adv_click_date')->insert(array_merge($where,['click_count'=>1]));
                }

            }

//            $re= Db('member_adv_total')->insertAll($insert_array);

            $lastTime=$this->msectime();
            echo date('Y-m-d H:i:s', time());
            echo ' 执行: ' . count($insert_array) . '条 执行结果: ';
//            var_dump($re) . PHP_EOL;
            redis()->lTrim('click_adv',count($sqls), -1);
            redis()->lLen('click_adv');
        }

    }
//写入定时任务日志
    function debug($msg = "", $data = [])
    {
        $time = date("Y-m-d H:i:s");
        $a = str_replace("\\/", "/", json_encode($data, JSON_UNESCAPED_UNICODE));
        file_put_contents("./application/crontab/click_adv.log", $time . ":" . $msg . " => " . $a . "\r\n", FILE_APPEND);
    }

    function msectime() {
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
    }

}