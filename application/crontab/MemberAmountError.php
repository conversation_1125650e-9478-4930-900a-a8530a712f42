<?php

namespace app\crontab;

use app\api\model\Pay;
use app\Service\AiService;
use app\Service\AppPayOrderService;
use app\Service\MemberAmountService;
use app\Service\MemberInfoService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class MemberAmountError extends Command
{
    protected function configure()
    {
        $this->setName('MemberAmountError')
            ->setDescription('定时计划：用户余额失败查询');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){

//        echo "进入成功：".date('Y-m-d H:i:s');

        $redis = redis();

        $redis_key="MemberAmountError";

//        $redis->del($redis_key);
        if($redis->setnx($redis_key,1)){
            $redis->expire($redis_key,2000);

            try{
                Db::name('central_wallet_record')->where(['status'=>MemberAmountService::STATUS_WAITING[0]])->order("id asc")->chunk(1000,function ($list)use($redis){
                    foreach ($list as $v){

                        echo date('Y-m-d H:i:s').' ,order_no: '. $v['order_no']."\n";
                        $member_detail_info = MemberInfoService::detail($v['member_id']);
                        $aiService = new AiService($member_detail_info);
                        $memberAmountService = new MemberAmountService($member_detail_info);
                        $result=[];
                        switch ($v['sub_type']){
                            case MemberAmountService::SUB_TYPE_INC_AI[0]:
                                $redis_key2="MemberAmountErrorLock:".$v['order_no'];
                                try {
                                    if($redis->get($redis_key2)<=2){
                                        $result = $aiService->into($v['order_no']);
                                    }

                                }catch (\Throwable $e){

                                    $redis->incr($redis_key2);
                                    $redis->expire($redis_key2,60*60);
                                    commonLog('MemberAmountError', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'MemberAmountError');
                                }


                                if((isset($result['status']) && $result['status']===0) && is_numeric($result['amount']) ){

                                    $memberAmountService->updateBalance($v['member_id'],intval($result['amount']*100),$v['sub_type'],$v['order_no'],MemberAmountService::STATUS_SUCCESS[0],2);
                                }else{
                                    Db::name('central_wallet_record')->where(['order_no'=>$v['order_no']])->update([
                                        'status'=>MemberAmountService::STATUS_FAIL[0]
                                    ]);
                                }
                                break;
                            case MemberAmountService::SUB_TYPE_REDUCE_AI[0]:

                                try {  //转出如果依旧查询第三方失败的话，这里直接先把💰退还给用户先
                                    $result = $aiService->out($v['order_no'],abs(strval($v['amount']))/100);
                                }catch (\Throwable $e){
                                    commonLog('MemberAmountError', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'MemberAmountError');
                                }


                                if(!(isset($result['status']) && $result['status']===0)){
                                    $amount = isset($result['amount']) ?$result['amount']*100: $v['amount'];
                                    $memberAmountService->updateBalance($v['member_id'],abs(intval($amount)),$v['sub_type'],$v['order_no'],MemberAmountService::STATUS_FAIL[0],2);

                                }else{
                                    Db::name('central_wallet_record')->where(['order_no'=>$v['order_no']])->update([
                                        'status'=>MemberAmountService::STATUS_SUCCESS[0]
                                    ]);
                                }

                                break;
                        }

                    }
                });
            }catch (\Throwable $e){
                commonLog('MemberAmountError', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'MemberAmountError');
            }finally{
                $redis->del($redis_key);
            }

        }

    }

}
