<?php

namespace app\crontab;

use app\api\model\Pay;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class MigrateOldData extends Command
{
    protected function configure()
    {
        $this->setName('migrateOldData')
            ->setDescription('定时计划:迁移旧数据');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){

//        echo "进入成功：".date('Y-m-d H:i:s');
        $start_time=strtotime(date('Y-m-d',time()));
        $site_log_one_time=$start_time-86400*7;
        $member_history_time=$start_time-86400*30;
        $video_record_time=$start_time-86400*30;
        $member_open_time=$start_time-86400*30;
        $member_search_time=$start_time-86400*30;
        $member_signin_time=$start_time-86400*60;
        $member_adv_total_time=$start_time-86400*180;

//        dump($video_record_time);die();

        $redis = redis();

        $redis_key="migrateOldData";

        echo date('Y-m-d H:i:s').'：开始处理任务时间：'.date('Y-m-d H:i:s')."\n";

        if($redis->setnx($redis_key,1)){
//        if(true){
            $redis->expire($redis_key,3600);
            try{
                /**------------------归档hg_site_log_one表数据start------------------*/
                Db::name('site_log_one')->where('create_time',"<",$site_log_one_time)->chunk(5000,function ($list){

//                    $install_data=[];
//                    foreach ($list as $v){
//                        $install_data[]=$v;
//                    }
//
//                    $result = Db::connect('mongodb')->table('hg_site_log_one')->insertAll($install_data);
//                    if($result){
                        Db::name('site_log_one')->whereIn('id',array_column($list,'id'))->delete();
//                    }
                });

                echo date('Y-m-d H:i:s').'：hg_site_log_one表归档完成'."\n";
//                dump(Db::name('site_log_one')->getLastSql());die();
                /**------------------归档hg_site_log_one表数据end------------------*/

                /**------------------归档hg_member_history表数据start------------------*/
                Db::name('member_history')->where('create_time',"<",$member_history_time)->chunk(5000,function ($list){

//                    $install_data=[];
//                    foreach ($list as $v){
//                        $install_data[]=$v;
//                    }
//
//                    $result = Db::connect('mongodb')->table('hg_member_history')->insertAll($install_data);
//                    if($result){
                        Db::name('member_history')->whereIn('id',array_column($list,'id'))->delete();
//                    }
                });
                sysconf('member_history_all',Db::connect('mongodb')->table('hg_member_history')->count());
                echo date('Y-m-d H:i:s').'：hg_member_history表归档完成'."\n";

                /**------------------归档hg_member_history表数据end------------------*/


                /**------------------归档xg_video_record表数据start------------------*/
                Db::connect('short_video')->table('xg_video_record')->where('create_time',"<",date('Y-m-d H:i:s',$video_record_time))->chunk(5000,function ($list){

//                    $install_data=[];
//                    foreach ($list as $v){
//                        $install_data[]=$v;
//                    }
//
//                    $result = Db::connect('mongodb')->table('xg_video_record')->insertAll($install_data);
//                    if($result){
                        Db::connect('short_video')->table('xg_video_record')->whereIn('id',array_column($list,'id'))->delete();
//                    }
                });
                echo date('Y-m-d H:i:s').'：xg_video_record表归档完成'."\n";
                /**------------------归档xg_video_record表数据end------------------*/

                /**------------------归档hg_member_open表数据start------------------*/
                Db::name('member_open')->where('create_time',"<",$member_open_time)->chunk(5000,function ($list){

//                    $install_data=[];
//                    foreach ($list as $v){
//                        $install_data[]=$v;
//                    }
//
//                    $result = Db::connect('mongodb')->table('hg_member_open')->insertAll($install_data);
//                    if($result){
                        Db::name('member_open')->whereIn('id',array_column($list,'id'))->delete();
//                    }
                });
                echo date('Y-m-d H:i:s').'：hg_member_open表归档完成'."\n";
                /**------------------归档hg_member_open表数据end------------------*/

                /**------------------归档hg_member_search表数据start------------------*/
                Db::name('member_search')->where('create_time',"<",$member_search_time)->chunk(5000,function ($list){

//                    $install_data=[];
//                    foreach ($list as $v){
//                        $install_data[]=$v;
//                    }
//
//                    $result = Db::connect('mongodb')->table('hg_member_search')->insertAll($install_data);
//                    if($result){
                        Db::name('member_search')->whereIn('id',array_column($list,'id'))->delete();
//                    }
                });
                echo date('Y-m-d H:i:s').'：hg_member_search表归档完成'."\n";
                /**------------------归档hg_member_search表数据end------------------*/

                /**------------------归档hg_member_signin表数据start------------------*/
                Db::name('member_signin')->where('create_time',"<",$member_signin_time)->chunk(5000,function ($list){

//                    $install_data=[];
//                    foreach ($list as $v){
//                        $install_data[]=$v;
//                    }
//
//                    $result = Db::connect('mongodb')->table('hg_member_signin')->insertAll($install_data);
//                    if($result){
                        Db::name('member_signin')->whereIn('id',array_column($list,'id'))->delete();
//                    }
                });
                echo date('Y-m-d H:i:s').'：hg_member_signin表归档完成'."\n";
                /**------------------归档hg_member_signin表数据end------------------*/

                /**------------------归档member_adv_total表数据start------------------*/
                Db::name('member_adv_total')->where('create_time',"<",$member_adv_total_time)->chunk(5000,function ($list){

//                    $install_data=[];
//                    foreach ($list as $v){
//                        $install_data[]=$v;
//                    }
//
//                    $result = Db::connect('mongodb')->table('member_adv_total')->insertAll($install_data);
//                    if($result){
                        Db::name('member_adv_total')->whereIn('id',array_column($list,'id'))->delete();
//                    }
                });
                echo date('Y-m-d H:i:s').'：member_adv_total表归档完成'."\n";
                /**------------------归档member_adv_total表数据end------------------*/


            }catch (\Throwable $e){
                echo date('Y-m-d H:i:s').'：执行错误：'.$e->getMessage().$e->getFile().$e->getLine()."\n";
            } finally {
                $redis->del($redis_key);
            }
        }
        echo date('Y-m-d H:i:s').'：结束处理任务时间：'.date('Y-m-d H:i:s')."\n";

    }

}
