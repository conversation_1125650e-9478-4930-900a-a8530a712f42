<?php

namespace app\crontab;

use app\api\model\Pay;
use app\common\BizConst;
use app\Helper\CacheHelper;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;
use function Complex\sec;

class MigrateOldDataNew extends Command
{

    private static $count_visitor = 0; //清除的游客总数
    private static $count_register = 0;//清除的用户总数

    private static $install_data=[]; //清除的数据

    private static $del_time; //清除的时间周期（单位秒）
    private static $sleep_time; //停止时间（单位秒）
    private static $start_time; //开始时间（单位秒）
    private static $is_execute; //是否执行清理 1执行 2不执行（不执行的话只是查看清理前的数据统计）

    const LOG_PREFIX = 'migrateOldDataNew:'; //日志前缀

    const CLUNK_COUNT=10000; //一次性读取和插入的条数


    protected function configure()
    {
        $this->setName('migrateOldDataNew')
            ->addArgument('is_execute', Argument::OPTIONAL, "是否执行清理 1执行 2不执行（不执行的话只是查看清理前的数据统计）")
            ->addArgument('del_time', Argument::OPTIONAL, "清除的时间周期（单位秒）")
            ->setDescription('定时计划:迁移旧数据新方案（搭配旧方案同时执行）');
    }
    protected function execute(Input $input, Output $output)
    {
        self::$del_time = $input->getArgument('del_time')??0;
        self::$is_execute = $input->getArgument('is_execute')??2;
        self::$start_time = time();

        $this->start();
    }

    public function start(){

        ini_set('memory_limit','8192M');

        $redis = redis();
        $redis_key="migrateOldDataNew1";
        self::printInfo("开始处理任务");

        if($redis->setnx($redis_key,1)){
//        if(true){
            $redis->expire($redis_key,60*60*3);
            try{
                $time=strtotime(date('Y-m-d 00:00:00',time()));
//        一、【保留前台用户】包括：
//            有百胜账变记录的用户
//            当前 有VIP权限的用户
//            近 1年有拉单付费记录（包括成功&失败）的用户
//            最近60日的新注册用户
//            最近 30天 有观看记录的用户
//            最近 60天 有签到记录的用户
//            当前钱包有余额的用户
//            最近1年内有购买过视频的用户

                //todo 由于需求给出的是保留法去排除，所以代码写的时候为了尽量保证性能，只能全部保留条件的先查出来插入内存
                //有百胜账变记录的用户（之前做百胜那个人居然连用户id都不记录，实在是可怕）
                $baison_transfer_list=Db::name('baison_transfer')->group('account')->field('account')->select();
                $baison_transfer_member_ids=[];
                if(!empty($baison_transfer_list)){
                    $baison_transfer_member_ids = Db::name('member_info')->whereIn('account',array_column($baison_transfer_list,'account'))->column('id');
                    if(!empty($baison_transfer_member_ids))$baison_transfer_member_ids=array_flip($baison_transfer_member_ids);
                }
                self::printInfo("查询有百胜账变记录的用户，总数:".count($baison_transfer_member_ids));

                //当前有VIP权限的用户
                $vip_member_ids = Db::name('member_info')->where([['expire_time','>',$time],['super_vip_expire_time','>',$time]])->field('id')->column('id');
                if(!empty($vip_member_ids))$vip_member_ids=array_flip($vip_member_ids);
                self::printInfo("查询当前有VIP权限的用户，总数:".count($vip_member_ids));

                //近 1年有拉单付费记录（包括成功&失败）的用户
                $app_pay_order_member_ids = Db::name('app_pay_order')->group('member_id')->where([['addtime','>',$time-60*60*24*365]])->column('member_id');
                if(!empty($app_pay_order_member_ids))$app_pay_order_member_ids=array_flip($app_pay_order_member_ids);
                self::printInfo("查询近1年有拉单付费记录（包括成功&失败）的用户，总数:".count($app_pay_order_member_ids));

                //最近60日的新注册用户
                $lately_register_member_ids = Db::name('member_info')->where([['create_time','>',$time-60*60*24*60]])->field('id')->column('id');
                if(!empty($lately_register_member_ids))$lately_register_member_ids=array_flip($lately_register_member_ids);
                self::printInfo("查询最近60日的新注册用户，总数:".count($lately_register_member_ids));

                //最近30天有观看记录的用户
                $member_history_member_ids = Db::name('member_history')->group('member_id')->where([['create_time','>',$time-60*60*24*30]])->column('member_id');
                if(!empty($member_history_member_ids))$member_history_member_ids=array_flip($member_history_member_ids);
                self::printInfo("查询最近30天有观看记录的用户，总数:".count($member_history_member_ids));

                //最近60天有签到记录的用户
                $member_signin_member_ids = Db::name('member_signin')->group('member_id')->where([['create_time','>',$time-60*60*24*60]])->column('member_id');
                if(!empty($member_signin_member_ids))$member_signin_member_ids=array_flip($member_signin_member_ids);
                self::printInfo("查询最近60天有签到记录的用户，总数:".count($member_signin_member_ids));

                //当前钱包有余额的用户
                $member_amount_member_ids = Db::name('member_amount')->where([['amount','>',0]])->column('member_id');
                if(!empty($member_amount_member_ids))$member_amount_member_ids=array_flip($member_amount_member_ids);
                self::printInfo("查询当前钱包有余额的用户，总数:".count($member_amount_member_ids));

                //最近1年内有购买过视频的用户
                $app_pay_movie_member_ids = Db::name('app_pay_movie')->group('member_id')->where([['addtime','>',$time-60*60*24*365]])->column('member_id');
                if(!empty($app_pay_movie_member_ids))$app_pay_movie_member_ids=array_flip($app_pay_movie_member_ids);
                self::printInfo("查询最近1年内有购买过视频的用户，总数:".count($app_pay_movie_member_ids));

                /*************************计算预计增加上量比例start*******************************************************/
                //计算全部保留的去重用户数
                $unique_member_ids = array_unique(array_merge(array_flip($baison_transfer_member_ids),array_flip($vip_member_ids),array_flip($app_pay_order_member_ids),array_flip($lately_register_member_ids),array_flip($member_history_member_ids),array_flip($member_signin_member_ids),array_flip($member_amount_member_ids),array_flip($app_pay_movie_member_ids)));

                $register_member_all_count = Db::name('member_info')->where('is_visitor',"=",BizConst::IS_REGISTER)->count();
                $visitor_member_all_count = Db::name('member_info')->where('is_visitor',"=",BizConst::IS_VISITOR)->count();

                $register_member_keep_count = count($unique_member_ids);
//                $register_member_keep_count = Db::name('member_info')->where('is_visitor',"=",BizConst::IS_REGISTER)->whereIn('id',$unique_member_ids)->count();
//                $visitor_member_keep_count = Db::name('member_info')->where('is_visitor',"=",BizConst::IS_VISITOR)->whereIn('id',$unique_member_ids)->count();

                self::printInfo("删除前总用户数：".$register_member_all_count."，删除前总游客数：".$visitor_member_all_count."，保留用户数：".$register_member_keep_count."，删除用户数：".($register_member_all_count-$register_member_keep_count).",预计增加上量比例:".(round(($register_member_all_count-$register_member_keep_count)/$register_member_all_count*100,2))."%");

                /*************************计算预计增加上量比例end*******************************************************/
                if(!empty(self::$del_time)){
                    self::$sleep_time =  intval(self::CLUNK_COUNT/(($register_member_all_count-$register_member_keep_count)/self::$del_time));
                    if(self::$sleep_time>self::$del_time)self::$sleep_time=self::$del_time;

                    self::printInfo("sleep_time时间:".self::$sleep_time);
                }


                if(self::$is_execute==2){
                    self::printInfo("结束");
                }

                Db::name('member_info')->where('is_visitor',"=",BizConst::IS_REGISTER)->where('is_bot','=',0)->where('create_time','<',$time)->field('id,device_id')->chunk(self::CLUNK_COUNT,function ($list)use(
                    $baison_transfer_member_ids,$vip_member_ids,$app_pay_order_member_ids,$lately_register_member_ids,$member_history_member_ids,$member_signin_member_ids,$member_amount_member_ids,$app_pay_movie_member_ids
                ){
//                    dump(444);die();
//                    dump(Db::name('member_info')->getLastSql());die();

                    foreach ($list as $v){
                        if(!(
                            isset($baison_transfer_member_ids[$v['id']])||
                            isset($vip_member_ids[$v['id']])||
                            isset($app_pay_order_member_ids[$v['id']])||
                            isset($lately_register_member_ids[$v['id']])||
                            isset($member_history_member_ids[$v['id']])||
                            isset($member_signin_member_ids[$v['id']])||
                            isset($member_amount_member_ids[$v['id']])||
                            isset($app_pay_movie_member_ids[$v['id']])
                        )){
                            self::$install_data[]=$v;

                            //积攒超过指定条数的话执行删除操作
                            if(count(self::$install_data)>=self::CLUNK_COUNT){
                                $this->delData(array_column(self::$install_data,'id'),array_column(self::$install_data,'device_id'));
                                self::$install_data=[];
                            }
                        }
                    }

                },'id','desc');

                if(!empty(self::$install_data)){ //把最后残余的条数也清理掉
                    $this->delData(array_column(self::$install_data,'id'),array_column(self::$install_data,'device_id'));
                }

            }catch (\Throwable $e){
                self::printInfo('执行错误：'.$e->getMessage().$e->getFile().$e->getLine().$e->getTraceAsString());
            } finally {
                $redis->del($redis_key);
            }
        }
        self::printInfo("结束处理任务，本次清理用户数：".self::$count_register."，清理游客数：".self::$count_visitor);

    }

    protected function delData(array $member_ids,array $device_ids){

//        $start_time=time();

        self::printInfo("开始查询符合删除的用户数据");

       $list = Db::name('member_info')->whereOr(function ($query)use($member_ids){
           $query->where([['is_visitor',"=",BizConst::IS_REGISTER]])->whereIn('id',$member_ids);
       })->whereOr(function ($query)use($device_ids){
            $query->where([['is_visitor',"=",BizConst::IS_VISITOR]])->whereIn('device_id',$device_ids);
        })->select();
        self::printInfo('结束查询符合删除的用户数据：'.(!empty($list)?count($list):0));

        $current_count_visitor=0;
        $current_count_register=0;

       if(!empty($list)){
           $result = Db::connect('mongodb')->table('hg_member_info')->insertAll($list);
           if($result){
               Db::name('member_info')->whereIn('id',array_column($list,'id'))->delete();
           }

           foreach ($list as $k=>$v){
               if($v['is_visitor']==1){
                    self::$count_visitor++;
                   $current_count_visitor++;
               }elseif ($v['is_visitor']==2){
                   self::$count_register++;
                   $current_count_register++;
               }
           }
       }

        self::printInfo('hg_member_info表归档，用户数：'.$current_count_register.'，游客数：'.$current_count_visitor.'，完成');

       foreach (self::delTableList() as $v){
           /**------------------归档hg_member_history表数据start------------------*/
           $list = Db::name($v['table_name'])->whereIn($v['where_field'],$member_ids)->select();
           if(!empty($list)){
               $result = Db::connect('mongodb')->table(config('database.prefix').$v['table_name'])->insertAll($list);
               if($result){
                   Db::name($v['table_name'])->whereIn($v['index_field'],array_column($list,$v['index_field']))->delete();
               }
           }
           self::printInfo(config('database.prefix').$v['table_name'].'表归档'.(!empty($list)?count($list):0).'条，完成');
       }

        //这里还要清除掉对应的用户注册的redis缓存
        $redis = redis();
        $del_device_ids = [];
        foreach ($device_ids as $device_id){
            $del_device_ids[]=CacheHelper::LOGIN_VISITOR_KEY.$device_id;
        }
        if(!empty($del_device_ids)){
            $redis->del(...$del_device_ids);
        }

        if(!empty(self::$sleep_time)){
            $sleep_time = intval(self::$sleep_time-(time()-self::$start_time));
            self::printInfo('开始暂停时间'.',停止时间：'.$sleep_time."s");
            if($sleep_time>0)sleep($sleep_time);
            self::printInfo('结束暂停时间');
            self::$start_time=time();
        }

    }

    /**
     * 输出信息封装
     */
    private static function printInfo(string $string){
        echo self::LOG_PREFIX.date('Y-m-d H:i:s').'：'.$string."\n";
    }

    /**
     * 删除的表配置
     * @return array[]
     */
    private static function delTableList(){

        return [
            ['table_name'=>'member_amount','where_field'=>'member_id','index_field'=>'id'],
            ['table_name'=>'member_search','where_field'=>'member_id','index_field'=>'id'],
            ['table_name'=>'member_signin','where_field'=>'member_id','index_field'=>'id'],
            ['table_name'=>'member_message','where_field'=>'member_id','index_field'=>'id'],
        ];
    }



}