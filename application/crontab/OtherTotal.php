<?php

namespace app\crontab;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;

class OtherTotal extends Command
{
    protected function configure()
    {
        $this->setName('otherTotal')
            ->addArgument('type', Argument::OPTIONAL, "类型")
            ->setDescription('定时计划：其他点击');
    }
    protected function execute(Input $input, Output $output)
    {
        $type = trim($input->getArgument('type'));
        if($type == 10  || $type == 20 || $type == 30){
            $this->start($type);
        }
    }
    public function start($type){
        $lastTime = $this->msectime() - 1000;
        while (true) {
            $len =   redis()->lLen('other_total'.$type) ;
            if (($this->msectime() - $lastTime < 800 && $len < 1000) || $len < 1) {
                sleep(5);
                continue;
            }
            $sqls =redis()->lRange('other_total'.$type,0,2000);
            $insert_array = [];
            foreach ($sqls as $v){
                $insert_array[]=json_decode($v,true);
            }
            $re= Db('other_total')->insertAll($insert_array);
            $lastTime=$this->msectime();
            echo date('Y-m-d H:i:s', time());
            echo ' 执行: ' . count($insert_array) . '条 执行结果: ';
            var_dump($re) . PHP_EOL;
            redis()->lTrim('other_total'.$type,count($sqls), -1);
            redis()->lLen('other_total'.$type);
        }
    }


//写入定时任务日志
    function debug($msg = "", $data = [])
    {
        $time = date("Y-m-d H:i:s");
        $a = str_replace("\\/", "/", json_encode($data, JSON_UNESCAPED_UNICODE));
        file_put_contents("./application/crontab/other_total.log", $time . ":" . $msg . " => " . $a . "\r\n", FILE_APPEND);
    }

    function msectime() {
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
    }

}