<?php

namespace app\crontab;

use app\api\model\Pay;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class PayTools extends Command
{
    protected function configure()
    {
        $this->setName('PayTools')
            ->setDescription('运营工具：充值奖励');
    }

    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }

    public function start()
    {
        $time = time();
        $re = Db::query("SELECT * FROM hg_app_pay_tools_log WHERE status = 1 AND give_time <= {$time} LIMIT 200");

        //增加余额
        foreach ($re as $key => $value) {
            $amount_info = Db::name('member_amount')->where(['member_id' => $value['member_id']])->value('amount');
            // 增加用户余额
            if ($value['amount_give']) {
                Db::query("UPDATE `hg_member_amount` SET `amount` = `amount` + {$value['amount_give']} WHERE `member_id` = {$value['member_id']}");
                $order = CreateDingdan($value['member_id'], 4, 3, rand(0, 9));

                Db::name('app_pay_deal')->insert([
                    'member_id' => $value['member_id'],
                    'order_id' => $order,
                    'amount' => $value['amount_give'],
                    'amount_before' => $amount_info,
                    'amount_now' => $amount_info + $value['amount_give'],
                    'type' => 4, //赠送
                    'sub_type' => 3, //累计充值赠送
                    'addtime' => time(),
                ]);

                Db::name('app_pay_tmp')->insert([
                    'order_id' => $order,
                    'member_id' => $value['member_id'],
                    'amount' => $value['amount_give'],
                    'addtime' => time(),
                    'status' => 0, //状态 赠送默认成功
                    'type' => 3 //类型 赠送
                ]);
            }
            // 增加会员月数
            if ($value['vip_give']) {
                $vip_give = $value['vip_give'] * 24 * 3600;
                $expire_time_old = Db::name('member_info')->where(['id' => $value['member_id']])->value('expire_time');
                if ($expire_time_old <= time()) {
                    //失效后续费或第一次开启
                    $expire_time_old = time();
                }
                $expire_time_new = $expire_time_old + $vip_give;

                Db::query("UPDATE `hg_member_info` SET `expire_time` = {$expire_time_new} WHERE `id` = {$value['member_id']}"); //增加天数

                Db::name('vip_expire_log')->insert([
                    'member_id' => $value['member_id'],
                    'vip_day' => $value['vip_give'],
                    'expire_time_old' => $expire_time_old,
                    'expire_time_new' => $expire_time_new,
                    'type' => 3,
                    'addtime' => time(),
                    'remark' => '累计充值达标自动发放'
                ]);
            }

            // //修改此发放数据
            Db::query("UPDATE `hg_app_pay_tools_log` SET `status` = 0 WHERE `id` = {$value['id']}");
        }
    }
}