<?php

namespace app\crontab;

use app\api\model\Pay;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class PayType extends Command
{
    protected function configure()
    {
        $this->setName('PayType')
            ->setDescription('修改订单类型');
    }

    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }

    public function start()
    {
        echo '---------------- 支付配置 start ----------------' . PHP_EOL;
        Db::name('app_pay')->chunk(100,function ($list){
            $wx = $zfb = [];
            foreach ($list as $item) {
                if (strstr($item['show_name'], '微信')) {
                    $wx[] = $item['id'];
                }
                if (strstr($item['show_name'], '支付宝')) {
                    $zfb[] = $item['id'];
                }
            }

            Db::startTrans();
            if ($wx) {
                Db::name('app_pay')->where('id','in',$wx)->update(['pay_type' => 1]);
            }
            if ($zfb) {
                Db::name('app_pay')->where('id','in',$zfb)->update(['pay_type' => 2]);
            }
            Db::commit();
        });
        echo '---------------- 支付配置 end ----------------' . PHP_EOL;

        echo '---------------- 订单 start ----------------' . PHP_EOL;
        Db::name('app_pay_order')->chunk(100,function ($list){
            $wx = $zfb = [];
            foreach ($list as $item) {
                if (strstr($item['info'], '微信')) {
                    $wx[] = $item['id'];
                }
                if (strstr($item['info'], '支付宝')) {
                    $zfb[] = $item['id'];
                }
            }

            Db::startTrans();
            if ($wx) {
                Db::name('app_pay_order')->where('id','in',$wx)->update(['pay_type' => 1]);
            }
            if ($zfb) {
                Db::name('app_pay_order')->where('id','in',$zfb)->update(['pay_type' => 2]);
            }
            Db::commit();
        });
        echo '---------------- 订单 end ----------------' . PHP_EOL;

    }
}