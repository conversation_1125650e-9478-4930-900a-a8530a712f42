<?php

namespace app\crontab;
use app\common\exception\BizException;
use app\extend\Telegram\TelegramBot;
use GuzzleHttp\Client;
use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;
use think\facade\Log;
use app\common\BizConst;

class RankingStatistics extends Command
{
    public $chessConfig = '';
    public $logTtile = "同步排行榜";
    public $logfile = "RankingStatistics";
    protected $httpClient = null;

    protected function configure()
    {
        $this->setName('RankingStatistics')
            ->setDescription($this->logTtile);
    }

    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }

    public function start(){
        ini_set('max_execution_time',0);
        $time = date("Y-m-d",time());
        echo "进入成功：".$time."\n";
        $redis = redis();
        commonLog($this->logTtile.':'.$time,["[开始]RankingStatistics--》开始"],$this->logfile);
        $page = 1;
        $items_per_page = 20;
        // 昨天的开始时间戳
        $start_timestamp = strtotime("yesterday");
        // 昨天的结束时间戳
        $end_timestamp = strtotime("today") - 1;
        while(true){
            $offset = ($page - 1) * $items_per_page;
            $re = Db::name('app_movie')->field("id")->order('id desc')->limit($offset,$items_per_page)->select();
            //echo Db::name('app_movie')->getLastSql()."\n";exit;
            $page++;
            if(!$re){
                echo "结束成功：".$time;
                break;
            }
            foreach($re as $key=>$value){
                $insert_data = [];
                $insert_status = false;
                $insert_data['movie_id'] = $value['id'];
                $insert_data['time'] = date("Y-m-d",strtotime("yesterday"));
                $app_pay_movie = Db::name('app_pay_movie')->field("SUM( amount ) AS pay_amount, COUNT( member_id ) AS pay_members")
                    ->where('addtime','>=',$start_timestamp)
                    ->where('addtime','<=',$end_timestamp)
                    ->where('movie_id','=',$value['id'])
                    ->find();
                if($app_pay_movie['pay_amount'] !=0 && $app_pay_movie['pay_members'] !=0){
                    //销售总额
                    $insert_data['pay_amount'] = $app_pay_movie['pay_amount'];
                    //销售总人数
                    $insert_data['pay_members'] = $app_pay_movie['pay_members'];
                    $insert_status = true;
                }
                $history_members = Db::name('member_history')->field("COUNT( member_id ) AS history_members")
                    ->where('create_time','>=',$start_timestamp)
                    ->where('create_time','<=',$end_timestamp)
                    ->where('movie_id','=',$value['id'])
                    ->find();
                if($history_members['history_members'] !=0){
                    //播放次数
                    $insert_data['history_members'] = $history_members['history_members'];
                    $insert_status = true;
                }
                $member_movie_up_down = Db::name('member_movie_up_down')->field("COUNT( member_id ) AS member_movie_up_down")
                    ->where('create_time','>=',$start_timestamp)
                    ->where('create_time','<=',$end_timestamp)
                    ->where('movie_id','=',$value['id'])
                    ->find();
                if($member_movie_up_down['member_movie_up_down'] !=0){
                    //点赞次数
                    $insert_data['member_movie_up_down'] = $member_movie_up_down['member_movie_up_down'];
                    $insert_status = true;
                }
                $member_comment = Db::name('member_comment')->field("COUNT( member_id ) AS member_comment")
                    ->where('create_time','>=',$start_timestamp)
                    ->where('create_time','<=',$end_timestamp)
                    ->where('movie_id','=',$value['id'])
                    ->find();
                if($member_comment['member_comment'] !=0){
                    //评论次数
                    $insert_data['member_comment'] = $member_comment['member_comment'];
                    $insert_status = true;
                }
                $folder_info = Db::name('member_folder_info')->field("COUNT( member_id ) AS folder_info")
                    ->where('create_time','>=',$start_timestamp)
                    ->where('create_time','<=',$end_timestamp)
                    ->where('movie_id','=',$value['id'])
                    ->find();
                if($folder_info['folder_info'] !=0){
                    //评论次数
                    $insert_data['member_folder'] = $folder_info['folder_info'];
                    $insert_status = true;
                }
                if($insert_status){
                    $this->insert_data($insert_data);
                }
            }
        }

    }

    //处理数据
    private function insert_data($insert_data){
        try {
        $insert = Db::name('movie_statistics')->insert($insert_data);
        }catch (\Throwable $e) {
            commonLog($this->logTtile.':', ["写入数据失败==>DATA:".json_encode($insert_data)."-->info:".$e->getMessage().$e->getFile().$e->getLine()], $this->logfile);
        }
    }
}
