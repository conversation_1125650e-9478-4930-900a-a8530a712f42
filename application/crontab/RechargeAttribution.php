<?php

namespace app\crontab;

use app\api\model\Pay;
use app\Service\AiOrderService;
use app\Service\AiService;
use app\Service\AppPayOrderService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class RechargeAttribution extends Command
{
    private static $date;

    protected function configure()
    {
        $this->setName('RechargeAttribution')
            ->addArgument('date', Argument::OPTIONAL, "具体日期")
            ->setDescription('定时计划：充值归因');
    }
    protected function execute(Input $input, Output $output)
    {
        self::$date = $input->getArgument('date')??0;
        $this->start();
    }
    public function start(){

        $redis = redis();

        $redis_key="RechargeAttributionCrontab";

        if(empty(self::$date)){
            $last_date = date('Y-m-d',time()-86400);
            $redis_last_time_key="RechargeAttributionCrontabLastTime:".$last_date;

            if(empty($redis->get($redis_last_time_key))){
                self::$date=$last_date;
                $redis->set($redis_last_time_key,1,86410);
            }else{
                self::$date = date('Y-m-d');
            }
        }

        $start_time_format = self::$date.' 00:00:00';
        $end_time_format = self::$date.' 23:59:59';


        $start_time = strtotime($start_time_format);
        $end_time = strtotime($end_time_format);

        if($redis->setnx($redis_key,1)){
//        if(true){
            $redis->expire($redis_key,60*30);

            try{

                $pay_order_where = [
                    ['status','=',AppPayOrderService::STATUS_SUCCESS[0]],
                    ['page_source','=',AppPayOrderService::PAGE_SOURCE_VIDEO[0]],
                    ['page_source_id','>',0],
                    ['order_type','IN',[AppPayOrderService::ORDER_TYPE_MOVIE[0],AppPayOrderService::ORDER_TYPE_AI[0]]],
                    ['successtime','between',[$start_time,$end_time]],
                ];


                $install_data = [];

                $app_movie_column = Db::name('app_movie')->column("title","id");


                $member_info_column=[];
                $app_pay_order_member = Db::name('app_pay_order')->where($pay_order_where)->column("DISTINCT member_id");
                if(!empty($app_pay_order_member)){
                    $member_info_column = Db::name('member_info')->whereIn('id',$app_pay_order_member)->where('create_time','between',[$start_time,$end_time])->column('account','id');
                }

                if(!empty($member_info_column)){
//                    $app_pay_order = Db::name('app_pay_order')->where($pay_order_where)->group("channelid,page_source_id")->fieldRaw("channelid,page_source_id,count(DISTINCT member_id) member_count ,sum(pay_amount) all_pay_amount")->select();

                    $app_pay_order=[];
                    $app_pay_order_temp = Db::name('app_pay_order')->where($pay_order_where)->fieldRaw("channelid,page_source_id,member_id,pay_amount")->select();

                    foreach ($app_pay_order_temp as $k=>$v){
                        $channelid_page_source = $v['channelid'].'-'.$v['page_source_id'];
                        $app_pay_order[$channelid_page_source]=[
                            'member_id'=>$v['member_id'],
                            'channelid'=>$v['channelid'],
                            'page_source_id'=>$v['page_source_id'],
                        ];

                        if(!isset($app_pay_order[$channelid_page_source]['member_count'])){
                            $app_pay_order[$channelid_page_source]['member_count']=0;
                        }
                        if(!isset($app_pay_order[$channelid_page_source]['all_pay_amount'])){
                            $app_pay_order[$channelid_page_source]['all_pay_amount']=0;
                        }
                        if(!isset($app_pay_order[$channelid_page_source]['member_ids'])){
                            $app_pay_order[$channelid_page_source]['member_ids']=[];
                        }
                        if(!in_array($v['member_id'],$app_pay_order[$channelid_page_source]['member_ids'] )){
                            $app_pay_order[$channelid_page_source]['member_count'] += 1;
                            $app_pay_order[$channelid_page_source]['member_ids'][] = $v['member_id'];
                        }

                        $app_pay_order[$channelid_page_source]['all_pay_amount'] += $v['pay_amount'];

                    }

                    foreach ($app_pay_order as $k=>$v){

                        if(isset($member_info_column[$v['member_id']]) && !empty($app_movie_column[$v['page_source_id']]) && !empty($v['channelid']) ){
                            $install_data[]=[
                                'channelid'=>$v['channelid'],
                                'movie_id'=>$v['page_source_id'],
                                'recharge_count'=>$v['member_count'],
                                'recharge'=>$v['all_pay_amount'],
                            ];
                        }
                    }
                    if(isset($install_data[""]))unset($install_data[""]);

                    foreach ($install_data as $k=>$v){
                        $v['date']=self::$date;
                        Db::name('channel_recharge_attr')->insert($v,true);
                    }
                }

            }catch (\Throwable $e){
                $redis->del($redis_key);
                commonLog('RechargeAttribution', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'RechargeAttribution');
                dump($e->getMessage().$e->getFile().$e->getLine());die();
            }finally{
                $redis->del($redis_key);
            }

        }

    }

}
