<?php

namespace app\crontab;

use app\api\model\Pay;
use app\Service\NewReportService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class SiteDay extends Command
{
    protected function configure()
    {
        $this->setName('SiteDay')
            ->setDescription('定时计划：日统计报表');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){

        echo "日统计报表进入成功：".date('Y-m-d H:i:s')."\n";;

        $redis = redis();
        $date = date('Y-m-d', strtotime("-1 day"));
        $star = strtotime($date . ' 00:00:00'); //今日开始时间
        $end = strtotime($date . ' 23:59:59'); //今日结束时间
        $d = Db::name('site_day')->where("create_date= '" . $date . "' ")->find();

        if (!$d) {
//            debug('十二点定时任务开始执行');

            $list = [];
            $list['movie_num'] = Db::query("SELECT COUNT('id') as movie FROM hg_app_movie WHERE create_time>={$star} AND create_time<={$end}")['0']['movie'];

            $list['user_num'] = Db::query("SELECT count('id') as `user` FROM hg_member_info WHERE is_visitor<>1 AND create_time>={$star} AND create_time<={$end}")['0']['user']; //今日注册用户

            $list['visitor_num'] = Db::query("SELECT count('id') as `visitor` FROM hg_member_info WHERE is_visitor=1 AND create_time>={$star} AND create_time<={$end}")['0']['visitor']; //今日游客数

            $list['history_num'] = Db::query("SELECT count('id') as `history` FROM hg_member_history WHERE create_time>={$star} AND create_time<={$end}")['0']['history']; //今日用户播放记录数

            $list['cache_num'] = Db::query("SELECT count('id') as `cache` FROM hg_member_cache WHERE create_time>={$star} AND create_time<={$end}")['0']['cache']; //今日用户缓存数

            $list['up_num'] = Db::query("SELECT count('id') as `up` FROM hg_member_movie_up_down WHERE create_time>={$star} AND create_time<={$end}")['0']['up']; //用户点赞数
            $list['like_num'] = Db::query("SELECT count('id') as `like` FROM hg_member_like WHERE create_time>={$star} AND create_time<={$end}")['0']['like']; //用户喜欢数

            $list['comment_num'] = Db::query("SELECT count('id') comment FROM hg_member_comment WHERE create_time>={$star} AND create_time<={$end}")['0']['comment']; //用户评论数
            $list['adv_num'] = Db::query("SELECT count('id') as `adv` FROM hg_member_click_adv WHERE create_time>={$star} AND create_time<={$end}")['0']['adv']; //
            //用户点击广告数
            $list['promo_num'] = Db::query("SELECT count('id') `promo` FROM hg_member_promo WHERE create_time>={$star} AND create_time<={$end}")['0']['promo'];
            $list['open_num'] = Db::query("SELECT count('id') `open` FROM hg_member_open WHERE create_time>={$star} AND create_time<={$end}")['0']['open'];


            $list['type_num1'] = Db::query("SELECT count(DISTINCT member_id) as total FROM hg_site_log_one WHERE create_time>={$star} AND create_time<={$end} AND type = 1")['0']['total']; //今日游客
            $list['type_num2'] = Db::query("SELECT count(DISTINCT member_id) as total FROM hg_site_log_one WHERE create_time>={$star} AND create_time<={$end} AND type = 2")['0']['total']; //今日用户
            $list['type_num3'] = Db::query("SELECT count(DISTINCT member_id) as total FROM hg_site_log_one WHERE create_time>={$star} AND create_time<={$end} AND type = 3")['0']['total']; //今日会员


            $list['qrcode_num'] = Db::query("SELECT count('id') `qrcode` FROM hg_member_info WHERE save_qrcode_time>={$star} AND save_qrcode_time<={$end}")['0']['qrcode'];
            $list['feedback_num'] = Db::query("SELECT count('id') `feedback` FROM hg_member_feedback WHERE create_time>={$star} AND create_time<={$end}")['0']['feedback'];
            $list['ip'] = Db::query("SELECT count( * ) as `ip`FROM ( SELECT create_ip FROM hg_site_log_one WHERE create_time >= {$star} AND create_time <= {$end} GROUP BY create_ip ) t")['0']['ip'];
            // $list['pv'] = Db::query("SELECT count('id') as `pv` FROM hg_site_log WHERE create_time>={$star} AND create_time<={$end}")['0']['pv'];
            $list['pv'] = $redis->hGet('site_log_pv', $date);
            if ($date == '2021-01-21') {
                $list['pv'] += 6270000;
            }

            $list['uv'] = Db::query("SELECT count('*') as `uv` FROM ( SELECT device_id FROM hg_site_log_one WHERE create_time >= {$star} AND create_time <= {$end} GROUP BY device_id ) d")['0']['uv'];


            //今日首冲人数
            $list['pay_first'] = Db::query("SELECT COUNT(*) as count from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} AND type = 1 AND is_first = 1")[0]['count'];
            //今日充值人数
            $list['pay_member'] = Db::query("SELECT COUNT(DISTINCT member_id) as count from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} AND type = 1")[0]['count'];
            //今日充值总额
            $list['pay_amount'] = Db::query("SELECT sum(amount) as count from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} AND type = 1")[0]['count'] / 100;
            //今日购买vip人数与总额
            $list['pay_vip'] = Db::query("SELECT COUNT(DISTINCT member_id) as count from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} AND type = 3 AND sub_type = 1")[0]['count'];
            $list['pay_vip_amount'] = -Db::query("SELECT sum(amount) as count from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} AND type = 3 AND sub_type = 1")[0]['count'] / 100;
            //今日购买视频人数与总额
            $list['pay_movie'] = Db::query("SELECT COUNT(DISTINCT member_id) as count from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} AND type = 3 AND sub_type = 2")[0]['count'];
            $list['pay_movie_amount'] = -Db::query("SELECT sum(amount) as count from hg_app_pay_deal WHERE addtime >= {$star} AND addtime <= {$end} AND type = 3 AND sub_type = 2")[0]['count'] / 100;


            $format_start = date('Y-m-d H:i:s',$star);
            $format_end = date('Y-m-d H:i:s',$end);

            //短视频点赞
            $newvideo_like = Db::connect('short_video')->query("SELECT COUNT('id') as video_like FROM xg_video_like WHERE create_time>='{$format_start}' AND create_time<='{$format_end}'")[0]['video_like'];
            $list['video_like'] = $newvideo_like + Db::query("SELECT COUNT('id') as video_like FROM hg_video_clip_like WHERE create_time>='{$format_start}' AND create_time<='{$format_end}' AND like_type = 1 AND cancel_time = 100")['0']['video_like'];

            //短视频评论
            $newvideo_comment = Db::connect('short_video')->query("SELECT COUNT('id') as video_comment FROM xg_video_comment  WHERE create_time>='{$format_start}' AND create_time<='{$format_end}'")[0]['video_comment'];
            $list['video_comment'] = $newvideo_comment + Db::query("SELECT COUNT('id') as video_comment FROM hg_video_clip_comment WHERE create_time>={$star} AND create_time<={$end} ")['0']['video_comment'];

            //短视频播放
            $newvideo_play = Db::connect('short_video')->query("SELECT COUNT('id') as video_play FROM xg_video_record WHERE create_time>='{$format_start}' AND create_time<='{$format_end}' ")[0]['video_play'];
            $list['video_play'] = $newvideo_play + Db::query("SELECT COUNT('id') as video_play FROM hg_video_clip_play WHERE create_time>='{$format_start}' AND create_time<='{$format_end}' ")['0']['video_play'];

            //新增短视频
            $newvideo = Db::connect('short_video')->query("SELECT COUNT('id') as video FROM xg_video_pool WHERE create_time>='{$format_start}' AND create_time<='{$format_end}'")[0]['video'];
            $list['video'] = $newvideo + Db::query("SELECT COUNT('id') as video FROM hg_video_clip WHERE create_time>='{$format_start}' AND create_time<='{$format_end}'")['0']['video']; //今日新增短视频

            $list['create_date'] = $date;
            echo "十二点定时任务数据查询完成：".date('Y-m-d H:i:s').json_encode($list,JSON_UNESCAPED_UNICODE)."\n";

            Db::name('site_day')->insert($list);
            echo "十二点定时任务数据添加完成：".date('Y-m-d H:i:s')."\n";;

        }

        echo "月报表数据预生成：开始：".date('Y-m-d H:i:s')."\n";
        (new NewReportService())->newReportData();
        echo "月报表数据预生成：结束：".date('Y-m-d H:i:s')."\n";
        
    }

}
