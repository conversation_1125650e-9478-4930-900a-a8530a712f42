<?php

namespace app\crontab;

use app\api\model\Pay;
use app\common\Constants\RedisConst;
use app\Service\SliceDataService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class SliceDataCron extends Command
{
    protected function configure()
    {
        $this->setName('sliceDataCron')
            ->setDescription('定时计划：定时上架待传片视频');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){

        echo "进入成功：".date('Y-m-d H:i:s');

        $redis = redis();

        $redis_key=RedisConst::SLICE_DATA_CRON_LOCK_KEY;

        if($redis->setnx($redis_key,1)){
            $redis->expire($redis_key,2000);
//            $redis->del($redis_key);

            try{

                Db::name('slice_data')->where([
                    ['status','=',SliceDataService::STATUS_EDITED_WAITING[0]],
                    ['cron_edited_time','<',date('Y-m-d H:i:s')]],
                )->chunk(1000,function ($list){
                    foreach ($list as $v){
                        if(!empty($v['cron_edited_time']) && !empty($v['cron_edited_data'])  ){
                            $cron_edited_data  = json_decode($v['cron_edited_data'],true);
                            if(!empty($cron_edited_data['post']) && !empty($cron_edited_data['data'])  && !empty($cron_edited_data['res'])  ){
                                echo date('Y-m-d H:i:s').' ,slice_data_id '. $v['id']."\n";
                                (new SliceDataService())->changeMoive($cron_edited_data['post'],$cron_edited_data['data'],$cron_edited_data['res']);
                            }
                        }
                    }
                });
            }finally{
                $redis->del($redis_key);
            }

        }

    }

}
