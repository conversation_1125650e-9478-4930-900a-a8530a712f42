<?php

namespace app\crontab;

use app\api\model\Pay;
use app\extend\Telegram\TelegramBot;
use Psr\Log\Test\DummyTest;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class SmsAccounts extends Command
{
    protected function configure()
    {
        $this->setName('SmsAccounts')
            ->setDescription('定时计划：短信余量通知接口');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){

        echo '遗弃用';die();

        echo "短信余额监控进入成功：".date('Y-m-d H:i:s')."\n";

        $threshold_count=300;
        $chat_id=sysconf('business_note_chat_id');

        try{
//            $data = getSmsAccounts();
            $data = getSmsAccountsAli();

            if(isset($data['Data']['AvailableAmount'])){
                $data['Data']['AvailableAmount'] = str_replace(',','',$data['Data']['AvailableAmount']);
                if(bccomp(strval($data['Data']['AvailableAmount']),strval($threshold_count),3)<1){
                    TelegramBot::sendMessageText($chat_id,"阿里云短信账号余额低于：".$threshold_count." , 当前短信账号余额：".$data['Data']['AvailableAmount']);
                }
            }else{
                TelegramBot::sendMessageText($chat_id,"短信余量查询接口异常:".json_encode($data));
            }
        }catch (\Throwable $e){
            TelegramBot::sendMessageText($chat_id,"短信余量查询接口报错:".$e->getMessage().$e->getFile().$e->getLine());
        }

        echo "短信余额监控结束：".date('Y-m-d H:i:s')."\n";

    }

}
