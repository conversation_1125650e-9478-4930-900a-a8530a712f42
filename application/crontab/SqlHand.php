<?php

namespace app\crontab;

use app\api\model\Pay;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class SqlHand extends Command
{
    protected function configure()
    {
        $this->setName('sqlHand')
            ->setDescription('定时计划：异步sql处理');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){

        echo "异步sql处理：进入成功：".date('Y-m-d H:i:s');

        $lastTime = $this->msectime() - 1000;

        $redis=redis();
        $redis_key='sql';
        $redis_sql_hand_timing_key='sql_hand_timing';
        while (true) {

            try {

                //每天断开一次释放内存
                if($redis->setnx($redis_sql_hand_timing_key,1)){
                    $redis->expire($redis_sql_hand_timing_key,86400);
                    exit();
                }

                $len =   $redis->lLen($redis_key) ;
                if (($this->msectime() - $lastTime < 800 && $len < 1000) || $len < 1) {
                    sleep(5);
                    continue;
                }

                $sqls = $redis->lPop($redis_key);
//                foreach ($sqls as $v){
                $result = Db::execute($sqls);
                echo date('Y-m-d H:i:s').'：执行sql：'.$sqls.'执行结果：'.$result."\n";
//                }

//                redis()->lTrim($redis_key,count($sqls), -1);
//                redis()->lLen($redis_key);
            }catch (\Throwable $e){
                echo date('Y-m-d H:i:s').'：执行错误：'.$e->getMessage().$e->getFile().$e->getLine().'sql：'.($sqls??'')."\n";
                if(strpos($e->getMessage(), "2006 MySQL server has gone away") || strpos($e->getMessage(), "Redis server") ){
                    sleep(10);
                    exit();
                }

            }
        }

    }

    function msectime() {
        list($t1, $t2) = explode(' ', microtime());
        return (float)sprintf('%.0f',(floatval($t1)+floatval($t2))*1000);
    }

}