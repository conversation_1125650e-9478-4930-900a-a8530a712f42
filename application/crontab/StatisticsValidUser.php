<?php

namespace app\crontab;

use app\api\model\Pay;
use app\extend\Telegram\TelegramBot;
use app\Service\AppPayOrderService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class StatisticsValidUser extends Command
{
    const CRON_NAME = 'StatisticsValidUser';

    protected function configure()
    {
        $this->setName(self::CRON_NAME)
            ->setDescription('定时计划：统计有效用户');
    }

    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }

    public function start(){

        echo "开始执行：".self::CRON_NAME.date('Y-m-d H:i:s')."\n";

//        $redis = redis();

//        $redis_key=self::CRON_NAME;

//        if($redis->setnx($redis_key,1)){
//            $redis->expire($redis_key,2000);


            try{

                $startDate = '2025-01-17';
                $endDate = '2025-02-15';
                $date_list = getDateRangeStartAndEnd($startDate,$endDate);

                foreach ($date_list as $k=>$v){
                    echo "处理日期：".date('Y-m-d',strtotime($v['start'])).date('Y-m-d H:i:s')."\n";


                    $channel_day_list = Db::name('channel_day')->where([
                        ['date','=',date('Ymd',strtotime($v['start']))],
                        ['reg','>',0]
                    ])->field('id,channelid')->select();

                    $start_time = strtotime($v['start']);
                    $end_time = strtotime($v['end']);

                    foreach ($channel_day_list as $k2=>$v2){
                        $count = 0;
                        $count_one = 0;
                        $count_one_more = 0;
                        Db::name('member_info')->where([
                            ['channelid','=',$v2['channelid']],
                            ['is_visitor','=',2],
                            ['create_time','>=',$start_time],
                            ['create_time','<=',$end_time],
                        ])->field("id,channelid")->chunk(5000,function ($list)use($start_time,$end_time,&$count,&$count_one,&$count_one_more,$v2){

                            $member_ids = array_column($list,'id');
                            $member_history_count = Db::name('member_history')->whereIn('member_id',$member_ids)->whereBetween('create_time',[$start_time,$end_time+86400*2])->group('member_id')->field("count(*) id_count")->select();

                            foreach ($member_history_count as $k3=>$v3){

                                if($v3['id_count']==1)$count_one++;
                                if($v3['id_count']>1)$count_one_more++;
                                $count++;
                            }


                        });
                        echo "处理日期：".date('Y-m-d',strtotime($v['start'])).' '.'channelid:'.$v2['channelid'].'  '.'count:'.$count.'  '.'count_one:'.$count_one.'  '.'count_one_more:'.$count_one_more."\n";
                        Db::name('channel_day')->where(['id'=>$v2['id']])->update(['valid_reg'=>$count_one,'valid_reg2'=>$count_one_more]);
                    }

                }

                }catch (\Throwable $e){
                    commonLog(self::CRON_NAME, ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], self::CRON_NAME);
                }finally{
//                    $redis->del($redis_key);

                }

//        }
        $chat_id=sysconf('timing_data_push_chat_id');
        TelegramBot::sendMessageText($chat_id,get_app_name()."统计有效用户结束:统计范围：".$startDate.'-'.$endDate);
        echo "结束执行：".self::CRON_NAME.date('Y-m-d H:i:s')."\n";

    }


}
