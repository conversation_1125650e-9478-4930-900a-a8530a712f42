<?php

namespace app\crontab;

use app\api\model\Pay;
use app\common\Constants\RedisConst;
use app\Helper\AppDiffHelper;
use app\Helper\CacheHelper;
use app\Helper\VideoHelper;
use app\server\AgentSocket;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;

class Temp extends Command
{
    protected function configure()
    {
        $this->setName('Temp')
            ->setDescription('定时计划：临时任务');
    }
    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }
    public function start(){

//        echo "进入成功：".date('Y-m-d H:i:s');

//        $this->upOrderChannelid();
//        $this->updateCache();
        $this->correctionMovieTime();

    }

    public function correctionMovieTime()
    {
        $redis = redis();

        $redis_key="correctionMovieTime";

        if($redis->setnx($redis_key,1)) {
            $redis->expire($redis_key, 86400 * 365);
            $list = Db::name('app_movie')->field("id,url,duration")->select();

            foreach ($list as $k => $v) {
                try {
                    commonLog('correctionMovieTimeInfo', ['info'=>'当前处理app_movie的id:'.$v['id']], 'correctionMovieTimeInfo');
                    $m3u8Raw =  (new VideoHelper())->rebuildM3u8RawLinks(0, parse_url($v['url'])['path'], source_config()['h5_movie_url']);
                    $total = 0;
                    preg_match_all('/#EXTINF:(\d+(\.\d+)?),/', $m3u8Raw, $matches);
                    foreach ($matches[1] as $duration) {
                        $total += (float)$duration; // 转换为浮点数并累加
                    }
                    $total = ceil($total);
                    if($total>10){
                        Db::name('app_movie')->where(['id' => $v['id']])->update(['duration' => gmstrftime('%H:%M:%S',$total)]);
                    }

                }catch (\Throwable $e){

                    commonLog('correctionMovieTime', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'correctionMovieTime');
                }

            }

            $list = Db::name('slice_data')->field("id,url,times")->select();
            foreach ($list as $k => $v) {
                try {
                    commonLog('correctionMovieTimeInfo', ['info'=>'当前处理slice_data的id:'.$v['id']], 'correctionMovieTimeInfo');
                    $m3u8Raw =  (new VideoHelper())->rebuildM3u8RawLinks(0, parse_url($v['url'])['path'], source_config()['h5_movie_url']);
                    $total = 0;
                    preg_match_all('/#EXTINF:(\d+(\.\d+)?),/', $m3u8Raw, $matches);
                    foreach ($matches[1] as $duration) {
                        $total += (float)$duration; // 转换为浮点数并累加
                    }
                    $total = floor($total);
                    if($total>10){
                        Db::name('slice_data')->where(['id' => $v['id']])->update(['times' => $total]);
                    }

                }catch (\Throwable $e){

                    commonLog('correctionMovieTime', ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], 'correctionMovieTime');
                }

            }

            $redis->del($redis_key);

        }

    }

    public function replenishVipId()
    {
        $redis = redis();

        $redis_key="replenishVipId";

        if($redis->setnx($redis_key,1)){
            $redis->expire($redis_key,86400*365);

            $list = Db::name('member_vip')->where(['vip_id'=>0])->select();
            foreach ($list as $k=>$v){
                $app_pay_order = Db::name('app_pay_order')->whereBetween('successtime',[$v['create_time'],$v['create_time']+300])->field("id,vip_id")->order("vip_id desc")->find();
                if(!empty($app_pay_order['vip_id'])){
                    Db::name('member_vip')->where(['id'=>$v['id']])->update(['vip_id'=>$app_pay_order['vip_id']]);
                }
            }
            $redis->del($redis_key);
        }


    }

    /**
     * @return void
     */
    public function initBot(){
        dump(date('Y-m-d H:i:s').' 机器人信息同步开始');
        $where = [
            ["is_bot", "=", 1],
            ["is_visitor", "=", 2],
        ];
        $list = Db::name('member_info')->where($where)->cache(CacheHelper::SHORT_VIDEO_BOT_LIST_KEY,86400*365)->select();
        foreach ($list as $k=>$v){
            $find = Db::name('member_bot')->where(['member_id'=>$v])->find();
            if(empty($find)){
                Db::name('member_bot')->insert([
                    'member_id'=>$v['id'],
                    'account'=>$v['account'],
                    'avatar'=>$v['avatar'],
                    'create_time'=>date('Y-m-d H:i:s',$v['create_time']),
                ]);
            }
        }
        dump(date('Y-m-d H:i:s').' 机器人信息同步完毕');


    }



    public function upOrderChannelid(){
        Db::name('app_pay_order')->where('channelid',"=",'')->chunk(1000,function ($list){
            foreach ($list as $v){
                $member_info = Db::name('member_info')->where('id',$v['member_id'])->field("channelid")->find();
                if(!empty($member_info)){
                    Db::name('app_pay_order')->where('id',$v['id'])->update(['channelid'=>$member_info['channelid']]);
                }

            }
        });

    }

    /**
     * 重新推送订单到代理后台
     * @return void
     */
    public function rePushOrder(){

        $date="2022-10-14";

        Db::name('app_pay_order')->where('addtime',">=",strtotime($date))->where('addtime',"<",strtotime($date)+60*60*24)->where('status',"=",0)->chunk(1000,function ($list){
            foreach ($list as $v){
                $res = (new AgentSocket())->payInc(strval($v['order_id']));
                dump($res);
            }
        });

    }

}
