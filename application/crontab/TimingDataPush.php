<?php

namespace app\crontab;

use app\api\model\Pay;
use app\extend\Telegram\TelegramBot;
use app\Service\AppPayOrderService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class TimingDataPush extends Command
{
    const CRON_NAME = 'TimingDataPush';

    const TIME_LIST = [
        '00:00:00',
        '00:30:00',
        '01:00:00',
        '01:30:00',
        '02:00:00',
        '02:30:00',
        '03:00:00',
        '03:30:00',
        '04:00:00',
        '04:30:00',
        '05:00:00',
        '05:30:00',
        '06:00:00',
        '06:30:00',
        '07:00:00',
        '07:30:00',
        '08:00:00',
        '08:30:00',
        '09:00:00',
        '09:30:00',
        '10:00:00',
        '10:30:00',
        '11:00:00',
        '11:30:00',
        '12:00:00',
        '12:30:00',
        '13:00:00',
        '13:30:00',
        '14:00:00',
        '14:30:00',
        '15:00:00',
        '15:30:00',
        '16:00:00',
        '16:30:00',
        '17:00:00',
        '17:30:00',
        '18:00:00',
        '18:30:00',
        '19:00:00',
        '19:30:00',
        '20:00:00',
        '20:30:00',
        '21:00:00',
        '21:30:00',
        '22:00:00',
        '22:30:00',
        '23:00:00',
        '23:30:00',
        '24:00:00',
    ];

    protected function configure()
    {
        $this->setName(self::CRON_NAME)
            ->setDescription('定时计划：定时数据飞机推送');
    }

    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }

    public function start(){

//        echo "进入成功：".date('Y-m-d H:i:s');

        $redis = redis();

        $redis_key=self::CRON_NAME;

        if($redis->setnx($redis_key,1)){
            $redis->expire($redis_key,2000);

            $date = date('Y-m-d');
            $start_time = strtotime(date('Y-m-d 00:00:00'));
            $end_time = strtotime(date('Y-m-d 23:59:59'));
            try{


                $member_info =  Db::name('member_info')->where([
                    ['is_visitor','=',2],
                    ['create_time','>=',$start_time],
                    ['create_time','<=',$end_time],
                ])->group("device_type , create_time DIV (30 * 60)")->field("device_type , count(id) member_count , FROM_UNIXTIME(create_time,'%Y-%m-%d %H:%i:00') from_create_time")->order("from_create_time asc")->select();

                $app_pay_order =  Db::name('app_pay_order')->where([
                    ['successtime','>=',$start_time],
                    ['successtime','<=',$end_time],
                    ['order_type','IN',[AppPayOrderService::ORDER_TYPE_MOVIE[0],AppPayOrderService::ORDER_TYPE_AI[0]]],
                ])->group("device_source , addtime DIV (30 * 60)")->field("device_source , round(sum(pay_amount)/100,2)  pay_amount_sum , FROM_UNIXTIME(addtime,'%Y-%m-%d %H:%i:00') from_create_time")->order("from_create_time asc")->select();


                $result_data_list = [];

                foreach (self::TIME_LIST as $k=>$v){

                    $his = self::TIME_LIST[($k==count(self::TIME_LIST)-1?$k:$k+1)];
                    foreach ($member_info as $k1=>$v1){

                        $init_datetime = $date.' '.$v;

                        if($v1['from_create_time']>=$init_datetime && $v1['from_create_time']<$date.' '.$his){
                            $datetime = $init_datetime;
                            $result_data_list[$datetime]['datetime']=$datetime;
                            if($v1['device_type']=='A'){
                                $result_data_list[$datetime]['member_count_A']=$v1['member_count'];
                            }

                            if(in_array($v1['device_type'],['H','IH'])){
                                if(!isset($result_data_list[$datetime]['member_count_IH']))$result_data_list[$datetime]['member_count_IH']=0;
                                $result_data_list[$datetime]['member_count_IH']+=$v1['member_count'];
                            }
                        }
                    }


                    foreach ($app_pay_order as $k2=>$v2){

                        $init_datetime = $date.' '.$v;
                        if($v2['from_create_time']>=$init_datetime && $v2['from_create_time']<$date.' '.$his){
                            $datetime = $init_datetime;
                            $result_data_list[$datetime]['datetime']=$datetime;
                            if($v2['device_source']=='A'){
                                $result_data_list[$datetime]['pay_amount_sum_A']=$v2['pay_amount_sum'];
                            }

                            if(in_array($v2['device_source'],['H','IH'])){
                                if(!isset($result_data_list[$datetime]['pay_amount_sum_IH']))$result_data_list[$datetime]['pay_amount_sum_IH']=0;
                                $result_data_list[$datetime]['pay_amount_sum_IH']+=$v2['pay_amount_sum'];
                            }
                        }

                    }

                    $datetime = $date.' '.$v;
                    $result_data_list[$datetime]['datetime']=$datetime;

                }

                $result_data_list_end=[];

                foreach ($result_data_list as $k=>$v){
                    if($k>date('Y-m-d H:i:s')){
                        unset($result_data_list[$k]);
                    }else{
                        $result_data_list_end[]=[
                            'datetime'=>$v['datetime'],
                            'member_count_A'=>$v['member_count_A']??0,
                            'member_count_IH'=>$v['member_count_IH']??0,
                            'member_count'=>($v['member_count_A']??0)+($v['member_count_IH']??0),
                            'pay_amount_sum_A'=>$v['pay_amount_sum_A']??0,
                            'pay_amount_sum_IH'=>$v['pay_amount_sum_IH']??0,
                            'pay_amount_sum'=>($v['pay_amount_sum_A']??0)+($v['pay_amount_sum_IH']??0),
                        ];
                    }
                }

                if(!empty($result_data_list_end)){
                    $title = ['时间','新增用户(android)','新增用户(ios)','总新增用户','支付金额(android)','支付金额(ios)','总支付金额'];
                    $filename = get_app_name().'-数据汇总-'.$date;
                    $chat_id=sysconf('timing_data_push_chat_id');
                    TelegramBot::sendMessageFile($chat_id,$this->excel_data($filename,$title,$result_data_list_end));
                }

            }catch (\Throwable $e){
                commonLog(self::CRON_NAME, ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], self::CRON_NAME);
            }finally{
                $redis->del($redis_key);
            }

        }

    }


    protected function excel_data($filename,$title,$data){

        require env('root_path') . 'extend' . DIRECTORY_SEPARATOR . 'PhpSpreadsheet' . DIRECTORY_SEPARATOR . 'autoload.php';
        $spreadsheet = new \PhpOffice\PhpSpreadsheet\Spreadsheet();
        $spreadsheet->getDefaultStyle()->getFont()->setName('Arial');
        $spreadsheet->getDefaultStyle()->getFont()->setSize(12);
        $sheet = $spreadsheet->getActiveSheet();

        // 表头
        foreach ($title as $key => $value) {
            $sheet->setCellValueByColumnAndRow($key + 1, 1, $value);
        }
        $sheet->getDefaultColumnDimension()->setWidth(15);
        $sheet->getColumnDimension('A')->setWidth(27);
        // 写入内容
        $row = 2;
        foreach ($data as $item) {
            $column = 1;
            if (!is_array($item)) {

                continue;
            }
            foreach ($item as $value) {
                $sheet->setCellValueByColumnAndRow($column, $row, $value);
                $column ++;
            }
            $row ++;
        }

        $savePath = UPLOAD_PATH . '/uploads/'. $filename.'.xlsx';

        // 保存 Excel 文件到服务器
        $writer = new Xlsx($spreadsheet);
        $writer->save($savePath);

        return $savePath;
    }

}
