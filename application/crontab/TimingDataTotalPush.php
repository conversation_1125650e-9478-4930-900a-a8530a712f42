<?php

namespace app\crontab;

use app\api\model\Pay;
use app\extend\Telegram\TelegramBot;
use app\Service\AppPayOrderService;
use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;
use think\Controller;
use think\Queue;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class TimingDataTotalPush extends Command
{
    const CRON_NAME = 'TimingDataTotalPush';

    const TIME_LIST = [
        "00:30", "01:30", "02:30", "03:30", "04:30", "05:30",
        "06:30", "07:30", "08:30", "09:30", "10:30", "11:30",
        "12:30", "13:30", "14:30", "15:30", "16:30", "17:30",
        "18:30", "19:30", "20:30", "21:30", "22:30", "23:30",
        "23:59"
    ];

    protected function configure()
    {
        $this->setName(self::CRON_NAME)
            ->setDescription('定时计划：定时统计数据飞机推送');
    }

    protected function execute(Input $input, Output $output)
    {
        $this->start();
    }

    public function start(){

//        echo "进入成功：".date('Y-m-d H:i:s');

        $redis = redis();

        $redis_key=self::CRON_NAME;

//        if(true){
        if($redis->setnx($redis_key,1)){
            $redis->expire($redis_key,2000);

            $time = date('H:i:s');
            foreach (self::TIME_LIST as $k=>$v){
                if(strtotime($time) >= strtotime($v.':59')){
                    $v_previous = self::TIME_LIST[$k-1]??"00:00";
                    $start_time = strtotime(date("Y-m-d $v_previous:00"));
                    $end_time = strtotime(date("Y-m-d $v:59"));
                }
            }


            if(empty($start_time)){
                $start_time = strtotime(date('Y-m-d 23:30:00',strtotime('yesterday')));
                $end_time = strtotime(date('Y-m-d 23:59:59',strtotime('yesterday')));
            }

            $chat_id = sysconf('data_total_push_chat_id');

            try{


                $member_info =  Db::name('member_info')->where([
                    ['create_time','>=',$start_time],
                    ['create_time','<=',$end_time],
                ])->group("device_type ")->field("device_type , SUM(IF(is_visitor=1,1,0)) visitor_count,SUM(IF(is_visitor=2,1,0)) member_count ")->select();

                $app_pay_order =  Db::name('app_pay_order')->where([
                    ['successtime','>=',$start_time],
                    ['successtime','<=',$end_time],
                    ['order_type','IN',[AppPayOrderService::ORDER_TYPE_MOVIE[0],AppPayOrderService::ORDER_TYPE_AI[0]]],
                ])->group("device_source ")->field("device_source , round(sum(pay_amount)/100,2)  pay_amount_sum ")->select();

                $outside_pay_order =  Db::name('outside_pay_order')->where([
                    ['paid_time','>=',date('Y-m-d H:i:s',$start_time)],
                    ['paid_time','<=',date('Y-m-d H:i:s',$end_time)],
                    ['status','=',0],
                ])->group("device_type ")->field("device_type , sum(pay_amount)  pay_amount_sum ")->select();
//                ])->group("status ")->field("status as device_type , sum(pay_amount)  pay_amount_sum ")->select();


                $start_time_yesterday = strtotime(date('Y-m-d 00:00:00',strtotime('yesterday')));
                $end_time_yesterday = strtotime(date('Y-m-d 23:59:59',strtotime('yesterday')));
                $member_info_yesterday =  Db::name('member_info')->where([
                    ['create_time','>=',$start_time_yesterday],
                    ['create_time','<=',$end_time_yesterday],
                ])->group("device_type ")->field("device_type , SUM(IF(is_visitor=1,1,0)) visitor_count,SUM(IF(is_visitor=2,1,0)) member_count ")->select();

                $app_pay_order_yesterday =  Db::name('app_pay_order')->where([
                    ['successtime','>=',$start_time_yesterday],
                    ['successtime','<=',$end_time_yesterday],
                    ['order_type','IN',[AppPayOrderService::ORDER_TYPE_MOVIE[0],AppPayOrderService::ORDER_TYPE_AI[0]]],
                ])->group("device_source ")->field("device_source , round(sum(pay_amount)/100,2)  pay_amount_sum ")->select();

                $outside_pay_order_yesterday =  Db::name('outside_pay_order')->where([
                    ['paid_time','>=',date('Y-m-d H:i:s',$start_time_yesterday)],
                    ['paid_time','<=',date('Y-m-d H:i:s',$end_time_yesterday)],
                    ['status','=',0],
                ])->group("device_type ")->field("device_type , sum(pay_amount)  pay_amount_sum ")->select();
//                ])->group("status ")->field("status as device_type , sum(pay_amount)  pay_amount_sum ")->select();


                $visitor_count_A=$member_count_A=$visitor_count_IH=$member_count_IH=$pay_amount_sum_A=$pay_amount_sum_IH=$ai_pay_amount_sum_A=$ai_pay_amount_sum_IH=0;
                $visitor_count_A_yesterday = $member_count_A_yesterday = $visitor_count_IH_yesterday = $member_count_IH_yesterday = $pay_amount_sum_A_yesterday =
                $pay_amount_sum_IH_yesterday = $ai_pay_amount_sum_A_yesterday = $ai_pay_amount_sum_IH_yesterday = 0;
                foreach ($member_info as $k=>$v){
                    if($v['device_type']=='A'){
                        $visitor_count_A = $v['visitor_count'];
                        $member_count_A = $v['member_count'];

                    }

                    if(in_array($v['device_type'],['H','IH'])){
                        $visitor_count_IH += $v['visitor_count'];
                        $member_count_IH += $v['member_count'];

                    }
                }

                foreach ($app_pay_order as $k=>$v){
                    if($v['device_source']=='A'){
                        $pay_amount_sum_A = formatNumber($v['pay_amount_sum']);
                    }

                    if(in_array($v['device_source'],['H','IH'])){
                        $pay_amount_sum_IH += $v['pay_amount_sum'];
                    }
                }
                foreach ($outside_pay_order as $k=>$v){
                    if($v['device_type']=='A'){
                        $ai_pay_amount_sum_A = $v['pay_amount_sum'];
                    }

                    if(in_array($v['device_type'],['H','IH'])){
                        $ai_pay_amount_sum_IH += $v['pay_amount_sum'];
                    }
                }


                foreach ($member_info_yesterday as $k=>$v){
                    if($v['device_type']=='A'){
                        $visitor_count_A_yesterday = $v['visitor_count'];
                        $member_count_A_yesterday = $v['member_count'];

                    }

                    if(in_array($v['device_type'],['H','IH'])){
                        $visitor_count_IH_yesterday += $v['visitor_count'];
                        $member_count_IH_yesterday += $v['member_count'];

                    }
                }

                foreach ($app_pay_order_yesterday as $k=>$v){
                    if($v['device_source']=='A'){
                        $pay_amount_sum_A_yesterday = formatNumber($v['pay_amount_sum']);
                    }

                    if(in_array($v['device_source'],['H','IH'])){
                        $pay_amount_sum_IH_yesterday += $v['pay_amount_sum'];
                    }
                }

                foreach ($outside_pay_order_yesterday as $k=>$v){
                    if($v['device_type']=='A'){
                        $ai_pay_amount_sum_A_yesterday = $v['pay_amount_sum'];
                    }

                    if(in_array($v['device_type'],['H','IH'])){
                        $ai_pay_amount_sum_IH_yesterday += $v['pay_amount_sum'];
                    }
                }

                $visitor_count_all = $visitor_count_A+$visitor_count_IH;
                $member_count_all = $member_count_A+$member_count_IH;
                $pay_amount_sum_all = $pay_amount_sum_A+$pay_amount_sum_IH;
                $ai_pay_amount_sum_all = $ai_pay_amount_sum_A+$ai_pay_amount_sum_IH;
                $total_pay_amount_sum_A = $pay_amount_sum_A+$ai_pay_amount_sum_A;
                $total_pay_amount_sum_IH = $pay_amount_sum_IH+$ai_pay_amount_sum_IH;
                $total_pay_amount_sum_all = $pay_amount_sum_all+$ai_pay_amount_sum_all;


                $visitor_count_all_yesterday = $visitor_count_A_yesterday+$visitor_count_IH_yesterday;
                $member_count_all_yesterday = $member_count_A_yesterday+$member_count_IH_yesterday;
                $pay_amount_sum_all_yesterday = $pay_amount_sum_A_yesterday+$pay_amount_sum_IH_yesterday;
                $ai_pay_amount_sum_all_yesterday = $ai_pay_amount_sum_A_yesterday+$ai_pay_amount_sum_IH_yesterday;
                $total_pay_amount_sum_A_yesterday = $pay_amount_sum_A_yesterday+$ai_pay_amount_sum_A_yesterday;
                $total_pay_amount_sum_IH_yesterday = $pay_amount_sum_IH_yesterday+$ai_pay_amount_sum_IH_yesterday;
                $total_pay_amount_sum_all_yesterday = $pay_amount_sum_all_yesterday+$ai_pay_amount_sum_all_yesterday;



                $tg_start_time=date('H:i',$start_time);
                $tg_end_time=date('H:i',$end_time);
                $send_content = [
                    'from' =>sysconf('app_name').' - 运营后台',
                    'subject' => '定时数据统计',
                    'date' => date('Y-m-d H:i:s'),
                    'content' =><<<text

📊统计区间 : $tg_start_time 至 $tg_end_time

•	📲 新增安装量：{$visitor_count_all}（🤖{$visitor_count_A} / 🍎{$visitor_count_IH}）
•	🧍 新增注册量：{$member_count_all}（🤖{$member_count_A} / 🍎{$member_count_IH}）
•	💰 充值金额 (点播)：{$pay_amount_sum_all}（🤖{$pay_amount_sum_A} / 🍎{$pay_amount_sum_IH}）
•	🤖 充值金额 (AI)：{$ai_pay_amount_sum_all}（🤖{$ai_pay_amount_sum_A} / 🍎{$ai_pay_amount_sum_IH}）
•	💵 充值金额 (总)：{$total_pay_amount_sum_all}（🤖{$total_pay_amount_sum_A} / 🍎{$total_pay_amount_sum_IH}）

📈 昨日数据

•	📲 新增安装量：{$visitor_count_all_yesterday}（🤖{$visitor_count_A_yesterday} / 🍎{$visitor_count_IH_yesterday}）
•	🧍 新增注册量：{$member_count_all_yesterday}（🤖{$member_count_A_yesterday} / 🍎{$member_count_IH_yesterday}）
•	💰 充值金额 (点播)：{$pay_amount_sum_all_yesterday}（🤖{$pay_amount_sum_A_yesterday} / 🍎{$pay_amount_sum_IH_yesterday}）
•	🤖 充值金额 (AI)：{$ai_pay_amount_sum_all_yesterday}（🤖{$ai_pay_amount_sum_A_yesterday} / 🍎{$ai_pay_amount_sum_IH_yesterday}）
•	💵 充值金额 (总)：{$total_pay_amount_sum_all_yesterday}（🤖{$total_pay_amount_sum_A_yesterday} / 🍎{$total_pay_amount_sum_IH_yesterday}）
text,
                ];
                $send_content['content']=urlencode($send_content['content']);
                TelegramBot::$token='**********************************************';
                TelegramBot::sendMessageTextMust($send_content,$chat_id);

            }catch (\Throwable $e){
                commonLog(self::CRON_NAME, ['error'=>$e->getMessage().$e->getFile().$e->getLine(),'trace'=>$e->getTrace()], self::CRON_NAME);
            }finally{
                $redis->del($redis_key);
            }

        }

    }


}
