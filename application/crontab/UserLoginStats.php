<?php

namespace app\crontab;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\Db;

class UserLoginStats extends Command
{
    protected function configure()
    {
        $this->setName('UserLoginStats')
            ->setDescription('统计2025年登录过且绑定手机号的用户数量');
    }

    protected function execute(Input $input, Output $output)
    {
        $startTime = time();
        $output->writeln(date('Y-m-d H:i:s', $startTime) . " 开始统计登录过且绑定手机号的用户数量...");

        $startTime = microtime(true);
        $result = $this->getUserLoginStatsBatch($output);
        $endTime = microtime(true);

        $executionTime = round($endTime - $startTime, 2);

        $output->writeln("=================================");
        $output->writeln("登录且绑定手机号用户总数: " . number_format($result));
        $output->writeln("查询执行时间: {$executionTime} 秒");
        $output->writeln("=================================");
        $output->writeln(date('Y-m-d H:i:s', time()) . " 统计登录过且绑定手机号的用户数量完成!");
    }

    private function getUserLoginStatsBatch($output)
    {
        $start_time = strtotime('2025-01-01 00:00:00');

        $totalCount = 0;
        $processedCount = 0;

        $totalLoginRecords = Db::name('user_login')->where('last_login_time', '>=', $start_time)->count();

        $output->writeln("总登录记录数: " . number_format($totalLoginRecords));

        try {
            Db::name('user_login')->field('id,member_id') ->where('last_login_time', '>=', $start_time)->chunk(10000, function ($loginRecords) use (&$totalCount, &$processedCount, $output, $totalLoginRecords) {

                    $currentBatchUserIds = array_column($loginRecords, 'member_id');
                    $processedCount += count($currentBatchUserIds);

                    $batchValidCount = Db::name('member_info')
                        ->where('id', 'in', $currentBatchUserIds)
                        ->where('mobile', '<>', '')
                        ->whereNotNull('mobile')
                        ->where('is_deleted', 1)
                        ->count();

                    $totalCount += $batchValidCount;

                    $progress = round(($processedCount / $totalLoginRecords) * 100, 2);
                    $output->writeln("进度: {$progress}% - 已处理: " . number_format($processedCount) . " 条登录记录，当前批次有效用户: " . number_format($batchValidCount) . "，累计: " . number_format($totalCount));
                });

            $output->writeln("数据处理完成！");

        } catch (\Throwable $e) {
            $output->writeln("处理过程中出现错误: " . $e->getMessage().$e->getFile().$e->getLine());
            exit();
        }

        return $totalCount;
    }
}
