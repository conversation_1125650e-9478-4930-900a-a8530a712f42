<?php

namespace app\crontab;

use think\console\Command;
use think\console\Input;
use think\console\input\Argument;
use think\console\Output;
use think\Db;

class Video extends Command
{
    protected function configure()
    {
        $this->setName('video')
            ->addArgument('time', Argument::OPTIONAL, "默认前一天")
            ->setDescription('定时计划：短视频统计');
    }

    protected function execute(Input $input, Output $output)
    {
        $time = '-'.trim($input->getArgument('time')) ?: '-1';
        $date = date('Y-m-d', strtotime($time . " day"));
        $star = strtotime($date . ' 00:00:00'); //今日开始时间
        $end = strtotime($date . ' 23:59:59'); //今日结束时间
        $data = Db::name('site_day')->where('create_date', $date)->find();
        if ($data) {
            $list = [];
            //今日新增短视频
            $newvideo = Db::connect('short_video')->query("SELECT COUNT('id') as video FROM xg_video_pool WHERE to_days(create_time) >= to_days(date_sub(curdate(),interval 1 day)) AND  to_days(create_time) < to_days(curdate())")[0]['video'];
            $list['video'] = $newvideo + Db::query("SELECT COUNT('id') as video FROM hg_video_clip WHERE create_time>={$star} AND create_time<={$end}")['0']['video']; //今日新增短视频
            //短视频分享
            $newvideo_share = Db::connect('short_video')->query("SELECT COUNT('id') as video_share FROM xg_share_video_list WHERE to_days(create_time) >= to_days(date_sub(curdate(),interval 1 day)) AND  to_days(create_time) < to_days(curdate())")[0]['video_share'];
            $list['video_share'] = $newvideo_share + Db::query("SELECT COUNT('id') as video_share FROM hg_video_clip_share WHERE create_time>={$star} AND create_time<={$end}")['0']['video_share'];
            //短视频点赞
            $newvideo_like = Db::connect('short_video')->query("SELECT COUNT('id') as video_like FROM xg_video_like WHERE to_days(create_time) >= to_days(date_sub(curdate(),interval 1 day)) AND  to_days(create_time) < to_days(curdate())")[0]['video_like'];
            $list['video_like'] = $newvideo_like + Db::query("SELECT COUNT('id') as video_like FROM hg_video_clip_like WHERE create_time>={$star} AND create_time<={$end} AND like_type = 1 AND cancel_time = 100")['0']['video_like'];
            //短视频评论
            $newvideo_comment = Db::connect('short_video')->query("SELECT COUNT('id') as video_comment FROM xg_video_comment WHERE to_days(create_time) >= to_days(date_sub(curdate(),interval 1 day)) AND  to_days(create_time) < to_days(curdate())")[0]['video_comment'];
            $list['video_comment'] = $newvideo_comment + Db::query("SELECT COUNT('id') as video_comment FROM hg_video_clip_comment WHERE create_time>={$star} AND create_time<={$end} ")['0']['video_comment'];
            //短视频播放
            $newvideo_play = Db::connect('short_video')->query("SELECT COUNT('id') as video_play FROM xg_video_record WHERE to_days(create_time) >= to_days(date_sub(curdate(),interval 1 day))  AND  to_days(create_time) < to_days(curdate())")[0]['video_play'];
            $list['video_play'] = $newvideo_play + Db::query("SELECT COUNT('id') as video_play FROM hg_video_clip_play WHERE create_time>={$star} AND create_time<={$end} ")['0']['video_play'];
            $this->debug('一点定时任务数据查询完成', $list);
            Db::name('site_day')
                ->where('id', $data['id'])
                ->update($list);
            $this->send_telegram('短视频统计更新成功');
            $this->debug('一点定时任务数据添加完成');
        }

    }
//写入定时任务日志
    function debug($msg = "", $data = [])
    {
        $time = date("Y-m-d H:i:s");
        $a = str_replace("\\/", "/", json_encode($data, JSON_UNESCAPED_UNICODE));
        file_put_contents("./application/crontab/video.log", $time . ":" . $msg . " => " . $a . "\r\n", FILE_APPEND);
    }
    private function send_telegram($msg)
    {
        $ch = curl_init();
        $url = "https://api.telegram.org/bot1475157040:AAFHcNFkfnUgi58S2rwapmwkpXG3wk5UB-Y/sendMessage?chat_id=-405653564&text=$msg";
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, 0);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, 0);
        $result = curl_exec($ch);
        curl_close($ch);
        return $result;
    }
}












