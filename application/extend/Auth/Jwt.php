<?php

namespace app\extend\Auth;

class Jwt
{
    private $deviceType = [
        'A' => 'android',
        'I' => 'ios',
        'H' => 'H5',
        'P' => 'PC',
    ];

    private static  $_instance = null;
    public static function getInstance () {
        if (is_null(self::$_instance)) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    public function getToken(array $data,$deviceId = '',$deviceType = ''){
        $key = config('jwt_key');  //这里是自定义的一个随机字串，应该写在config文件中的，解密时也会用，相当    于加密中常用的 盐  salt
        $token = [
            "iss"=> "niuhuan",             //签发者
            "aud"=> $deviceType,    //设备类型
            'jti' => $deviceId,     //设备的唯一标识
            "iat" => time(),
            "nbf" => time(),        //生效时间,马上生效
            "exp" => time()+ config('jwt_timeout'), //token 过期时间
        ];
        $token['user_info'] = $data;
        $jwt = \Firebase\JWT\JWT::encode($token,$key,"HS256");
        return $jwt;
    }

    public function check($jwt)
    {
        $key = config('jwt_key');
        try {
            $info = \Firebase\JWT\JWT::decode($jwt,$key,["HS256"]);
            return object2array($info);
        }catch (\Exception $exception){
            return '';
        }
    }
}