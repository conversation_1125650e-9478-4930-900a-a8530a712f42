<?php
/**
 * 描述
 * @auth nj911<<EMAIL>>
 * @date: 2021-04-28 12:13
 */

namespace app\extend\Cdn;


class Baidu
{
    /**
     * 主密码
     * @var string
     */
    protected $key1 = 'ZgwDeDVXrXIwN';

    /**
     * 辅密码
     * @var string
     */
    protected $key2 = 'C7IXSrgF1qjLe';

    /**
     * 获取鉴权query参数key=value
     * @param $fileRelativePath
     * @return string
     */
    public function getSignKeyValue($fileRelativePath)
    {
        // 过期时间，这里设置3小时后的timestamp.
        $expiredTime = strtotime('+3 hours');
        $sstring = $fileRelativePath."-".$expiredTime."-0-0-".$this->key1;
        $md5 = md5($sstring);
        $authKey = "auth_key=".$expiredTime."-0-0-".$md5;

        return $authKey;
    }
}
