<?php
/**
 * 腾讯云cdn
 */

namespace app\extend\Cdn;


class Tencent
{
    /**
     * 主密码
     * @var string
     */
    protected $key1 = '';

    /**
     * 辅密码
     * @var string
     */
    protected $key2 = 'C7IXSrgF1qjLe';

    /**
     * 获取鉴权query参数key=value
     * @param $fileRelativePath
     * @return string
     */
    public function getSignKeyValue($fileRelativePath)
    {
        $this->key1=sysconf('cdn_key');
        // 过期时间，这里设置3小时后的timestamp.
        $expiredTime = strtotime('+3 hours');
        $sstring = $fileRelativePath."-".$expiredTime."-0-0-".$this->key1;
        $md5 = md5($sstring);
        $authKey = "auth_key=".$expiredTime."-0-0-".$md5;

        return $authKey;
    }

    /**
     * 获取鉴权query参数key=value
     * @param $fileRelativePath
     * @return string
     */
    public function getAiSignKeyValue($fileRelativePath)
    {
        $this->key1=sysconf('ai_cdn_key');
        // 过期时间，这里设置3小时后的timestamp.
        $expiredTime = strtotime('+3 hours');
        $sstring = $fileRelativePath."-".$expiredTime."-0-0-".$this->key1;
        $md5 = md5($sstring);
        $authKey = "auth_key=".$expiredTime."-0-0-".$md5;

        return $authKey;
    }
}
