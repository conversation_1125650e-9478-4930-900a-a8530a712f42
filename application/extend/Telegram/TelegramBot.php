<?php
namespace app\extend\Telegram;


/**
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 *
 *  Telegram-机器人
 *
 * <AUTHOR>
 * @example
 *
 *
 * 使用说明
 *
 */

class TelegramBot
{
    /**
     * api地址
     * @var string
     * 列：http://cdn.wayada.com/video_user/rg/gq
     *
     */
    public static $api  = 'https://api.telegram.org';

    /**
     * token
     * @var string
     *
     */
    public static $token = "7908920456:AAE0YSzU5qQhhBqwl0bPkdxa8T9nnKcZLt8";

    /**
     *
     * 获取消息列表
     * @var string
     * 列：http://cdn.wayada.com/video_user/rg/gq
     *
     */
    public static function getUpdates()
    {

    	$uri = Util::makeUri(self::$api,self::$token).'/getupdates';
    	$ret = Util::curl($uri);
    	return Util::jsonDecode($ret);
    }

    /**
     *
     * 发送消息
     *
     * @param  int     $chat_id
     * @param  string  $text
     *
     * @return mixed $data default array,
     *  else Exception
     *
     */
    public static function sendMessage( $chat_id,string $text = null)
    {
    	$uri = Util::makeUri(self::$api,self::$token).'/sendmessage '.'-d "chat_id='.$chat_id.'&parse_mode=MarkdownV2&text='.$text.'"';
    	$ret = Util::curl($uri);
    	return Util::jsonDecode($ret);
    }

    /**
     *
     * 发送消息text文本
     *
     * @param  int     $chat_id
     * @param  string  $text
     *
     * @return mixed $data default array,
     *  else Exception
     *
     */
    public static function sendMessageText( $chat_id,string $text = null)
    {
        $uri = Util::makeUri(self::$api,self::$token).'/sendmessage '.'-d "chat_id='.$chat_id.'&text='.$text.'"';
        $ret = Util::curl($uri);
        return Util::jsonDecode($ret);
    }


    /**
     *
     * 发送消息text文本
     *
     * @param  int     $chat_id
     * @param  string  $text
     *
     * @return mixed $data default array,
     *  else Exception
     *
     */
    public static function sendMessageTextEscape( $chat_id,string $text = null)
    {
        $uri = Util::makeUri(self::$api,self::$token).'/sendmessage '.'-d "chat_id='.$chat_id.'&text='.self::escapeMessage($text).'"';
        $ret = Util::curl($uri);
        return Util::jsonDecode($ret);
    }


    /**
     * 转义发送消息的特殊字符
     * @return array|string|string[]
     */
    public static function escapeMessage(string $text){

        foreach (explode(',',"\\,`") as $k=>$v){
            $text = str_replace($v,"\\".$v,$text);
        }

        return $text;
    }

    /**
     * 设置base64去掉一切telegram特殊字符（推荐以后一切飞机发送都用这个方法）
     * @param array $content
     * @return void
     */
    public static function  sendMessageTextMust(array $content,string $chat_id = null){
        if(empty($chat_id)){
            $chat_id = sysconf('business_note_chat_id');
        }
        $send_result = self::sendMessageTextEscape($chat_id,TelegramBot::makePHPError($content));
        if(empty($send_result) && !empty($content['content'])){
            $content['content']='请用base64解析报错内容 : '.base64_encode($content['content']);
            self::sendMessageTextEscape($chat_id,TelegramBot::makePHPError($content));
        }
    }


    /**
     *
     * 发送文件
     *
     * @param string $chat_id
     * @param string $file_path
     * @return mixed $data default array,
     *  else Exception
     */
    public static function sendMessageFile(string $chat_id,string $file_path)
    {
        // 初始化 cURL 请求
        $ch = curl_init();

        // 设置 cURL 请求参数
        curl_setopt($ch, CURLOPT_URL, Util::makeUri(self::$api,self::$token).'/sendDocument');
        curl_setopt($ch, CURLOPT_POST, 1);

        // 构建发送的数据
        $post_fields = [
            'chat_id' => $chat_id,
            'document' => new \CURLFile($file_path), // 或者使用 'document' => new CURLFile($file_path) 发送本地文件
        ];

        curl_setopt($ch, CURLOPT_POSTFIELDS, $post_fields);

        // 执行 cURL 请求
        $result = curl_exec($ch);

        // 检查执行是否成功
//        if (curl_errno($ch)) {
//            echo 'Error:' . curl_error($ch);
//        } else {
//            echo $result;
//        }

        // 关闭 cURL 资源
        curl_close($ch);

        return $result;

    }



    /**
     *
     * 生成Body
     *
     * @param  array  $params
     * @return string
     *
     */
    public static function makeBody($params)
    {
        return <<<text
监控者:{$params['monitor']}
来源:{$params['from']}
主题:{$params['subject']}
日期:{$params['date']}
内容:{$params['content']}
text;
    }

    /**
     *
     * 生成Body
     *
     * @param  array  $params
     * @return string
     *
     */
    public static function makePHPError($params)
    {
//        监控者:{$params['monitor']}
        return <<<text
来源 : {$params['from']}
主题 : {$params['subject']}
日期 : {$params['date']}
内容 : {$params['content']}
text;
    }


    /**
     *
     * 生成订单Body
     *
     * @param  array  $params
     * @return string
     *
     */
    public static function makeOrderBody($params)
    {
        $content = json_decode($params['content'],true);
        return <<<text
来源:{$params['from']}
主题:{$params['subject']}
时间:{$params['date']}
物品:{$content['thing']}
订单号:{$content['orderSn']}
状态:{$content['status']}
通道:{$content['aisle']}
支付方式:{$content['payType']}
用户昵称：{$content['nickname']}
用户UID:{$content['useruid']}
渠道UID:{$content['channelUid']}

text;
    }
}




class Util
{
    /**
     * curl -i
     * @var string
     */
    public static $baseCurl  = "curl -k";

    /**
     * 检查缓存是否命中规则
     * @var array
     */
    public static $cacheRule = [
        '/X-Qnm-Cache: Hit/',
        '/X-Cache: HIT TCP_MEM_HIT/',
    ];


    /**
     *
     *  curl
     *
     * @param  string  $url
     *
     * @return mixed $data default array,
     *  else Exception
     *
     * @example
     *
     * 说明：
     *
     *  1.基于linux -curl 组件
     *  2.基于Php shell_exec执行系统命令，注php.ini配置请开启
     *
     *
     */
    public static function curl(string $url = null)
    {
        $cmd = self::$baseCurl." ".$url;
        return shell_exec($cmd);
    }



    /**
     *
     *  生成基础地址前缀
     *
     * @param  string  $url
     * @param  string  $url
     *
     * @return mixed $data default array,
     *  else Exception
     *
     * @example
     *
     *
     */

    public static function makeUri(string $api = null,string $token = null)
    {
    	return $api.'/'.'bot'.$token;
    }


    /**
     *
     *  释放数组
     *
     * @param  string  $url
     *
     * @return mixed $data default array,
     *  else Exception
     *
     * @example
     *
     * 说明：
     */
    public static function Idestruct(array &$arr)
    {
        while (!empty($arr)) {
            $sub = array_splice($arr, 0, 1000);
            unset($sub);
        }
        unset($arr);
    }
    public static function jsonDecode($data)
    {
    	return json_decode($data,true);
    }
}
