<?php
// +----------------------------------------------------------------------
// | ThinkPHP [ WE CAN DO IT JUST THINK IT ]
// +----------------------------------------------------------------------
// | Copyright (c) 2006-2016 http://thinkphp.cn All rights reserved.
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: yunwuxin <<EMAIL>>
// +----------------------------------------------------------------------

return [
    'connector'  => 'Redis',        // Redis 驱动
    'expire'     => 300,             // 任务的过期时间，默认为60秒; 若要禁用，则设置为 null
    'default'    => env('CACHE_PREFIX'),           // 默认的队列名称
    'host'       => env('REDIS_HOST'),        // redis 主机ip
    'port'       => env('REDIS_PORT'),           // redis 端口
    'password'   => env('REDIS_PASSWORD'),             // redis 密码
    'select'     => env('REDIS_DB'),              // 使用哪一个 db，默认为 db0
    'timeout'    => 0,
    'persistent' => false,
];

