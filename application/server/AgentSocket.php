<?php


namespace app\server;


use app\Service\AppPayOrderService;
use curl\GuzzleCurl;
use think\Db;

class AgentSocket
{

    /**
     * 支付状态映射 key为本项目订单状态，value为代理系统订单状态
     */
    const ORDER_MAP=[
        0=>1,
        1=>3,
        2=>2,
    ];

    /**
     * 会员登录
     * @param int $member_id
     * @return array|bool|float|int|mixed|\stdClass|string|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function userLogin(int $member_id)
    {
        try {
            $userinfo = Db::name('member_info')->where('id',$member_id)->find();

            $data = [
                'uid' => $userinfo['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'channel_uid' => $userinfo['channelid'],
                'device_type' => $userinfo['device_type'],
                'app_version' => $userinfo['app_version'],
                'device_no' => $userinfo['device_id'],
            ];

            $result = GuzzleCurl::getInstance()->setUrl('user/login')->setParams($data)->async();
        }catch (\Throwable $e) {

        } finally {
            $error_reason=empty($e)?'':__FUNCTION__.'请求报错：'.$e->getMessage().$e->getFile().$e->getLine();
            if(!empty($error_reason)){
                logInfo(['data'=>$data??[],'result'=>$result??[],'error_reason'=>$error_reason],'userLogin' ,'userLogin', 'http_request');
            }

        }
        return $result??[];

    }

    /**
     * 会员新增
     * @param int $member_id
     * @return array|bool|float|int|mixed|\stdClass|string|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function userInc(int $member_id)
    {

        try {
            $userinfo = Db::name('member_info')->where('id',$member_id)->find();

            $data =[
                'uid' => $userinfo['id'],
                'user_id' => $userinfo['id'],
                'created_at' => date('Y-m-d H:i:s'),
                'channel_uid' => $userinfo['channelid'],
                'app_version' => $userinfo['app_version'],
                'device_type' => $userinfo['device_type'],
                'device_no' => $userinfo['device_id'],
                'share_code' => '',
                'mobile' => $userinfo['mobile'],
                'nickname' => $userinfo['nick_name'],
                'ip' => $userinfo['create_ip'],
                'mom_app_uid' => sysconf('mom_app_uid'),
            ];

            $result = GuzzleCurl::getInstance()->setUrl('user/inc')->setParams($data)->async();
        }catch (\Throwable $e) {

        } finally {
            $error_reason=empty($e)?'':__FUNCTION__.'请求报错：'.$e->getMessage().$e->getFile().$e->getLine();
            if(!empty($error_reason)){
                logInfo(['data'=>$data??[],'result'=>$result??[],'error_reason'=>$error_reason],'userInc' ,'userInc', 'http_request');
            }
        }
        return $result??[];

    }


    /**
     * 下载量统计
     * @return array|bool|float|int|mixed|\stdClass|string|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function downloadCount(string $channelid, string $created_at='')
    {

        try {

            $data =[
                'created_at' => !$created_at ? date('Y-m-d H:i:s') : $created_at,
                'channel_uid' => $channelid,
            ];

            $result = GuzzleCurl::getInstance()->setUrl('user/downloadCount')->setParams($data)->async();
        }catch (\Throwable $e) {

        } finally {
            $error_reason=empty($e)?'':__FUNCTION__.'请求报错：'.$e->getMessage().$e->getFile().$e->getLine();
            if(!empty($error_reason)){
                logInfo(['data'=>$data??[],'result'=>$result??[],'error_reason'=>$error_reason],'downloadCount' ,'downloadCount', 'http_request');
            }
        }
        return $result??[];

    }



    /**
     * 订单增量
     * @param string $order_id
     * @return array|bool|float|int|mixed|\stdClass|string|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function payInc(string $order_id)
    {

        try {

            $order_detail = Db::name('app_pay_order')->where('order_id',$order_id)->find();

            $member_detail = Db::name('member_info')->where('id',$order_detail['member_id'])->find();

            $data = [
                'uid' => $member_detail['id'],
                'channel_uid' => $member_detail['channelid'],
                'updated_at' => date('Y-m-d H:i:s',$order_detail['addtime']),
                'paid_at' => date('Y-m-d H:i:s',$order_detail['addtime']),
                'share_code' => '',
                'transaction_no' => $order_detail['order_id'],
                'order_sn' => $order_detail['order_id'],
                'pay_status' => self::ORDER_MAP[$order_detail['status']]??3,
                'order_status' => self::ORDER_MAP[$order_detail['status']]??3,

                'created_at' => date('Y-m-d H:i:s',$order_detail['addtime']),
                'price' => round($order_detail['order_amount']/100,2),
                'real_pay_price' => round($order_detail['pay_amount']/100,2),
                'buy_gold_num' => 1,
                'buy_vip_info' => '{}',
                'pay_channel' => $order_detail['pay'] ?: 'pay',
                'device_type' => 'A',
                'payment_code' => $order_detail['pay_id'] ?: '1',
                'buy_type' => 1,

                'mom_app_uid'=>sysconf('mom_app_uid')
            ];

            $result = GuzzleCurl::getInstance()->setUrl('pay/inc')->setParams($data)->async();
        }catch (\Throwable $e) {
            $error_reason=__FUNCTION__.'请求报错：'.$e->getMessage().$e->getFile().$e->getLine();
            logInfo(['data'=>$data??[],'result'=>$result??[],'error_reason'=>$error_reason],'payInc' ,'payInc', 'http_request');
        }
        return $result??[];
    }

    /**
     * 订单增量（外部订单）
     * @param string $order_id
     * @return array|bool|float|int|mixed|\stdClass|string|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     * @throws \think\exception\DbException
     */
    public function payIncOutside(array $original_data)
    {
        //这里默认外部的订单只接收支付成功订单的
        try {

            $data = $original_data;
            $member_detail = Db::name('member_info')->where('account',$data['account'])->find();

            if(!empty($member_detail)){
                $channelid = $member_detail['channelid']??'';
                $data = [
                    'uid' => $member_detail['id'],
                    'channel_uid' => $channelid,
                    'updated_at' => $data['paidTime'],
                    'paid_at' => $data['paidTime'],
                    'share_code' => '',
                    'transaction_no' => $data['orderNo'],
                    'order_sn' => $data['orderNo'],
                    'pay_status' => $data['order_status'],
                    'order_status' => $data['order_status'],

                    'created_at' => $data['paidTime'],
                    'price' => $data['realPayPrice'],
                    'real_pay_price' => $data['realPayPrice'],
                    'buy_gold_num' => 1,
                    'buy_vip_info' => '{}',
                    'pay_channel' => 'pay',
                    'device_type' => $data['device_type']??'',
                    'payment_code' => '1',
                    'buy_type' => 1,

                    'mom_app_uid'=>sysconf('mom_app_uid')
                ];

                $result = GuzzleCurl::getInstance()->setUrl('pay/inc')->setParams($data)->async();


                if(empty(Db::name('outside_pay_order')->where([
                    ['order_sn','=',$data['order_sn']],
                    ['source','=',$original_data['source']],
                ])->field('id')->find())){
                    //渠道今日充值人数
                    $pay_member_Ymd_key = 'pay_member_all'.date('Ymd',strtotime($data['created_at'])).'_'.$channelid;
                    redis()->sadd($pay_member_Ymd_key,$member_detail['id']);
                    redis()->expire($pay_member_Ymd_key,86500);

                    channeldata('all_recharge',$channelid,$data['real_pay_price']*100,strtotime($data['created_at']));
                    Db::name('outside_pay_order')->insert([
                        'order_sn'=>$data['order_sn'],
                        'source'=>$original_data['source'],
                        'channelid'=>$channelid,
                        'member_id'=>$member_detail['id'],
                        'account'=>$original_data['account'],
                        'order_amount'=>$data['price'],
                        'pay_amount'=>$data['real_pay_price'],
                        'status'=>$data['pay_status']==1?0:2,
                        'paid_time'=>$data['created_at'],
                        'device_type'=>$data['device_type']??'',
                    ]);
                }

                $outside_pay_order =  Db::name('outside_pay_order')->where([
                    ['member_id','=',$member_detail['id']],
                    ['order_sn','<>',$data['order_sn']],
                    ['status','=',0],
                ])->field('id')->find();

                if(empty($outside_pay_order)){
                    $pay_order =  Db::name('app_pay_order')->where([
                        ['member_id','=',$member_detail['id']],
                        ['status','=',0],
                        ['is_first','IN',[AppPayOrderService::ORDER_TYPE_MOVIE[0],AppPayOrderService::ORDER_TYPE_AI[0]]],
                    ])->field('id')->find();

                    if(empty($pay_order)){
                        channeldata('all_first_pay',$channelid,$data['real_pay_price']*100,strtotime($data['created_at']));
                    }

                }

            }

        }catch (\Throwable $e) {
            $error_reason=__FUNCTION__.'请求报错：'.$e->getMessage().$e->getFile().$e->getLine();
            logInfo(['data'=>$data??[],'result'=>$result??[],'error_reason'=>$error_reason],'payInc' ,'payInc', 'http_request');
        }
        return $result??[];
    }


    public function getAdList($channelUid)
    {
        return []; //由于常年没人用这个功能，为了提高性能直接关掉此接口

        $cache_key = 'getAdList:'.$channelUid;

        $res =  cacheGet($cache_key);

        if(!isset($res)){

            try {
                $res = [];
                $info = [
                    'channel_uid' => $channelUid ?: 'NH',
                    'mom_app_uid' => '7EzlBD',
                ];
                $data = GuzzleCurl::getInstance()->setUrl('common/channel-ad')->setParams($info)->async();
                debug('ad',[sysconf('agent_api_url').'common/channel-ad',$info,$data]);
                if ($data['code'] == 200) {
                    if (!empty($data['data'])) {
                        foreach ($data['data'] as $key => $val) {
                            $res[$val['alias']][$key]['adv_title'] = $val['title'];
                            $res[$val['alias']][$key]['adv_img'] = $val['link_url'];
                            $res[$val['alias']][$key]['adv_url'] = $val['image_url'];
                            $res[$val['alias']][$key]['id'] = $val['id'];
                            $res[$val['alias']][$key]['adv_isshow'] = $val['day_status'];
                            $res[$val['alias']][$key]['adv_content'] = $val['ag_ads_content'];
                            $res[$val['alias']][$key]['adv_btntext'] = $val['button_content'];
                            $res[$val['alias']][$key]['adv_jump'] = $val['jump_type'] == 0 ? 1 : 2;
                        }
                    }
                }
            }catch (\Throwable $e) {
                $error_reason=__FUNCTION__.'请求报错：'.$e->getMessage().$e->getFile().$e->getLine();
                logInfo(['data'=>$data??[],'result'=>$result??[],'error_reason'=>$error_reason],'getAdList' ,'getAdList', 'http_request');
            }
            cacheSet($cache_key,$res,600);
        }

        return $res;
    }
}
