{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "ce615e6ff7bd70369565bd32bc973b5c", "packages": [{"name": "aliyuncs/oss-sdk-php", "version": "v2.6.0", "source": {"type": "git", "url": "https://github.com/aliyun/aliyun-oss-php-sdk.git", "reference": "572d0f8e099e8630ae7139ed3fdedb926c7a760f"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/aliyun/aliyun-oss-php-sdk/zipball/572d0f8e099e8630ae7139ed3fdedb926c7a760f", "reference": "572d0f8e099e8630ae7139ed3fdedb926c7a760f", "shasum": ""}, "require": {"php": ">=5.3"}, "require-dev": {"phpunit/phpunit": "*", "satooshi/php-coveralls": "*"}, "type": "library", "autoload": {"psr-4": {"OSS\\": "src/OSS"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "Aliyuncs", "homepage": "http://www.aliyun.com"}], "description": "Aliyun OSS SDK for PHP", "homepage": "http://www.aliyun.com/product/oss/", "support": {"issues": "https://github.com/aliyun/aliyun-oss-php-sdk/issues", "source": "https://github.com/aliyun/aliyun-oss-php-sdk/tree/v2.6.0"}, "time": "2022-08-03T08:06:01+00:00"}, {"name": "cloned/luckybox", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/cloned/luckybox.git", "reference": "fb9d6a99bec8def290b2155c16ade8ec76edea72"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/cloned/luckybox/zipball/fb9d6a99bec8def290b2155c16ade8ec76edea72", "reference": "fb9d6a99bec8def290b2155c16ade8ec76edea72", "shasum": ""}, "require": {"php": ">=5.3.0"}, "default-branch": true, "type": "library", "autoload": {"psr-0": {"LuckyBox": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://cloned.jp/"}], "description": "A library to pick up the one with a specified probability from some items.", "homepage": "https://github.com/cloned/luckybox", "keywords": ["lottery", "luckybox", "random"], "support": {"issues": "https://github.com/cloned/luckybox/issues", "source": "https://github.com/cloned/luckybox/tree/master"}, "time": "2013-10-20T06:56:36+00:00"}, {"name": "composer/ca-bundle", "version": "dev-main", "source": {"type": "git", "url": "https://github.com/composer/ca-bundle.git", "reference": "30897edbfb15e784fe55587b4f73ceefd3c4d98c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/composer/ca-bundle/zipball/30897edbfb15e784fe55587b4f73ceefd3c4d98c", "reference": "30897edbfb15e784fe55587b4f73ceefd3c4d98c", "shasum": ""}, "require": {"ext-openssl": "*", "ext-pcre": "*", "php": "^5.3.2 || ^7.0 || ^8.0"}, "require-dev": {"phpstan/phpstan": "^0.12.55", "psr/log": "^1.0", "symfony/phpunit-bridge": "^4.2 || ^5", "symfony/process": "^2.5 || ^3.0 || ^4.0 || ^5.0 || ^6.0"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-main": "1.x-dev"}}, "autoload": {"psr-4": {"Composer\\CaBundle\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON>", "email": "j.bog<PERSON><PERSON>@seld.be", "homepage": "http://seld.be"}], "description": "Lets you find a path to the system CA bundle, and includes a fallback to the Mozilla CA bundle.", "keywords": ["cabundle", "cacert", "certificate", "ssl", "tls"], "support": {"irc": "irc://irc.freenode.org/composer", "issues": "https://github.com/composer/ca-bundle/issues", "source": "https://github.com/composer/ca-bundle/tree/1.3.3"}, "funding": [{"url": "https://packagist.com", "type": "custom"}, {"url": "https://github.com/composer", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/composer/composer", "type": "tidelift"}], "time": "2022-07-20T07:14:26+00:00"}, {"name": "elasticsearch/elasticsearch", "version": "7.x-dev", "source": {"type": "git", "url": "https://github.com/elastic/elasticsearch-php.git", "reference": "c078ccb082f4cdfb3ef81971a8ca6afb9844d6ce"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/elastic/elasticsearch-php/zipball/c078ccb082f4cdfb3ef81971a8ca6afb9844d6ce", "reference": "c078ccb082f4cdfb3ef81971a8ca6afb9844d6ce", "shasum": ""}, "require": {"ext-json": ">=1.3.7", "ezimuel/ringphp": "^1.1.2", "php": "^7.3 || ^8.0", "psr/log": "^1|^2"}, "require-dev": {"ext-yaml": "*", "ext-zip": "*", "mockery/mockery": "^1.2", "phpstan/phpstan": "^0.12", "phpunit/phpunit": "^9.3", "squizlabs/php_codesniffer": "^3.4", "symfony/finder": "~4.0"}, "suggest": {"ext-curl": "*", "monolog/monolog": "Allows for client-level logging and tracing"}, "type": "library", "autoload": {"files": ["src/autoload.php"], "psr-4": {"Elasticsearch\\": "src/Elasticsearch/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>"}, {"name": "<PERSON>"}], "description": "PHP Client for Elasticsearch", "keywords": ["client", "elasticsearch", "search"], "support": {"issues": "https://github.com/elastic/elasticsearch-php/issues", "source": "https://github.com/elastic/elasticsearch-php/tree/7.x"}, "time": "2023-02-13T08:11:52+00:00"}, {"name": "ezimuel/guzzlestreams", "version": "3.1.0", "source": {"type": "git", "url": "https://github.com/ezimuel/guzzlestreams.git", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/guzzlestreams/zipball/b4b5a025dfee70d6cd34c780e07330eb93d5b997", "reference": "b4b5a025dfee70d6cd34c780e07330eb93d5b997", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "~9.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "3.0-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Stream\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/streams (abandoned) to be used with elasticsearch-php", "homepage": "http://guzzlephp.org/", "keywords": ["Guzzle", "stream"], "support": {"source": "https://github.com/ezimuel/guzzlestreams/tree/3.1.0"}, "time": "2022-10-24T12:58:50+00:00"}, {"name": "ezimuel/ringphp", "version": "1.2.2", "source": {"type": "git", "url": "https://github.com/ezimuel/ringphp.git", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ezimuel/ringphp/zipball/7887fc8488013065f72f977dcb281994f5fde9f4", "reference": "7887fc8488013065f72f977dcb281994f5fde9f4", "shasum": ""}, "require": {"ezimuel/guzzlestreams": "^3.0.1", "php": ">=5.4.0", "react/promise": "~2.0"}, "replace": {"guzzlehttp/ringphp": "self.version"}, "require-dev": {"ext-curl": "*", "phpunit/phpunit": "~9.0"}, "suggest": {"ext-curl": "Guzzle will use specific adapters if cURL is present"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Ring\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}], "description": "Fork of guzzle/RingPHP (abandoned) to be used with elasticsearch-php", "support": {"source": "https://github.com/ezimuel/ringphp/tree/1.2.2"}, "time": "2022-12-07T11:28:53+00:00"}, {"name": "geoip2/geoip2", "version": "v2.13.0", "source": {"type": "git", "url": "**************:maxmind/GeoIP2-php.git", "reference": "6a41d8fbd6b90052bc34dff3b4252d0f88067b23"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/GeoIP2-php/zipball/6a41d8fbd6b90052bc34dff3b4252d0f88067b23", "reference": "6a41d8fbd6b90052bc34dff3b4252d0f88067b23", "shasum": ""}, "require": {"ext-json": "*", "maxmind-db/reader": "~1.8", "maxmind/web-service-common": "~0.8", "php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "phpstan/phpstan": "*", "phpunit/phpunit": "^8.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"GeoIp2\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind GeoIP2 PHP API", "homepage": "https://github.com/maxmind/GeoIP2-php", "keywords": ["IP", "geoip", "geoip2", "geolocation", "maxmind"], "time": "2022-08-05T20:32:58+00:00"}, {"name": "godruoyi/php-snowflake", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/godruoyi/php-snowflake.git", "reference": "6a35e7d6fbd35624c73d7afa92667e9c58d27059"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/godruoyi/php-snowflake/zipball/6a35e7d6fbd35624c73d7afa92667e9c58d27059", "reference": "6a35e7d6fbd35624c73d7afa92667e9c58d27059", "shasum": ""}, "require": {"php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "~7|^8|^9"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"Godruoyi\\Snowflake\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "An ID Generator for PHP based on Snowflake Algorithm (Twitter announced).", "homepage": "https://github.com/godruoyi/php-snowflake", "keywords": ["Unique ID", "laravel snowflake", "order id", "php snowflake", "php unique id", "snowflake algorithm", "unique order id"], "support": {"issues": "https://github.com/godruoyi/php-snowflake/issues", "source": "https://github.com/godruoyi/php-snowflake/tree/2.1.1"}, "funding": [{"url": "https://images.godruoyi.com/wechat.png", "type": "custom"}], "time": "2022-10-31T09:24:19+00:00"}, {"name": "guzzlehttp/guzzle", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/guzzle/guzzle.git", "reference": "8459341c16f96b9610dcdfe22bd3060d60c0da04"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/guzzle/zipball/8459341c16f96b9610dcdfe22bd3060d60c0da04", "reference": "8459341c16f96b9610dcdfe22bd3060d60c0da04", "shasum": ""}, "require": {"ext-json": "*", "guzzlehttp/promises": "^1.5", "guzzlehttp/psr7": "^1.9 || ^2.4", "php": "^7.2.5 || ^8.0", "psr/http-client": "^1.0", "symfony/deprecation-contracts": "^2.2 || ^3.0"}, "provide": {"psr/http-client-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "ext-curl": "*", "php-http/client-integration-tests": "^3.0", "phpunit/phpunit": "^8.5.29 || ^9.5.23", "psr/log": "^1.1 || ^2.0 || ^3.0"}, "suggest": {"ext-curl": "Required for CURL handler support", "ext-intl": "Required for Internationalized Domain Name (IDN) support", "psr/log": "Required for using the Log middleware"}, "default-branch": true, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "7.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "jereme<PERSON>@gmail.com", "homepage": "https://github.com/jeremeamia"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle is a PHP HTTP client library", "keywords": ["client", "curl", "framework", "http", "http client", "psr-18", "psr-7", "rest", "web service"], "support": {"issues": "https://github.com/guzzle/guzzle/issues", "source": "https://github.com/guzzle/guzzle/tree/master"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/guzzle", "type": "tidelift"}], "time": "2022-08-29T11:03:19+00:00"}, {"name": "guzzlehttp/promises", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/guzzle/promises.git", "reference": "b94b2807d85443f9719887892882d0329d1e2598"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/promises/zipball/b94b2807d85443f9719887892882d0329d1e2598", "reference": "b94b2807d85443f9719887892882d0329d1e2598", "shasum": ""}, "require": {"php": ">=5.5"}, "require-dev": {"symfony/phpunit-bridge": "^4.4 || ^5.1"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "1.5-dev"}}, "autoload": {"files": ["src/functions_include.php"], "psr-4": {"GuzzleHttp\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}], "description": "Guzzle promises library", "keywords": ["promise"], "support": {"issues": "https://github.com/guzzle/promises/issues", "source": "https://github.com/guzzle/promises/tree/1.5.2"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/promises", "type": "tidelift"}], "time": "2022-08-28T14:55:35+00:00"}, {"name": "guzzlehttp/psr7", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/guzzle/psr7.git", "reference": "69568e4293f4fa993f3b0e51c9723e1e17c41379"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/guzzle/psr7/zipball/69568e4293f4fa993f3b0e51c9723e1e17c41379", "reference": "69568e4293f4fa993f3b0e51c9723e1e17c41379", "shasum": ""}, "require": {"php": "^7.2.5 || ^8.0", "psr/http-factory": "^1.0", "psr/http-message": "^1.0", "ralouphie/getallheaders": "^3.0"}, "provide": {"psr/http-factory-implementation": "1.0", "psr/http-message-implementation": "1.0"}, "require-dev": {"bamarni/composer-bin-plugin": "^1.8.1", "http-interop/http-factory-tests": "^0.9", "phpunit/phpunit": "^8.5.29 || ^9.5.23"}, "suggest": {"laminas/laminas-httphandlerrunner": "Emit PSR-7 responses"}, "default-branch": true, "type": "library", "extra": {"bamarni-bin": {"bin-links": true, "forward-command": false}, "branch-alias": {"dev-master": "2.4-dev"}}, "autoload": {"psr-4": {"GuzzleHttp\\Psr7\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/GrahamCampbell"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/mtdowling"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/gmponos"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Nyholm"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/sagikazarmark"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/Tobion"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://sagikazarmark.hu"}], "description": "PSR-7 message implementation that also provides common utility methods", "keywords": ["http", "message", "psr-7", "request", "response", "stream", "uri", "url"], "support": {"issues": "https://github.com/guzzle/psr7/issues", "source": "https://github.com/guzzle/psr7/tree/2.4.1"}, "funding": [{"url": "https://github.com/GrahamCampbell", "type": "github"}, {"url": "https://github.com/Nyholm", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/guzzlehttp/psr7", "type": "tidelift"}], "time": "2022-08-28T14:45:39+00:00"}, {"name": "jonahgeorge/jaeger-client-php", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/jonahgeorge/jaeger-client-php.git", "reference": "5f3ce14f5f198ef3c0fb9c51a76ef4b67981adb9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/jonahgeorge/jaeger-client-php/zipball/5f3ce14f5f198ef3c0fb9c51a76ef4b67981adb9", "reference": "5f3ce14f5f198ef3c0fb9c51a76ef4b67981adb9", "shasum": ""}, "require": {"ext-sockets": "*", "opentracing/opentracing": "^1.0", "packaged/thrift": "^0.13", "php": "^7.1 || ^8.0 || ^8.1", "psr/cache": "^1.0 || ^2.0 || ^3.0", "psr/log": "^1.0 || ^2.0 || ^3.0"}, "require-dev": {"cache/array-adapter": "^1.0", "phpunit/phpunit": "^7 || ^8 || ^9", "squizlabs/php_codesniffer": "3.*", "symfony/polyfill-php73": "^1.10"}, "default-branch": true, "type": "library", "autoload": {"files": ["./src/Jaeger/Constants.php"], "psr-4": {"Jaeger\\": "src/<PERSON><PERSON><PERSON>/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "homepage": "http://twitter.com/jonahgeorge"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Contributors", "homepage": "https://github.com/jonahgeorge/jaeger-client-php/graphs/contributors"}], "description": "Jaeger Bindings for PHP OpenTracing API", "keywords": ["jaeger", "opentracing", "trace", "tracing"], "support": {"issues": "https://github.com/jonahgeorge/jaeger-client-php/issues", "source": "https://github.com/jonahgeorge/jaeger-client-php/tree/v1.4.3"}, "time": "2022-07-21T13:25:37+00:00"}, {"name": "maxmind-db/reader", "version": "v1.11.0", "source": {"type": "git", "url": "https://github.com/maxmind/MaxMind-DB-Reader-php.git", "reference": "b1f3c0699525336d09cc5161a2861268d9f2ae5b"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/MaxMind-DB-Reader-php/zipball/b1f3c0699525336d09cc5161a2861268d9f2ae5b", "reference": "b1f3c0699525336d09cc5161a2861268d9f2ae5b", "shasum": ""}, "require": {"php": ">=7.2"}, "conflict": {"ext-maxminddb": "<1.10.1,>=2.0.0"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "php-coveralls/php-coveralls": "^2.1", "phpstan/phpstan": "*", "phpunit/phpcov": ">=6.0.0", "phpunit/phpunit": ">=8.0.0,<10.0.0", "squizlabs/php_codesniffer": "3.*"}, "suggest": {"ext-bcmath": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-gmp": "bcmath or gmp is required for decoding larger integers with the pure PHP decoder", "ext-maxminddb": "A C-based database decoder that provides significantly faster lookups"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Db\\": "src/MaxMind/Db"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://www.maxmind.com/"}], "description": "MaxMind DB Reader API", "homepage": "https://github.com/maxmind/MaxMind-DB-Reader-php", "keywords": ["database", "geoip", "geoip2", "geolocation", "maxmind"], "support": {"issues": "https://github.com/maxmind/MaxMind-DB-Reader-php/issues", "source": "https://github.com/maxmind/MaxMind-DB-Reader-php/tree/v1.11.0"}, "time": "2021-10-18T15:23:10+00:00"}, {"name": "maxmind/web-service-common", "version": "v0.9.0", "source": {"type": "git", "url": "https://github.com/maxmind/web-service-common-php.git", "reference": "4dc5a3e8df38aea4ca3b1096cee3a038094e9b53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/maxmind/web-service-common-php/zipball/4dc5a3e8df38aea4ca3b1096cee3a038094e9b53", "reference": "4dc5a3e8df38aea4ca3b1096cee3a038094e9b53", "shasum": ""}, "require": {"composer/ca-bundle": "^1.0.3", "ext-curl": "*", "ext-json": "*", "php": ">=7.2"}, "require-dev": {"friendsofphp/php-cs-fixer": "3.*", "phpstan/phpstan": "*", "phpunit/phpunit": "^8.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"psr-4": {"MaxMind\\Exception\\": "src/Exception", "MaxMind\\WebService\\": "src/WebService"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "Internal MaxMind Web Service API", "homepage": "https://github.com/maxmind/web-service-common-php", "support": {"issues": "https://github.com/maxmind/web-service-common-php/issues", "source": "https://github.com/maxmind/web-service-common-php/tree/v0.9.0"}, "time": "2022-03-28T17:43:20+00:00"}, {"name": "nategood/httpful", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/nategood/httpful.git", "reference": "0cded3ea97ba905600de9ceb9ef13f3ab681587c"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/nategood/httpful/zipball/0cded3ea97ba905600de9ceb9ef13f3ab681587c", "reference": "0cded3ea97ba905600de9ceb9ef13f3ab681587c", "shasum": ""}, "require": {"ext-curl": "*", "php": ">=7.2"}, "require-dev": {"phpunit/phpunit": "*"}, "default-branch": true, "type": "library", "autoload": {"psr-0": {"Httpful": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "http://nategood.com"}], "description": "A Readable, Chainable, REST friendly, PHP HTTP Client", "homepage": "http://github.com/nategood/httpful", "keywords": ["api", "curl", "http", "requests", "rest", "restful"], "support": {"issues": "https://github.com/nategood/httpful/issues", "source": "https://github.com/nategood/httpful/tree/v0.3.2"}, "time": "2020-01-25T01:13:13+00:00"}, {"name": "opentracing/opentracing", "version": "1.0.2", "source": {"type": "git", "url": "https://github.com/opentracing/opentracing-php.git", "reference": "cd60bd1fb2a25280600bc74c7f9e0c13881a9116"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/opentracing/opentracing-php/zipball/cd60bd1fb2a25280600bc74c7f9e0c13881a9116", "reference": "cd60bd1fb2a25280600bc74c7f9e0c13881a9116", "shasum": ""}, "require": {"php": "^7.1 || ^8.0"}, "require-dev": {"phpstan/phpstan": "~0.12", "phpunit/phpunit": "^7.0 || ^9.0", "squizlabs/php_codesniffer": "3.*"}, "type": "library", "autoload": {"files": ["src/OpenTracing/Tags.php", "src/OpenTracing/Formats.php"], "psr-4": {"OpenTracing\\": "src/OpenTracing/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "OpenTracing API for PHP", "support": {"issues": "https://github.com/opentracing/opentracing-php/issues", "source": "https://github.com/opentracing/opentracing-php/tree/1.0.2"}, "time": "2022-01-27T19:59:21+00:00"}, {"name": "packaged/thrift", "version": "0.13.01", "source": {"type": "git", "url": "https://github.com/packaged/thrift.git", "reference": "e3dbcfb79e319971d64264ffe9c340590cc8a228"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/packaged/thrift/zipball/e3dbcfb79e319971d64264ffe9c340590cc8a228", "reference": "e3dbcfb79e319971d64264ffe9c340590cc8a228", "shasum": ""}, "require": {"php": "^5.5 || ^7.0 || ^8.0"}, "type": "library", "autoload": {"psr-4": {"Thrift\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "description": "Apache Thrift", "homepage": "http://thrift.apache.org/", "keywords": ["apache", "thrift"], "support": {"issues": "https://github.com/packaged/thrift/issues", "source": "https://github.com/packaged/thrift/tree/0.13.01"}, "time": "2021-01-25T13:32:28+00:00"}, {"name": "psr/cache", "version": "1.0.1", "source": {"type": "git", "url": "https://github.com/php-fig/cache.git", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/cache/zipball/d11b50ad223250cf17b86e38383413f5a6764bf8", "reference": "d11b50ad223250cf17b86e38383413f5a6764bf8", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Cache\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for caching libraries", "keywords": ["cache", "psr", "psr-6"], "support": {"source": "https://github.com/php-fig/cache/tree/master"}, "time": "2016-08-06T20:24:11+00:00"}, {"name": "psr/http-client", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/php-fig/http-client.git", "reference": "22b2ef5687f43679481615605d7a15c557ce85b1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-client/zipball/22b2ef5687f43679481615605d7a15c557ce85b1", "reference": "22b2ef5687f43679481615605d7a15c557ce85b1", "shasum": ""}, "require": {"php": "^7.0 || ^8.0", "psr/http-message": "^1.0"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Client\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for HTTP clients", "homepage": "https://github.com/php-fig/http-client", "keywords": ["http", "http-client", "psr", "psr-18"], "support": {"source": "https://github.com/php-fig/http-client/tree/master"}, "time": "2020-09-19T09:12:31+00:00"}, {"name": "psr/http-factory", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/php-fig/http-factory.git", "reference": "5a4f141ac2e5bc35e615134f127e1833158d2944"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-factory/zipball/5a4f141ac2e5bc35e615134f127e1833158d2944", "reference": "5a4f141ac2e5bc35e615134f127e1833158d2944", "shasum": ""}, "require": {"php": ">=7.0.0", "psr/http-message": "^1.0"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interfaces for PSR-7 HTTP message factories", "keywords": ["factory", "http", "message", "psr", "psr-17", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-factory/tree/master"}, "time": "2022-07-14T07:21:53+00:00"}, {"name": "psr/http-message", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/php-fig/http-message.git", "reference": "efd67d1dc14a7ef4fc4e518e7dee91c271d524e4"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/http-message/zipball/efd67d1dc14a7ef4fc4e518e7dee91c271d524e4", "reference": "efd67d1dc14a7ef4fc4e518e7dee91c271d524e4", "shasum": ""}, "require": {"php": ">=5.3.0"}, "default-branch": true, "type": "library", "extra": {"branch-alias": {"dev-master": "1.0.x-dev"}}, "autoload": {"psr-4": {"Psr\\Http\\Message\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "http://www.php-fig.org/"}], "description": "Common interface for HTTP messages", "homepage": "https://github.com/php-fig/http-message", "keywords": ["http", "http-message", "psr", "psr-7", "request", "response"], "support": {"source": "https://github.com/php-fig/http-message/tree/master"}, "time": "2019-08-29T13:16:46+00:00"}, {"name": "psr/log", "version": "1.1.4", "source": {"type": "git", "url": "https://github.com/php-fig/log.git", "reference": "d49695b909c3b7628b6289db5479a1c204601f11"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/php-fig/log/zipball/d49695b909c3b7628b6289db5479a1c204601f11", "reference": "d49695b909c3b7628b6289db5479a1c204601f11", "shasum": ""}, "require": {"php": ">=5.3.0"}, "type": "library", "extra": {"branch-alias": {"dev-master": "1.1.x-dev"}}, "autoload": {"psr-4": {"Psr\\Log\\": "Psr/Log/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "PHP-FIG", "homepage": "https://www.php-fig.org/"}], "description": "Common interface for logging libraries", "homepage": "https://github.com/php-fig/log", "keywords": ["log", "psr", "psr-3"], "support": {"source": "https://github.com/php-fig/log/tree/1.1.4"}, "time": "2021-05-03T11:20:27+00:00"}, {"name": "ralouphie/getallheaders", "version": "3.0.3", "source": {"type": "git", "url": "https://github.com/ralouphie/getallheaders.git", "reference": "120b605dfeb996808c31b6477290a714d356e822"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ralouphie/getallheaders/zipball/120b605dfeb996808c31b6477290a714d356e822", "reference": "120b605dfeb996808c31b6477290a714d356e822", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"php-coveralls/php-coveralls": "^2.1", "phpunit/phpunit": "^5 || ^6.5"}, "type": "library", "autoload": {"files": ["src/getallheaders.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "A polyfill for getallheaders.", "support": {"issues": "https://github.com/ralouphie/getallheaders/issues", "source": "https://github.com/ralouphie/getallheaders/tree/develop"}, "time": "2019-03-08T08:55:37+00:00"}, {"name": "react/promise", "version": "2.x-dev", "source": {"type": "git", "url": "https://github.com/reactphp/promise.git", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/reactphp/promise/zipball/1a8460931ea36dc5c76838fec5734d55c88c6831", "reference": "1a8460931ea36dc5c76838fec5734d55c88c6831", "shasum": ""}, "require": {"php": ">=5.4.0"}, "require-dev": {"phpunit/phpunit": "^9.6 || ^5.7 || ^4.8.36"}, "type": "library", "autoload": {"files": ["src/functions_include.php"], "psr-4": {"React\\Promise\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://sorgalla.com/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://clue.engineering/"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://wyrihaximus.net/"}, {"name": "<PERSON>", "email": "<EMAIL>", "homepage": "https://cboden.dev/"}], "description": "A lightweight implementation of CommonJS Promises/A for PHP", "keywords": ["promise", "promises"], "support": {"issues": "https://github.com/reactphp/promise/issues", "source": "https://github.com/reactphp/promise/tree/v2.11.0"}, "funding": [{"url": "https://opencollective.com/reactphp", "type": "open_collective"}], "time": "2023-11-16T16:16:50+00:00"}, {"name": "ritaswc/zx-ip-address", "version": "2.0.67", "source": {"type": "git", "url": "https://github.com/ritaswc/zx-ip-address.git", "reference": "a5c1fceee6e4de03611dd9bdc3e856760ffd54d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/ritaswc/zx-ip-address/zipball/a5c1fceee6e4de03611dd9bdc3e856760ffd54d1", "reference": "a5c1fceee6e4de03611dd9bdc3e856760ffd54d1", "shasum": ""}, "require": {"php": ">=5.6"}, "require-dev": {"phpunit/phpunit": "*"}, "type": "library", "autoload": {"psr-4": {"Ritaswc\\ZxIPAddress\\": "src/ZxIPAddress"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "description": "IPv4/IPv6 offline address database.IPv4/IPv6双栈地址离线数据库。IPv4 by 纯真网络，IPv6 by ZX Inc.", "support": {"issues": "https://github.com/ritaswc/zx-ip-address/issues", "source": "https://github.com/ritaswc/zx-ip-address/tree/2.0.67"}, "funding": [{"url": "https://blog.yinghualuo.cn/blog/2020/11/sponsor.png", "type": "custom"}], "abandoned": "zx-inc/zxipdb", "time": "2021-08-25T13:47:50+00:00"}, {"name": "symfony/deprecation-contracts", "version": "2.5.x-dev", "source": {"type": "git", "url": "https://github.com/symfony/deprecation-contracts.git", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/symfony/deprecation-contracts/zipball/e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "reference": "e8b495ea28c1d97b5e0c121748d6f9b53d075c66", "shasum": ""}, "require": {"php": ">=7.1"}, "type": "library", "extra": {"branch-alias": {"dev-main": "2.5-dev"}, "thanks": {"name": "symfony/contracts", "url": "https://github.com/symfony/contracts"}}, "autoload": {"files": ["function.php"]}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "Symfony Community", "homepage": "https://symfony.com/contributors"}], "description": "A generic function and convention to trigger deprecation notices", "homepage": "https://symfony.com", "support": {"source": "https://github.com/symfony/deprecation-contracts/tree/2.5"}, "funding": [{"url": "https://symfony.com/sponsor", "type": "custom"}, {"url": "https://github.com/fabpot", "type": "github"}, {"url": "https://tidelift.com/funding/github/packagist/symfony/symfony", "type": "tidelift"}], "time": "2022-01-02T09:53:40+00:00"}, {"name": "topthink/framework", "version": "5.1.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/framework.git", "reference": "1ce73c7ef9bdb60fb3bcc78d080fb545a26657ab"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/framework/zipball/1ce73c7ef9bdb60fb3bcc78d080fb545a26657ab", "reference": "1ce73c7ef9bdb60fb3bcc78d080fb545a26657ab", "shasum": ""}, "require": {"php": ">=5.6.0", "topthink/think-installer": "2.*"}, "require-dev": {"johnkary/phpunit-speedtrap": "^1.0", "mikey179/vfsstream": "~1.6", "phpdocumentor/reflection-docblock": "^2.0", "phploc/phploc": "2.*", "phpunit/phpunit": "^5.0|^6.0", "sebastian/phpcpd": "2.*", "squizlabs/php_codesniffer": "2.*"}, "type": "think-framework", "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "the new thinkphp framework", "homepage": "http://thinkphp.cn/", "keywords": ["framework", "orm", "thinkphp"], "support": {"issues": "https://github.com/top-think/framework/issues", "source": "https://github.com/top-think/framework/tree/5.1"}, "time": "2022-07-05T05:19:13+00:00"}, {"name": "topthink/think-captcha", "version": "2.0.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/think-captcha.git", "reference": "54c8a51552f99ff9ea89ea9c272383a8f738ceee"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-captcha/zipball/54c8a51552f99ff9ea89ea9c272383a8f738ceee", "reference": "54c8a51552f99ff9ea89ea9c272383a8f738ceee", "shasum": ""}, "require": {"topthink/framework": "5.1.*"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\captcha\\": "src/"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "captcha package for thinkphp5", "support": {"issues": "https://github.com/top-think/think-captcha/issues", "source": "https://github.com/top-think/think-captcha/tree/2.0"}, "time": "2017-12-31T16:37:49+00:00"}, {"name": "topthink/think-helper", "version": "v3.1.6", "source": {"type": "git", "url": "https://github.com/top-think/think-helper.git", "reference": "769acbe50a4274327162f9c68ec2e89a38eb2aff"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-helper/zipball/769acbe50a4274327162f9c68ec2e89a38eb2aff", "reference": "769acbe50a4274327162f9c68ec2e89a38eb2aff", "shasum": ""}, "require": {"php": ">=7.1.0"}, "require-dev": {"phpunit/phpunit": "^9.5"}, "type": "library", "autoload": {"files": ["src/helper.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP6 Helper Package", "support": {"issues": "https://github.com/top-think/think-helper/issues", "source": "https://github.com/top-think/think-helper/tree/v3.1.6"}, "time": "2021-12-15T04:27:55+00:00"}, {"name": "topthink/think-image", "version": "dev-master", "source": {"type": "git", "url": "https://github.com/top-think/think-image.git", "reference": "47b6018825bd9520fb93afad601983f3c68cda53"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-image/zipball/47b6018825bd9520fb93afad601983f3c68cda53", "reference": "47b6018825bd9520fb93afad601983f3c68cda53", "shasum": ""}, "require": {"ext-gd": "*"}, "require-dev": {"phpunit/phpunit": "4.8.*", "topthink/framework": "^5.0"}, "default-branch": true, "type": "library", "autoload": {"psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Image Package", "support": {"issues": "https://github.com/top-think/think-image/issues", "source": "https://github.com/top-think/think-image/tree/master"}, "time": "2023-04-08T12:22:22+00:00"}, {"name": "topthink/think-installer", "version": "2.0.x-dev", "source": {"type": "git", "url": "https://github.com/top-think/think-installer.git", "reference": "38ba647706e35d6704b5d370c06f8a160b635f88"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-installer/zipball/38ba647706e35d6704b5d370c06f8a160b635f88", "reference": "38ba647706e35d6704b5d370c06f8a160b635f88", "shasum": ""}, "require": {"composer-plugin-api": "^1.0||^2.0"}, "require-dev": {"composer/composer": "^1.0||^2.0"}, "default-branch": true, "type": "composer-plugin", "extra": {"class": "think\\composer\\Plugin"}, "autoload": {"psr-4": {"think\\composer\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "support": {"issues": "https://github.com/top-think/think-installer/issues", "source": "https://github.com/top-think/think-installer/tree/v2.0.5"}, "time": "2021-01-14T12:12:14+00:00"}, {"name": "topthink/think-mongo", "version": "v1.8.5", "source": {"type": "git", "url": "https://github.com/top-think/think-mongo.git", "reference": "657cc79bd5f090a58b0cc83776073dd69c83a3d1"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-mongo/zipball/657cc79bd5f090a58b0cc83776073dd69c83a3d1", "reference": "657cc79bd5f090a58b0cc83776073dd69c83a3d1", "shasum": ""}, "type": "library", "autoload": {"files": [], "psr-4": {"think\\mongo\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "liu21st", "email": "<EMAIL>"}], "description": "mongodb driver for thinkphp5", "support": {"issues": "https://github.com/top-think/think-mongo/issues", "source": "https://github.com/top-think/think-mongo/tree/master"}, "time": "2018-06-03T01:51:27+00:00"}, {"name": "topthink/think-queue", "version": "v2.0.4", "source": {"type": "git", "url": "https://github.com/top-think/think-queue.git", "reference": "d9b8f38c7af8ad770257b0d7db711ce8b12a6969"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/top-think/think-queue/zipball/d9b8f38c7af8ad770257b0d7db711ce8b12a6969", "reference": "d9b8f38c7af8ad770257b0d7db711ce8b12a6969", "shasum": ""}, "require": {"topthink/framework": "5.1.*", "topthink/think-helper": ">=1.0.4", "topthink/think-installer": "^2.0"}, "type": "think-extend", "extra": {"think-config": {"queue": "src/config.php"}}, "autoload": {"files": ["src/common.php"], "psr-4": {"think\\": "src"}}, "notification-url": "https://packagist.org/downloads/", "license": ["Apache-2.0"], "authors": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "description": "The ThinkPHP5 Queue Package", "support": {"issues": "https://github.com/top-think/think-queue/issues", "source": "https://github.com/top-think/think-queue/tree/2.0"}, "time": "2018-05-11T06:55:55+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "dev", "stability-flags": {"jonahgeorge/jaeger-client-php": 20, "godruoyi/php-snowflake": 20, "nategood/httpful": 20, "cloned/luckybox": 20, "topthink/think-image": 20}, "prefer-stable": false, "prefer-lowest": false, "platform": {"ext-json": "*"}, "platform-dev": [], "plugin-api-version": "2.6.0"}