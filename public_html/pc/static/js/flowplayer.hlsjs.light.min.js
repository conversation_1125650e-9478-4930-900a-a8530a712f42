/*!

   hlsjs engine plugin (light) for Flowplayer HTML5

   Copyright (c) 2015-2017, Flowplayer Drive Oy

   Released under the MIT License:
   http://www.opensource.org/licenses/mit-license.php

   Includes hls.light.js
   Copyright (c) 2017 Dailymotion (http://www.dailymotion.com)
   https://github.com/video-dev/hls.js/blob/master/LICENSE

   Requires Flowplayer HTML5 version 7 or greater
   v1.1.1-42-g4295710

*/
/*@cc_on @*/
/*@
  @if (@_jscript_version > 10)
  @*/
!function(e){function t(a){if(r[a])return r[a].exports;var i=r[a]={exports:{},id:a,loaded:!1};return e[a].call(i.exports,i,i.exports,t),i.loaded=!0,i.exports}var r={};return t.m=e,t.c=r,t.p="",t(0)}([function(e,t,r){e.exports=r(3)},function(e,t,r){!function(){"use strict";var t=function(e,t){var r,a="hlsjs",i=t.common,n=t.extend,o=t.support,s=o.browser,l=t.version,d=window,u=d.MediaSource||d.WebKitMediaSource,f=d.performance,h=function(e){return e.toLowerCase().indexOf("mpegurl")>-1},c=function(e){var t=e.clip&&e.clip.hlsQualities||e.hlsQualities;return o.inlineVideo&&(t===!0||t&&t.length)},p=function(e){var t=i.findDirect("video",e)[0]||i.find(".fp-player>video",e)[0];t&&(i.find("source",t).forEach(function(e){e.removeAttribute("src")}),t.removeAttribute("src"),t.load(),i.removeNode(t))},v=function(s,l){var d,v,g,m,y,b=t.bean,E="is-seeking",R="is-poster",A=function(e,t,r){if(e.debug&&console.log("recovery."+a,"<-",t),i.removeClass(l,"is-paused"),i.addClass(l,E),r)v.startLoad();else{var n=f.now();!m||n-m>3e3?(m=f.now(),v.recoverMediaError()):(!y||n-y>3e3)&&(y=f.now(),v.swapAudioCodec(),v.recoverMediaError())}g>0&&(g-=1),b.one(d,"seeked."+a,function(){d.paused&&(i.removeClass(l,R),s.poster=!1,d.play()),i.removeClass(l,E)})},T=function(e,t,r){var a={code:e};return e>2&&(a.video=n(s.video,{src:t,url:r||t})),a},S=0,L=-1,_=function(e,t){var r,a=t.levels,i=function(e){return isNaN(Number(e))?e.level:e};if(e&&!(a.length<2)){if("drive"===e){switch(a.length){case 4:r=[1,2,3];break;case 5:r=[1,2,3,4];break;case 6:r=[1,3,4,5];break;case 7:r=[1,3,5,6];break;case 8:r=[1,3,6,7];break;default:if(a.length<3||a[0].height&&a[2].height&&a[0].height===a[2].height)return;r=[1,2]}r.unshift(-1)}else switch(typeof e){case"object":r=e.map(i);break;case"string":r=e.split(/\s*,\s*/).map(Number);break;default:r=a.map(function(e,t){return t}),r.unshift(-1)}r=r.filter(function(e){if(e>-1&&e<a.length){var t=a[e];return!t.videoCodec||t.videoCodec&&u.isTypeSupported("video/mp4;codecs="+t.videoCodec)}return e===-1}),s.video.qualities=r.map(function(t,r){var n=a[t],o="object"==typeof e?e.filter(function(e){return i(e)===t})[0]:t,s="Level "+(r+1);return t<0?s=o.label||"Auto":o.label?s=o.label:(n.width&&n.height&&(s=Math.min(n.width,n.height)+"p"),"drive"!==e&&n.bitrate&&(s+=" ("+Math.round(n.bitrate/1e3)+"k)")),{value:t,label:s}}),L>-1||r.indexOf(-1)<0?(v.loadLevel=r.indexOf(L)<0?r[0]:L,v.config.startLevel=v.loadLevel,s.video.quality=v.loadLevel):s.video.quality=-1,L=s.video.quality}},w={engineName:a,pick:function(e){var t=e.filter(function(e){return h(e.type)})[0];return"string"==typeof t.src&&(t.src=i.createAbsoluteUrl(t.src)),t},load:function(t){var u=s.conf,f={ended:"finish",loadeddata:"ready",pause:"pause",play:"resume",progress:"buffer",ratechange:"speed",seeked:"seek",timeupdate:"progress",volumechange:"volume",error:"error"},h=e.Events,R=!!t.autoplay||!!u.autoplay||!!u.splash,w=t.hlsQualities||u.hlsQualities,D=n(r,u.hlsjs,t.hlsjs),O=n({},D);if(t.hlsQualities===!1&&(w=!1),v?(v.destroy(),(s.video.src&&t.src!==s.video.src||t.index)&&i.attr(d,"autoplay","autoplay")):(p(l),d=i.createElement("video",{class:"fp-engine "+a+"-engine",autoplay:!!R&&"autoplay",volume:s.volumeLevel}),o.mutedAutoplay&&!u.splash&&R&&(d.muted=!0),Object.keys(f).forEach(function(e){var t,r=f[e],i=e+"."+a;b.on(d,i,function(e){u.debug&&r.indexOf("progress")<0&&console.log(i,"->",r,e.originalEvent);var a,o,l=d.currentTime,f=d.seekable,h=s.video,c=s.dvr?h.seekOffset:s.live?v.liveSyncPosition:0,p=d.buffered,g=[],m=h.src;switch(r){case"ready":t=n(h,{duration:d.duration,seekable:f.length&&f.end(null),width:d.videoWidth,height:d.videoHeight,url:m});break;case"resume":D.bufferWhilePaused||v.startLoad(l),l<c&&(d.currentTime=c);break;case"seek":!D.bufferWhilePaused&&d.paused&&v.stopLoad(),t=l;break;case"pause":D.bufferWhilePaused||v.stopLoad();break;case"progress":t=l;break;case"speed":t=d.playbackRate;break;case"volume":t=d.volume;break;case"buffer":for(a=0;a<p.length;a+=1)g.push(p.end(a));t=g.filter(function(e){return e>=l}).sort()[0],h.buffer=t;break;case"finish":D.bufferWhilePaused&&v.autoLevelEnabled&&(h.loop||u.playlist.length<2||u.advance===!1)&&(v.nextLoadLevel=S);break;case"error":if(o=d.error&&d.error.code,D.recoverMediaError&&(3===o||!o)||D.recoverNetworkError&&2===o||D.recover&&(2===o||3===o))return e.preventDefault(),void A(u,r,2===o);t=T(o,m)}s.trigger(r,[s,t])})}),s.on("error."+a,function(){v&&s.engine.unload()}).on("beforeseek."+a,function(e,t,r){void 0===r?e.preventDefault():!D.bufferWhilePaused&&t.paused&&v.startLoad(r)}),s.on("quality."+a,function(e,t,r){D.smoothSwitching?v.nextLevel=r:v.currentLevel=r,L=r}),i.prepend(i.find(".fp-player",l)[0],d)),s.video=t,S=0,Object.keys(D).forEach(function(t){e.DefaultConfig.hasOwnProperty(t)||delete O[t];var r=D[t];switch(t){case"adaptOnStartOnly":r&&(O.startLevel=-1);break;case"autoLevelCapping":r===!1&&(r=-1),O[t]=r;break;case"startLevel":switch(r){case"auto":r=-1;break;case"firstLevel":r=void 0}O[t]=r;break;case"recover":D.recoverMediaError=!1,D.recoverNetworkError=!1,g=r;break;case"strict":r&&(D.recoverMediaError=!1,D.recoverNetworkError=!1,g=0)}}),v=new e(O),s.engine[a]=v,m=null,y=null,Object.keys(h).forEach(function(t){var r=h[t],a=D.listeners,o=a&&a.indexOf(r)>-1;v.on(r,function(r,a){var d,f={},h=s.conf.errors,p=e.ErrorTypes,m=e.ErrorDetails,y=s.video,b=y.src;switch(t){case"MANIFEST_PARSED":c(u)&&!s.pluginQualitySelectorEnabled&&_(w,a);break;case"MANIFEST_LOADED":!a.audioTracks||!a.audioTracks.length||v.audioTracks&&v.audioTracks.length||(h.push("Alternate audio tracks not supported by light plugin build."),f=T(h.length-1,s.video.src),s.trigger("error",[s,f]),h.slice(0,h.length-1));break;case"MEDIA_ATTACHED":v.loadSource(b);break;case"FRAG_LOADED":D.bufferWhilePaused&&!s.live&&v.autoLevelEnabled&&v.nextLoadLevel>S&&(S=v.nextLoadLevel);break;case"LEVEL_UPDATED":s.live&&(n(y,{seekOffset:a.details.fragments[0].start+v.config.nudgeOffset,duration:v.liveSyncPosition}),s.dvr&&s.playing&&s.trigger("dvrwindow",[s,{start:y.seekOffset,end:v.liveSyncPosition}]));break;case"BUFFER_APPENDED":i.removeClass(l,E);break;case"ERROR":if(a.fatal||D.strict){switch(a.type){case p.NETWORK_ERROR:D.recoverNetworkError||g?A(u,a.type,!0):a.frag&&a.frag.url?(f.url=a.frag.url,d=2):d=4;break;case p.MEDIA_ERROR:D.recoverMediaError||g?A(u,a.type):d=3;break;default:d=5}void 0!==d&&(f=T(d,b,a.url),s.trigger("error",[s,f]))}else a.details!==m.FRAG_LOOP_LOADING_ERROR&&a.details!==m.BUFFER_STALLED_ERROR||i.addClass(l,E)}o&&s.trigger(r,[s,a])})}),D.adaptOnStartOnly&&b.one(d,"timeupdate."+a,function(){v.loadLevel=v.loadLevel}),v.attachMedia(d),R&&d.paused){var k=d.play();void 0!==k&&k.catch(function(){o.mutedAutoplay||(s.unload(),s.message("Please click the play button",3e3))})}},resume:function(){d.play()},pause:function(){d.pause()},seek:function(e){d&&(d.currentTime=e)},volume:function(e){d&&(d.volume=e)},speed:function(e){d.playbackRate=e,s.trigger("speed",[s,e])},unload:function(){if(v){var e="."+a;v.destroy(),v=0,s.off(e),b.off(l,e),b.off(d,e),i.removeNode(d),d=0}}};return w};e.isSupported()&&(parseInt(l.split(".")[0])>6||/adhoc|dev/.test(l))&&(v.engineName=a,v[a+"ClientVersion"]=e.version,v.canPlay=function(e,t){return t[a]!==!1&&t.clip[a]!==!1&&(r=n({bufferWhilePaused:!0,smoothSwitching:!0,recoverMediaError:!0},t[a],t.clip[a]),h(e)&&(!(s.safari&&o.dataload)||r.safari))},!o.mutedAutoplay||"7.1.1"!==l&&"7.1.0"!==l||t(function(e,t){var r=e.conf;r.splash||r.autoplay||(e.splash=!0,r.splash="string"!=typeof r.poster||r.poster,r.poster=void 0,r.autoplay=!0,p(t))}),t.engines.unshift(v))};"object"==typeof e&&e.exports?e.exports=t.bind(void 0,r(2)):window.Hls&&window.flowplayer&&t(window.Hls,window.flowplayer)}()},function(e,t,r){!function(t,r){e.exports=r()}(this,function(){return function(e){function t(a){if(r[a])return r[a].exports;var i=r[a]={i:a,l:!1,exports:{}};return e[a].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var r={};return t.m=e,t.c=r,t.d=function(e,r,a){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=8)}([function(e,t,r){"use strict";function a(){}function i(e,t){return t="["+e+"] > "+t}function n(e){var t=self.console[e];return t?function(){for(var r=arguments.length,a=Array(r),n=0;n<r;n++)a[n]=arguments[n];a[0]&&(a[0]=i(e,a[0])),t.apply(self.console,a)}:a}function o(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];r.forEach(function(t){d[t]=e[t]?e[t].bind(e):n(t)})}r.d(t,"a",function(){return u}),r.d(t,"b",function(){return f});var s="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l={trace:a,debug:a,log:a,warn:a,info:a,error:a},d=l,u=function(e){if(e===!0||"object"===("undefined"==typeof e?"undefined":s(e))){o(e,"debug","log","info","warn","error");try{d.log()}catch(e){d=l}}else d=l},f=d},function(e,t,r){"use strict";t.a={MEDIA_ATTACHING:"hlsMediaAttaching",MEDIA_ATTACHED:"hlsMediaAttached",MEDIA_DETACHING:"hlsMediaDetaching",MEDIA_DETACHED:"hlsMediaDetached",BUFFER_RESET:"hlsBufferReset",BUFFER_CODECS:"hlsBufferCodecs",BUFFER_CREATED:"hlsBufferCreated",BUFFER_APPENDING:"hlsBufferAppending",BUFFER_APPENDED:"hlsBufferAppended",BUFFER_EOS:"hlsBufferEos",BUFFER_FLUSHING:"hlsBufferFlushing",BUFFER_FLUSHED:"hlsBufferFlushed",MANIFEST_LOADING:"hlsManifestLoading",MANIFEST_LOADED:"hlsManifestLoaded",MANIFEST_PARSED:"hlsManifestParsed",LEVEL_SWITCH:"hlsLevelSwitch",LEVEL_SWITCHING:"hlsLevelSwitching",LEVEL_SWITCHED:"hlsLevelSwitched",LEVEL_LOADING:"hlsLevelLoading",LEVEL_LOADED:"hlsLevelLoaded",LEVEL_UPDATED:"hlsLevelUpdated",LEVEL_PTS_UPDATED:"hlsLevelPtsUpdated",AUDIO_TRACKS_UPDATED:"hlsAudioTracksUpdated",AUDIO_TRACK_SWITCH:"hlsAudioTrackSwitch",AUDIO_TRACK_SWITCHING:"hlsAudioTrackSwitching",AUDIO_TRACK_SWITCHED:"hlsAudioTrackSwitched",AUDIO_TRACK_LOADING:"hlsAudioTrackLoading",AUDIO_TRACK_LOADED:"hlsAudioTrackLoaded",SUBTITLE_TRACKS_UPDATED:"hlsSubtitleTracksUpdated",SUBTITLE_TRACK_SWITCH:"hlsSubtitleTrackSwitch",SUBTITLE_TRACK_LOADING:"hlsSubtitleTrackLoading",SUBTITLE_TRACK_LOADED:"hlsSubtitleTrackLoaded",SUBTITLE_FRAG_PROCESSED:"hlsSubtitleFragProcessed",INIT_PTS_FOUND:"hlsInitPtsFound",FRAG_LOADING:"hlsFragLoading",FRAG_LOAD_PROGRESS:"hlsFragLoadProgress",FRAG_LOAD_EMERGENCY_ABORTED:"hlsFragLoadEmergencyAborted",FRAG_LOADED:"hlsFragLoaded",FRAG_DECRYPTED:"hlsFragDecrypted",FRAG_PARSING_INIT_SEGMENT:"hlsFragParsingInitSegment",FRAG_PARSING_USERDATA:"hlsFragParsingUserdata",FRAG_PARSING_METADATA:"hlsFragParsingMetadata",FRAG_PARSING_DATA:"hlsFragParsingData",FRAG_PARSED:"hlsFragParsed",FRAG_BUFFERED:"hlsFragBuffered",FRAG_CHANGED:"hlsFragChanged",FPS_DROP:"hlsFpsDrop",FPS_DROP_LEVEL_CAPPING:"hlsFpsDropLevelCapping",ERROR:"hlsError",DESTROYING:"hlsDestroying",KEY_LOADING:"hlsKeyLoading",KEY_LOADED:"hlsKeyLoaded",STREAM_STATE_TRANSITION:"hlsStreamStateTransition"}},function(e,t,r){"use strict";r.d(t,"b",function(){return a}),r.d(t,"a",function(){return i});var a={NETWORK_ERROR:"networkError",MEDIA_ERROR:"mediaError",MUX_ERROR:"muxError",OTHER_ERROR:"otherError"},i={MANIFEST_LOAD_ERROR:"manifestLoadError",MANIFEST_LOAD_TIMEOUT:"manifestLoadTimeOut",MANIFEST_PARSING_ERROR:"manifestParsingError",MANIFEST_INCOMPATIBLE_CODECS_ERROR:"manifestIncompatibleCodecsError",LEVEL_LOAD_ERROR:"levelLoadError",LEVEL_LOAD_TIMEOUT:"levelLoadTimeOut",LEVEL_SWITCH_ERROR:"levelSwitchError",AUDIO_TRACK_LOAD_ERROR:"audioTrackLoadError",AUDIO_TRACK_LOAD_TIMEOUT:"audioTrackLoadTimeOut",FRAG_LOAD_ERROR:"fragLoadError",FRAG_LOOP_LOADING_ERROR:"fragLoopLoadingError",FRAG_LOAD_TIMEOUT:"fragLoadTimeOut",FRAG_DECRYPT_ERROR:"fragDecryptError",FRAG_PARSING_ERROR:"fragParsingError",REMUX_ALLOC_ERROR:"remuxAllocError",KEY_LOAD_ERROR:"keyLoadError",KEY_LOAD_TIMEOUT:"keyLoadTimeOut",BUFFER_ADD_CODEC_ERROR:"bufferAddCodecError",BUFFER_APPEND_ERROR:"bufferAppendError",BUFFER_APPENDING_ERROR:"bufferAppendingError",BUFFER_STALLED_ERROR:"bufferStalledError",BUFFER_FULL_ERROR:"bufferFullError",BUFFER_SEEK_OVER_HOLE:"bufferSeekOverHole",BUFFER_NUDGE_ON_STALL:"bufferNudgeOnStall",INTERNAL_EXCEPTION:"internalException"}},function(e,t){},function(e,t,r){"use strict";function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var i=function(){function e(){a(this,e)}return e.isHeader=function(e,t){return t+10<=e.length&&73===e[t]&&68===e[t+1]&&51===e[t+2]&&e[t+3]<255&&e[t+4]<255&&e[t+6]<128&&e[t+7]<128&&e[t+8]<128&&e[t+9]<128},e.isFooter=function(e,t){return t+10<=e.length&&51===e[t]&&68===e[t+1]&&73===e[t+2]&&e[t+3]<255&&e[t+4]<255&&e[t+6]<128&&e[t+7]<128&&e[t+8]<128&&e[t+9]<128},e.getID3Data=function(t,r){for(var a=r,i=0;e.isHeader(t,r);){i+=10;var n=e._readSize(t,r+6);i+=n,e.isFooter(t,r+10)&&(i+=10),r+=i}if(i>0)return t.subarray(a,a+i)},e._readSize=function(e,t){var r=0;return r=(127&e[t])<<21,r|=(127&e[t+1])<<14,r|=(127&e[t+2])<<7,r|=127&e[t+3]},e.getTimeStamp=function(t){for(var r=e.getID3Frames(t),a=0;a<r.length;a++){var i=r[a];if(e.isTimeStampFrame(i))return e._readTimeStamp(i)}},e.isTimeStampFrame=function(e){return e&&"PRIV"===e.key&&"com.apple.streaming.transportStreamTimestamp"===e.info},e._getFrameData=function(t){var r=String.fromCharCode(t[0],t[1],t[2],t[3]),a=e._readSize(t,4),i=10;return{type:r,size:a,data:t.subarray(i,i+a)}},e.getID3Frames=function(t){for(var r=0,a=[];e.isHeader(t,r);){var i=e._readSize(t,r+6);r+=10;for(var n=r+i;r+8<n;){var o=e._getFrameData(t.subarray(r)),s=e._decodeFrame(o);s&&a.push(s),r+=o.size+10}e.isFooter(t,r)&&(r+=10)}return a},e._decodeFrame=function(t){return"PRIV"===t.type?e._decodePrivFrame(t):"T"===t.type[0]?e._decodeTextFrame(t):"W"===t.type[0]?e._decodeURLFrame(t):void 0},e._readTimeStamp=function(e){if(8===e.data.byteLength){var t=new Uint8Array(e.data),r=1&t[3],a=(t[4]<<23)+(t[5]<<15)+(t[6]<<7)+t[7];return a/=45,r&&(a+=47721858.84),Math.round(a)}},e._decodePrivFrame=function(t){if(!(t.size<2)){var r=e._utf8ArrayToStr(t.data),a=new Uint8Array(t.data.subarray(r.length+1));return{key:t.type,info:r,data:a.buffer}}},e._decodeTextFrame=function(t){if(!(t.size<2)){if("TXXX"===t.type){var r=1,a=e._utf8ArrayToStr(t.data.subarray(r));r+=a.length+1;var i=e._utf8ArrayToStr(t.data.subarray(r));return{key:t.type,info:a,data:i}}var n=e._utf8ArrayToStr(t.data.subarray(1));return{key:t.type,data:n}}},e._decodeURLFrame=function(t){if("WXXX"===t.type){if(t.size<2)return;var r=1,a=e._utf8ArrayToStr(t.data.subarray(r));r+=a.length+1;var i=e._utf8ArrayToStr(t.data.subarray(r));return{key:t.type,info:a,data:i}}var n=e._utf8ArrayToStr(t.data);return{key:t.type,data:n}},e._utf8ArrayToStr=function(e){for(var t=void 0,r=void 0,a="",i=0,n=e.length;i<n;){var o=e[i++];switch(o>>4){case 0:return a;case 1:case 2:case 3:case 4:case 5:case 6:case 7:a+=String.fromCharCode(o);break;case 12:case 13:t=e[i++],a+=String.fromCharCode((31&o)<<6|63&t);break;case 14:t=e[i++],r=e[i++],a+=String.fromCharCode((15&o)<<12|(63&t)<<6|(63&r)<<0)}}return a},e}();t.a=i},function(e,t){function r(){this._events=this._events||{},this._maxListeners=this._maxListeners||void 0}function a(e){return"function"==typeof e}function i(e){return"number"==typeof e}function n(e){return"object"==typeof e&&null!==e}function o(e){return void 0===e}e.exports=r,r.EventEmitter=r,r.prototype._events=void 0,r.prototype._maxListeners=void 0,r.defaultMaxListeners=10,r.prototype.setMaxListeners=function(e){if(!i(e)||e<0||isNaN(e))throw TypeError("n must be a positive number");return this._maxListeners=e,this},r.prototype.emit=function(e){var t,r,i,s,l,d;if(this._events||(this._events={}),"error"===e&&(!this._events.error||n(this._events.error)&&!this._events.error.length)){if(t=arguments[1],t instanceof Error)throw t;var u=new Error('Uncaught, unspecified "error" event. ('+t+")");throw u.context=t,u}if(r=this._events[e],o(r))return!1;if(a(r))switch(arguments.length){case 1:r.call(this);break;case 2:r.call(this,arguments[1]);break;case 3:r.call(this,arguments[1],arguments[2]);break;default:s=Array.prototype.slice.call(arguments,1),r.apply(this,s)}else if(n(r))for(s=Array.prototype.slice.call(arguments,1),d=r.slice(),i=d.length,l=0;l<i;l++)d[l].apply(this,s);return!0},r.prototype.addListener=function(e,t){var i;if(!a(t))throw TypeError("listener must be a function");return this._events||(this._events={}),this._events.newListener&&this.emit("newListener",e,a(t.listener)?t.listener:t),this._events[e]?n(this._events[e])?this._events[e].push(t):this._events[e]=[this._events[e],t]:this._events[e]=t,n(this._events[e])&&!this._events[e].warned&&(i=o(this._maxListeners)?r.defaultMaxListeners:this._maxListeners,i&&i>0&&this._events[e].length>i&&(this._events[e].warned=!0,console.error("(node) warning: possible EventEmitter memory leak detected. %d listeners added. Use emitter.setMaxListeners() to increase limit.",this._events[e].length),"function"==typeof console.trace&&console.trace())),this},r.prototype.on=r.prototype.addListener,r.prototype.once=function(e,t){function r(){this.removeListener(e,r),i||(i=!0,t.apply(this,arguments))}if(!a(t))throw TypeError("listener must be a function");var i=!1;return r.listener=t,this.on(e,r),this},r.prototype.removeListener=function(e,t){var r,i,o,s;if(!a(t))throw TypeError("listener must be a function");if(!this._events||!this._events[e])return this;if(r=this._events[e],o=r.length,i=-1,r===t||a(r.listener)&&r.listener===t)delete this._events[e],this._events.removeListener&&this.emit("removeListener",e,t);else if(n(r)){for(s=o;s-- >0;)if(r[s]===t||r[s].listener&&r[s].listener===t){i=s;break}if(i<0)return this;1===r.length?(r.length=0,delete this._events[e]):r.splice(i,1),this._events.removeListener&&this.emit("removeListener",e,t)}return this},r.prototype.removeAllListeners=function(e){var t,r;if(!this._events)return this;if(!this._events.removeListener)return 0===arguments.length?this._events={}:this._events[e]&&delete this._events[e],this;if(0===arguments.length){for(t in this._events)"removeListener"!==t&&this.removeAllListeners(t);return this.removeAllListeners("removeListener"),this._events={},this}if(r=this._events[e],a(r))this.removeListener(e,r);else if(r)for(;r.length;)this.removeListener(e,r[r.length-1]);return delete this._events[e],this},r.prototype.listeners=function(e){var t;return t=this._events&&this._events[e]?a(this._events[e])?[this._events[e]]:this._events[e].slice():[]},r.prototype.listenerCount=function(e){if(this._events){var t=this._events[e];if(a(t))return 1;if(t)return t.length}return 0},r.listenerCount=function(e,t){return e.listenerCount(t)}},function(e,t,r){!function(t){var r=/^((?:[^\/;?#]+:)?)(\/\/[^\/\;?#]*)?(.*?)??(;.*?)?(\?.*?)?(#.*?)?$/,a=/^([^\/;?#]*)(.*)$/,i=/(?:\/|^)\.(?=\/)/g,n=/(?:\/|^)\.\.\/(?!\.\.\/).*?(?=\/)/g,o={buildAbsoluteURL:function(e,t,r){if(r=r||{},e=e.trim(),t=t.trim(),!t){if(!r.alwaysNormalize)return e;var i=this.parseURL(e);if(!s)throw new Error("Error trying to parse base URL.");return i.path=o.normalizePath(i.path),o.buildURLFromParts(i)}var n=this.parseURL(t);if(!n)throw new Error("Error trying to parse relative URL.");if(n.scheme)return r.alwaysNormalize?(n.path=o.normalizePath(n.path),o.buildURLFromParts(n)):t;var s=this.parseURL(e);if(!s)throw new Error("Error trying to parse base URL.");if(!s.netLoc&&s.path&&"/"!==s.path[0]){var l=a.exec(s.path);s.netLoc=l[1],s.path=l[2]}s.netLoc&&!s.path&&(s.path="/");var d={scheme:s.scheme,netLoc:n.netLoc,path:null,params:n.params,query:n.query,fragment:n.fragment};if(!n.netLoc&&(d.netLoc=s.netLoc,"/"!==n.path[0]))if(n.path){var u=s.path,f=u.substring(0,u.lastIndexOf("/")+1)+n.path;d.path=o.normalizePath(f)}else d.path=s.path,n.params||(d.params=s.params,n.query||(d.query=s.query));return null===d.path&&(d.path=r.alwaysNormalize?o.normalizePath(n.path):n.path),o.buildURLFromParts(d)},parseURL:function(e){var t=r.exec(e);return t?{scheme:t[1]||"",netLoc:t[2]||"",path:t[3]||"",params:t[4]||"",query:t[5]||"",fragment:t[6]||""}:null},normalizePath:function(e){for(e=e.split("").reverse().join("").replace(i,"");e.length!==(e=e.replace(n,"")).length;);return e.split("").reverse().join("")},buildURLFromParts:function(e){return e.scheme+e.netLoc+e.path+e.params+e.query+e.fragment}};e.exports=o}(this)},function(e,t,r){"use strict";function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function o(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function s(e,t,r,a){var i,n,o,s,l,d=navigator.userAgent.toLowerCase(),u=a,f=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];return i=((192&t[r+2])>>>6)+1,n=(60&t[r+2])>>>2,n>f.length-1?void e.trigger(Event.ERROR,{type:O.b.MEDIA_ERROR,details:O.a.FRAG_PARSING_ERROR,fatal:!0,reason:"invalid ADTS sampling index:"+n}):(s=(1&t[r+2])<<2,s|=(192&t[r+3])>>>6,M.b.log("manifest codec:"+a+",ADTS data:type:"+i+",sampleingIndex:"+n+"["+f[n]+"Hz],channelConfig:"+s),/firefox/i.test(d)?n>=6?(i=5,l=new Array(4),o=n-3):(i=2,l=new Array(2),o=n):d.indexOf("android")!==-1?(i=2,l=new Array(2),o=n):(i=5,l=new Array(4),a&&(a.indexOf("mp4a.40.29")!==-1||a.indexOf("mp4a.40.5")!==-1)||!a&&n>=6?o=n-3:((a&&a.indexOf("mp4a.40.2")!==-1&&(n>=6&&1===s||/vivaldi/i.test(d))||!a&&1===s)&&(i=2,l=new Array(2)),o=n)),l[0]=i<<3,l[0]|=(14&n)>>1,l[1]|=(1&n)<<7,l[1]|=s<<3,5===i&&(l[1]|=(14&o)>>1,l[2]=(1&o)<<7,l[2]|=8,l[3]=0),{config:l,samplerate:f[n],channelCount:s,codec:"mp4a.40."+i,manifestCodec:u})}function l(e,t){return 255===e[t]&&240===(246&e[t+1])}function d(e,t){return 1&e[t+1]?7:9}function u(e,t){return(3&e[t+3])<<11|e[t+4]<<3|(224&e[t+5])>>>5}function f(e,t){return!!(t+1<e.length&&l(e,t))}function h(e,t){if(t+1<e.length&&l(e,t)){var r=d(e,t),a=r;t+5<e.length&&(a=u(e,t));var i=t+a;if(i===e.length||i+1<e.length&&l(e,i))return!0}return!1}function c(e,t,r,a,i){if(!e.samplerate){var n=s(t,r,a,i);e.config=n.config,e.samplerate=n.samplerate,e.channelCount=n.channelCount,e.codec=n.codec,e.manifestCodec=n.manifestCodec,M.b.log("parsed codec:"+e.codec+",rate:"+n.samplerate+",nb channel:"+n.channelCount)}}function p(e){return 9216e4/e}function v(e,t,r,a,i){var n,o,s,l=e.length;if(n=d(e,t),o=u(e,t),o-=n,o>0&&t+n+o<=l)return s=r+a*i,{headerLength:n,frameLength:o,stamp:s}}function g(e,t,r,a,i){var n=p(e.samplerate),o=v(t,r,a,i,n);if(o){var s=o.stamp,l=o.headerLength,d=o.frameLength,u={unit:t.subarray(r+l,r+l+d),pts:s,dts:s};return e.samples.push(u),e.len+=d,{sample:u,length:d+l}}}function m(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function y(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function b(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function E(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function R(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function A(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function T(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function S(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function L(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function w(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var D=r(1),O=r(2),k=function(){function e(t,r){a(this,e),this.subtle=t,this.aesIV=r}return e.prototype.decrypt=function(e,t){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},t,e)},e}(),x=k,I=function(){function e(t,r){i(this,e),this.subtle=t,this.key=r}return e.prototype.expandKey=function(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])},e}(),P=I,C=function(){function e(){n(this,e),this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.initTable()}return e.prototype.uint8ArrayToUint32Array_=function(e){for(var t=new DataView(e),r=new Uint32Array(4),a=0;a<4;a++)r[a]=t.getUint32(4*a);return r},e.prototype.initTable=function(){var e=this.sBox,t=this.invSBox,r=this.subMix,a=r[0],i=r[1],n=r[2],o=r[3],s=this.invSubMix,l=s[0],d=s[1],u=s[2],f=s[3],h=new Uint32Array(256),c=0,p=0,v=0;for(v=0;v<256;v++)v<128?h[v]=v<<1:h[v]=v<<1^283;for(v=0;v<256;v++){var g=p^p<<1^p<<2^p<<3^p<<4;g=g>>>8^255&g^99,e[c]=g,t[g]=c;var m=h[c],y=h[m],b=h[y],E=257*h[g]^16843008*g;a[c]=E<<24|E>>>8,i[c]=E<<16|E>>>16,n[c]=E<<8|E>>>24,o[c]=E,E=16843009*b^65537*y^257*m^16843008*c,l[g]=E<<24|E>>>8,d[g]=E<<16|E>>>16,u[g]=E<<8|E>>>24,f[g]=E,c?(c=m^h[h[h[b^m]]],p^=h[h[p]]):c=p=1}},e.prototype.expandKey=function(e){for(var t=this.uint8ArrayToUint32Array_(e),r=!0,a=0;a<t.length&&r;)r=t[a]===this.key[a],a++;if(!r){this.key=t;var i=this.keySize=t.length;if(4!==i&&6!==i&&8!==i)throw new Error("Invalid aes key size="+i);var n=this.ksRows=4*(i+6+1),o=void 0,s=void 0,l=this.keySchedule=new Uint32Array(n),d=this.invKeySchedule=new Uint32Array(n),u=this.sBox,f=this.rcon,h=this.invSubMix,c=h[0],p=h[1],v=h[2],g=h[3],m=void 0,y=void 0;for(o=0;o<n;o++)o<i?m=l[o]=t[o]:(y=m,o%i===0?(y=y<<8|y>>>24,y=u[y>>>24]<<24|u[y>>>16&255]<<16|u[y>>>8&255]<<8|u[255&y],y^=f[o/i|0]<<24):i>6&&o%i===4&&(y=u[y>>>24]<<24|u[y>>>16&255]<<16|u[y>>>8&255]<<8|u[255&y]),l[o]=m=(l[o-i]^y)>>>0);for(s=0;s<n;s++)o=n-s,y=3&s?l[o]:l[o-4],s<4||o<=4?d[s]=y:d[s]=c[u[y>>>24]]^p[u[y>>>16&255]]^v[u[y>>>8&255]]^g[u[255&y]],d[s]=d[s]>>>0}},e.prototype.networkToHostOrderSwap=function(e){return e<<24|(65280&e)<<8|(16711680&e)>>8|e>>>24},e.prototype.decrypt=function(e,t,r){for(var a,i,n=this.keySize+6,o=this.invKeySchedule,s=this.invSBox,l=this.invSubMix,d=l[0],u=l[1],f=l[2],h=l[3],c=this.uint8ArrayToUint32Array_(r),p=c[0],v=c[1],g=c[2],m=c[3],y=new Int32Array(e),b=new Int32Array(y.length),E=void 0,R=void 0,A=void 0,T=void 0,S=void 0,L=void 0,_=void 0,w=void 0,D=void 0,O=void 0,k=void 0,x=void 0,I=this.networkToHostOrderSwap;t<y.length;){for(D=I(y[t]),O=I(y[t+1]),k=I(y[t+2]),x=I(y[t+3]),S=D^o[0],L=x^o[1],_=k^o[2],w=O^o[3],a=4,i=1;i<n;i++)E=d[S>>>24]^u[L>>16&255]^f[_>>8&255]^h[255&w]^o[a],R=d[L>>>24]^u[_>>16&255]^f[w>>8&255]^h[255&S]^o[a+1],A=d[_>>>24]^u[w>>16&255]^f[S>>8&255]^h[255&L]^o[a+2],T=d[w>>>24]^u[S>>16&255]^f[L>>8&255]^h[255&_]^o[a+3],S=E,L=R,_=A,w=T,a+=4;E=s[S>>>24]<<24^s[L>>16&255]<<16^s[_>>8&255]<<8^s[255&w]^o[a],R=s[L>>>24]<<24^s[_>>16&255]<<16^s[w>>8&255]<<8^s[255&S]^o[a+1],A=s[_>>>24]<<24^s[w>>16&255]<<16^s[S>>8&255]<<8^s[255&L]^o[a+2],T=s[w>>>24]<<24^s[S>>16&255]<<16^s[L>>8&255]<<8^s[255&_]^o[a+3],a+=3,b[t]=I(E^p),b[t+1]=I(T^v),b[t+2]=I(A^g),b[t+3]=I(R^m),p=D,v=O,g=k,m=x,t+=4}return b.buffer},e.prototype.destroy=function(){this.key=void 0,this.keySize=void 0,this.ksRows=void 0,this.sBox=void 0,this.invSBox=void 0,this.subMix=void 0,this.invSubMix=void 0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.rcon=void 0},e}(),F=C,M=r(0),N=function(){function e(t,r){o(this,e),this.observer=t,this.config=r,this.logEnabled=!0;try{var a=crypto?crypto:self.crypto;this.subtle=a.subtle||a.webkitSubtle}catch(e){}this.disableWebCrypto=!this.subtle}return e.prototype.isSync=function(){return this.disableWebCrypto&&this.config.enableSoftwareAES},e.prototype.decrypt=function(e,t,r,a){var i=this;if(this.disableWebCrypto&&this.config.enableSoftwareAES){this.logEnabled&&(M.b.log("JS AES decrypt"),this.logEnabled=!1);var n=this.decryptor;n||(this.decryptor=n=new F),n.expandKey(t),a(n.decrypt(e,0,r))}else{this.logEnabled&&(M.b.log("WebCrypto AES decrypt"),this.logEnabled=!1);var o=this.subtle;this.key!==t&&(this.key=t,this.fastAesKey=new P(o,t)),this.fastAesKey.expandKey().then(function(n){var s=new x(o,r);s.decrypt(e,n).catch(function(n){i.onWebCryptoError(n,e,t,r,a)}).then(function(e){a(e)})}).catch(function(n){i.onWebCryptoError(n,e,t,r,a)})}},e.prototype.onWebCryptoError=function(e,t,r,a,i){this.config.enableSoftwareAES?(M.b.log("WebCrypto Error, disable WebCrypto API"),this.disableWebCrypto=!0,this.logEnabled=!0,this.decrypt(t,r,a,i)):(M.b.error("decrypting error : "+e.message),this.observer.trigger(Event.ERROR,{type:O.b.MEDIA_ERROR,details:O.a.FRAG_DECRYPT_ERROR,fatal:!0,reason:e.message}))},e.prototype.destroy=function(){var e=this.decryptor;e&&(e.destroy(),this.decryptor=void 0)},e}(),U=N,B=r(4),G=function(){function e(t,r,a){m(this,e),this.observer=t,this.config=a,this.remuxer=r}return e.prototype.resetInitSegment=function(e,t,r,a){this._audioTrack={container:"audio/adts",type:"audio",id:0,sequenceNumber:0,isAAC:!0,samples:[],len:0,manifestCodec:t,duration:a,inputTimeScale:9e4}},e.prototype.resetTimeStamp=function(){},e.probe=function(e){if(!e)return!1;for(var t=B.a.getID3Data(e,0)||[],r=t.length,a=e.length;r<a;r++)if(h(e,r))return M.b.log("ADTS sync word found !"),!0;return!1},e.prototype.append=function(e,t,r,a){for(var i=this._audioTrack,n=B.a.getID3Data(e,0)||[],o=B.a.getTimeStamp(n),s=o?90*o:9e4*t,l=0,d=s,u=e.length,h=n.length,p=[{pts:d,dts:d,data:n}];h<u-1;)if(f(e,h)&&h+5<u){c(i,this.observer,e,h,i.manifestCodec);var v=g(i,e,h,s,l);if(!v){M.b.log("Unable to parse AAC frame");break}h+=v.length,d=v.sample.pts,l++}else B.a.isHeader(e,h)?(n=B.a.getID3Data(e,h),p.push({pts:d,dts:d,data:n}),h+=n.length):h++;this.remuxer.remux(i,{samples:[]},{samples:p,inputTimeScale:9e4},{samples:[]},t,r,a)},e.prototype.destroy=function(){},e}(),H=G,j=Math.pow(2,32)-1,W=function(){function e(t,r){y(this,e),this.observer=t,this.remuxer=r}return e.prototype.resetTimeStamp=function(e){this.initPTS=e},e.prototype.resetInitSegment=function(t,r,a,i){if(t&&t.byteLength){var n=this.initData=e.parseInitSegment(t),o={};n.audio&&n.video?o.audiovideo={container:"video/mp4",codec:r+","+a,initSegment:i?t:null}:(n.audio&&(o.audio={container:"audio/mp4",codec:r,initSegment:i?t:null}),n.video&&(o.video={container:"video/mp4",codec:a,initSegment:i?t:null})),this.observer.trigger(D.a.FRAG_PARSING_INIT_SEGMENT,{tracks:o})}else r&&(this.audioCodec=r),a&&(this.videoCodec=a)},e.probe=function(t){if(t.length>=8){var r=e.bin2str(t.subarray(4,8));return["moof","ftyp","styp"].indexOf(r)>=0}return!1},e.bin2str=function(e){return String.fromCharCode.apply(null,e)},e.readUint32=function(e,t){e.data&&(t+=e.start,e=e.data);var r=e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3];return r<0?4294967296+r:r},e.writeUint32=function(e,t,r){e.data&&(t+=e.start,e=e.data),e[t]=r>>24,e[t+1]=r>>16&255,e[t+2]=r>>8&255,e[t+3]=255&r},e.findBox=function(t,r){var a,i,n,o,s,l,d,u=[];if(t.data?(l=t.start,o=t.end,t=t.data):(l=0,o=t.byteLength),!r.length)return null;for(a=l;a<o;)i=e.readUint32(t,a),n=e.bin2str(t.subarray(a+4,a+8)),d=i>1?a+i:o,n===r[0]&&(1===r.length?u.push({data:t,start:a+8,end:d}):(s=e.findBox({data:t,start:a+8,end:d},r.slice(1)),s.length&&(u=u.concat(s)))),a=d;return u},e.parseInitSegment=function(t){var r=[],a=e.findBox(t,["moov","trak"]);return a.forEach(function(t){var a=e.findBox(t,["tkhd"])[0];if(a){var i=a.data[a.start],n=0===i?12:20,o=e.readUint32(a,n),s=e.findBox(t,["mdia","mdhd"])[0];if(s){i=s.data[s.start],n=0===i?12:20;var l=e.readUint32(s,n),d=e.findBox(t,["mdia","hdlr"])[0];if(d){var u=e.bin2str(d.data.subarray(d.start+8,d.start+12)),f={soun:"audio",vide:"video"}[u];f&&(r[o]={timescale:l,type:f},r[f]={timescale:l,id:o})}}}}),r},e.getStartDTS=function(t,r){var a,i,n;return a=e.findBox(r,["moof","traf"]),
i=[].concat.apply([],a.map(function(r){return e.findBox(r,["tfhd"]).map(function(a){var i,n,o;return i=e.readUint32(a,4),n=t[i].timescale||9e4,o=e.findBox(r,["tfdt"]).map(function(t){var r,a;return r=t.data[t.start],a=e.readUint32(t,4),1===r&&(a*=Math.pow(2,32),a+=e.readUint32(t,8)),a})[0],o/n})})),n=Math.min.apply(null,i),isFinite(n)?n:0},e.offsetStartDTS=function(t,r,a){e.findBox(r,["moof","traf"]).map(function(r){return e.findBox(r,["tfhd"]).map(function(i){var n=e.readUint32(i,4),o=t[n].timescale||9e4;e.findBox(r,["tfdt"]).map(function(t){var r=t.data[t.start],i=e.readUint32(t,4);if(0===r)e.writeUint32(t,4,i-a*o);else{i*=Math.pow(2,32),i+=e.readUint32(t,8),i-=a*o,i=Math.max(i,0);var n=Math.floor(i/(j+1)),s=Math.floor(i%(j+1));e.writeUint32(t,4,n),e.writeUint32(t,8,s)}})})})},e.prototype.append=function(t,r,a,i){var n=this.initData;n||(this.resetInitSegment(t,this.audioCodec,this.videoCodec),n=this.initData);var o=void 0,s=this.initPTS;if(void 0===s){var l=e.getStartDTS(n,t);this.initPTS=s=l-r,this.observer.trigger(D.a.INIT_PTS_FOUND,{initPTS:s})}e.offsetStartDTS(n,t,s),o=e.getStartDTS(n,t),this.remuxer.remux(n.audio,n.video,null,null,o,a,i,t)},e.prototype.destroy=function(){},e}(),V=W,K={BitratesMap:[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],SamplingRateMap:[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],appendFrame:function(e,t,r,a,i){if(!(r+24>t.length)){var n=this.parseHeader(t,r);if(n&&r+n.frameLength<=t.length){var o=10368e4/n.sampleRate,s=a+i*o,l={unit:t.subarray(r,r+n.frameLength),pts:s,dts:s};return e.config=[],e.channelCount=n.channelCount,e.samplerate=n.sampleRate,e.samples.push(l),e.len+=n.frameLength,{sample:l,length:n.frameLength}}}},parseHeader:function(e,t){var r=e[t+1]>>3&3,a=e[t+1]>>1&3,i=e[t+2]>>4&15,n=e[t+2]>>2&3,o=!!(2&e[t+2]);if(1!==r&&0!==i&&15!==i&&3!==n){var s=3===r?3-a:3===a?3:4,l=1e3*K.BitratesMap[14*s+i-1],d=3===r?0:2===r?1:2,u=K.SamplingRateMap[3*d+n],f=o?1:0,h=e[t+3]>>6===3?1:2,c=3===a?(3===r?12:6)*l/u+f<<2:(3===r?144:72)*l/u+f|0;return{sampleRate:u,channelCount:h,frameLength:c}}},isHeaderPattern:function(e,t){return 255===e[t]&&224===(224&e[t+1])&&0!==(6&e[t+1])},isHeader:function(e,t){return!!(t+1<e.length&&this.isHeaderPattern(e,t))},probe:function(e,t){if(t+1<e.length&&this.isHeaderPattern(e,t)){var r=4,a=this.parseHeader(e,t),i=r;a&&a.frameLength&&(i=a.frameLength);var n=t+i;if(n===e.length||n+1<e.length&&this.isHeaderPattern(e,n))return!0}return!1}},Y=K,z=function(){function e(t){b(this,e),this.data=t,this.bytesAvailable=t.byteLength,this.word=0,this.bitsAvailable=0}return e.prototype.loadWord=function(){var e=this.data,t=this.bytesAvailable,r=e.byteLength-t,a=new Uint8Array(4),i=Math.min(4,t);if(0===i)throw new Error("no bytes available");a.set(e.subarray(r,r+i)),this.word=new DataView(a.buffer).getUint32(0),this.bitsAvailable=8*i,this.bytesAvailable-=i},e.prototype.skipBits=function(e){var t;this.bitsAvailable>e?(this.word<<=e,this.bitsAvailable-=e):(e-=this.bitsAvailable,t=e>>3,e-=t>>3,this.bytesAvailable-=t,this.loadWord(),this.word<<=e,this.bitsAvailable-=e)},e.prototype.readBits=function(e){var t=Math.min(this.bitsAvailable,e),r=this.word>>>32-t;return e>32&&M.b.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=t,this.bitsAvailable>0?this.word<<=t:this.bytesAvailable>0&&this.loadWord(),t=e-t,t>0&&this.bitsAvailable?r<<t|this.readBits(t):r},e.prototype.skipLZ=function(){var e;for(e=0;e<this.bitsAvailable;++e)if(0!==(this.word&2147483648>>>e))return this.word<<=e,this.bitsAvailable-=e,e;return this.loadWord(),e+this.skipLZ()},e.prototype.skipUEG=function(){this.skipBits(1+this.skipLZ())},e.prototype.skipEG=function(){this.skipBits(1+this.skipLZ())},e.prototype.readUEG=function(){var e=this.skipLZ();return this.readBits(e+1)-1},e.prototype.readEG=function(){var e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)},e.prototype.readBoolean=function(){return 1===this.readBits(1)},e.prototype.readUByte=function(){return this.readBits(8)},e.prototype.readUShort=function(){return this.readBits(16)},e.prototype.readUInt=function(){return this.readBits(32)},e.prototype.skipScalingList=function(e){var t,r,a=8,i=8;for(t=0;t<e;t++)0!==i&&(r=this.readEG(),i=(a+r+256)%256),a=0===i?a:i},e.prototype.readSPS=function(){var e,t,r,a,i,n,o,s,l,d=0,u=0,f=0,h=0,c=this.readUByte.bind(this),p=this.readBits.bind(this),v=this.readUEG.bind(this),g=this.readBoolean.bind(this),m=this.skipBits.bind(this),y=this.skipEG.bind(this),b=this.skipUEG.bind(this),E=this.skipScalingList.bind(this);if(c(),e=c(),t=p(5),m(3),r=c(),b(),100===e||110===e||122===e||244===e||44===e||83===e||86===e||118===e||128===e){var R=v();if(3===R&&m(1),b(),b(),m(1),g())for(s=3!==R?8:12,l=0;l<s;l++)g()&&E(l<6?16:64)}b();var A=v();if(0===A)v();else if(1===A)for(m(1),y(),y(),a=v(),l=0;l<a;l++)y();b(),m(1),i=v(),n=v(),o=p(1),0===o&&m(1),m(1),g()&&(d=v(),u=v(),f=v(),h=v());var T=[1,1];if(g()&&g()){var S=c();switch(S){case 1:T=[1,1];break;case 2:T=[12,11];break;case 3:T=[10,11];break;case 4:T=[16,11];break;case 5:T=[40,33];break;case 6:T=[24,11];break;case 7:T=[20,11];break;case 8:T=[32,11];break;case 9:T=[80,33];break;case 10:T=[18,11];break;case 11:T=[15,11];break;case 12:T=[64,33];break;case 13:T=[160,99];break;case 14:T=[4,3];break;case 15:T=[3,2];break;case 16:T=[2,1];break;case 255:T=[c()<<8|c(),c()<<8|c()]}}return{width:Math.ceil(16*(i+1)-2*d-2*u),height:(2-o)*(n+1)*16-(o?2:4)*(f+h),pixelRatio:T}},e.prototype.readSliceType=function(){return this.readUByte(),this.readUEG(),this.readUEG()},e}(),X=z,q=function(){function e(t,r,a,i){E(this,e),this.decryptdata=a,this.discardEPB=i,this.decrypter=new U(t,r)}return e.prototype.decryptBuffer=function(e,t){this.decrypter.decrypt(e,this.decryptdata.key.buffer,this.decryptdata.iv.buffer,t)},e.prototype.decryptAacSample=function(e,t,r,a){var i=e[t].unit,n=i.subarray(16,i.length-i.length%16),o=n.buffer.slice(n.byteOffset,n.byteOffset+n.length),s=this;this.decryptBuffer(o,function(n){n=new Uint8Array(n),i.set(n,16),a||s.decryptAacSamples(e,t+1,r)})},e.prototype.decryptAacSamples=function(e,t,r){for(;;t++){if(t>=e.length)return void r();if(!(e[t].unit.length<32)){var a=this.decrypter.isSync();if(this.decryptAacSample(e,t,r,a),!a)return}}},e.prototype.getAvcEncryptedData=function(e){for(var t=16*Math.floor((e.length-48)/160)+16,r=new Int8Array(t),a=0,i=32;i<=e.length-16;i+=160,a+=16)r.set(e.subarray(i,i+16),a);return r},e.prototype.getAvcDecryptedUnit=function(e,t){t=new Uint8Array(t);for(var r=0,a=32;a<=e.length-16;a+=160,r+=16)e.set(t.subarray(r,r+16),a);return e},e.prototype.decryptAvcSample=function(e,t,r,a,i,n){var o=this.discardEPB(i.data),s=this.getAvcEncryptedData(o),l=this;this.decryptBuffer(s.buffer,function(s){i.data=l.getAvcDecryptedUnit(o,s),n||l.decryptAvcSamples(e,t,r+1,a)})},e.prototype.decryptAvcSamples=function(e,t,r,a){for(;;t++,r=0){if(t>=e.length)return void a();for(var i=e[t].units;!(r>=i.length);r++){var n=i[r];if(!(n.length<=48||1!==n.type&&5!==n.type)){var o=this.decrypter.isSync();if(this.decryptAvcSample(e,t,r,a,n,o),!o)return}}}},e}(),Q=q,J=function(){function e(t,r,a,i){R(this,e),this.observer=t,this.config=a,this.typeSupported=i,this.remuxer=r,this.sampleAes=null}return e.prototype.setDecryptData=function(e){null!=e&&null!=e.key&&"SAMPLE-AES"===e.method?this.sampleAes=new Q(this.observer,this.config,e,this.discardEPB):this.sampleAes=null},e.probe=function(e){return e.length>=564&&71===e[0]&&71===e[188]&&71===e[376]},e.prototype.resetInitSegment=function(e,t,r,a){this.pmtParsed=!1,this._pmtId=-1,this._avcTrack={container:"video/mp2t",type:"video",id:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],len:0,dropped:0},this._audioTrack={container:"video/mp2t",type:"audio",id:-1,inputTimeScale:9e4,duration:a,sequenceNumber:0,samples:[],len:0,isAAC:!0},this._id3Track={type:"id3",id:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],len:0},this._txtTrack={type:"text",id:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],len:0},this.aacOverFlow=null,this.aacLastPTS=null,this.avcSample=null,this.audioCodec=t,this.videoCodec=r,this._duration=a},e.prototype.resetTimeStamp=function(){},e.prototype.append=function(e,t,r,a){var i,n,o,s,l,d,u=e.length,f=!1;this.contiguous=r;var h=this.pmtParsed,c=this._avcTrack,p=this._audioTrack,v=this._id3Track,g=c.id,m=p.id,y=v.id,b=this._pmtId,E=c.pesData,R=p.pesData,A=v.pesData,T=this._parsePAT,S=this._parsePMT,L=this._parsePES,_=this._parseAVCPES.bind(this),w=this._parseAACPES.bind(this),k=this._parseMPEGPES.bind(this),x=this._parseID3PES.bind(this);for(u-=u%188,i=0;i<u;i+=188)if(71===e[i]){if(n=!!(64&e[i+1]),o=((31&e[i+1])<<8)+e[i+2],s=(48&e[i+3])>>4,s>1){if(l=i+5+e[i+4],l===i+188)continue}else l=i+4;switch(o){case g:n&&(E&&(d=L(E))&&_(d,!1),E={data:[],size:0}),E&&(E.data.push(e.subarray(l,i+188)),E.size+=i+188-l);break;case m:n&&(R&&(d=L(R))&&(p.isAAC?w(d):k(d)),R={data:[],size:0}),R&&(R.data.push(e.subarray(l,i+188)),R.size+=i+188-l);break;case y:n&&(A&&(d=L(A))&&x(d),A={data:[],size:0}),A&&(A.data.push(e.subarray(l,i+188)),A.size+=i+188-l);break;case 0:n&&(l+=e[l]+1),b=this._pmtId=T(e,l);break;case b:n&&(l+=e[l]+1);var I=S(e,l,this.typeSupported.mpeg===!0||this.typeSupported.mp3===!0,null!=this.sampleAes);g=I.avc,g>0&&(c.id=g),m=I.audio,m>0&&(p.id=m,p.isAAC=I.isAAC),y=I.id3,y>0&&(v.id=y),f&&!h&&(M.b.log("reparse from beginning"),f=!1,i=-188),h=this.pmtParsed=!0;break;case 17:case 8191:break;default:f=!0}}else this.observer.trigger(D.a.ERROR,{type:O.b.MEDIA_ERROR,details:O.a.FRAG_PARSING_ERROR,fatal:!1,reason:"TS packet did not start with 0x47"});E&&(d=L(E))?(_(d,!0),c.pesData=null):c.pesData=E,R&&(d=L(R))?(p.isAAC?w(d):k(d),p.pesData=null):(R&&R.size&&M.b.log("last AAC PES packet truncated,might overlap between fragments"),p.pesData=R),A&&(d=L(A))?(x(d),v.pesData=null):v.pesData=A,null==this.sampleAes?this.remuxer.remux(p,c,v,this._txtTrack,t,r,a):this.decryptAndRemux(p,c,v,this._txtTrack,t,r,a)},e.prototype.decryptAndRemux=function(e,t,r,a,i,n,o){if(e.samples&&e.isAAC){var s=this;this.sampleAes.decryptAacSamples(e.samples,0,function(){s.decryptAndRemuxAvc(e,t,r,a,i,n,o)})}else this.decryptAndRemuxAvc(e,t,r,a,i,n,o)},e.prototype.decryptAndRemuxAvc=function(e,t,r,a,i,n,o){if(t.samples){var s=this;this.sampleAes.decryptAvcSamples(t.samples,0,0,function(){s.remuxer.remux(e,t,r,a,i,n,o)})}else this.remuxer.remux(e,t,r,a,i,n,o)},e.prototype.destroy=function(){this._initPTS=this._initDTS=void 0,this._duration=0},e.prototype._parsePAT=function(e,t){return(31&e[t+10])<<8|e[t+11]},e.prototype._parsePMT=function(e,t,r,a){var i,n,o,s,l={audio:-1,avc:-1,id3:-1,isAAC:!0};for(i=(15&e[t+1])<<8|e[t+2],n=t+3+i-4,o=(15&e[t+10])<<8|e[t+11],t+=12+o;t<n;){switch(s=(31&e[t+1])<<8|e[t+2],e[t]){case 207:if(!a){M.b.log("unkown stream type:"+e[t]);break}case 15:l.audio===-1&&(l.audio=s);break;case 21:l.id3===-1&&(l.id3=s);break;case 219:if(!a){M.b.log("unkown stream type:"+e[t]);break}case 27:l.avc===-1&&(l.avc=s);break;case 3:case 4:r?l.audio===-1&&(l.audio=s,l.isAAC=!1):M.b.log("MPEG audio found, not supported in this browser for now");break;case 36:M.b.warn("HEVC stream type found, not supported for now");break;default:M.b.log("unkown stream type:"+e[t])}t+=((15&e[t+3])<<8|e[t+4])+5}return l},e.prototype._parsePES=function(e){var t,r,a,i,n,o,s,l,d,u=0,f=e.data;if(!e||0===e.size)return null;for(;f[0].length<19&&f.length>1;){var h=new Uint8Array(f[0].length+f[1].length);h.set(f[0]),h.set(f[1],f[0].length),f[0]=h,f.splice(1,1)}if(t=f[0],a=(t[0]<<16)+(t[1]<<8)+t[2],1===a){if(i=(t[4]<<8)+t[5],i&&i>e.size-6)return null;r=t[7],192&r&&(s=536870912*(14&t[9])+4194304*(255&t[10])+16384*(254&t[11])+128*(255&t[12])+(254&t[13])/2,s>4294967295&&(s-=8589934592),64&r?(l=536870912*(14&t[14])+4194304*(255&t[15])+16384*(254&t[16])+128*(255&t[17])+(254&t[18])/2,l>4294967295&&(l-=8589934592),s-l>54e5&&(M.b.warn(Math.round((s-l)/9e4)+"s delta between PTS and DTS, align them"),s=l)):l=s),n=t[8],d=n+9,e.size-=d,o=new Uint8Array(e.size);for(var c=0,p=f.length;c<p;c++){t=f[c];var v=t.byteLength;if(d){if(d>v){d-=v;continue}t=t.subarray(d),v-=d,d=0}o.set(t,u),u+=v}return i&&(i-=n+3),{data:o,pts:s,dts:l,len:i}}return null},e.prototype.pushAccesUnit=function(e,t){if(e.units.length&&e.frame){var r=t.samples,a=r.length;!this.config.forceKeyFrameOnDiscontinuity||e.key===!0||t.sps&&(a||this.contiguous)?(e.id=a,r.push(e)):t.dropped++}e.debug.length&&M.b.log(e.pts+"/"+e.dts+":"+e.debug)},e.prototype._parseAVCPES=function(e,t){var r,a,i,n=this,o=this._avcTrack,s=this._parseAVCNALu(e.data),l=!1,d=this.avcSample,u=!1,f=this.pushAccesUnit.bind(this),h=function(e,t,r,a){return{key:e,pts:t,dts:r,units:[],debug:a}};e.data=null,d&&s.length&&!o.audFound&&(f(d,o),d=this.avcSample=h(!1,e.pts,e.dts,"")),s.forEach(function(t){switch(t.type){case 1:a=!0,d||(d=n.avcSample=h(!0,e.pts,e.dts,"")),l&&(d.debug+="NDR "),d.frame=!0;var s=t.data;if(u&&s.length>4){var c=new X(s).readSliceType();2!==c&&4!==c&&7!==c&&9!==c||(d.key=!0)}break;case 5:a=!0,d||(d=n.avcSample=h(!0,e.pts,e.dts,"")),l&&(d.debug+="IDR "),d.key=!0,d.frame=!0;break;case 6:a=!0,l&&d&&(d.debug+="SEI "),r=new X(n.discardEPB(t.data)),r.readUByte();for(var p=0,v=0,g=!1,m=0;!g&&r.bytesAvailable>1;){p=0;do m=r.readUByte(),p+=m;while(255===m);v=0;do m=r.readUByte(),v+=m;while(255===m);if(4===p&&0!==r.bytesAvailable){g=!0;var y=r.readUByte();if(181===y){var b=r.readUShort();if(49===b){var E=r.readUInt();if(1195456820===E){var R=r.readUByte();if(3===R){var A=r.readUByte(),T=r.readUByte(),S=31&A,L=[A,T];for(i=0;i<S;i++)L.push(r.readUByte()),L.push(r.readUByte()),L.push(r.readUByte());n._insertSampleInOrder(n._txtTrack.samples,{type:3,pts:e.pts,bytes:L})}}}}}else if(v<r.bytesAvailable)for(i=0;i<v;i++)r.readUByte()}break;case 7:if(a=!0,u=!0,l&&d&&(d.debug+="SPS "),!o.sps){r=new X(t.data);var _=r.readSPS();o.width=_.width,o.height=_.height,o.pixelRatio=_.pixelRatio,o.sps=[t.data],o.duration=n._duration;var w=t.data.subarray(1,4),D="avc1.";for(i=0;i<3;i++){var O=w[i].toString(16);O.length<2&&(O="0"+O),D+=O}o.codec=D}break;case 8:a=!0,l&&d&&(d.debug+="PPS "),o.pps||(o.pps=[t.data]);break;case 9:a=!1,o.audFound=!0,d&&f(d,o),d=n.avcSample=h(!1,e.pts,e.dts,l?"AUD ":"");break;case 12:a=!1;break;default:a=!1,d&&(d.debug+="unknown NAL "+t.type+" ")}if(d&&a){var k=d.units;k.push(t)}}),t&&d&&(f(d,o),this.avcSample=null)},e.prototype._insertSampleInOrder=function(e,t){var r=e.length;if(r>0){if(t.pts>=e[r-1].pts)e.push(t);else for(var a=r-1;a>=0;a--)if(t.pts<e[a].pts){e.splice(a,0,t);break}}else e.push(t)},e.prototype._getLastNalUnit=function(){var e=this.avcSample,t=void 0;if(!e||0===e.units.length){var r=this._avcTrack,a=r.samples;e=a[a.length-1]}if(e){var i=e.units;t=i[i.length-1]}return t},e.prototype._parseAVCNALu=function(e){var t,r,a,i,n,o=0,s=e.byteLength,l=this._avcTrack,d=l.naluState||0,u=d,f=[],h=-1;for(d===-1&&(h=0,n=31&e[0],d=0,o=1);o<s;)if(t=e[o++],d)if(1!==d)if(t)if(1===t){if(h>=0)a={data:e.subarray(h,o-d-1),type:n},f.push(a);else{var c=this._getLastNalUnit();if(c&&(u&&o<=4-u&&c.state&&(c.data=c.data.subarray(0,c.data.byteLength-u)),r=o-d-1,r>0)){var p=new Uint8Array(c.data.byteLength+r);p.set(c.data,0),p.set(e.subarray(0,r),c.data.byteLength),c.data=p}}o<s?(i=31&e[o],h=o,n=i,d=0):d=-1}else d=0;else d=3;else d=t?0:2;else d=t?0:1;if(h>=0&&d>=0&&(a={data:e.subarray(h,s),type:n,state:d},f.push(a)),0===f.length){var v=this._getLastNalUnit();if(v){var g=new Uint8Array(v.data.byteLength+e.byteLength);g.set(v.data,0),g.set(e,v.data.byteLength),v.data=g}}return l.naluState=d,f},e.prototype.discardEPB=function(e){for(var t,r,a=e.byteLength,i=[],n=1;n<a-2;)0===e[n]&&0===e[n+1]&&3===e[n+2]?(i.push(n+2),n+=2):n++;if(0===i.length)return e;t=a-i.length,r=new Uint8Array(t);var o=0;for(n=0;n<t;o++,n++)o===i[0]&&(o++,i.shift()),r[n]=e[o];return r},e.prototype._parseAACPES=function(e){var t,r,a,i,n,o=this._audioTrack,s=e.data,l=e.pts,d=0,u=this.aacOverFlow,h=this.aacLastPTS;if(u){var v=new Uint8Array(u.byteLength+s.byteLength);v.set(u,0),v.set(s,u.byteLength),s=v}for(a=d,n=s.length;a<n-1&&!f(s,a);a++);if(a){var m,y;if(a<n-1?(m="AAC PES did not start with ADTS header,offset:"+a,y=!1):(m="no ADTS header found in AAC PES",y=!0),M.b.warn("parsing error:"+m),this.observer.trigger(D.a.ERROR,{type:O.b.MEDIA_ERROR,details:O.a.FRAG_PARSING_ERROR,fatal:y,reason:m}),y)return}if(c(o,this.observer,s,a,this.audioCodec),r=0,t=p(o.samplerate),u&&h){var b=h+t;Math.abs(b-l)>1&&(M.b.log("AAC: align PTS for overlapping frames by "+Math.round((b-l)/90)),l=b)}for(;a<n;)if(f(s,a)&&a+5<n){var E=g(o,s,a,l,r);if(!E)break;a+=E.length,i=E.sample.pts,r++}else a++;u=a<n?s.subarray(a,n):null,this.aacOverFlow=u,this.aacLastPTS=i},e.prototype._parseMPEGPES=function(e){for(var t=e.data,r=t.length,a=0,i=0,n=e.pts;i<r;)if(Y.isHeader(t,i)){var o=Y.appendFrame(this._audioTrack,t,i,n,a);if(!o)break;i+=o.length,a++}else i++},e.prototype._parseID3PES=function(e){this._id3Track.samples.push(e)},e}(),Z=J,$=function(){function e(t,r,a){A(this,e),this.observer=t,this.config=a,this.remuxer=r}return e.prototype.resetInitSegment=function(e,t,r,a){this._audioTrack={container:"audio/mpeg",type:"audio",id:-1,sequenceNumber:0,isAAC:!1,samples:[],len:0,manifestCodec:t,duration:a,inputTimeScale:9e4}},e.prototype.resetTimeStamp=function(){},e.probe=function(e){var t,r,a=B.a.getID3Data(e,0);if(a&&void 0!==B.a.getTimeStamp(a))for(t=a.length,r=Math.min(e.length-1,t+100);t<r;t++)if(Y.probe(e,t))return M.b.log("MPEG Audio sync word found !"),!0;return!1},e.prototype.append=function(e,t,r,a){for(var i=B.a.getID3Data(e,0),n=90*B.a.getTimeStamp(i),o=i.length,s=e.length,l=0,d=0,u=this._audioTrack,f=[{pts:n,dts:n,data:i}];o<s;)if(Y.isHeader(e,o)){var h=Y.appendFrame(u,e,o,n,l);if(!h)break;o+=h.length,d=h.sample.pts,l++}else B.a.isHeader(e,o)?(i=B.a.getID3Data(e,o),f.push({pts:d,dts:d,data:i}),o+=i.length):o++;this.remuxer.remux(u,{samples:[]},{samples:f,inputTimeScale:9e4},{samples:[]},t,r,a)},e.prototype.destroy=function(){},e}(),ee=$,te=function(){function e(){T(this,e)}return e.getSilentFrame=function(e,t){switch(e){case"mp4a.40.2":if(1===t)return new Uint8Array([0,200,0,128,35,128]);if(2===t)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(3===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(4===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(5===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(6===t)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]);break;default:if(1===t)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(2===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(3===t)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94])}return null},e}(),re=te,ae=Math.pow(2,32)-1,ie=function(){function e(){S(this,e)}return e.init=function(){e.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]};var t;for(t in e.types)e.types.hasOwnProperty(t)&&(e.types[t]=[t.charCodeAt(0),t.charCodeAt(1),t.charCodeAt(2),t.charCodeAt(3)]);var r=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),a=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);e.HDLR_TYPES={video:r,audio:a};var i=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n=new Uint8Array([0,0,0,0,0,0,0,0]);e.STTS=e.STSC=e.STCO=n,e.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),e.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),e.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),e.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);var o=new Uint8Array([105,115,111,109]),s=new Uint8Array([97,118,99,49]),l=new Uint8Array([0,0,0,1]);e.FTYP=e.box(e.types.ftyp,o,l,o,s),e.DINF=e.box(e.types.dinf,e.box(e.types.dref,i))},e.box=function(e){for(var t,r=Array.prototype.slice.call(arguments,1),a=8,i=r.length,n=i;i--;)a+=r[i].byteLength;for(t=new Uint8Array(a),t[0]=a>>24&255,t[1]=a>>16&255,t[2]=a>>8&255,t[3]=255&a,t.set(e,4),i=0,a=8;i<n;i++)t.set(r[i],a),a+=r[i].byteLength;return t},e.hdlr=function(t){return e.box(e.types.hdlr,e.HDLR_TYPES[t])},e.mdat=function(t){return e.box(e.types.mdat,t)},e.mdhd=function(t,r){r*=t;var a=Math.floor(r/(ae+1)),i=Math.floor(r%(ae+1));return e.box(e.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,255&t,a>>24,a>>16&255,a>>8&255,255&a,i>>24,i>>16&255,i>>8&255,255&i,85,196,0,0]))},e.mdia=function(t){return e.box(e.types.mdia,e.mdhd(t.timescale,t.duration),e.hdlr(t.type),e.minf(t))},e.mfhd=function(t){return e.box(e.types.mfhd,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,255&t]))},e.minf=function(t){return"audio"===t.type?e.box(e.types.minf,e.box(e.types.smhd,e.SMHD),e.DINF,e.stbl(t)):e.box(e.types.minf,e.box(e.types.vmhd,e.VMHD),e.DINF,e.stbl(t))},e.moof=function(t,r,a){return e.box(e.types.moof,e.mfhd(t),e.traf(a,r))},e.moov=function(t){for(var r=t.length,a=[];r--;)a[r]=e.trak(t[r]);return e.box.apply(null,[e.types.moov,e.mvhd(t[0].timescale,t[0].duration)].concat(a).concat(e.mvex(t)))},e.mvex=function(t){for(var r=t.length,a=[];r--;)a[r]=e.trex(t[r]);return e.box.apply(null,[e.types.mvex].concat(a))},e.mvhd=function(t,r){r*=t;var a=Math.floor(r/(ae+1)),i=Math.floor(r%(ae+1)),n=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,255&t,a>>24,a>>16&255,a>>8&255,255&a,i>>24,i>>16&255,i>>8&255,255&i,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return e.box(e.types.mvhd,n)},e.sdtp=function(t){var r,a,i=t.samples||[],n=new Uint8Array(4+i.length);for(a=0;a<i.length;a++)r=i[a].flags,n[a+4]=r.dependsOn<<4|r.isDependedOn<<2|r.hasRedundancy;return e.box(e.types.sdtp,n)},e.stbl=function(t){return e.box(e.types.stbl,e.stsd(t),e.box(e.types.stts,e.STTS),e.box(e.types.stsc,e.STSC),e.box(e.types.stsz,e.STSZ),e.box(e.types.stco,e.STCO))},e.avc1=function(t){var r,a,i,n=[],o=[];for(r=0;r<t.sps.length;r++)a=t.sps[r],i=a.byteLength,n.push(i>>>8&255),n.push(255&i),n=n.concat(Array.prototype.slice.call(a));for(r=0;r<t.pps.length;r++)a=t.pps[r],i=a.byteLength,o.push(i>>>8&255),o.push(255&i),o=o.concat(Array.prototype.slice.call(a));var s=e.box(e.types.avcC,new Uint8Array([1,n[3],n[4],n[5],255,224|t.sps.length].concat(n).concat([t.pps.length]).concat(o))),l=t.width,d=t.height,u=t.pixelRatio[0],f=t.pixelRatio[1];return e.box(e.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,l>>8&255,255&l,d>>8&255,255&d,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),s,e.box(e.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),e.box(e.types.pasp,new Uint8Array([u>>24,u>>16&255,u>>8&255,255&u,f>>24,f>>16&255,f>>8&255,255&f])))},e.esds=function(e){var t=e.config.length;return new Uint8Array([0,0,0,0,3,23+t,0,1,0,4,15+t,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([t]).concat(e.config).concat([6,1,2]))},e.mp4a=function(t){var r=t.samplerate;return e.box(e.types.mp4a,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t.channelCount,0,16,0,0,0,0,r>>8&255,255&r,0,0]),e.box(e.types.esds,e.esds(t)))},e.mp3=function(t){var r=t.samplerate;return e.box(e.types[".mp3"],new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,t.channelCount,0,16,0,0,0,0,r>>8&255,255&r,0,0]))},e.stsd=function(t){return"audio"===t.type?t.isAAC||"mp3"!==t.codec?e.box(e.types.stsd,e.STSD,e.mp4a(t)):e.box(e.types.stsd,e.STSD,e.mp3(t)):e.box(e.types.stsd,e.STSD,e.avc1(t))},e.tkhd=function(t){var r=t.id,a=t.duration*t.timescale,i=t.width,n=t.height,o=Math.floor(a/(ae+1)),s=Math.floor(a%(ae+1));return e.box(e.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,r>>24&255,r>>16&255,r>>8&255,255&r,0,0,0,0,o>>24,o>>16&255,o>>8&255,255&o,s>>24,s>>16&255,s>>8&255,255&s,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,i>>8&255,255&i,0,0,n>>8&255,255&n,0,0]))},e.traf=function(t,r){var a=e.sdtp(t),i=t.id,n=Math.floor(r/(ae+1)),o=Math.floor(r%(ae+1));return e.box(e.types.traf,e.box(e.types.tfhd,new Uint8Array([0,0,0,0,i>>24,i>>16&255,i>>8&255,255&i])),e.box(e.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,255&n,o>>24,o>>16&255,o>>8&255,255&o])),e.trun(t,a.length+16+20+8+16+8+8),a)},e.trak=function(t){return t.duration=t.duration||4294967295,e.box(e.types.trak,e.tkhd(t),e.mdia(t))},e.trex=function(t){var r=t.id;return e.box(e.types.trex,new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,255&r,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))},e.trun=function(t,r){var a,i,n,o,s,l,d=t.samples||[],u=d.length,f=12+16*u,h=new Uint8Array(f);for(r+=8+f,h.set([0,0,15,1,u>>>24&255,u>>>16&255,u>>>8&255,255&u,r>>>24&255,r>>>16&255,r>>>8&255,255&r],0),a=0;a<u;a++)i=d[a],n=i.duration,o=i.size,s=i.flags,l=i.cts,h.set([n>>>24&255,n>>>16&255,n>>>8&255,255&n,o>>>24&255,o>>>16&255,o>>>8&255,255&o,s.isLeading<<2|s.dependsOn,s.isDependedOn<<6|s.hasRedundancy<<4|s.paddingValue<<1|s.isNonSync,61440&s.degradPrio,15&s.degradPrio,l>>>24&255,l>>>16&255,l>>>8&255,255&l],12+16*a);return e.box(e.types.trun,h)},e.initSegment=function(t){e.types||e.init();var r,a=e.moov(t);return r=new Uint8Array(e.FTYP.byteLength+a.byteLength),r.set(e.FTYP),r.set(a,e.FTYP.byteLength),r},e}(),ne=ie,oe=1e4,se=function(){function e(t,r,a,i){L(this,e),this.observer=t,this.config=r,this.typeSupported=a;var n=navigator.userAgent;this.isSafari=i&&i.indexOf("Apple")>-1&&n&&!n.match("CriOS"),this.ISGenerated=!1}return e.prototype.destroy=function(){},e.prototype.resetTimeStamp=function(e){this._initPTS=this._initDTS=e},e.prototype.resetInitSegment=function(){this.ISGenerated=!1},e.prototype.remux=function(e,t,r,a,i,n,o){if(this.ISGenerated||this.generateIS(e,t,i),this.ISGenerated){var s=e.samples.length,l=t.samples.length,d=i,u=i;if(s&&l){var f=(e.samples[0].dts-t.samples[0].dts)/t.inputTimeScale;d+=Math.max(0,f),u+=Math.max(0,-f)}if(s){e.timescale||(M.b.warn("regenerate InitSegment as audio detected"),this.generateIS(e,t,i));var h=this.remuxAudio(e,d,n,o);if(l){var c=void 0;h&&(c=h.endPTS-h.startPTS),t.timescale||(M.b.warn("regenerate InitSegment as video detected"),this.generateIS(e,t,i)),this.remuxVideo(t,u,n,c,o)}}else{var p=void 0;l&&(p=this.remuxVideo(t,u,n,o)),p&&e.codec&&this.remuxEmptyAudio(e,d,n,p)}}r.samples.length&&this.remuxID3(r,i),a.samples.length&&this.remuxText(a,i),this.observer.trigger(D.a.FRAG_PARSED)},e.prototype.generateIS=function(e,t,r){var a,i,n=this.observer,o=e.samples,s=t.samples,l=this.typeSupported,d="audio/mp4",u={},f={tracks:u},h=void 0===this._initPTS;if(h&&(a=i=1/0),e.config&&o.length&&(e.timescale=e.samplerate,M.b.log("audio sampling rate : "+e.samplerate),e.isAAC||(l.mpeg?(d="audio/mpeg",e.codec=""):l.mp3&&(e.codec="mp3")),u.audio={container:d,codec:e.codec,initSegment:!e.isAAC&&l.mpeg?new Uint8Array:ne.initSegment([e]),metadata:{channelCount:e.channelCount}},h&&(a=i=o[0].pts-e.inputTimeScale*r)),t.sps&&t.pps&&s.length){var c=t.inputTimeScale;t.timescale=c,u.video={container:"video/mp4",codec:t.codec,initSegment:ne.initSegment([t]),metadata:{width:t.width,height:t.height}},h&&(a=Math.min(a,s[0].pts-c*r),i=Math.min(i,s[0].dts-c*r),this.observer.trigger(D.a.INIT_PTS_FOUND,{initPTS:a}))}Object.keys(u).length?(n.trigger(D.a.FRAG_PARSING_INIT_SEGMENT,f),this.ISGenerated=!0,h&&(this._initPTS=a,this._initDTS=i)):n.trigger(D.a.ERROR,{type:O.b.MEDIA_ERROR,details:O.a.FRAG_PARSING_ERROR,fatal:!1,reason:"no audio/video samples found"})},e.prototype.remuxVideo=function(e,t,r,a,i){var n,o,s,l,d,u,f,h,c=8,p=e.timescale,v=e.samples,g=[],m=v.length,y=this._PTSNormalize,b=this._initDTS,E=this.nextAvcDts,R=this.isSafari;R&&(r|=v.length&&E&&(i&&Math.abs(t-E/p)<.1||Math.abs(v[0].pts-E-b)<p/5)),r||(E=t*p),v.forEach(function(e){e.pts=y(e.pts-b,E),e.dts=y(e.dts-b,E)}),v.sort(function(e,t){var r=e.dts-t.dts,a=e.pts-t.pts;return r?r:a?a:e.id-t.id});var A=v.reduce(function(e,t){return Math.max(Math.min(e,t.pts-t.dts),-18e3)},0);if(A<0){M.b.warn("PTS < DTS detected in video samples, shifting DTS by "+Math.round(A/90)+" ms to overcome this issue");for(var T=0;T<v.length;T++)v[T].dts+=A}var S=v[0];d=Math.max(S.dts,0),l=Math.max(S.pts,0);var L=Math.round((d-E)/90);r&&L&&(L>1?M.b.log("AVC:"+L+" ms hole between fragments detected,filling it"):L<-1&&M.b.log("AVC:"+-L+" ms overlapping between fragments detected"),d=E,v[0].dts=d,l=Math.max(l-L,E),v[0].pts=l,M.b.log("Video/PTS/DTS adjusted: "+Math.round(l/90)+"/"+Math.round(d/90)+",delta:"+L+" ms")),u=d,S=v[v.length-1],h=Math.max(S.dts,0),f=Math.max(S.pts,0,h),R&&(n=Math.round((h-d)/(v.length-1)));for(var _=0,w=0,k=0;k<m;k++){for(var x=v[k],I=x.units,P=I.length,C=0,F=0;F<P;F++)C+=I[F].data.length;w+=C,_+=P,x.length=C,R?x.dts=d+k*n:x.dts=Math.max(x.dts,d),x.pts=Math.max(x.pts,x.dts)}var N=w+4*_+8;try{o=new Uint8Array(N)}catch(e){return void this.observer.trigger(D.a.ERROR,{type:O.b.MUX_ERROR,details:O.a.REMUX_ALLOC_ERROR,fatal:!1,bytes:N,reason:"fail allocating video mdat "+N})}var U=new DataView(o.buffer);U.setUint32(0,N),o.set(ne.types.mdat,4);for(var B=0;B<m;B++){for(var G=v[B],H=G.units,j=0,W=void 0,V=0,K=H.length;V<K;V++){var Y=H[V],z=Y.data,X=Y.data.byteLength;U.setUint32(c,X),c+=4,o.set(z,c),c+=X,j+=4+X}if(R)W=Math.max(0,n*Math.round((G.pts-G.dts)/n));else{if(B<m-1)n=v[B+1].dts-G.dts;else{var q=this.config,Q=G.dts-v[B>0?B-1:B].dts;if(q.stretchShortVideoTrack){var J=q.maxBufferHole,Z=q.maxSeekHole,$=Math.floor(Math.min(J,Z)*p),ee=(a?l+a*p:this.nextAudioPts)-G.pts;ee>$?(n=ee-Q,n<0&&(n=Q),M.b.log("It is approximately "+ee/90+" ms to the next segment; using duration "+n/90+" ms for the last video frame.")):n=Q}else n=Q}W=Math.round(G.pts-G.dts)}g.push({size:j,duration:n,cts:W,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:G.key?2:1,isNonSync:G.key?0:1}})}this.nextAvcDts=h+n;var te=e.dropped;if(e.len=0,e.nbNalu=0,e.dropped=0,g.length&&navigator.userAgent.toLowerCase().indexOf("chrome")>-1){var re=g[0].flags;re.dependsOn=2,re.isNonSync=0}e.samples=g,s=ne.moof(e.sequenceNumber++,d,e),e.samples=[];var ae={data1:s,data2:o,startPTS:l/p,endPTS:(f+n)/p,startDTS:d/p,endDTS:this.nextAvcDts/p,type:"video",nb:g.length,dropped:te};return this.observer.trigger(D.a.FRAG_PARSING_DATA,ae),ae},e.prototype.remuxAudio=function(e,t,r,a){var i,n,o,s,l,d,u,f=e.inputTimeScale,h=e.timescale,c=f/h,p=e.isAAC?1024:1152,v=p*c,g=this._PTSNormalize,m=this._initDTS,y=!e.isAAC&&this.typeSupported.mpeg,b=e.samples,E=[],R=this.nextAudioPts;if(r|=b.length&&R&&(a&&Math.abs(t-R/f)<.1||Math.abs(b[0].pts-R-m)<20*v),r||(R=t*f),b.forEach(function(e){e.pts=e.dts=g(e.pts-m,R)}),b.sort(function(e,t){return e.pts-t.pts}),a&&e.isAAC)for(var A=this.config.maxAudioFramesDrift,T=0,S=R;T<b.length;){var L,_=b[T],w=_.pts;L=w-S;var k=Math.abs(1e3*L/f);if(L<=-A*v)M.b.warn("Dropping 1 audio frame @ "+(S/f).toFixed(3)+"s due to "+Math.round(k)+" ms overlap."),b.splice(T,1),e.len-=_.unit.length;else if(L>=A*v&&k<oe&&S){var x=Math.round(L/v);M.b.warn("Injecting "+x+" audio frame @ "+(S/f).toFixed(3)+"s due to "+Math.round(1e3*L/f)+" ms gap.");for(var I=0;I<x;I++){var P=Math.max(S,0);o=re.getSilentFrame(e.manifestCodec||e.codec,e.channelCount),
o||(M.b.log("Unable to get silent frame for given audio codec; duplicating last frame instead."),o=_.unit.subarray()),b.splice(T,0,{unit:o,pts:P,dts:P}),e.len+=o.length,S+=v,T++}_.pts=_.dts=S,S+=v,T++}else Math.abs(L)>.1*v,_.pts=_.dts=S,S+=v,T++}for(var C=0,F=b.length;C<F;C++){var N=b[C],U=N.unit,B=N.pts;if(void 0!==u)n.duration=Math.round((B-u)/c);else{var G=Math.round(1e3*(B-R)/f),H=0;if(r&&e.isAAC&&G){if(G>0&&G<oe)H=Math.round((B-R)/v),M.b.log(G+" ms hole between AAC samples detected,filling it"),H>0&&(o=re.getSilentFrame(e.manifestCodec||e.codec,e.channelCount),o||(o=U.subarray()),e.len+=H*o.length);else if(G<-12){M.b.log("drop overlapping AAC sample, expected/parsed/delta:"+(R/f).toFixed(3)+"s/"+(B/f).toFixed(3)+"s/"+-G+"ms"),e.len-=U.byteLength;continue}B=R}if(d=Math.max(0,B),!(e.len>0))return;var j=y?e.len:e.len+8;i=y?0:8;try{s=new Uint8Array(j)}catch(e){return void this.observer.trigger(D.a.ERROR,{type:O.b.MUX_ERROR,details:O.a.REMUX_ALLOC_ERROR,fatal:!1,bytes:j,reason:"fail allocating audio mdat "+j})}if(!y){var W=new DataView(s.buffer);W.setUint32(0,j),s.set(ne.types.mdat,4)}for(var V=0;V<H;V++)o=re.getSilentFrame(e.manifestCodec||e.codec,e.channelCount),o||(M.b.log("Unable to get silent frame for given audio codec; duplicating this frame instead."),o=U.subarray()),s.set(o,i),i+=o.byteLength,n={size:o.byteLength,cts:0,duration:1024,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:1}},E.push(n)}s.set(U,i);var K=U.byteLength;i+=K,n={size:K,cts:0,duration:0,flags:{isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:1}},E.push(n),u=B}var Y=0,z=E.length;if(z>=2&&(Y=E[z-2].duration,n.duration=Y),z){this.nextAudioPts=R=u+c*Y,e.len=0,e.samples=E,l=y?new Uint8Array:ne.moof(e.sequenceNumber++,d/c,e),e.samples=[];var X=d/f,q=R/f,Q={data1:l,data2:s,startPTS:X,endPTS:q,startDTS:X,endDTS:q,type:"audio",nb:z};return this.observer.trigger(D.a.FRAG_PARSING_DATA,Q),Q}return null},e.prototype.remuxEmptyAudio=function(e,t,r,a){var i=e.inputTimeScale,n=e.samplerate?e.samplerate:i,o=i/n,s=this.nextAudioPts,l=(void 0!==s?s:a.startDTS*i)+this._initDTS,d=a.endDTS*i+this._initDTS,u=1024,f=o*u,h=Math.ceil((d-l)/f),c=re.getSilentFrame(e.manifestCodec||e.codec,e.channelCount);if(M.b.warn("remux empty Audio"),!c)return void M.b.trace("Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec!");for(var p=[],v=0;v<h;v++){var g=l+v*f;p.push({unit:c,pts:g,dts:g}),e.len+=c.length}e.samples=p,this.remuxAudio(e,t,r)},e.prototype.remuxID3=function(e,t){var r,a=e.samples.length,i=e.inputTimeScale,n=this._initPTS,o=this._initDTS;if(a){for(var s=0;s<a;s++)r=e.samples[s],r.pts=(r.pts-n)/i,r.dts=(r.dts-o)/i;this.observer.trigger(D.a.FRAG_PARSING_METADATA,{samples:e.samples})}e.samples=[],t=t},e.prototype.remuxText=function(e,t){e.samples.sort(function(e,t){return e.pts-t.pts});var r,a=e.samples.length,i=e.inputTimeScale,n=this._initPTS;if(a){for(var o=0;o<a;o++)r=e.samples[o],r.pts=(r.pts-n)/i;this.observer.trigger(D.a.FRAG_PARSING_USERDATA,{samples:e.samples})}e.samples=[],t=t},e.prototype._PTSNormalize=function(e,t){var r;if(void 0===t)return e;for(r=t<e?-8589934592:8589934592;Math.abs(e-t)>4294967296;)e+=r;return e},e}(),le=se,de=function(){function e(t){_(this,e),this.observer=t}return e.prototype.destroy=function(){},e.prototype.resetTimeStamp=function(){},e.prototype.resetInitSegment=function(){},e.prototype.remux=function(e,t,r,a,i,n,o,s){var l=this.observer,d="";e&&(d+="audio"),t&&(d+="video"),l.trigger(D.a.FRAG_PARSING_DATA,{data1:s,startPTS:i,startDTS:i,type:d,nb:1,dropped:0}),l.trigger(D.a.FRAG_PARSED)},e}(),ue=de,fe=function(){function e(t,r,a,i){w(this,e),this.observer=t,this.typeSupported=r,this.config=a,this.vendor=i}return e.prototype.destroy=function(){var e=this.demuxer;e&&e.destroy()},e.prototype.push=function(e,t,r,a,i,n,o,s,l,d,u,f){if(e.byteLength>0&&null!=t&&null!=t.key&&"AES-128"===t.method){var h=this.decrypter;null==h&&(h=this.decrypter=new U(this.observer,this.config));var c,p=this;try{c=performance.now()}catch(e){c=Date.now()}h.decrypt(e,t.key.buffer,t.iv.buffer,function(e){var h;try{h=performance.now()}catch(e){h=Date.now()}p.observer.trigger(D.a.FRAG_DECRYPTED,{stats:{tstart:c,tdecrypt:h}}),p.pushDecrypted(new Uint8Array(e),t,new Uint8Array(r),a,i,n,o,s,l,d,u,f)})}else this.pushDecrypted(new Uint8Array(e),t,new Uint8Array(r),a,i,n,o,s,l,d,u,f)},e.prototype.pushDecrypted=function(e,t,r,a,i,n,o,s,l,d,u,f){var h=this.demuxer;if(!h||o&&!this.probe(e)){for(var c=this.observer,p=this.typeSupported,v=this.config,g=[{demux:Z,remux:le},{demux:H,remux:le},{demux:ee,remux:le},{demux:V,remux:ue}],m=0,y=g.length;m<y;m++){var b=g[m],E=b.demux.probe;if(E(e)){var R=this.remuxer=new b.remux(c,v,p,this.vendor);h=new b.demux(c,R,v,p),this.probe=E;break}}if(!h)return void c.trigger(D.a.ERROR,{type:O.b.MEDIA_ERROR,details:O.a.FRAG_PARSING_ERROR,fatal:!0,reason:"no demux matching with content found"});this.demuxer=h}var A=this.remuxer;(o||s)&&(h.resetInitSegment(r,a,i,d),A.resetInitSegment()),o&&(h.resetTimeStamp(f),A.resetTimeStamp(f)),"function"==typeof h.setDecryptData&&h.setDecryptData(t),h.append(e,n,l,u)},e}();t.a=fe},function(e,t,r){"use strict";function a(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function i(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function n(e,t){var r=le[t];return!!r&&r[e.slice(0,4)]===!0}function o(e){return MediaSource.isTypeSupported('video/mp4;codecs="'+e+'"')}function s(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function l(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function d(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function f(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function h(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function c(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function p(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function v(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function g(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function m(e,t,r){var a=e[t],i=e[r],n=i.startPTS;isNaN(n)?r>t?i.start=a.start+a.duration:i.start=Math.max(a.start-i.duration,0):r>t?(a.duration=n-a.start,a.duration<0&&ee.b.warn("negative duration computed for frag "+a.sn+",level "+a.level+", there should be some duration drift between playlist and fragment!")):(i.duration=a.start-n,i.duration<0&&ee.b.warn("negative duration computed for frag "+i.sn+",level "+i.level+", there should be some duration drift between playlist and fragment!"))}function y(e,t,r,a,i,n){var o=r;if(!isNaN(t.startPTS)){var s=Math.abs(t.startPTS-r);isNaN(t.deltaPTS)?t.deltaPTS=s:t.deltaPTS=Math.max(s,t.deltaPTS),o=Math.max(r,t.startPTS),r=Math.min(r,t.startPTS),a=Math.max(a,t.endPTS),i=Math.min(i,t.startDTS),n=Math.max(n,t.endDTS)}var l=r-t.start;t.start=t.startPTS=r,t.maxStartPTS=o,t.endPTS=a,t.startDTS=i,t.endDTS=n,t.duration=a-r;var d=t.sn;if(!e||d<e.startSN||d>e.endSN)return 0;var u,f,h;for(u=d-e.startSN,f=e.fragments,f[u]=t,h=u;h>0;h--)m(f,h,h-1);for(h=u;h<f.length-1;h++)m(f,h,h+1);return e.PTSKnown=!0,l}function b(e,t){var r,a=Math.max(e.startSN,t.startSN)-t.startSN,i=Math.min(e.endSN,t.endSN)-t.startSN,n=t.startSN-e.startSN,o=e.fragments,s=t.fragments,l=0;if(i<a)return void(t.PTSKnown=!1);for(var d=a;d<=i;d++){var u=o[n+d],f=s[d];f&&u&&(l=u.cc-f.cc,isNaN(u.startPTS)||(f.start=f.startPTS=u.startPTS,f.endPTS=u.endPTS,f.duration=u.duration,f.backtracked=u.backtracked,f.dropped=u.dropped,r=f))}if(l)for(ee.b.log("discontinuity sliding from playlist, take drift into account"),d=0;d<s.length;d++)s[d].cc+=l;if(r)y(t,r,r.startPTS,r.endPTS,r.startDTS,r.endDTS);else if(n>=0&&n<o.length){var h=o[n].start;for(d=0;d<s.length;d++)s[d].start+=h}t.PTSKnown=e.PTSKnown}function E(e,t){for(var r=null,a=0;a<e.length;a+=1){var i=e[a];if(i&&i.cc===t){r=i;break}}return r}function R(e,t,r){var a=!1;return t&&t.details&&r&&(r.endCC>r.startCC||e&&e.cc<r.startCC)&&(a=!0),a}function A(e,t){var r=e.fragments,a=t.fragments;if(!a.length||!r.length)return void ee.b.log("No fragments to align");var i=E(r,a[0].cc);return!i||i&&!i.startPTS?void ee.b.log("No frag in previous level to align on"):i}function T(e,t){t.fragments.forEach(function(t){if(t){var r=t.start+e;t.start=t.startPTS=r,t.endPTS=r+t.duration}}),t.PTSKnown=!0}function S(e,t,r){if(R(e,t,r)){var a=A(t.details,r);a&&(ee.b.log("Adjusting PTS using last level due to CC increase within current level"),T(a.start,r))}if(r.PTSKnown===!1&&t&&t.details){var i=t.details.programDateTime,n=r.programDateTime,o=(n-i)/1e3+t.details.fragments[0].start;isNaN(o)||(ee.b.log("adjusting PTS using programDateTime delta, sliding:"+o.toFixed(3)),T(o,r))}}function L(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function w(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function D(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function O(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function k(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function x(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function I(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function P(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function C(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function F(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function M(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function N(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function U(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function B(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function G(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function H(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function j(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function W(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function V(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function K(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Y(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}function z(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}function X(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function q(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(t,"__esModule",{value:!0});var Q=r(6),J=r.n(Q),Z=r(1),$=r(2),ee=r(0),te="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},re=function(){function e(t){a(this,e),this.hls=t,this.onEvent=this.onEvent.bind(this);for(var r=arguments.length,i=Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];this.handledEvents=i,this.useGenericHandler=!0,this.registerListeners()}return e.prototype.destroy=function(){this.unregisterListeners()},e.prototype.isEventHandler=function(){return"object"===te(this.handledEvents)&&this.handledEvents.length&&"function"==typeof this.onEvent},e.prototype.registerListeners=function(){this.isEventHandler()&&this.handledEvents.forEach(function(e){if("hlsEventGeneric"===e)throw new Error("Forbidden event name: "+e);this.hls.on(e,this.onEvent)},this)},e.prototype.unregisterListeners=function(){this.isEventHandler()&&this.handledEvents.forEach(function(e){this.hls.off(e,this.onEvent)},this)},e.prototype.onEvent=function(e,t){this.onEventGeneric(e,t)},e.prototype.onEventGeneric=function(e,t){var r=function(e,t){var r="on"+e.replace("hls","");if("function"!=typeof this[r])throw new Error("Event "+e+" has no generic handler in this "+this.constructor.name+" class (tried "+r+")");return this[r].bind(this,t)};try{r.call(this,e,t).call()}catch(t){ee.b.error("internal error happened while processing "+e+":"+t.message),this.hls.trigger(Z.a.ERROR,{type:$.b.OTHER_ERROR,details:$.a.INTERNAL_EXCEPTION,fatal:!1,event:e,err:t})}},e}(),ae=re,ie=/^(\d+)x(\d+)$/,ne=/\s*(.+?)\s*=((?:\".*?\")|.*?)(?:,|$)/g,oe=function(){function e(t){i(this,e),"string"==typeof t&&(t=e.parseAttrList(t));for(var r in t)t.hasOwnProperty(r)&&(this[r]=t[r])}return e.prototype.decimalInteger=function(e){var t=parseInt(this[e],10);return t>Number.MAX_SAFE_INTEGER?1/0:t},e.prototype.hexadecimalInteger=function(e){if(this[e]){var t=(this[e]||"0x").slice(2);t=(1&t.length?"0":"")+t;for(var r=new Uint8Array(t.length/2),a=0;a<t.length/2;a++)r[a]=parseInt(t.slice(2*a,2*a+2),16);return r}return null},e.prototype.hexadecimalIntegerAsNumber=function(e){var t=parseInt(this[e],16);return t>Number.MAX_SAFE_INTEGER?1/0:t},e.prototype.decimalFloatingPoint=function(e){return parseFloat(this[e])},e.prototype.enumeratedString=function(e){return this[e]},e.prototype.decimalResolution=function(e){var t=ie.exec(this[e]);if(null!==t)return{width:parseInt(t[1],10),height:parseInt(t[2],10)}},e.parseAttrList=function(e){var t,r={};for(ne.lastIndex=0;null!==(t=ne.exec(e));){var a=t[2],i='"';0===a.indexOf(i)&&a.lastIndexOf(i)===a.length-1&&(a=a.slice(1,-1)),r[t[1]]=a}return r},e}(),se=oe,le={audio:{a3ds:!0,"ac-3":!0,"ac-4":!0,alac:!0,alaw:!0,dra1:!0,"dts+":!0,"dts-":!0,dtsc:!0,dtse:!0,dtsh:!0,"ec-3":!0,enca:!0,g719:!0,g726:!0,m4ae:!0,mha1:!0,mha2:!0,mhm1:!0,mhm2:!0,mlpa:!0,mp4a:!0,"raw ":!0,Opus:!0,samr:!0,sawb:!0,sawp:!0,sevc:!0,sqcp:!0,ssmv:!0,twos:!0,ulaw:!0},video:{avc1:!0,avc2:!0,avc3:!0,avc4:!0,avcp:!0,drac:!0,dvav:!0,dvhe:!0,encv:!0,hev1:!0,hvc1:!0,mjp2:!0,mp4v:!0,mvc1:!0,mvc2:!0,mvc3:!0,mvc4:!0,resv:!0,rv60:!0,s263:!0,svc1:!0,svc2:!0,"vc-1":!0,vp08:!0,vp09:!0}},de=function(){function e(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,r,a){return r&&e(t.prototype,r),a&&e(t,a),t}}(),ue=/#EXT-X-STREAM-INF:([^\n\r]*)[\r\n]+([^\r\n]+)/g,fe=/#EXT-X-MEDIA:(.*)/g,he=new RegExp([/#EXTINF:(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/|(?!#)(\S+)/.source,/|#EXT-X-BYTERANGE:*(.+)/.source,/|#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/|#.*/.source].join(""),"g"),ce=/(?:(?:#(EXTM3U))|(?:#EXT-X-(PLAYLIST-TYPE):(.+))|(?:#EXT-X-(MEDIA-SEQUENCE): *(\d+))|(?:#EXT-X-(TARGETDURATION): *(\d+))|(?:#EXT-X-(KEY):(.+))|(?:#EXT-X-(START):(.+))|(?:#EXT-X-(ENDLIST))|(?:#EXT-X-(DISCONTINUITY-SEQ)UENCE:(\d+))|(?:#EXT-X-(DIS)CONTINUITY))|(?:#EXT-X-(VERSION):(\d+))|(?:#EXT-X-(MAP):(.+))|(?:(#)(.*):(.*))|(?:(#)(.*))(?:.*)\r?\n?/,pe=function(){function e(){d(this,e),this.method=null,this.key=null,this.iv=null,this._uri=null}return de(e,[{key:"uri",get:function(){return!this._uri&&this.reluri&&(this._uri=J.a.buildAbsoluteURL(this.baseuri,this.reluri,{alwaysNormalize:!0})),this._uri}}]),e}(),ve=function(){function e(){d(this,e),this._url=null,this._byteRange=null,this._decryptdata=null,this.tagList=[]}return e.prototype.createInitializationVector=function(e){for(var t=new Uint8Array(16),r=12;r<16;r++)t[r]=e>>8*(15-r)&255;return t},e.prototype.fragmentDecryptdataFromLevelkey=function(e,t){var r=e;return e&&e.method&&e.uri&&!e.iv&&(r=new pe,r.method=e.method,r.baseuri=e.baseuri,r.reluri=e.reluri,r.iv=this.createInitializationVector(t)),r},e.prototype.cloneObj=function(e){return JSON.parse(JSON.stringify(e))},de(e,[{key:"url",get:function(){return!this._url&&this.relurl&&(this._url=J.a.buildAbsoluteURL(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url},set:function(e){this._url=e}},{key:"programDateTime",get:function(){return!this._programDateTime&&this.rawProgramDateTime&&(this._programDateTime=new Date(Date.parse(this.rawProgramDateTime))),this._programDateTime}},{key:"byteRange",get:function(){if(!this._byteRange){var e=this._byteRange=[];if(this.rawByteRange){var t=this.rawByteRange.split("@",2);if(1===t.length){var r=this.lastByteRangeEndOffset;e[0]=r?r:0}else e[0]=parseInt(t[1]);e[1]=parseInt(t[0])+e[0]}}return this._byteRange}},{key:"byteRangeStartOffset",get:function(){return this.byteRange[0]}},{key:"byteRangeEndOffset",get:function(){return this.byteRange[1]}},{key:"decryptdata",get:function(){return this._decryptdata||(this._decryptdata=this.fragmentDecryptdataFromLevelkey(this.levelkey,this.sn)),this._decryptdata}}]),e}(),ge=function(e){function t(r){d(this,t);var a=s(this,e.call(this,r,Z.a.MANIFEST_LOADING,Z.a.LEVEL_LOADING,Z.a.AUDIO_TRACK_LOADING,Z.a.SUBTITLE_TRACK_LOADING));return a.loaders={},a}return l(t,e),t.prototype.destroy=function(){for(var e in this.loaders){var t=this.loaders[e];t&&t.destroy()}this.loaders={},ae.prototype.destroy.call(this)},t.prototype.onManifestLoading=function(e){this.load(e.url,{type:"manifest"})},t.prototype.onLevelLoading=function(e){this.load(e.url,{type:"level",level:e.level,id:e.id})},t.prototype.onAudioTrackLoading=function(e){this.load(e.url,{type:"audioTrack",id:e.id})},t.prototype.onSubtitleTrackLoading=function(e){this.load(e.url,{type:"subtitleTrack",id:e.id})},t.prototype.load=function(e,t){var r=this.loaders[t.type];if(r){var a=r.context;if(a&&a.url===e)return void ee.b.trace("playlist request ongoing");ee.b.warn("abort previous loader for type:"+t.type),r.abort()}var i=this.hls.config,n=void 0,o=void 0,s=void 0,l=void 0;"manifest"===t.type?(n=i.manifestLoadingMaxRetry,o=i.manifestLoadingTimeOut,s=i.manifestLoadingRetryDelay,l=i.manifestLoadingMaxRetryTimeout):(n=i.levelLoadingMaxRetry,o=i.levelLoadingTimeOut,s=i.levelLoadingRetryDelay,l=i.levelLoadingMaxRetryTimeout,ee.b.log("loading playlist for "+t.type+" "+(t.level||t.id))),r=this.loaders[t.type]=t.loader="undefined"!=typeof i.pLoader?new i.pLoader(i):new i.loader(i),t.url=e,t.responseType="";var d=void 0,u=void 0;d={timeout:o,maxRetry:n,retryDelay:s,maxRetryDelay:l},u={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)},r.load(t,d,u)},t.prototype.resolve=function(e,t){return J.a.buildAbsoluteURL(t,e,{alwaysNormalize:!0})},t.prototype.parseMasterPlaylist=function(e,t){function r(e,t){["video","audio"].forEach(function(r){var a=e.filter(function(e){return n(e,r)});if(a.length){var i=a.filter(function(e){return 0===e.lastIndexOf("avc1",0)||0===e.lastIndexOf("mp4a",0)});t[r+"Codec"]=i.length>0?i[0]:a[0],e=e.filter(function(e){return a.indexOf(e)===-1})}}),t.unknownCodecs=e}var a=[],i=void 0;for(ue.lastIndex=0;null!=(i=ue.exec(e));){var o={},s=o.attrs=new se(i[1]);o.url=this.resolve(i[2],t);var l=s.decimalResolution("RESOLUTION");l&&(o.width=l.width,o.height=l.height),o.bitrate=s.decimalInteger("AVERAGE-BANDWIDTH")||s.decimalInteger("BANDWIDTH"),o.name=s.NAME,r([].concat((s.CODECS||"").split(/[ ,]+/)),o),o.videoCodec&&o.videoCodec.indexOf("avc1")!==-1&&(o.videoCodec=this.avc1toavcoti(o.videoCodec)),a.push(o)}return a},t.prototype.parseMasterPlaylistMedia=function(e,t,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=void 0,n=[],o=0;for(fe.lastIndex=0;null!=(i=fe.exec(e));){var s={},l=new se(i[1]);l.TYPE===r&&(s.groupId=l["GROUP-ID"],s.name=l.NAME,s.type=r,s.default="YES"===l.DEFAULT,s.autoselect="YES"===l.AUTOSELECT,s.forced="YES"===l.FORCED,l.URI&&(s.url=this.resolve(l.URI,t)),s.lang=l.LANGUAGE,s.name||(s.name=s.lang),a&&(s.audioCodec=a),s.id=o++,n.push(s))}return n},t.prototype.avc1toavcoti=function(e){var t,r=e.split(".");return r.length>2?(t=r.shift()+".",t+=parseInt(r.shift()).toString(16),t+=("000"+parseInt(r.shift()).toString(16)).substr(-4)):t=e,t},t.prototype.parseLevelPlaylist=function(e,t,r,a){var i,n,o=0,s=0,l={type:null,version:null,url:t,fragments:[],live:!0,startSN:0},d=new pe,u=0,f=null,h=new ve;for(he.lastIndex=0;null!==(i=he.exec(e));){var c=i[1];if(c){h.duration=parseFloat(c);var p=(" "+i[2]).slice(1);h.title=p?p:null,h.tagList.push(p?["INF",c,p]:["INF",c])}else if(i[3]){if(!isNaN(h.duration)){var v=o++;h.type=a,h.start=s,h.levelkey=d,h.sn=v,h.level=r,h.cc=u,h.baseurl=t,h.relurl=(" "+i[3]).slice(1),l.fragments.push(h),f=h,s+=h.duration,h=new ve}}else if(i[4]){if(h.rawByteRange=(" "+i[4]).slice(1),f){var g=f.byteRangeEndOffset;g&&(h.lastByteRangeEndOffset=g)}}else if(i[5])h.rawProgramDateTime=(" "+i[5]).slice(1),h.tagList.push(["PROGRAM-DATE-TIME",h.rawProgramDateTime]),void 0===l.programDateTime&&(l.programDateTime=new Date(new Date(Date.parse(i[5]))-1e3*s));else{for(i=i[0].match(ce),n=1;n<i.length&&void 0===i[n];n++);var m=(" "+i[n+1]).slice(1),y=(" "+i[n+2]).slice(1);switch(i[n]){case"#":h.tagList.push(y?[m,y]:[m]);break;case"PLAYLIST-TYPE":l.type=m.toUpperCase();break;case"MEDIA-SEQUENCE":o=l.startSN=parseInt(m);break;case"TARGETDURATION":l.targetduration=parseFloat(m);break;case"VERSION":l.version=parseInt(m);break;case"EXTM3U":break;case"ENDLIST":l.live=!1;break;case"DIS":u++,h.tagList.push(["DIS"]);break;case"DISCONTINUITY-SEQ":u=parseInt(m);break;case"KEY":var b=m,E=new se(b),R=E.enumeratedString("METHOD"),A=E.URI,T=E.hexadecimalInteger("IV");R&&(d=new pe,A&&["AES-128","SAMPLE-AES"].indexOf(R)>=0&&(d.method=R,d.baseuri=t,d.reluri=A,d.key=null,d.iv=T));break;case"START":var S=m,L=new se(S),_=L.decimalFloatingPoint("TIME-OFFSET");isNaN(_)||(l.startTimeOffset=_);break;case"MAP":var w=new se(m);h.relurl=w.URI,h.rawByteRange=w.BYTERANGE,h.baseurl=t,h.level=r,h.type=a,h.sn="initSegment",l.initSegment=h,h=new ve;break;default:ee.b.warn("line parsed but not handled: "+i)}}}return h=f,h&&!h.relurl&&(l.fragments.pop(),s-=h.duration),l.totalduration=s,l.averagetargetduration=s/l.fragments.length,l.endSN=o-1,l.startCC=l.fragments[0]?l.fragments[0].cc:0,l.endCC=u,l},t.prototype.loadsuccess=function(e,t,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=e.data,n=e.url,o=r.type,s=r.id,l=r.level,d=this.hls;if(this.loaders[o]=void 0,void 0!==n&&0!==n.indexOf("data:")||(n=r.url),t.tload=performance.now(),0===i.indexOf("#EXTM3U"))if(i.indexOf("#EXTINF:")>0){var u="audioTrack"!==o&&"subtitleTrack"!==o,f=isNaN(l)?isNaN(s)?0:s:l,h=this.parseLevelPlaylist(i,n,f,"audioTrack"===o?"audio":"subtitleTrack"===o?"subtitle":"main");h.tload=t.tload,"manifest"===o&&d.trigger(Z.a.MANIFEST_LOADED,{levels:[{url:n,details:h}],audioTracks:[],url:n,stats:t,networkDetails:a}),t.tparsed=performance.now(),h.targetduration?u?d.trigger(Z.a.LEVEL_LOADED,{details:h,level:l||0,id:s||0,stats:t,networkDetails:a}):"audioTrack"===o?d.trigger(Z.a.AUDIO_TRACK_LOADED,{details:h,id:s,stats:t,networkDetails:a}):"subtitleTrack"===o&&d.trigger(Z.a.SUBTITLE_TRACK_LOADED,{details:h,id:s,stats:t,networkDetails:a}):d.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:$.a.MANIFEST_PARSING_ERROR,fatal:!0,url:n,reason:"invalid targetduration",networkDetails:a})}else{var c=this.parseMasterPlaylist(i,n);if(c.length){var p=this.parseMasterPlaylistMedia(i,n,"AUDIO",c[0].audioCodec),v=this.parseMasterPlaylistMedia(i,n,"SUBTITLES");if(p.length){var g=!1;p.forEach(function(e){e.url||(g=!0)}),g===!1&&c[0].audioCodec&&!c[0].attrs.AUDIO&&(ee.b.log("audio codec signaled in quality level, but no embedded audio track signaled, create one"),p.unshift({type:"main",name:"main"}))}d.trigger(Z.a.MANIFEST_LOADED,{levels:c,audioTracks:p,subtitles:v,url:n,stats:t,networkDetails:a})}else d.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:$.a.MANIFEST_PARSING_ERROR,fatal:!0,url:n,reason:"no level found in manifest",networkDetails:a})}else d.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:$.a.MANIFEST_PARSING_ERROR,fatal:!0,url:n,reason:"no EXTM3U delimiter",networkDetails:a})},t.prototype.loaderror=function(e,t){var r,a,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=t.loader;switch(t.type){case"manifest":r=$.a.MANIFEST_LOAD_ERROR,a=!0;break;case"level":r=$.a.LEVEL_LOAD_ERROR,a=!1;break;case"audioTrack":r=$.a.AUDIO_TRACK_LOAD_ERROR,a=!1}n&&(n.abort(),this.loaders[t.type]=void 0),this.hls.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:r,fatal:a,url:n.url,loader:n,response:e,context:t,networkDetails:i})},t.prototype.loadtimeout=function(e,t){var r,a,i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,n=t.loader;switch(t.type){case"manifest":r=$.a.MANIFEST_LOAD_TIMEOUT,a=!0;break;case"level":r=$.a.LEVEL_LOAD_TIMEOUT,a=!1;break;case"audioTrack":r=$.a.AUDIO_TRACK_LOAD_TIMEOUT,a=!1}n&&(n.abort(),this.loaders[t.type]=void 0),this.hls.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:r,fatal:a,url:n.url,loader:n,context:t,networkDetails:i})},t}(ae),me=ge,ye=function(e){function t(r){u(this,t);var a=f(this,e.call(this,r,Z.a.FRAG_LOADING));return a.loaders={},a}return h(t,e),t.prototype.destroy=function(){var e=this.loaders;for(var t in e){var r=e[t];r&&r.destroy()}this.loaders={},ae.prototype.destroy.call(this)},t.prototype.onFragLoading=function(e){var t=e.frag,r=t.type,a=this.loaders[r],i=this.hls.config;t.loaded=0,a&&(ee.b.warn("abort previous fragment loader for type:"+r),a.abort()),a=this.loaders[r]=t.loader="undefined"!=typeof i.fLoader?new i.fLoader(i):new i.loader(i);var n=void 0,o=void 0,s=void 0;n={url:t.url,frag:t,responseType:"arraybuffer",progressData:!1};var l=t.byteRangeStartOffset,d=t.byteRangeEndOffset;isNaN(l)||isNaN(d)||(n.rangeStart=l,n.rangeEnd=d),o={timeout:i.fragLoadingTimeOut,maxRetry:0,retryDelay:0,maxRetryDelay:i.fragLoadingMaxRetryTimeout},s={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this),onProgress:this.loadprogress.bind(this)},a.load(n,o,s)},t.prototype.loadsuccess=function(e,t,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=e.data,n=r.frag;n.loader=void 0,this.loaders[n.type]=void 0,this.hls.trigger(Z.a.FRAG_LOADED,{payload:i,frag:n,stats:t,networkDetails:a})},t.prototype.loaderror=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=t.loader;a&&a.abort(),this.loaders[t.type]=void 0,this.hls.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:$.a.FRAG_LOAD_ERROR,fatal:!1,frag:t.frag,response:e,networkDetails:r})},t.prototype.loadtimeout=function(e,t){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:null,a=t.loader;a&&a.abort(),this.loaders[t.type]=void 0,this.hls.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:$.a.FRAG_LOAD_TIMEOUT,fatal:!1,frag:t.frag,networkDetails:r})},t.prototype.loadprogress=function(e,t,r){var a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:null,i=t.frag;i.loaded=e.loaded,this.hls.trigger(Z.a.FRAG_LOAD_PROGRESS,{frag:i,stats:e,networkDetails:a})},t}(ae),be=ye,Ee=function(e){function t(r){c(this,t);var a=p(this,e.call(this,r,Z.a.KEY_LOADING));return a.loaders={},a.decryptkey=null,a.decrypturl=null,a}return v(t,e),t.prototype.destroy=function(){for(var e in this.loaders){var t=this.loaders[e];t&&t.destroy()}this.loaders={},ae.prototype.destroy.call(this)},t.prototype.onKeyLoading=function(e){var t=e.frag,r=t.type,a=this.loaders[r],i=t.decryptdata,n=i.uri;if(n!==this.decrypturl||null===this.decryptkey){var o=this.hls.config;a&&(ee.b.warn("abort previous key loader for type:"+r),a.abort()),t.loader=this.loaders[r]=new o.loader(o),this.decrypturl=n,this.decryptkey=null;var s=void 0,l=void 0,d=void 0;s={url:n,frag:t,responseType:"arraybuffer"},l={timeout:o.fragLoadingTimeOut,maxRetry:o.fragLoadingMaxRetry,retryDelay:o.fragLoadingRetryDelay,maxRetryDelay:o.fragLoadingMaxRetryTimeout},d={onSuccess:this.loadsuccess.bind(this),onError:this.loaderror.bind(this),onTimeout:this.loadtimeout.bind(this)},t.loader.load(s,l,d)}else this.decryptkey&&(i.key=this.decryptkey,this.hls.trigger(Z.a.KEY_LOADED,{frag:t}))},t.prototype.loadsuccess=function(e,t,r){var a=r.frag;this.decryptkey=a.decryptdata.key=new Uint8Array(e.data),a.loader=void 0,this.loaders[a.type]=void 0,this.hls.trigger(Z.a.KEY_LOADED,{frag:a})},t.prototype.loaderror=function(e,t){var r=t.frag,a=r.loader;a&&a.abort(),this.loaders[t.type]=void 0,this.hls.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:$.a.KEY_LOAD_ERROR,fatal:!1,frag:r,response:e})},t.prototype.loadtimeout=function(e,t){var r=t.frag,a=r.loader;a&&a.abort(),this.loaders[t.type]=void 0,this.hls.trigger(Z.a.ERROR,{type:$.b.NETWORK_ERROR,details:$.a.KEY_LOAD_TIMEOUT,fatal:!1,frag:r})},t}(ae),Re=Ee,Ae={search:function(e,t){for(var r=0,a=e.length-1,i=null,n=null;r<=a;){
i=(r+a)/2|0,n=e[i];var o=t(n);if(o>0)r=i+1;else{if(!(o<0))return n;a=i-1}}return null}},Te=Ae,Se={isBuffered:function(e,t){if(e)for(var r=e.buffered,a=0;a<r.length;a++)if(t>=r.start(a)&&t<=r.end(a))return!0;return!1},bufferInfo:function(e,t,r){if(e){var a,i=e.buffered,n=[];for(a=0;a<i.length;a++)n.push({start:i.start(a),end:i.end(a)});return this.bufferedInfo(n,t,r)}return{len:0,start:t,end:t,nextStart:void 0}},bufferedInfo:function(e,t,r){var a,i,n,o,s,l=[];for(e.sort(function(e,t){var r=e.start-t.start;return r?r:t.end-e.end}),s=0;s<e.length;s++){var d=l.length;if(d){var u=l[d-1].end;e[s].start-u<r?e[s].end>u&&(l[d-1].end=e[s].end):l.push(e[s])}else l.push(e[s])}for(s=0,a=0,i=n=t;s<l.length;s++){var f=l[s].start,h=l[s].end;if(t+r>=f&&t<h)i=f,n=h,a=n-t;else if(t+r<f){o=f;break}}return{len:a,start:i,end:n,nextStart:o}}},Le=Se,_e=r(7),we=r(5),De=r.n(we),Oe=r(9),ke=r.n(Oe),xe=function(){function e(t,r){g(this,e),this.hls=t,this.id=r;var a=this.observer=new De.a,i=t.config;a.trigger=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];a.emit.apply(a,[e,e].concat(r))},a.off=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),i=1;i<t;i++)r[i-1]=arguments[i];a.removeListener.apply(a,[e].concat(r))};var n=function(e,r){r=r||{},r.frag=this.frag,r.id=this.id,t.trigger(e,r)}.bind(this);a.on(Z.a.FRAG_DECRYPTED,n),a.on(Z.a.FRAG_PARSING_INIT_SEGMENT,n),a.on(Z.a.FRAG_PARSING_DATA,n),a.on(Z.a.FRAG_PARSED,n),a.on(Z.a.ERROR,n),a.on(Z.a.FRAG_PARSING_METADATA,n),a.on(Z.a.FRAG_PARSING_USERDATA,n),a.on(Z.a.INIT_PTS_FOUND,n);var o={mp4:MediaSource.isTypeSupported("video/mp4"),mpeg:MediaSource.isTypeSupported("audio/mpeg"),mp3:MediaSource.isTypeSupported('audio/mp4; codecs="mp3"')},s=navigator.vendor;if(i.enableWorker&&"undefined"!=typeof Worker){ee.b.log("demuxing in webworker");var l=void 0;try{l=this.w=ke()(10),this.onwmsg=this.onWorkerMessage.bind(this),l.addEventListener("message",this.onwmsg),l.onerror=function(e){t.trigger(Z.a.ERROR,{type:$.b.OTHER_ERROR,details:$.a.INTERNAL_EXCEPTION,fatal:!0,event:"demuxerWorker",err:{message:e.message+" ("+e.filename+":"+e.lineno+")"}})},l.postMessage({cmd:"init",typeSupported:o,vendor:s,id:r,config:JSON.stringify(i)})}catch(e){ee.b.error("error while initializing DemuxerWorker, fallback on DemuxerInline"),l&&URL.revokeObjectURL(l.objectURL),this.demuxer=new _e.a(a,o,i,s),this.w=void 0}}else this.demuxer=new _e.a(a,o,i,s)}return e.prototype.destroy=function(){var e=this.w;if(e)e.removeEventListener("message",this.onwmsg),e.terminate(),this.w=null;else{var t=this.demuxer;t&&(t.destroy(),this.demuxer=null)}var r=this.observer;r&&(r.removeAllListeners(),this.observer=null)},e.prototype.push=function(e,t,r,a,i,n,o,s){var l=this.w,d=isNaN(i.startDTS)?i.start:i.startDTS,u=i.decryptdata,f=this.frag,h=!(f&&i.cc===f.cc),c=!(f&&i.level===f.level),p=f&&i.sn===f.sn+1,v=!c&&p;if(h&&ee.b.log(this.id+":discontinuity detected"),c&&ee.b.log(this.id+":switch detected"),this.frag=i,l)l.postMessage({cmd:"demux",data:e,decryptdata:u,initSegment:t,audioCodec:r,videoCodec:a,timeOffset:d,discontinuity:h,trackSwitch:c,contiguous:v,duration:n,accurateTimeOffset:o,defaultInitPTS:s},[e]);else{var g=this.demuxer;g&&g.push(e,u,t,r,a,d,h,c,v,n,o,s)}},e.prototype.onWorkerMessage=function(e){var t=e.data,r=this.hls;switch(t.event){case"init":URL.revokeObjectURL(this.w.objectURL);break;case Z.a.FRAG_PARSING_DATA:t.data.data1=new Uint8Array(t.data1),t.data2&&(t.data.data2=new Uint8Array(t.data2));default:t.data=t.data||{},t.data.frag=this.frag,t.data.id=this.id,r.trigger(t.event,t.data)}},e}(),Ie=xe,Pe={toString:function(e){for(var t="",r=e.length,a=0;a<r;a++)t+="["+e.start(a).toFixed(3)+","+e.end(a).toFixed(3)+"]";return t}},Ce=Pe,Fe=function(){function e(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,r,a){return r&&e(t.prototype,r),a&&e(t,a),t}}(),Me={STOPPED:"STOPPED",IDLE:"IDLE",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",WAITING_LEVEL:"WAITING_LEVEL",PARSING:"PARSING",PARSED:"PARSED",BUFFER_FLUSHING:"BUFFER_FLUSHING",ENDED:"ENDED",ERROR:"ERROR"},Ne=function(e){function t(r){L(this,t);var a=_(this,e.call(this,r,Z.a.MEDIA_ATTACHED,Z.a.MEDIA_DETACHING,Z.a.MANIFEST_LOADING,Z.a.MANIFEST_PARSED,Z.a.LEVEL_LOADED,Z.a.KEY_LOADED,Z.a.FRAG_LOADED,Z.a.FRAG_LOAD_EMERGENCY_ABORTED,Z.a.FRAG_PARSING_INIT_SEGMENT,Z.a.FRAG_PARSING_DATA,Z.a.FRAG_PARSED,Z.a.ERROR,Z.a.AUDIO_TRACK_SWITCHING,Z.a.AUDIO_TRACK_SWITCHED,Z.a.BUFFER_CREATED,Z.a.BUFFER_APPENDED,Z.a.BUFFER_FLUSHED));return a.config=r.config,a.audioCodecSwap=!1,a.ticks=0,a._state=Me.STOPPED,a.ontick=a.tick.bind(a),a}return w(t,e),t.prototype.destroy=function(){this.stopLoad(),this.timer&&(clearInterval(this.timer),this.timer=null),ae.prototype.destroy.call(this),this.state=Me.STOPPED},t.prototype.startLoad=function(e){if(this.levels){var t=this.lastCurrentTime,r=this.hls;if(this.stopLoad(),this.timer||(this.timer=setInterval(this.ontick,100)),this.level=-1,this.fragLoadError=0,!this.startFragRequested){var a=r.startLevel;a===-1&&(a=0,this.bitrateTest=!0),this.level=r.nextLoadLevel=a,this.loadedmetadata=!1}t>0&&e===-1&&(ee.b.log("override startPosition with lastCurrentTime @"+t.toFixed(3)),e=t),this.state=Me.IDLE,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=e,this.tick()}else this.forceStartLoad=!0,this.state=Me.STOPPED},t.prototype.stopLoad=function(){var e=this.fragCurrent;e&&(e.loader&&e.loader.abort(),this.fragCurrent=null),this.fragPrevious=null,this.demuxer&&(this.demuxer.destroy(),this.demuxer=null),this.state=Me.STOPPED,this.forceStartLoad=!1},t.prototype.tick=function(){this.ticks++,1===this.ticks&&(this.doTick(),this.ticks>1&&setTimeout(this.tick,1),this.ticks=0)},t.prototype.doTick=function(){switch(this.state){case Me.ERROR:break;case Me.BUFFER_FLUSHING:this.fragLoadError=0;break;case Me.IDLE:this._doTickIdle();break;case Me.WAITING_LEVEL:var e=this.levels[this.level];e&&e.details&&(this.state=Me.IDLE);break;case Me.FRAG_LOADING_WAITING_RETRY:var t=performance.now(),r=this.retryDate;(!r||t>=r||this.media&&this.media.seeking)&&(ee.b.log("mediaController: retryDate reached, switch back to IDLE state"),this.state=Me.IDLE);break;case Me.ERROR:case Me.STOPPED:case Me.FRAG_LOADING:case Me.PARSING:case Me.PARSED:case Me.ENDED:}this._checkBuffer(),this._checkFragmentChanged()},t.prototype._doTickIdle=function(){var e=this.hls,t=e.config,r=this.media;if(void 0!==this.levelLastLoaded&&(r||!this.startFragRequested&&t.startFragPrefetch)){var a=void 0;a=this.loadedmetadata?r.currentTime:this.nextLoadPosition;var i=e.nextLoadLevel,n=this.levels[i];if(n){var o=n.bitrate,s=void 0;s=o?Math.max(8*t.maxBufferSize/o,t.maxBufferLength):t.maxBufferLength,s=Math.min(s,t.maxMaxBufferLength);var l=Le.bufferInfo(this.mediaBuffer?this.mediaBuffer:r,a,t.maxBufferHole),d=l.len;if(!(d>=s)){ee.b.trace("buffer length of "+d.toFixed(3)+" is below max of "+s.toFixed(3)+". checking for more payload ..."),this.level=e.nextLoadLevel=i;var u=n.details;if("undefined"==typeof u||u.live&&this.levelLastLoaded!==i)return void(this.state=Me.WAITING_LEVEL);var f=this.fragPrevious;if(!u.live&&f&&!f.backtracked&&f.sn===u.endSN&&!l.nextStart){var h=Math.min(r.duration,f.start+f.duration);if(h-Math.max(l.end,f.start)<=Math.max(.2,f.duration)){var c={};return this.altAudio&&(c.type="video"),this.hls.trigger(Z.a.BUFFER_EOS,c),void(this.state=Me.ENDED)}}this._fetchPayloadOrEos(a,l,u)}}}},t.prototype._fetchPayloadOrEos=function(e,t,r){var a=this.fragPrevious,i=this.level,n=r.fragments,o=n.length;if(0!==o){var s=n[0].start,l=n[o-1].start+n[o-1].duration,d=t.end,u=void 0;if(r.initSegment&&!r.initSegment.data)u=r.initSegment;else if(r.live){var f=this.config.initialLiveManifestSize;if(o<f)return void ee.b.warn("Can not start playback of a level, reason: not enough fragments "+o+" < "+f);if(u=this._ensureFragmentAtLivePoint(r,d,s,l,a,n,o),null===u)return}else d<s&&(u=n[0]);u||(u=this._findFragment(s,a,o,n,d,l,r)),u&&this._loadFragmentOrKey(u,i,r,e,d)}},t.prototype._ensureFragmentAtLivePoint=function(e,t,r,a,i,n,o){var s=this.hls.config,l=this.media,d=void 0,u=void 0!==s.liveMaxLatencyDuration?s.liveMaxLatencyDuration:s.liveMaxLatencyDurationCount*e.targetduration;if(t<Math.max(r-s.maxFragLookUpTolerance,a-u)){var f=this.liveSyncPosition=this.computeLivePosition(r,e);ee.b.log("buffer end: "+t.toFixed(3)+" is located too far from the end of live sliding playlist, reset currentTime to : "+f.toFixed(3)),t=f,l&&l.readyState&&l.duration>f&&(l.currentTime=f),this.nextLoadPosition=f}if(e.PTSKnown&&t>a&&l&&l.readyState)return null;if(this.startFragRequested&&!e.PTSKnown){if(i){var h=i.sn+1;if(h>=e.startSN&&h<=e.endSN){var c=n[h-e.startSN];i.cc===c.cc&&(d=c,ee.b.log("live playlist, switching playlist, load frag with next SN: "+d.sn))}d||(d=Te.search(n,function(e){return i.cc-e.cc}),d&&ee.b.log("live playlist, switching playlist, load frag with same CC: "+d.sn))}d||(d=n[Math.min(o-1,Math.round(o/2))],ee.b.log("live playlist, switching playlist, unknown, load middle frag : "+d.sn))}return d},t.prototype._findFragment=function(e,t,r,a,i,n,o){var s=this.hls.config,l=void 0,d=void 0,u=s.maxFragLookUpTolerance,f=t?a[t.sn-a[0].sn+1]:void 0,h=function(e){var t=Math.min(u,e.duration+(e.deltaPTS?e.deltaPTS:0));return e.start+e.duration-t<=i?1:e.start-t>i&&e.start?-1:0};if(i<n?(i>n-u&&(u=0),d=f&&!h(f)?f:Te.search(a,h)):d=a[r-1],d){l=d;var c=l.sn-o.startSN,p=t&&l.level===t.level,v=a[c-1],g=a[c+1];if(t&&l.sn===t.sn)if(p&&!l.backtracked)if(l.sn<o.endSN){var m=t.deltaPTS;m&&m>s.maxBufferHole&&t.dropped&&c?(l=v,ee.b.warn("SN just loaded, with large PTS gap between audio and video, maybe frag is not starting with a keyframe ? load previous one to try to overcome this"),t.loadCounter--):(l=g,ee.b.log("SN just loaded, load next one: "+l.sn))}else l=null;else l.backtracked&&(g&&g.backtracked?(ee.b.warn("Already backtracked from fragment "+g.sn+", will not backtrack to fragment "+l.sn+". Loading fragment "+g.sn),l=g):(ee.b.warn("Loaded fragment with dropped frames, backtracking 1 segment to find a keyframe"),l.dropped=0,v?(v.loadCounter&&v.loadCounter--,l=v,l.backtracked=!0):c&&(l=null)))}return l},t.prototype._loadFragmentOrKey=function(e,t,r,a,i){var n=this.hls,o=n.config;if(!e.decryptdata||null==e.decryptdata.uri||null!=e.decryptdata.key){if(ee.b.log("Loading "+e.sn+" of ["+r.startSN+" ,"+r.endSN+"],level "+t+", currentTime:"+a.toFixed(3)+",bufferEnd:"+i.toFixed(3)),void 0!==this.fragLoadIdx?this.fragLoadIdx++:this.fragLoadIdx=0,e.loadCounter){e.loadCounter++;var s=o.fragLoadingLoopThreshold;if(e.loadCounter>s&&Math.abs(this.fragLoadIdx-e.loadIdx)<s)return void n.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.FRAG_LOOP_LOADING_ERROR,fatal:!1,frag:e})}else e.loadCounter=1;return e.loadIdx=this.fragLoadIdx,e.autoLevel=n.autoLevelEnabled,e.bitrateTest=this.bitrateTest,this.fragCurrent=e,this.startFragRequested=!0,isNaN(e.sn)||e.bitrateTest||(this.nextLoadPosition=e.start+e.duration),n.trigger(Z.a.FRAG_LOADING,{frag:e}),this.demuxer||(this.demuxer=new Ie(n,"main")),void(this.state=Me.FRAG_LOADING)}ee.b.log("Loading key for "+e.sn+" of ["+r.startSN+" ,"+r.endSN+"],level "+t),this.state=Me.KEY_LOADING,n.trigger(Z.a.KEY_LOADING,{frag:e})},t.prototype.getBufferedFrag=function(e){return Te.search(this._bufferedFrags,function(t){return e<t.startPTS?-1:e>t.endPTS?1:0})},t.prototype.followingBufferedFrag=function(e){return e?this.getBufferedFrag(e.endPTS+.5):null},t.prototype._checkFragmentChanged=function(){var e,t,r=this.media;if(r&&r.readyState&&r.seeking===!1&&(t=r.currentTime,t>r.playbackRate*this.lastCurrentTime&&(this.lastCurrentTime=t),Le.isBuffered(r,t)?e=this.getBufferedFrag(t):Le.isBuffered(r,t+.1)&&(e=this.getBufferedFrag(t+.1)),e)){var a=e;if(a!==this.fragPlaying){this.hls.trigger(Z.a.FRAG_CHANGED,{frag:a});var i=a.level;this.fragPlaying&&this.fragPlaying.level===i||this.hls.trigger(Z.a.LEVEL_SWITCHED,{level:i}),this.fragPlaying=a}}},t.prototype.immediateLevelSwitch=function(){if(ee.b.log("immediateLevelSwitch"),!this.immediateSwitch){this.immediateSwitch=!0;var e=this.media,t=void 0;e?(t=e.paused,e.pause()):t=!0,this.previouslyPaused=t}var r=this.fragCurrent;r&&r.loader&&r.loader.abort(),this.fragCurrent=null,void 0!==this.fragLoadIdx&&(this.fragLoadIdx+=2*this.config.fragLoadingLoopThreshold),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)},t.prototype.immediateLevelSwitchEnd=function(){var e=this.media;e&&e.buffered.length&&(this.immediateSwitch=!1,Le.isBuffered(e,e.currentTime)&&(e.currentTime-=1e-4),this.previouslyPaused||e.play())},t.prototype.nextLevelSwitch=function(){var e=this.media;if(e&&e.readyState){var t=void 0,r=void 0,a=void 0;if(void 0!==this.fragLoadIdx&&(this.fragLoadIdx+=2*this.config.fragLoadingLoopThreshold),r=this.getBufferedFrag(e.currentTime),r&&r.startPTS>1&&this.flushMainBuffer(0,r.startPTS-1),e.paused)t=0;else{var i=this.hls.nextLoadLevel,n=this.levels[i],o=this.fragLastKbps;t=o&&this.fragCurrent?this.fragCurrent.duration*n.bitrate/(1e3*o)+1:0}if(a=this.getBufferedFrag(e.currentTime+t),a&&(a=this.followingBufferedFrag(a))){var s=this.fragCurrent;s&&s.loader&&s.loader.abort(),this.fragCurrent=null,this.flushMainBuffer(a.maxStartPTS,Number.POSITIVE_INFINITY)}}},t.prototype.flushMainBuffer=function(e,t){this.state=Me.BUFFER_FLUSHING;var r={startOffset:e,endOffset:t};this.altAudio&&(r.type="video"),this.hls.trigger(Z.a.BUFFER_FLUSHING,r)},t.prototype.onMediaAttached=function(e){var t=this.media=this.mediaBuffer=e.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),this.onvended=this.onMediaEnded.bind(this),t.addEventListener("seeking",this.onvseeking),t.addEventListener("seeked",this.onvseeked),t.addEventListener("ended",this.onvended);var r=this.config;this.levels&&r.autoStartLoad&&this.hls.startLoad(r.startPosition)},t.prototype.onMediaDetaching=function(){var e=this.media;e&&e.ended&&(ee.b.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0);var t=this.levels;t&&t.forEach(function(e){e.details&&e.details.fragments.forEach(function(e){e.loadCounter=void 0,e.backtracked=void 0})}),e&&(e.removeEventListener("seeking",this.onvseeking),e.removeEventListener("seeked",this.onvseeked),e.removeEventListener("ended",this.onvended),this.onvseeking=this.onvseeked=this.onvended=null),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.stopLoad()},t.prototype.onMediaSeeking=function(){var e=this.media,t=e?e.currentTime:void 0,r=this.config;isNaN(t)||ee.b.log("media seeking to "+t.toFixed(3));var a=this.mediaBuffer?this.mediaBuffer:e,i=Le.bufferInfo(a,t,this.config.maxBufferHole);if(this.state===Me.FRAG_LOADING){var n=this.fragCurrent;if(0===i.len&&n){var o=r.maxFragLookUpTolerance,s=n.start-o,l=n.start+n.duration+o;t<s||t>l?(n.loader&&(ee.b.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),n.loader.abort()),this.fragCurrent=null,this.fragPrevious=null,this.state=Me.IDLE):ee.b.log("seeking outside of buffer but within currently loaded fragment range")}}else this.state===Me.ENDED&&(0===i.len&&(this.fragPrevious=0),this.state=Me.IDLE);e&&(this.lastCurrentTime=t),this.state!==Me.FRAG_LOADING&&void 0!==this.fragLoadIdx&&(this.fragLoadIdx+=2*r.fragLoadingLoopThreshold),this.loadedmetadata||(this.nextLoadPosition=this.startPosition=t),this.tick()},t.prototype.onMediaSeeked=function(){var e=this.media,t=e?e.currentTime:void 0;isNaN(t)||ee.b.log("media seeked to "+t.toFixed(3)),this.tick()},t.prototype.onMediaEnded=function(){ee.b.log("media ended"),this.startPosition=this.lastCurrentTime=0},t.prototype.onManifestLoading=function(){ee.b.log("trigger BUFFER_RESET"),this.hls.trigger(Z.a.BUFFER_RESET),this._bufferedFrags=[],this.stalled=!1,this.startPosition=this.lastCurrentTime=0},t.prototype.onManifestParsed=function(e){var t,r=!1,a=!1;e.levels.forEach(function(e){t=e.audioCodec,t&&(t.indexOf("mp4a.40.2")!==-1&&(r=!0),t.indexOf("mp4a.40.5")!==-1&&(a=!0))}),this.audioCodecSwitch=r&&a,this.audioCodecSwitch&&ee.b.log("both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=e.levels,this.startFragRequested=!1;var i=this.config;(i.autoStartLoad||this.forceStartLoad)&&this.hls.startLoad(i.startPosition)},t.prototype.onLevelLoaded=function(e){var t=e.details,r=e.level,a=this.levels[this.levelLastLoaded],i=this.levels[r],n=t.totalduration,o=0;if(ee.b.log("level "+r+" loaded ["+t.startSN+","+t.endSN+"],duration:"+n),t.live){var s=i.details;s&&t.fragments.length>0?(b(s,t),o=t.fragments[0].start,this.liveSyncPosition=this.computeLivePosition(o,s),t.PTSKnown&&!isNaN(o)?ee.b.log("live playlist sliding:"+o.toFixed(3)):(ee.b.log("live playlist - outdated PTS, unknown sliding"),S(this.fragPrevious,a,t))):(ee.b.log("live playlist - first load, unknown sliding"),t.PTSKnown=!1,S(this.fragPrevious,a,t))}else t.PTSKnown=!1;if(i.details=t,this.levelLastLoaded=r,this.hls.trigger(Z.a.LEVEL_UPDATED,{details:t,level:r}),this.startFragRequested===!1){if(this.startPosition===-1||this.lastCurrentTime===-1){var l=t.startTimeOffset;isNaN(l)?t.live?(this.startPosition=this.computeLivePosition(o,t),ee.b.log("configure startPosition to "+this.startPosition)):this.startPosition=0:(l<0&&(ee.b.log("negative start time offset "+l+", count from end of last fragment"),l=o+n+l),ee.b.log("start time offset found in playlist, adjust startPosition to "+l),this.startPosition=l),this.lastCurrentTime=this.startPosition}this.nextLoadPosition=this.startPosition}this.state===Me.WAITING_LEVEL&&(this.state=Me.IDLE),this.tick()},t.prototype.onKeyLoaded=function(){this.state===Me.KEY_LOADING&&(this.state=Me.IDLE,this.tick())},t.prototype.onFragLoaded=function(e){var t=this.fragCurrent,r=e.frag;if(this.state===Me.FRAG_LOADING&&t&&"main"===r.type&&r.level===t.level&&r.sn===t.sn){var a=e.stats,i=this.levels[t.level],n=i.details;if(ee.b.log("Loaded  "+t.sn+" of ["+n.startSN+" ,"+n.endSN+"],level "+t.level),this.bitrateTest=!1,this.stats=a,r.bitrateTest===!0&&this.hls.nextLoadLevel)this.state=Me.IDLE,this.startFragRequested=!1,a.tparsed=a.tbuffered=performance.now(),this.hls.trigger(Z.a.FRAG_BUFFERED,{stats:a,frag:t,id:"main"}),this.tick();else if("initSegment"===r.sn)this.state=Me.IDLE,a.tparsed=a.tbuffered=performance.now(),n.initSegment.data=e.payload,this.hls.trigger(Z.a.FRAG_BUFFERED,{stats:a,frag:t,id:"main"}),this.tick();else{this.state=Me.PARSING;var o=n.totalduration,s=t.level,l=t.sn,d=this.config.defaultAudioCodec||i.audioCodec;this.audioCodecSwap&&(ee.b.log("swapping playlist audio codec"),void 0===d&&(d=this.lastAudioCodec),d&&(d=d.indexOf("mp4a.40.5")!==-1?"mp4a.40.2":"mp4a.40.5")),this.pendingBuffering=!0,this.appended=!1,ee.b.log("Parsing "+l+" of ["+n.startSN+" ,"+n.endSN+"],level "+s+", cc "+t.cc);var u=this.demuxer;u||(u=this.demuxer=new Ie(this.hls,"main"));var f=this.media,h=f&&f.seeking,c=!h&&(n.PTSKnown||!n.live),p=n.initSegment?n.initSegment.data:[];u.push(e.payload,p,d,i.videoCodec,t,o,c,void 0)}}this.fragLoadError=0},t.prototype.onFragParsingInitSegment=function(e){var t=this.fragCurrent,r=e.frag;if(t&&"main"===e.id&&r.sn===t.sn&&r.level===t.level&&this.state===Me.PARSING){var a,i,n=e.tracks;if(n.audio&&this.altAudio&&delete n.audio,i=n.audio){var o=this.levels[this.level].audioCodec,s=navigator.userAgent.toLowerCase();o&&this.audioCodecSwap&&(ee.b.log("swapping playlist audio codec"),o=o.indexOf("mp4a.40.5")!==-1?"mp4a.40.2":"mp4a.40.5"),this.audioCodecSwitch&&1!==i.metadata.channelCount&&s.indexOf("firefox")===-1&&(o="mp4a.40.5"),s.indexOf("android")!==-1&&"audio/mpeg"!==i.container&&(o="mp4a.40.2",ee.b.log("Android: force audio codec to "+o)),i.levelCodec=o,i.id=e.id}i=n.video,i&&(i.levelCodec=this.levels[this.level].videoCodec,i.id=e.id),this.hls.trigger(Z.a.BUFFER_CODECS,n);for(a in n){i=n[a],ee.b.log("main track:"+a+",container:"+i.container+",codecs[level/parsed]=["+i.levelCodec+"/"+i.codec+"]");var l=i.initSegment;l&&(this.appended=!0,this.pendingBuffering=!0,this.hls.trigger(Z.a.BUFFER_APPENDING,{type:a,data:l,parent:"main",content:"initSegment"}))}this.tick()}},t.prototype.onFragParsingData=function(e){var t=this,r=this.fragCurrent,a=e.frag;if(r&&"main"===e.id&&a.sn===r.sn&&a.level===r.level&&("audio"!==e.type||!this.altAudio)&&this.state===Me.PARSING){var i=this.levels[this.level],n=r;if(isNaN(e.endPTS)&&(e.endPTS=e.startPTS+r.duration,e.endDTS=e.startDTS+r.duration),ee.b.log("Parsed "+e.type+",PTS:["+e.startPTS.toFixed(3)+","+e.endPTS.toFixed(3)+"],DTS:["+e.startDTS.toFixed(3)+"/"+e.endDTS.toFixed(3)+"],nb:"+e.nb+",dropped:"+(e.dropped||0)),"video"===e.type)if(n.dropped=e.dropped,n.dropped){if(!n.backtracked)return ee.b.warn("missing video frame(s), backtracking fragment"),n.backtracked=!0,this.nextLoadPosition=e.startPTS,this.state=Me.IDLE,this.fragPrevious=n,void this.tick();ee.b.warn("Already backtracked on this fragment, appending with the gap")}else n.backtracked=!1;var o=y(i.details,n,e.startPTS,e.endPTS,e.startDTS,e.endDTS),s=this.hls;s.trigger(Z.a.LEVEL_PTS_UPDATED,{details:i.details,level:this.level,drift:o,type:e.type,start:e.startPTS,end:e.endPTS}),[e.data1,e.data2].forEach(function(r){r&&r.length&&t.state===Me.PARSING&&(t.appended=!0,t.pendingBuffering=!0,s.trigger(Z.a.BUFFER_APPENDING,{type:e.type,data:r,parent:"main",content:"data"}))}),this.tick()}},t.prototype.onFragParsed=function(e){var t=this.fragCurrent,r=e.frag;t&&"main"===e.id&&r.sn===t.sn&&r.level===t.level&&this.state===Me.PARSING&&(this.stats.tparsed=performance.now(),this.state=Me.PARSED,this._checkAppendedParsed())},t.prototype.onAudioTrackSwitching=function(e){var t=!!e.url,r=e.id;if(!t){if(this.mediaBuffer!==this.media){ee.b.log("switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;var a=this.fragCurrent;a.loader&&(ee.b.log("switching to main audio track, cancel main fragment load"),a.loader.abort()),this.fragCurrent=null,this.fragPrevious=null,this.demuxer&&(this.demuxer.destroy(),this.demuxer=null),this.state=Me.IDLE}var i=this.hls;i.trigger(Z.a.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:"audio"}),i.trigger(Z.a.AUDIO_TRACK_SWITCHED,{id:r}),this.altAudio=!1}},t.prototype.onAudioTrackSwitched=function(e){var t=e.id,r=!!this.hls.audioTracks[t].url;if(r){var a=this.videoBuffer;a&&this.mediaBuffer!==a&&(ee.b.log("switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=a)}this.altAudio=r,this.tick()},t.prototype.onBufferCreated=function(e){var t=e.tracks,r=void 0,a=void 0,i=!1;for(var n in t){var o=t[n];"main"===o.id?(a=n,r=o,"video"===n&&(this.videoBuffer=t[n].buffer)):i=!0}i&&r?(ee.b.log("alternate track found, use "+a+".buffered to schedule main fragment loading"),this.mediaBuffer=r.buffer):this.mediaBuffer=this.media},t.prototype.onBufferAppended=function(e){if("main"===e.parent){var t=this.state;t!==Me.PARSING&&t!==Me.PARSED||(this.pendingBuffering=e.pending>0,this._checkAppendedParsed())}},t.prototype._checkAppendedParsed=function(){if(!(this.state!==Me.PARSED||this.appended&&this.pendingBuffering)){var e=this.fragCurrent;if(e){var t=this.mediaBuffer?this.mediaBuffer:this.media;ee.b.log("main buffered : "+Ce.toString(t.buffered));var r=this._bufferedFrags.filter(function(e){return Le.isBuffered(t,(e.startPTS+e.endPTS)/2)});r.push(e),this._bufferedFrags=r.sort(function(e,t){return e.startPTS-t.startPTS}),this.fragPrevious=e;var a=this.stats;a.tbuffered=performance.now(),this.fragLastKbps=Math.round(8*a.total/(a.tbuffered-a.tfirst)),this.hls.trigger(Z.a.FRAG_BUFFERED,{stats:a,frag:e,id:"main"}),this.state=Me.IDLE}this.tick()}},t.prototype.onError=function(e){var t=e.frag||this.fragCurrent;if(!t||"main"===t.type){var r=!!this.media&&Le.isBuffered(this.media,this.media.currentTime)&&Le.isBuffered(this.media,this.media.currentTime+.5);switch(e.details){case $.a.FRAG_LOAD_ERROR:case $.a.FRAG_LOAD_TIMEOUT:case $.a.KEY_LOAD_ERROR:case $.a.KEY_LOAD_TIMEOUT:if(!e.fatal)if(this.fragLoadError+1<=this.config.fragLoadingMaxRetry){var a=Math.min(Math.pow(2,this.fragLoadError)*this.config.fragLoadingRetryDelay,this.config.fragLoadingMaxRetryTimeout);t.loadCounter=0,ee.b.warn("mediaController: frag loading failed, retry in "+a+" ms"),this.retryDate=performance.now()+a,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.fragLoadError++,this.state=Me.FRAG_LOADING_WAITING_RETRY}else ee.b.error("mediaController: "+e.details+" reaches max retry, redispatch as fatal ..."),e.fatal=!0,this.state=Me.ERROR;break;case $.a.FRAG_LOOP_LOADING_ERROR:e.fatal||(r?(this._reduceMaxBufferLength(t.duration),this.state=Me.IDLE):t.autoLevel&&0!==t.level||(e.fatal=!0,this.state=Me.ERROR));break;case $.a.LEVEL_LOAD_ERROR:case $.a.LEVEL_LOAD_TIMEOUT:this.state!==Me.ERROR&&(e.fatal?(this.state=Me.ERROR,ee.b.warn("streamController: "+e.details+",switch to "+this.state+" state ...")):e.levelRetry||this.state!==Me.WAITING_LEVEL||(this.state=Me.IDLE));break;case $.a.BUFFER_FULL_ERROR:"main"!==e.parent||this.state!==Me.PARSING&&this.state!==Me.PARSED||(r?(this._reduceMaxBufferLength(this.config.maxBufferLength),this.state=Me.IDLE):(ee.b.warn("buffer full error also media.currentTime is not buffered, flush everything"),this.fragCurrent=null,this.flushMainBuffer(0,Number.POSITIVE_INFINITY)))}}},t.prototype._reduceMaxBufferLength=function(e){var t=this.config;t.maxMaxBufferLength>=e&&(t.maxMaxBufferLength/=2,ee.b.warn("main:reduce max buffer length to "+t.maxMaxBufferLength+"s"),void 0!==this.fragLoadIdx&&(this.fragLoadIdx+=2*t.fragLoadingLoopThreshold))},t.prototype._checkBuffer=function(){var e=this.media,t=this.config;if(e&&e.readyState){var r=e.currentTime,a=this.mediaBuffer?this.mediaBuffer:e,i=a.buffered;if(!this.loadedmetadata&&i.length){this.loadedmetadata=!0;var n=e.seeking?r:this.startPosition,o=Le.isBuffered(a,n),s=i.start(0),l=!o&&Math.abs(n-s)<t.maxSeekHole;(r!==n||l)&&(ee.b.log("target start position:"+n),l&&(n=s,ee.b.log("target start position not buffered, seek to buffered.start(0) "+n)),ee.b.log("adjust currentTime from "+r+" to "+n),e.currentTime=n)}else if(this.immediateSwitch)this.immediateLevelSwitchEnd();else{var d=Le.bufferInfo(e,r,0),u=!(e.paused||e.ended||0===e.buffered.length),f=.5,h=r!==this.lastCurrentTime;if(h)this.stallReported&&(ee.b.warn("playback not stuck anymore @"+r+", after "+Math.round(performance.now()-this.stalled)+"ms"),this.stallReported=!1),this.stalled=void 0,this.nudgeRetry=0;else if(u){var c=performance.now(),p=this.hls;if(this.stalled){var v=c-this.stalled,g=d.len,m=this.nudgeRetry||0;if(g<=f&&v>1e3*t.lowBufferWatchdogPeriod){this.stallReported||(this.stallReported=!0,ee.b.warn("playback stalling in low buffer @"+r),p.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.BUFFER_STALLED_ERROR,fatal:!1,buffer:g}));var y=d.nextStart,b=y-r;if(y&&b<t.maxSeekHole&&b>0){this.nudgeRetry=++m;var E=m*t.nudgeOffset;ee.b.log("adjust currentTime from "+e.currentTime+" to next buffered @ "+y+" + nudge "+E),e.currentTime=y+E,this.stalled=void 0,p.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.BUFFER_SEEK_OVER_HOLE,fatal:!1,hole:y+E-r})}}else if(g>f&&v>1e3*t.highBufferWatchdogPeriod)if(this.stallReported||(this.stallReported=!0,ee.b.warn("playback stalling in high buffer @"+r),p.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.BUFFER_STALLED_ERROR,fatal:!1,buffer:g})),this.stalled=void 0,this.nudgeRetry=++m,m<t.nudgeMaxRetry){var R=e.currentTime,A=R+m*t.nudgeOffset;ee.b.log("adjust currentTime from "+R+" to "+A),e.currentTime=A,p.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.BUFFER_NUDGE_ON_STALL,fatal:!1})}else ee.b.error("still stuck in high buffer @"+r+" after "+t.nudgeMaxRetry+", raise fatal error"),p.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.BUFFER_STALLED_ERROR,fatal:!0})}else this.stalled=c,this.stallReported=!1}}}},t.prototype.onFragLoadEmergencyAborted=function(){this.state=Me.IDLE,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tick()},t.prototype.onBufferFlushed=function(){var e=this.mediaBuffer?this.mediaBuffer:this.media;this._bufferedFrags=this._bufferedFrags.filter(function(t){return Le.isBuffered(e,(t.startPTS+t.endPTS)/2)}),void 0!==this.fragLoadIdx&&(this.fragLoadIdx+=2*this.config.fragLoadingLoopThreshold),this.state=Me.IDLE,this.fragPrevious=null},t.prototype.swapAudioCodec=function(){this.audioCodecSwap=!this.audioCodecSwap},t.prototype.computeLivePosition=function(e,t){var r=void 0!==this.config.liveSyncDuration?this.config.liveSyncDuration:this.config.liveSyncDurationCount*t.targetduration;return e+Math.max(0,t.totalduration-r)},Fe(t,[{key:"state",set:function(e){if(this.state!==e){var t=this.state;this._state=e,ee.b.log("main stream:"+t+"->"+e),this.hls.trigger(Z.a.STREAM_STATE_TRANSITION,{previousState:t,nextState:e})}},get:function(){return this._state}},{key:"currentLevel",get:function(){var e=this.media;if(e){var t=this.getBufferedFrag(e.currentTime);if(t)return t.level}return-1}},{key:"nextBufferedFrag",get:function(){var e=this.media;return e?this.followingBufferedFrag(this.getBufferedFrag(e.currentTime)):null}},{key:"nextLevel",get:function(){var e=this.nextBufferedFrag;return e?e.level:-1}},{key:"liveSyncPosition",get:function(){return this._liveSyncPosition},set:function(e){this._liveSyncPosition=e}}]),t}(ae),Ue=Ne,Be=function(){function e(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,r,a){return r&&e(t.prototype,r),a&&e(t,a),t}}(),Ge=function(e){function t(r){D(this,t);var a=O(this,e.call(this,r,Z.a.MANIFEST_LOADED,Z.a.LEVEL_LOADED,Z.a.FRAG_LOADED,Z.a.ERROR));return a._manualLevel=-1,a.timer=null,a}return k(t,e),t.prototype.destroy=function(){this.cleanTimer(),this._manualLevel=-1},t.prototype.cleanTimer=function(){null!==this.timer&&(clearTimeout(this.timer),this.timer=null)},t.prototype.startLoad=function(){this.canload=!0;var e=this._levels;e&&e.forEach(function(e){e.loadError=0;var t=e.details;t&&t.live&&(e.details=void 0)}),this.timer&&this.tick()},t.prototype.stopLoad=function(){this.canload=!1},t.prototype.onManifestLoaded=function(e){var t=[],r=void 0,a={},i=null,n=!1,s=!1,l=/chrome|firefox/.test(navigator.userAgent.toLowerCase());if(e.levels.forEach(function(e){e.loadError=0,e.fragmentError=!1,n=n||!!e.videoCodec,s=s||!!e.audioCodec||!(!e.attrs||!e.attrs.AUDIO),l===!0&&e.audioCodec&&e.audioCodec.indexOf("mp4a.40.34")!==-1&&(e.audioCodec=void 0),i=a[e.bitrate],void 0===i?(e.url=[e.url],e.urlId=0,a[e.bitrate]=e,t.push(e)):i.url.push(e.url)}),n===!0&&s===!0&&(t=t.filter(function(e){var t=e.videoCodec;return!!t})),t=t.filter(function(e){var t=e.audioCodec,r=e.videoCodec;return(!t||o(t))&&(!r||o(r))}),t.length>0){r=t[0].bitrate,t.sort(function(e,t){return e.bitrate-t.bitrate}),this._levels=t;for(var d=0;d<t.length;d++)if(t[d].bitrate===r){this._firstLevel=d,ee.b.log("manifest loaded,"+t.length+" level(s) found, first bitrate:"+r);break}this.hls.trigger(Z.a.MANIFEST_PARSED,{levels:t,firstLevel:this._firstLevel,stats:e.stats,audio:s,video:n,altAudio:e.audioTracks.length>0})}else this.hls.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:this.hls.url,reason:"no level with compatible codecs found in manifest"})},t.prototype.setLevelInternal=function(e){var t=this._levels,r=this.hls;if(e>=0&&e<t.length){if(this.cleanTimer(),this._level!==e){ee.b.log("switching to level "+e),this._level=e;var a=t[e];a.level=e,r.trigger(Z.a.LEVEL_SWITCH,a),r.trigger(Z.a.LEVEL_SWITCHING,a)}var i=t[e],n=i.details;if(!n||n.live===!0){var o=i.urlId;r.trigger(Z.a.LEVEL_LOADING,{url:i.url[o],level:e,id:o})}}else r.trigger(Z.a.ERROR,{type:$.b.OTHER_ERROR,details:$.a.LEVEL_SWITCH_ERROR,level:e,fatal:!1,reason:"invalid level idx"})},t.prototype.onError=function(e){
var t=this;if(e.fatal===!0)return void(e.type===$.b.NETWORK_ERROR&&this.cleanTimer());var r=e.details,a=!1,i=!1,n=void 0,o=void 0,s=this.hls,l=s.config,d=s.media;switch(r){case $.a.FRAG_LOAD_ERROR:case $.a.FRAG_LOAD_TIMEOUT:case $.a.FRAG_LOOP_LOADING_ERROR:case $.a.KEY_LOAD_ERROR:case $.a.KEY_LOAD_TIMEOUT:n=e.frag.level,i=!0;break;case $.a.LEVEL_LOAD_ERROR:case $.a.LEVEL_LOAD_TIMEOUT:n=e.context.level,a=!0;break;case $.a.REMUX_ALLOC_ERROR:n=e.level}if(void 0!==n){o=this._levels[n],o.loadError++,o.fragmentError=i;var u=o.url.length;if(u>1&&o.loadError<u)o.urlId=(o.urlId+1)%u,o.details=void 0,ee.b.warn("level controller,"+r+" for level "+n+": switching to redundant stream id "+o.urlId);else if(this._manualLevel===-1&&0!==n)ee.b.warn("level controller,"+r+": switch-down for next fragment"),this.hls.nextAutoLevel=Math.max(0,n-1);else if(o&&o.details&&o.details.live)ee.b.warn("level controller,"+r+" on live stream, discard"),a===!0&&(this._level=void 0);else if(a===!0){var f=!!d&&Le.isBuffered(d,d.currentTime)&&Le.isBuffered(d,d.currentTime+.5);f===!0?(ee.b.warn("level controller,"+r+", but media buffered, retry in "+l.levelLoadingRetryDelay+"ms"),this.timer=setTimeout(function(){return t.tick()},l.levelLoadingRetryDelay),e.levelRetry=!0):(ee.b.error("cannot recover "+r+" error"),this._level=void 0,this.cleanTimer(),e.fatal=!0)}}},t.prototype.onFragLoaded=function(e){var t=e.frag;if(void 0!==t&&"main"===t.type){var r=this._levels[t.level];void 0!==r&&(r.fragmentError=!1,r.loadError=0)}},t.prototype.onLevelLoaded=function(e){var t=this,r=e.level;if(r===this._level){var a=this._levels[r];a.fragmentError===!1&&(a.loadError=0);var i=e.details;if(i.live){var n=1e3*(i.averagetargetduration?i.averagetargetduration:i.targetduration),o=a.details;o&&i.endSN===o.endSN&&(n/=2,ee.b.log("same live playlist, reload twice faster")),n-=performance.now()-e.stats.trequest,n=Math.max(1e3,Math.round(n)),ee.b.log("live playlist, reload in "+n+" ms"),this.timer=setTimeout(function(){return t.tick()},n)}else this.cleanTimer()}},t.prototype.tick=function(){var e=this._level;if(void 0!==e&&this.canload){var t=this._levels[e];if(t&&t.url){var r=t.urlId;this.hls.trigger(Z.a.LEVEL_LOADING,{url:t.url[r],level:e,id:r})}}},Be(t,[{key:"levels",get:function(){return this._levels}},{key:"level",get:function(){return this._level},set:function(e){var t=this._levels;t&&t.length>e&&(this._level===e&&void 0!==t[e].details||this.setLevelInternal(e))}},{key:"manualLevel",get:function(){return this._manualLevel},set:function(e){this._manualLevel=e,void 0===this._startLevel&&(this._startLevel=e),e!==-1&&(this.level=e)}},{key:"firstLevel",get:function(){return this._firstLevel},set:function(e){this._firstLevel=e}},{key:"startLevel",get:function(){if(void 0===this._startLevel){var e=this.hls.config.startLevel;return void 0!==e?e:this._firstLevel}return this._startLevel},set:function(e){this._startLevel=e}},{key:"nextLoadLevel",get:function(){return this._manualLevel!==-1?this._manualLevel:this.hls.nextAutoLevel},set:function(e){this.level=e,this._manualLevel===-1&&(this.hls.nextAutoLevel=e)}}]),t}(ae),He=Ge,je=r(4),We=function(e){function t(r){x(this,t);var a=I(this,e.call(this,r,Z.a.MEDIA_ATTACHED,Z.a.MEDIA_DETACHING,Z.a.FRAG_PARSING_METADATA));return a.id3Track=void 0,a.media=void 0,a}return P(t,e),t.prototype.destroy=function(){ae.prototype.destroy.call(this)},t.prototype.onMediaAttached=function(e){this.media=e.media,!this.media},t.prototype.onMediaDetaching=function(){this.media=void 0},t.prototype.onFragParsingMetadata=function(e){var t=e.frag,r=e.samples;this.id3Track||(this.id3Track=this.media.addTextTrack("metadata","id3"),this.id3Track.mode="hidden");for(var a=window.WebKitDataCue||window.VTTCue||window.TextTrackCue,i=0;i<r.length;i++){var n=je.a.getID3Frames(r[i].data);if(n){var o=r[i].pts,s=i<r.length-1?r[i+1].pts:t.endPTS;o===s&&(s+=1e-4);for(var l=0;l<n.length;l++){var d=n[l];if(!je.a.isTimeStampFrame(d)){var u=new a(o,s,"");u.value=d,this.id3Track.addCue(u)}}}}},t}(ae),Ve=We,Ke=function(){function e(t){C(this,e),this.alpha_=t?Math.exp(Math.log(.5)/t):0,this.estimate_=0,this.totalWeight_=0}return e.prototype.sample=function(e,t){var r=Math.pow(this.alpha_,e);this.estimate_=t*(1-r)+r*this.estimate_,this.totalWeight_+=e},e.prototype.getTotalWeight=function(){return this.totalWeight_},e.prototype.getEstimate=function(){if(this.alpha_){var e=1-Math.pow(this.alpha_,this.totalWeight_);return this.estimate_/e}return this.estimate_},e}(),Ye=Ke,ze=function(){function e(t,r,a,i){F(this,e),this.hls=t,this.defaultEstimate_=i,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new Ye(r),this.fast_=new Ye(a)}return e.prototype.sample=function(e,t){e=Math.max(e,this.minDelayMs_);var r=8e3*t/e,a=e/1e3;this.fast_.sample(a,r),this.slow_.sample(a,r)},e.prototype.canEstimate=function(){var e=this.fast_;return e&&e.getTotalWeight()>=this.minWeight_},e.prototype.getEstimate=function(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_},e.prototype.destroy=function(){},e}(),Xe=ze,qe=function(){function e(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,r,a){return r&&e(t.prototype,r),a&&e(t,a),t}}(),Qe=function(e){function t(r){M(this,t);var a=N(this,e.call(this,r,Z.a.FRAG_LOADING,Z.a.FRAG_LOADED,Z.a.FRAG_BUFFERED,Z.a.ERROR));return a.lastLoadedFragLevel=0,a._nextAutoLevel=-1,a.hls=r,a.timer=null,a._bwEstimator=null,a.onCheck=a._abandonRulesCheck.bind(a),a}return U(t,e),t.prototype.destroy=function(){this.clearTimer(),ae.prototype.destroy.call(this)},t.prototype.onFragLoading=function(e){var t=e.frag;if("main"===t.type){if(this.timer||(this.timer=setInterval(this.onCheck,100)),!this._bwEstimator){var r=this.hls,a=e.frag.level,i=r.levels[a].details.live,n=r.config,o=void 0,s=void 0;i?(o=n.abrEwmaFastLive,s=n.abrEwmaSlowLive):(o=n.abrEwmaFastVoD,s=n.abrEwmaSlowVoD),this._bwEstimator=new Xe(r,s,o,n.abrEwmaDefaultEstimate)}this.fragCurrent=t}},t.prototype._abandonRulesCheck=function(){var e=this.hls,t=e.media,r=this.fragCurrent,a=r.loader,i=e.minAutoLevel;if(!a||a.stats&&a.stats.aborted)return ee.b.warn("frag loader destroy or aborted, disarm abandonRules"),void this.clearTimer();var n=a.stats;if(t&&n&&(!t.paused&&0!==t.playbackRate||!t.readyState)&&r.autoLevel&&r.level){var o=performance.now()-n.trequest,s=Math.abs(t.playbackRate);if(o>500*r.duration/s){var l=e.levels,d=Math.max(1,n.bw?n.bw/8:1e3*n.loaded/o),u=l[r.level],f=u.realBitrate?Math.max(u.realBitrate,u.bitrate):u.bitrate,h=n.total?n.total:Math.max(n.loaded,Math.round(r.duration*f/8)),c=t.currentTime,p=(h-n.loaded)/d,v=(Le.bufferInfo(t,c,e.config.maxBufferHole).end-c)/s;if(v<2*r.duration/s&&p>v){var g=void 0,m=void 0;for(m=r.level-1;m>i;m--){var y=l[m].realBitrate?Math.max(l[m].realBitrate,l[m].bitrate):l[m].bitrate;if(g=r.duration*y/(6.4*d),g<v)break}g<p&&(ee.b.warn("loading too slow, abort fragment loading and switch to level "+m+":fragLoadedDelay["+m+"]<fragLoadedDelay["+(r.level-1)+"];bufferStarvationDelay:"+g.toFixed(1)+"<"+p.toFixed(1)+":"+v.toFixed(1)),e.nextLoadLevel=m,this._bwEstimator.sample(o,n.loaded),a.abort(),this.clearTimer(),e.trigger(Z.a.FRAG_LOAD_EMERGENCY_ABORTED,{frag:r,stats:n}))}}}},t.prototype.onFragLoaded=function(e){var t=e.frag;if("main"===t.type&&!isNaN(t.sn)){if(this.clearTimer(),this.lastLoadedFragLevel=t.level,this._nextAutoLevel=-1,this.hls.config.abrMaxWithRealBitrate){var r=this.hls.levels[t.level],a=(r.loaded?r.loaded.bytes:0)+e.stats.loaded,i=(r.loaded?r.loaded.duration:0)+e.frag.duration;r.loaded={bytes:a,duration:i},r.realBitrate=Math.round(8*a/i)}if(e.frag.bitrateTest){var n=e.stats;n.tparsed=n.tbuffered=n.tload,this.onFragBuffered(e)}}},t.prototype.onFragBuffered=function(e){var t=e.stats,r=e.frag;if(!(t.aborted===!0||1!==r.loadCounter||"main"!==r.type||isNaN(r.sn)||r.bitrateTest&&t.tload!==t.tbuffered)){var a=t.tparsed-t.trequest;ee.b.log("latency/loading/parsing/append/kbps:"+Math.round(t.tfirst-t.trequest)+"/"+Math.round(t.tload-t.tfirst)+"/"+Math.round(t.tparsed-t.tload)+"/"+Math.round(t.tbuffered-t.tparsed)+"/"+Math.round(8*t.loaded/(t.tbuffered-t.trequest))),this._bwEstimator.sample(a,t.loaded),t.bwEstimate=this._bwEstimator.getEstimate(),r.bitrateTest?this.bitrateTestDelay=a/1e3:this.bitrateTestDelay=0}},t.prototype.onError=function(e){switch(e.details){case $.a.FRAG_LOAD_ERROR:case $.a.FRAG_LOAD_TIMEOUT:this.clearTimer()}},t.prototype.clearTimer=function(){clearInterval(this.timer),this.timer=null},t.prototype._findBestLevel=function(e,t,r,a,i,n,o,s,l){for(var d=i;d>=a;d--){var u=l[d],f=u.details,h=f?f.totalduration/f.fragments.length:t,c=!!f&&f.live,p=void 0;p=d<=e?o*r:s*r;var v=l[d].realBitrate?Math.max(l[d].realBitrate,l[d].bitrate):l[d].bitrate,g=v*h/p;if(ee.b.trace("level/adjustedbw/bitrate/avgDuration/maxFetchDuration/fetchDuration: "+d+"/"+Math.round(p)+"/"+v+"/"+h+"/"+n+"/"+g),p>v&&(!g||c&&!this.bitrateTestDelay||g<n))return d}return-1},qe(t,[{key:"nextAutoLevel",get:function(){var e=this._nextAutoLevel,t=this._bwEstimator;if(!(e===-1||t&&t.canEstimate()))return e;var r=this._nextABRAutoLevel;return e!==-1&&(r=Math.min(e,r)),r},set:function(e){this._nextAutoLevel=e}},{key:"_nextABRAutoLevel",get:function(){var e=this.hls,t=e.maxAutoLevel,r=e.levels,a=e.config,i=e.minAutoLevel,n=e.media,o=this.lastLoadedFragLevel,s=this.fragCurrent?this.fragCurrent.duration:0,l=n?n.currentTime:0,d=n&&0!==n.playbackRate?Math.abs(n.playbackRate):1,u=this._bwEstimator?this._bwEstimator.getEstimate():a.abrEwmaDefaultEstimate,f=(Le.bufferInfo(n,l,a.maxBufferHole).end-l)/d,h=this._findBestLevel(o,s,u,i,t,f,a.abrBandWidthFactor,a.abrBandWidthUpFactor,r);if(h>=0)return h;ee.b.trace("rebuffering expected to happen, lets try to find a quality level minimizing the rebuffering");var c=s?Math.min(s,a.maxStarvationDelay):a.maxStarvationDelay,p=a.abrBandWidthFactor,v=a.abrBandWidthUpFactor;if(0===f){var g=this.bitrateTestDelay;if(g){var m=s?Math.min(s,a.maxLoadingDelay):a.maxLoadingDelay;c=m-g,ee.b.trace("bitrate test took "+Math.round(1e3*g)+"ms, set first fragment max fetchDuration to "+Math.round(1e3*c)+" ms"),p=v=1}}return h=this._findBestLevel(o,s,u,i,t,f+c,p,v,r),Math.max(h,0)}}]),t}(ae),Je=Qe,Ze=function(e){function t(r){B(this,t);var a=G(this,e.call(this,r,Z.a.MEDIA_ATTACHING,Z.a.MEDIA_DETACHING,Z.a.MANIFEST_PARSED,Z.a.BUFFER_RESET,Z.a.BUFFER_APPENDING,Z.a.BUFFER_CODECS,Z.a.BUFFER_EOS,Z.a.BUFFER_FLUSHING,Z.a.LEVEL_PTS_UPDATED,Z.a.LEVEL_UPDATED));return a._msDuration=null,a._levelDuration=null,a.onsbue=a.onSBUpdateEnd.bind(a),a.onsbe=a.onSBUpdateError.bind(a),a.pendingTracks={},a.tracks={},a}return H(t,e),t.prototype.destroy=function(){ae.prototype.destroy.call(this)},t.prototype.onLevelPtsUpdated=function(e){var t=e.type,r=this.tracks.audio;if("audio"===t&&r&&"audio/mpeg"===r.container){var a=this.sourceBuffer.audio,i=Math.abs(a.timestampOffset-e.start);if(i>.1){var n=a.updating;try{a.abort()}catch(e){n=!0,ee.b.warn("can not abort audio buffer: "+e)}n?this.audioTimestampOffset=e.start:(ee.b.warn("change mpeg audio timestamp offset from "+a.timestampOffset+" to "+e.start),a.timestampOffset=e.start)}}},t.prototype.onManifestParsed=function(e){var t=e.audio,r=e.video||e.levels.length&&e.audio,a=0;e.altAudio&&(t||r)&&(a=(t?1:0)+(r?1:0),ee.b.log(a+" sourceBuffer(s) expected")),this.sourceBufferNb=a},t.prototype.onMediaAttaching=function(e){var t=this.media=e.media;if(t){var r=this.mediaSource=new MediaSource;this.onmso=this.onMediaSourceOpen.bind(this),this.onmse=this.onMediaSourceEnded.bind(this),this.onmsc=this.onMediaSourceClose.bind(this),r.addEventListener("sourceopen",this.onmso),r.addEventListener("sourceended",this.onmse),r.addEventListener("sourceclose",this.onmsc),t.src=URL.createObjectURL(r)}},t.prototype.onMediaDetaching=function(){ee.b.log("media source detaching");var e=this.mediaSource;if(e){if("open"===e.readyState)try{e.endOfStream()}catch(e){ee.b.warn("onMediaDetaching:"+e.message+" while calling endOfStream")}e.removeEventListener("sourceopen",this.onmso),e.removeEventListener("sourceended",this.onmse),e.removeEventListener("sourceclose",this.onmsc),this.media&&(URL.revokeObjectURL(this.media.src),this.media.removeAttribute("src"),this.media.load()),this.mediaSource=null,this.media=null,this.pendingTracks={},this.tracks={},this.sourceBuffer={},this.flushRange=[],this.segments=[],this.appended=0}this.onmso=this.onmse=this.onmsc=null,this.hls.trigger(Z.a.MEDIA_DETACHED)},t.prototype.onMediaSourceOpen=function(){ee.b.log("media source opened"),this.hls.trigger(Z.a.MEDIA_ATTACHED,{media:this.media});var e=this.mediaSource;e&&e.removeEventListener("sourceopen",this.onmso),this.checkPendingTracks()},t.prototype.checkPendingTracks=function(){var e=this.pendingTracks,t=Object.keys(e).length;t&&(this.sourceBufferNb<=t||0===this.sourceBufferNb)&&(this.createSourceBuffers(e),this.pendingTracks={},this.doAppending())},t.prototype.onMediaSourceClose=function(){ee.b.log("media source closed")},t.prototype.onMediaSourceEnded=function(){ee.b.log("media source ended")},t.prototype.onSBUpdateEnd=function(){if(this.audioTimestampOffset){var e=this.sourceBuffer.audio;ee.b.warn("change mpeg audio timestamp offset from "+e.timestampOffset+" to "+this.audioTimestampOffset),e.timestampOffset=this.audioTimestampOffset,delete this.audioTimestampOffset}this._needsFlush&&this.doFlush(),this._needsEos&&this.checkEos(),this.appending=!1;var t=this.parent,r=this.segments.reduce(function(e,r){return r.parent===t?e+1:e},0);this.hls.trigger(Z.a.BUFFER_APPENDED,{parent:t,pending:r}),this._needsFlush||this.doAppending(),this.updateMediaElementDuration()},t.prototype.onSBUpdateError=function(e){ee.b.error("sourceBuffer error:",e),this.hls.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.BUFFER_APPENDING_ERROR,fatal:!1})},t.prototype.onBufferReset=function(){var e=this.sourceBuffer;for(var t in e){var r=e[t];try{this.mediaSource.removeSourceBuffer(r),r.removeEventListener("updateend",this.onsbue),r.removeEventListener("error",this.onsbe)}catch(e){}}this.sourceBuffer={},this.flushRange=[],this.segments=[],this.appended=0},t.prototype.onBufferCodecs=function(e){if(0===Object.keys(this.sourceBuffer).length){for(var t in e)this.pendingTracks[t]=e[t];var r=this.mediaSource;r&&"open"===r.readyState&&this.checkPendingTracks()}},t.prototype.createSourceBuffers=function(e){var t=this.sourceBuffer,r=this.mediaSource;for(var a in e)if(!t[a]){var i=e[a],n=i.levelCodec||i.codec,o=i.container+";codecs="+n;ee.b.log("creating sourceBuffer("+o+")");try{var s=t[a]=r.addSourceBuffer(o);s.addEventListener("updateend",this.onsbue),s.addEventListener("error",this.onsbe),this.tracks[a]={codec:n,container:i.container},i.buffer=s}catch(e){ee.b.error("error while trying to add sourceBuffer:"+e.message),this.hls.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.BUFFER_ADD_CODEC_ERROR,fatal:!1,err:e,mimeType:o})}}this.hls.trigger(Z.a.BUFFER_CREATED,{tracks:e})},t.prototype.onBufferAppending=function(e){this._needsFlush||(this.segments?this.segments.push(e):this.segments=[e],this.doAppending())},t.prototype.onBufferAppendFail=function(e){ee.b.error("sourceBuffer error:",e.event),this.hls.trigger(Z.a.ERROR,{type:$.b.MEDIA_ERROR,details:$.a.BUFFER_APPENDING_ERROR,fatal:!1})},t.prototype.onBufferEos=function(e){var t=this.sourceBuffer,r=e.type;for(var a in t)r&&a!==r||t[a].ended||(t[a].ended=!0,ee.b.log(a+" sourceBuffer now EOS"));this.checkEos()},t.prototype.checkEos=function(){var e=this.sourceBuffer,t=this.mediaSource;if(!t||"open"!==t.readyState)return void(this._needsEos=!1);for(var r in e){var a=e[r];if(!a.ended)return;if(a.updating)return void(this._needsEos=!0)}ee.b.log("all media data available, signal endOfStream() to MediaSource and stop loading fragment");try{t.endOfStream()}catch(e){ee.b.warn("exception while calling mediaSource.endOfStream()")}this._needsEos=!1},t.prototype.onBufferFlushing=function(e){this.flushRange.push({start:e.startOffset,end:e.endOffset,type:e.type}),this.flushBufferCounter=0,this.doFlush()},t.prototype.onLevelUpdated=function(e){var t=e.details;0!==t.fragments.length&&(this._levelDuration=t.totalduration+t.fragments[0].start,this.updateMediaElementDuration())},t.prototype.updateMediaElementDuration=function(){var e=this.media,t=this.mediaSource,r=this.sourceBuffer,a=this._levelDuration;if(null!==a&&e&&t&&r&&0!==e.readyState&&"open"===t.readyState){for(var i in r)if(r[i].updating)return;null===this._msDuration&&(this._msDuration=t.duration);var n=e.duration;(a>this._msDuration&&a>n||n===1/0||isNaN(n))&&(ee.b.log("Updating mediasource duration to "+a.toFixed(3)),this._msDuration=t.duration=a)}},t.prototype.doFlush=function(){for(;this.flushRange.length;){var e=this.flushRange[0];if(!this.flushBuffer(e.start,e.end,e.type))return void(this._needsFlush=!0);this.flushRange.shift(),this.flushBufferCounter=0}if(0===this.flushRange.length){this._needsFlush=!1;var t=0,r=this.sourceBuffer;try{for(var a in r)t+=r[a].buffered.length}catch(e){ee.b.error("error while accessing sourceBuffer.buffered")}this.appended=t,this.hls.trigger(Z.a.BUFFER_FLUSHED)}},t.prototype.doAppending=function(){var e=this.hls,t=this.sourceBuffer,r=this.segments;if(Object.keys(t).length){if(this.media.error)return this.segments=[],void ee.b.error("trying to append although a media error occured, flush segment and abort");if(this.appending)return;if(r&&r.length){var a=r.shift();try{var i=a.type,n=t[i];n?n.updating?r.unshift(a):(n.ended=!1,this.parent=a.parent,n.appendBuffer(a.data),this.appendError=0,this.appended++,this.appending=!0):this.onSBUpdateEnd()}catch(t){ee.b.error("error while trying to append buffer:"+t.message),r.unshift(a);var o={type:$.b.MEDIA_ERROR,parent:a.parent};if(22===t.code)return this.segments=[],o.details=$.a.BUFFER_FULL_ERROR,o.fatal=!1,void e.trigger(Z.a.ERROR,o);if(this.appendError?this.appendError++:this.appendError=1,o.details=$.a.BUFFER_APPEND_ERROR,this.appendError>e.config.appendErrorMaxRetry)return ee.b.log("fail "+e.config.appendErrorMaxRetry+" times to append segment in sourceBuffer"),r=[],o.fatal=!0,void e.trigger(Z.a.ERROR,o);o.fatal=!1,e.trigger(Z.a.ERROR,o)}}}},t.prototype.flushBuffer=function(e,t,r){var a,i,n,o,s,l,d=this.sourceBuffer;if(Object.keys(d).length){if(ee.b.log("flushBuffer,pos/start/end: "+this.media.currentTime.toFixed(3)+"/"+e+"/"+t),this.flushBufferCounter<this.appended){for(var u in d)if(!r||u===r){if(a=d[u],a.ended=!1,a.updating)return ee.b.warn("cannot flush, sb updating in progress"),!1;try{for(i=0;i<a.buffered.length;i++)if(n=a.buffered.start(i),o=a.buffered.end(i),navigator.userAgent.toLowerCase().indexOf("firefox")!==-1&&t===Number.POSITIVE_INFINITY?(s=e,l=t):(s=Math.max(n,e),l=Math.min(o,t)),Math.min(l,o)-s>.5)return this.flushBufferCounter++,ee.b.log("flush "+u+" ["+s+","+l+"], of ["+n+","+o+"], pos:"+this.media.currentTime),a.remove(s,l),!1}catch(e){ee.b.warn("exception while accessing sourcebuffer, it might have been removed from MediaSource")}}}else ee.b.warn("abort flushing too many retries");ee.b.log("buffer flushed")}return!0},t}(ae),$e=Ze,et=function(){function e(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,r,a){return r&&e(t.prototype,r),a&&e(t,a),t}}(),tt=function(e){function t(r){return j(this,t),W(this,e.call(this,r,Z.a.FPS_DROP_LEVEL_CAPPING,Z.a.MEDIA_ATTACHING,Z.a.MANIFEST_PARSED))}return V(t,e),t.prototype.destroy=function(){this.hls.config.capLevelToPlayerSize&&(this.media=this.restrictedLevels=null,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(this.timer=clearInterval(this.timer)))},t.prototype.onFpsDropLevelCapping=function(e){t.isLevelAllowed(e.droppedLevel,this.restrictedLevels)&&this.restrictedLevels.push(e.droppedLevel)},t.prototype.onMediaAttaching=function(e){this.media=e.media instanceof HTMLVideoElement?e.media:null},t.prototype.onManifestParsed=function(e){var t=this.hls;this.restrictedLevels=[],t.config.capLevelToPlayerSize&&(this.autoLevelCapping=Number.POSITIVE_INFINITY,this.levels=e.levels,t.firstLevel=this.getMaxLevel(e.firstLevel),clearInterval(this.timer),this.timer=setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())},t.prototype.detectPlayerSize=function(){if(this.media){var e=this.levels?this.levels.length:0;if(e){var t=this.hls;t.autoLevelCapping=this.getMaxLevel(e-1),t.autoLevelCapping>this.autoLevelCapping&&t.streamController.nextLevelSwitch(),this.autoLevelCapping=t.autoLevelCapping}}},t.prototype.getMaxLevel=function(e){var r=this;if(!this.levels)return-1;var a=this.levels.filter(function(a,i){return t.isLevelAllowed(i,r.restrictedLevels)&&i<=e});return t.getMaxLevelByMediaSize(a,this.mediaWidth,this.mediaHeight)},t.isLevelAllowed=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[];return t.indexOf(e)===-1},t.getMaxLevelByMediaSize=function(e,t,r){if(!e||e&&!e.length)return-1;for(var a=function(e,t){return!t||(e.width!==t.width||e.height!==t.height)},i=e.length-1,n=0;n<e.length;n+=1){var o=e[n];if((o.width>=t||o.height>=r)&&a(o,e[n+1])){i=n;break}}return i},et(t,[{key:"mediaWidth",get:function(){var e=void 0,r=this.media;return r&&(e=r.width||r.clientWidth||r.offsetWidth,e*=t.contentScaleFactor),e}},{key:"mediaHeight",get:function(){var e=void 0,r=this.media;return r&&(e=r.height||r.clientHeight||r.offsetHeight,e*=t.contentScaleFactor),e}}],[{key:"contentScaleFactor",get:function(){var e=1;try{e=window.devicePixelRatio}catch(e){}return e}}]),t}(ae),rt=tt,at=function(e){function t(r){return K(this,t),Y(this,e.call(this,r,Z.a.MEDIA_ATTACHING))}return z(t,e),t.prototype.destroy=function(){this.timer&&clearInterval(this.timer),this.isVideoPlaybackQualityAvailable=!1},t.prototype.onMediaAttaching=function(e){var t=this.hls.config;if(t.capLevelOnFPSDrop){var r=this.video=e.media instanceof HTMLVideoElement?e.media:null;"function"==typeof r.getVideoPlaybackQuality&&(this.isVideoPlaybackQualityAvailable=!0),clearInterval(this.timer),this.timer=setInterval(this.checkFPSInterval.bind(this),t.fpsDroppedMonitoringPeriod)}},t.prototype.checkFPS=function(e,t,r){var a=performance.now();if(t){if(this.lastTime){var i=a-this.lastTime,n=r-this.lastDroppedFrames,o=t-this.lastDecodedFrames,s=1e3*n/i,l=this.hls;if(l.trigger(Z.a.FPS_DROP,{currentDropped:n,currentDecoded:o,totalDroppedFrames:r}),s>0&&n>l.config.fpsDroppedMonitoringThreshold*o){var d=l.currentLevel;ee.b.warn("drop FPS ratio greater than max allowed value for currentLevel: "+d),d>0&&(l.autoLevelCapping===-1||l.autoLevelCapping>=d)&&(d-=1,l.trigger(Z.a.FPS_DROP_LEVEL_CAPPING,{level:d,droppedLevel:l.currentLevel}),l.autoLevelCapping=d,l.streamController.nextLevelSwitch())}}this.lastTime=a,this.lastDroppedFrames=r,this.lastDecodedFrames=t}},t.prototype.checkFPSInterval=function(){var e=this.video;if(e)if(this.isVideoPlaybackQualityAvailable){var t=e.getVideoPlaybackQuality();this.checkFPS(e,t.totalVideoFrames,t.droppedVideoFrames)}else this.checkFPS(e,e.webkitDecodedFrameCount,e.webkitDroppedFrameCount)},t}(ae),it=at,nt=function(){function e(t){X(this,e),t&&t.xhrSetup&&(this.xhrSetup=t.xhrSetup)}return e.prototype.destroy=function(){this.abort(),this.loader=null},e.prototype.abort=function(){var e=this.loader;e&&4!==e.readyState&&(this.stats.aborted=!0,e.abort()),window.clearTimeout(this.requestTimeout),this.requestTimeout=null,window.clearTimeout(this.retryTimeout),this.retryTimeout=null},e.prototype.load=function(e,t,r){this.context=e,this.config=t,this.callbacks=r,this.stats={trequest:performance.now(),retry:0},this.retryDelay=t.retryDelay,this.loadInternal()},e.prototype.loadInternal=function(){var e,t=this.context;e=this.loader=new XMLHttpRequest;var r=this.stats;r.tfirst=0,r.loaded=0;var a=this.xhrSetup;try{if(a)try{a(e,t.url)}catch(r){e.open("GET",t.url,!0),a(e,t.url)}e.readyState||e.open("GET",t.url,!0)}catch(r){return void this.callbacks.onError({code:e.status,text:r.message},t,e)}t.rangeEnd&&e.setRequestHeader("Range","bytes="+t.rangeStart+"-"+(t.rangeEnd-1)),e.onreadystatechange=this.readystatechange.bind(this),e.onprogress=this.loadprogress.bind(this),e.responseType=t.responseType,this.requestTimeout=window.setTimeout(this.loadtimeout.bind(this),this.config.timeout),e.send()},e.prototype.readystatechange=function(e){var t=e.currentTarget,r=t.readyState,a=this.stats,i=this.context,n=this.config;if(!a.aborted&&r>=2)if(window.clearTimeout(this.requestTimeout),0===a.tfirst&&(a.tfirst=Math.max(performance.now(),a.trequest)),4===r){var o=t.status;if(o>=200&&o<300){a.tload=Math.max(a.tfirst,performance.now());var s=void 0,l=void 0;"arraybuffer"===i.responseType?(s=t.response,l=s.byteLength):(s=t.responseText,l=s.length),a.loaded=a.total=l;var d={url:t.responseURL,data:s};this.callbacks.onSuccess(d,a,i,t)}else a.retry>=n.maxRetry||o>=400&&o<499?(ee.b.error(o+" while loading "+i.url),this.callbacks.onError({code:o,text:t.statusText},i,t)):(ee.b.warn(o+" while loading "+i.url+", retrying in "+this.retryDelay+"..."),this.destroy(),this.retryTimeout=window.setTimeout(this.loadInternal.bind(this),this.retryDelay),this.retryDelay=Math.min(2*this.retryDelay,n.maxRetryDelay),a.retry++)}else this.requestTimeout=window.setTimeout(this.loadtimeout.bind(this),n.timeout)},e.prototype.loadtimeout=function(){ee.b.warn("timeout while loading "+this.context.url),this.callbacks.onTimeout(this.stats,this.context,null)},e.prototype.loadprogress=function(e){var t=e.currentTarget,r=this.stats;r.loaded=e.loaded,e.lengthComputable&&(r.total=e.total);var a=this.callbacks.onProgress;a&&a(r,this.context,null,t)},e}(),ot=nt,st=r(3),lt=(r.n(st),{autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,initialLiveManifestSize:1,maxBufferLength:30,maxBufferSize:6e7,maxBufferHole:.5,maxSeekHole:2,lowBufferWatchdogPeriod:.5,highBufferWatchdogPeriod:3,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.2,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxMaxBufferLength:600,enableWorker:!0,enableSoftwareAES:!0,manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,startLevel:void 0,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3,fragLoadingLoopThreshold:3,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:ot,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,fetchSetup:void 0,abrController:Je,bufferController:$e,capLevelController:rt,fpsController:it,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0}),dt=function(){function e(e,t){for(var r=0;r<t.length;r++){var a=t[r];a.enumerable=a.enumerable||!1,a.configurable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(e,a.key,a)}}return function(t,r,a){return r&&e(t.prototype,r),a&&e(t,a),t}}(),ut=function(){function e(){var t=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};q(this,e);var a=e.DefaultConfig;if((r.liveSyncDurationCount||r.liveMaxLatencyDurationCount)&&(r.liveSyncDuration||r.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");for(var i in a)i in r||(r[i]=a[i]);if(void 0!==r.liveMaxLatencyDurationCount&&r.liveMaxLatencyDurationCount<=r.liveSyncDurationCount)throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be gt "liveSyncDurationCount"');if(void 0!==r.liveMaxLatencyDuration&&(r.liveMaxLatencyDuration<=r.liveSyncDuration||void 0===r.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be gt "liveSyncDuration"');Object(ee.a)(r.debug),this.config=r,this._autoLevelCapping=-1;var n=this.observer=new De.a;n.trigger=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];n.emit.apply(n,[e,e].concat(r))},n.off=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),a=1;a<t;a++)r[a-1]=arguments[a];n.removeListener.apply(n,[e].concat(r))},this.on=n.on.bind(n),this.off=n.off.bind(n),this.trigger=n.trigger.bind(n);var o=this.abrController=new r.abrController(this),s=new r.bufferController(this),l=new r.capLevelController(this),d=new r.fpsController(this),u=new me(this),f=new be(this),h=new Re(this),c=new Ve(this),p=this.levelController=new He(this),v=this.streamController=new Ue(this),g=[p,v],m=r.audioStreamController;m&&g.push(new m(this)),this.networkControllers=g;var y=[u,f,h,o,s,l,d,c];if(m=r.audioTrackController){var b=new m(this);this.audioTrackController=b,y.push(b)}if(m=r.subtitleTrackController){var E=new m(this);this.subtitleTrackController=E,y.push(E)}[r.subtitleStreamController,r.timelineController].forEach(function(e){e&&y.push(new e(t))}),this.coreComponents=y}return e.isSupported=function(){var e=window.MediaSource=window.MediaSource||window.WebKitMediaSource,t=window.SourceBuffer=window.SourceBuffer||window.WebKitSourceBuffer,r=e&&"function"==typeof e.isTypeSupported&&e.isTypeSupported('video/mp4; codecs="avc1.42E01E,mp4a.40.2"'),a=!t||t.prototype&&"function"==typeof t.prototype.appendBuffer&&"function"==typeof t.prototype.remove;return r&&a},dt(e,null,[{key:"version",get:function(){return"0.8.4"}},{key:"Events",get:function(){return Z.a}},{key:"ErrorTypes",get:function(){return $.b}},{key:"ErrorDetails",get:function(){return $.a}},{key:"DefaultConfig",get:function(){return e.defaultConfig?e.defaultConfig:lt},set:function(t){e.defaultConfig=t}}]),e.prototype.destroy=function(){ee.b.log("destroy"),this.trigger(Z.a.DESTROYING),this.detachMedia(),this.coreComponents.concat(this.networkControllers).forEach(function(e){e.destroy()}),this.url=null,this.observer.removeAllListeners(),this._autoLevelCapping=-1},e.prototype.attachMedia=function(e){ee.b.log("attachMedia"),this.media=e,this.trigger(Z.a.MEDIA_ATTACHING,{media:e})},e.prototype.detachMedia=function(){ee.b.log("detachMedia"),this.trigger(Z.a.MEDIA_DETACHING),this.media=null},e.prototype.loadSource=function(e){e=J.a.buildAbsoluteURL(window.location.href,e,{alwaysNormalize:!0}),ee.b.log("loadSource:"+e),this.url=e,this.trigger(Z.a.MANIFEST_LOADING,{url:e})},e.prototype.startLoad=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:-1;ee.b.log("startLoad("+e+")"),this.networkControllers.forEach(function(t){t.startLoad(e)})},e.prototype.stopLoad=function(){ee.b.log("stopLoad"),this.networkControllers.forEach(function(e){e.stopLoad()})},e.prototype.swapAudioCodec=function(){ee.b.log("swapAudioCodec"),this.streamController.swapAudioCodec()},e.prototype.recoverMediaError=function(){ee.b.log("recoverMediaError");var e=this.media;this.detachMedia(),this.attachMedia(e)},dt(e,[{key:"levels",get:function(){return this.levelController.levels}},{key:"currentLevel",get:function(){return this.streamController.currentLevel},set:function(e){ee.b.log("set currentLevel:"+e),this.loadLevel=e,this.streamController.immediateLevelSwitch()}},{key:"nextLevel",get:function(){return this.streamController.nextLevel},set:function(e){ee.b.log("set nextLevel:"+e),this.levelController.manualLevel=e,this.streamController.nextLevelSwitch()}},{key:"loadLevel",get:function(){return this.levelController.level},set:function(e){ee.b.log("set loadLevel:"+e),this.levelController.manualLevel=e}},{key:"nextLoadLevel",get:function(){return this.levelController.nextLoadLevel},set:function(e){this.levelController.nextLoadLevel=e}},{key:"firstLevel",get:function(){return Math.max(this.levelController.firstLevel,this.minAutoLevel);
},set:function(e){ee.b.log("set firstLevel:"+e),this.levelController.firstLevel=e}},{key:"startLevel",get:function(){return this.levelController.startLevel},set:function(e){ee.b.log("set startLevel:"+e);var t=this;e!==-1&&(e=Math.max(e,t.minAutoLevel)),t.levelController.startLevel=e}},{key:"autoLevelCapping",get:function(){return this._autoLevelCapping},set:function(e){ee.b.log("set autoLevelCapping:"+e),this._autoLevelCapping=e}},{key:"autoLevelEnabled",get:function(){return this.levelController.manualLevel===-1}},{key:"manualLevel",get:function(){return this.levelController.manualLevel}},{key:"minAutoLevel",get:function(){for(var e=this,t=e.levels,r=e.config.minAutoBitrate,a=t?t.length:0,i=0;i<a;i++){var n=t[i].realBitrate?Math.max(t[i].realBitrate,t[i].bitrate):t[i].bitrate;if(n>r)return i}return 0}},{key:"maxAutoLevel",get:function(){var e=this,t=e.levels,r=e.autoLevelCapping,a=void 0;return a=r===-1&&t&&t.length?t.length-1:r}},{key:"nextAutoLevel",get:function(){var e=this;return Math.min(Math.max(e.abrController.nextAutoLevel,e.minAutoLevel),e.maxAutoLevel)},set:function(e){var t=this;t.abrController.nextAutoLevel=Math.max(t.minAutoLevel,e)}},{key:"audioTracks",get:function(){var e=this.audioTrackController;return e?e.audioTracks:[]}},{key:"audioTrack",get:function(){var e=this.audioTrackController;return e?e.audioTrack:-1},set:function(e){var t=this.audioTrackController;t&&(t.audioTrack=e)}},{key:"liveSyncPosition",get:function(){return this.streamController.liveSyncPosition}},{key:"subtitleTracks",get:function(){var e=this.subtitleTrackController;return e?e.subtitleTracks:[]}},{key:"subtitleTrack",get:function(){var e=this.subtitleTrackController;return e?e.subtitleTrack:-1},set:function(e){var t=this.subtitleTrackController;t&&(t.subtitleTrack=e)}}]),e}();t.default=ut},function(e,t,r){function a(e){function t(a){if(r[a])return r[a].exports;var i=r[a]={i:a,l:!1,exports:{}};return e[a].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var r={};t.m=e,t.c=r,t.i=function(e){return e},t.d=function(e,r,a){t.o(e,r)||Object.defineProperty(e,r,{configurable:!1,enumerable:!0,get:a})},t.n=function(e){var r=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(r,"a",r),r},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/",t.oe=function(e){throw console.error(e),e};var a=t(t.s=ENTRY_MODULE);return a.default||a}function i(e){return(e+"").replace(/[.?*+^$[\]\\(){}|-]/g,"\\$&")}function n(e){var t=[],r=e.toString(),a=r.match(/^function\s?\(\w+,\s*\w+,\s*(\w+)\)/);if(!a)return t;for(var n,o=a[1],s=new RegExp("(\\\\n|\\W)"+i(o)+"\\((/\\*.*?\\*/)?s?.*?([\\.|\\-|\\w|/|@]+).*?\\)","g");n=s.exec(r);)t.push(n[3]);return t}function o(e,t){for(var r=[t],a=[],i={};r.length;){var o=r.pop();if(!i[o]&&e[o]){i[o]=!0,a.push(o);var s=n(e[o]);r=r.concat(s)}}return a}e.exports=function(e,t){t=t||{};var i=r.m,n=t.all?Object.keys(i):o(i,e),s="("+a.toString().replace("ENTRY_MODULE",JSON.stringify(e))+")({"+n.map(function(e){return""+JSON.stringify(e)+": "+i[e].toString()}).join(",")+"})(self);",l=new window.Blob([s],{type:"text/javascript"});if(t.bare)return l;var d=window.URL||window.webkitURL||window.mozURL||window.msURL,u=d.createObjectURL(l),f=new window.Worker(u);return f.objectURL=u,f}},function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0});var a=r(7),i=r(1),n=r(0),o=r(5),s=r.n(o),l=function(e){var t=new s.a;t.trigger=function(e){for(var r=arguments.length,a=Array(r>1?r-1:0),i=1;i<r;i++)a[i-1]=arguments[i];t.emit.apply(t,[e,e].concat(a))},t.off=function(e){for(var r=arguments.length,a=Array(r>1?r-1:0),i=1;i<r;i++)a[i-1]=arguments[i];t.removeListener.apply(t,[e].concat(a))};var r=function(t,r){e.postMessage({event:t,data:r})};e.addEventListener("message",function(i){var o=i.data;switch(o.cmd){case"init":var s=JSON.parse(o.config);e.demuxer=new a.a(t,o.typeSupported,s,o.vendor);try{Object(n.a)(s.debug===!0)}catch(e){console.warn("demuxerWorker: unable to enable logs")}r("init",null);break;case"demux":e.demuxer.push(o.data,o.decryptdata,o.initSegment,o.audioCodec,o.videoCodec,o.timeOffset,o.discontinuity,o.trackSwitch,o.contiguous,o.duration,o.accurateTimeOffset,o.defaultInitPTS)}}),t.on(i.a.FRAG_DECRYPTED,r),t.on(i.a.FRAG_PARSING_INIT_SEGMENT,r),t.on(i.a.FRAG_PARSED,r),t.on(i.a.ERROR,r),t.on(i.a.FRAG_PARSING_METADATA,r),t.on(i.a.FRAG_PARSING_USERDATA,r),t.on(i.a.INIT_PTS_FOUND,r),t.on(i.a.FRAG_PARSING_DATA,function(t,r){var a=[],i={event:t,data:r};r.data1&&(i.data1=r.data1.buffer,a.push(r.data1.buffer),delete r.data1),r.data2&&(i.data2=r.data2.buffer,a.push(r.data2.buffer),delete r.data2),e.postMessage(i,a)})};t.default=l}]).default})},function(e,t,r){var a=r(1);a(flowplayer)}]);
/*@
  @end
@*/
