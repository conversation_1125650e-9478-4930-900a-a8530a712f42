/*!

   Flowplayer v7.2.7 (Monday, 13. August 2018 08:40AM) | flowplayer.com/license

*/
!function(a){function u(e,t,n,i){for(var r,o=n.slice(),a=(c=e,(u=t).currentTarget=c,u.eventPhase=u.target===u.currentTarget?2:3,u),s=0,l=o.length;s<l&&(handler=o[s],"object"==typeof handler&&"function"==typeof handler.handleEvent?handler.handleEvent(a):handler.call(e,a),!a.stoppedImmediatePropagation);s++);var u,c;return r=!a.stoppedPropagation,i&&r&&e.parentNode?e.parentNode.dispatchEvent(a):!a.defaultPrevented}function r(e,t){return{configurable:!0,get:e,set:t}}function e(e,t,n){var i=y(t||e,n);m(e,"textContent",r(function(){return i.get.call(this)},function(e){i.set.call(this,e)}))}function c(e,t){for(var n=e.length;n--&&e[n]!==t;);return n}function t(){if("BR"===this.tagName)return"\n";for(var e=this.firstChild,t=[];e;)8!==e.nodeType&&7!==e.nodeType&&t.push(e.textContent),e=e.nextSibling;return t.join("")}function o(e){!l&&C.test(document.readyState)&&(l=!l,document.detachEvent(d,o),(e=document.createEvent("Event")).initEvent(p,!0,!0),document.dispatchEvent(e))}function n(e){for(var t;t=this.lastChild;)this.removeChild(t);null!=e&&this.appendChild(document.createTextNode(e))}function f(e,t){return t||(t=a.event),t.target||(t.target=t.srcElement||t.fromElement||document),t.timeStamp||(t.timeStamp=(new Date).getTime()),t}if(!document.createEvent){var i,s=!0,l=!1,d="onreadystatechange",p="DOMContentLoaded",h="__IE8__"+Math.random(),g=a.Object,m=g.defineProperty||function(e,t,n){e[t]=n.value},v=g.defineProperties||function(t,e){for(var n in e)if(w.call(e,n))try{m(t,n,e[n])}catch(e){a.console&&console.log(n+" failed on object:",t,e.message)}},y=g.getOwnPropertyDescriptor,w=g.prototype.hasOwnProperty,b=a.Element.prototype,I=a.Text.prototype,M=/^[a-z]+$/,C=/loaded|complete/,A={},S=document.createElement("div");e(a.HTMLCommentElement.prototype,b,"nodeValue"),e(a.HTMLScriptElement.prototype,null,"text"),e(I,null,"nodeValue"),e(a.HTMLTitleElement.prototype,null,"text"),m(a.HTMLStyleElement.prototype,"textContent",(i=y(a.CSSStyleSheet.prototype,"cssText"),r(function(){return i.get.call(this.styleSheet)},function(e){i.set.call(this.styleSheet,e)}))),v(b,{textContent:{get:t,set:n},firstElementChild:{get:function(){for(var e=this.childNodes||[],t=0,n=e.length;t<n;t++)if(1==e[t].nodeType)return e[t]}},lastElementChild:{get:function(){for(var e=this.childNodes||[],t=e.length;t--;)if(1==e[t].nodeType)return e[t]}},previousElementSibling:{get:function(){for(var e=this.previousSibling;e&&1!=e.nodeType;)e=e.previousSibling;return e}},nextElementSibling:{get:function(){for(var e=this.nextSibling;e&&1!=e.nodeType;)e=e.nextSibling;return e}},childElementCount:{get:function(){for(var e=0,t=this.childNodes||[],n=t.length;n--;e+=1==t[n].nodeType);return e}},addEventListener:{value:function(e,t,n){var i,r=this,o="on"+e,a=r[h]||m(r,h,{value:{}})[h],s=a[o]||(a[o]={}),l=s.h||(s.h=[]);if(!w.call(s,"w")){if(s.w=function(e){return e[h]||u(r,f(0,e),l,!1)},!w.call(A,o))if(M.test(e))try{(i=document.createEventObject())[h]=!0,9!=r.nodeType&&null==r.parentNode&&S.appendChild(r),r.fireEvent(o,i),A[o]=!0}catch(i){for(A[o]=!1;S.hasChildNodes();)S.removeChild(S.firstChild)}else A[o]=!1;(s.n=A[o])&&r.attachEvent(o,s.w)}c(l,t)<0&&l[n?"unshift":"push"](t)}},dispatchEvent:{value:function(e){var t,n=this,i="on"+e.type,r=n[h],o=r&&r[i],a=!!o;return e.target||(e.target=n),a?o.n?n.fireEvent(i,e):u(n,e,o.h,!0):!(t=n.parentNode)||t.dispatchEvent(e),!e.defaultPrevented}},removeEventListener:{value:function(e,t,n){var i="on"+e,r=this[h],o=r&&r[i],a=o&&o.h,s=a?c(a,t):-1;-1<s&&a.splice(s,1)}}}),v(I,{addEventListener:{value:b.addEventListener},dispatchEvent:{value:b.dispatchEvent},removeEventListener:{value:b.removeEventListener}}),v(a.XMLHttpRequest.prototype,{addEventListener:{value:function(t,e,n){var i=this,r="on"+t,o=i[h]||m(i,h,{value:{}})[h],a=o[r]||(o[r]={}),s=a.h||(a.h=[]);c(s,e)<0&&(i[r]||(i[r]=function(){var e=document.createEvent("Event");e.initEvent(t,!0,!0),i.dispatchEvent(e)}),s[n?"unshift":"push"](e))}},dispatchEvent:{value:function(e){var t="on"+e.type,n=this[h],i=n&&n[t];return!!i&&(i.n?this.fireEvent(t,e):u(this,e,i.h,!0))}},removeEventListener:{value:b.removeEventListener}}),v(a.Event.prototype,{bubbles:{value:!0,writable:!0},cancelable:{value:!0,writable:!0},preventDefault:{value:function(){this.cancelable&&(this.defaultPrevented=!0,this.returnValue=!1)}},stopPropagation:{value:function(){this.stoppedPropagation=!0,this.cancelBubble=!0}},stopImmediatePropagation:{value:function(){this.stoppedImmediatePropagation=!0,this.stopPropagation()}},initEvent:{value:function(e,t,n){this.type=e,this.bubbles=!!t,this.cancelable=!!n,this.bubbles||this.stopPropagation()}}}),v(a.HTMLDocument.prototype,{textContent:{get:function(){return 11===this.nodeType?t.call(this):null},set:function(e){11===this.nodeType&&n.call(this,e)}},addEventListener:{value:function(e,t,n){var i=this;b.addEventListener.call(i,e,t,n),s&&e===p&&!C.test(i.readyState)&&(s=!1,i.attachEvent(d,o),a==top&&function t(e){try{i.documentElement.doScroll("left"),o()}catch(e){setTimeout(t,50)}}())}},dispatchEvent:{value:b.dispatchEvent},removeEventListener:{value:b.removeEventListener},createEvent:{value:function(e){var t;if("Event"!==e)throw new Error("unsupported "+e);return(t=document.createEventObject()).timeStamp=(new Date).getTime(),t}}}),v(a.Window.prototype,{getComputedStyle:{value:function(){function n(e){this._=e}function i(){}var l=/^(?:[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|))(?!px)[a-z%]+$/,u=/^(top|right|bottom|left)$/,c=/\-([a-z])/g,f=function(e,t){return t.toUpperCase()};return n.prototype.getPropertyValue=function(e){var t,n,i,r=this._,o=r.style,a=r.currentStyle,s=r.runtimeStyle;return e=("float"===e?"style-float":e).replace(c,f),t=a?a[e]:o[e],l.test(t)&&!u.test(e)&&(n=o.left,(i=s&&s.left)&&(s.left=a.left),o.left="fontSize"===e?"1em":t,t=o.pixelLeft+"px",o.left=n,i&&(s.left=i)),null==t?t:t+""||"auto"},i.prototype.getPropertyValue=function(){return null},function(e,t){return t?new i(e):new n(e)}}()},addEventListener:{value:function(e,t,n){var i,r=a,o="on"+e;r[o]||(r[o]=function(e){return u(r,f(0,e),i,!1)}),c(i=r[o][h]||(r[o][h]=[]),t)<0&&i[n?"unshift":"push"](t)}},dispatchEvent:{value:function(e){var t=a["on"+e.type];return!t||!1!==t.call(a,e)&&!e.defaultPrevented}},removeEventListener:{value:function(e,t,n){var i=(a["on"+e]||g)[h],r=i?c(i,t):-1;-1<r&&i.splice(r,1)}}})}}(this),function(e){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=e();else if("function"==typeof define&&define.amd)define([],e);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).flowplayer=e()}}(function(){return function o(a,s,l){function u(t,e){if(!s[t]){if(!a[t]){var n="function"==typeof require&&require;if(!e&&n)return n(t,!0);if(c)return c(t,!0);var i=new Error("Cannot find module '"+t+"'");throw i.code="MODULE_NOT_FOUND",i}var r=s[t]={exports:{}};a[t][0].call(r.exports,function(e){return u(a[t][1][e]||e)},r,r.exports,o,a,s,l)}return s[t].exports}for(var c="function"==typeof require&&require,e=0;e<l.length;e++)u(l[e]);return u}({1:[function(e,t,n){"use strict";var o=t.exports={},r=e("class-list"),a=window.jQuery,i=e("punycode"),s=e("computed-style");o.noop=function(){},o.identity=function(e){return e},o.removeNode=function(e){e&&e.parentNode&&e.parentNode.removeChild(e)},o.find=function(e,t){return a?a(e,t).toArray():(t=t||document,Array.prototype.map.call(t.querySelectorAll(e),function(e){return e}))},o.text=function(e,t){e["innerText"in e?"innerText":"textContent"]=t},o.findDirect=function(e,t){return o.find(e,t).filter(function(e){return e.parentNode===t})},o.hasClass=function(e,t){return"string"==typeof e.className&&r(e).contains(t)},o.isSameDomain=function(e){var t=window.location,n=o.createElement("a",{href:e});return t.hostname===n.hostname&&t.protocol===n.protocol&&t.port===n.port},o.css=function(t,n,e){return"object"==typeof n?Object.keys(n).forEach(function(e){o.css(t,e,n[e])}):void 0!==e?""===e?t?t.style.removeProperty(n):void 0:t?t.style.setProperty(n,e):void 0:t?s(t,n):void 0},o.createElement=function(t,n,i){try{var e=document.createElement(t);for(var r in n)n.hasOwnProperty(r)&&("css"===r?o.css(e,n[r]):o.attr(e,r,n[r]));return i&&(e.innerHTML=i),e}catch(e){if(!a)throw e;return a("<"+t+">"+i+"</"+t+">").attr(n)[0]}},o.toggleClass=function(e,t,n){if(e){var i=r(e);void 0===n?i.toggle(t):n?i.add(t):n||i.remove(t)}},o.addClass=function(e,t){return o.toggleClass(e,t,!0)},o.removeClass=function(e,t){return o.toggleClass(e,t,!1)},o.append=function(e,t){return e.appendChild(t),e},o.appendTo=function(e,t){return o.append(t,e),e},o.prepend=function(e,t){e.insertBefore(t,e.firstChild)},o.insertAfter=function(e,t,n){t==o.lastChild(e)&&e.appendChild(n);var i=Array.prototype.indexOf.call(e.children,t);e.insertBefore(n,e.children[i+1])},o.html=function(e,t){(e=e.length?e:[e]).forEach(function(e){e.innerHTML=t})},o.attr=function(t,n,i){if("class"===n&&(n="className"),o.hasOwnOrPrototypeProperty(t,n))try{t[n]=i}catch(e){if(!a)throw e;a(t).attr(n,i)}else!1===i?t.removeAttribute(n):t.setAttribute(n,i);return t},o.prop=function(e,t,n){if(void 0===n)return e&&e[t];e[t]=n},o.offset=function(e){var t=e.getBoundingClientRect();return e.offsetWidth/e.offsetHeight>e.clientWidth/e.clientHeight&&(t={left:100*t.left,right:100*t.right,top:100*t.top,bottom:100*t.bottom,width:100*t.width,height:100*t.height}),t},o.width=function(e,t){if(t)return e.style.width=(""+t).replace(/px$/,"")+"px";var n=o.offset(e).width;return void 0===n?e.offsetWidth:n},o.height=function(e,t){if(t)return e.style.height=(""+t).replace(/px$/,"")+"px";var n=o.offset(e).height;return void 0===n?e.offsetHeight:n},o.lastChild=function(e){return e.children[e.children.length-1]},o.hasParent=function(e,t){for(var n=e.parentElement;n;){if("string"!=typeof t){if(n===t)return!0}else if(o.matches(n,t))return!0;n=n.parentElement}return!1},o.createAbsoluteUrl=function(e){return o.createElement("a",{href:e}).href},o.xhrGet=function(e,t,n){var i=new XMLHttpRequest;i.onreadystatechange=function(){if(4===this.readyState)return 400<=this.status?n():void t(this.responseText)},i.open("get",e,!0),i.send()},o.pick=function(t,e){var n={};return e.forEach(function(e){t.hasOwnProperty(e)&&(n[e]=t[e])}),n},o.hostname=function(e){return i.toUnicode(e||window.location.hostname)},o.browser={webkit:"WebkitAppearance"in document.documentElement.style},o.getPrototype=function(e){return Object.getPrototypeOf?Object.getPrototypeOf(e):e.__proto__},o.hasOwnOrPrototypeProperty=function(e,t){for(var n=e;n;){if(Object.prototype.hasOwnProperty.call(n,t))return!0;n=o.getPrototype(n)}return!1},o.matches=function(e,t){var n=Element.prototype;return(n.matches||n.matchesSelector||n.mozMatchesSelector||n.msMatchesSelector||n.oMatchesSelector||n.webkitMatchesSelector||function(e){for(var t=(this.document||this.ownerDocument).querySelectorAll(e),n=0;t[n]&&t[n]!==this;)n++;return!!t[n]}).call(e,t)},function(e){function n(e){return e.replace(/-[a-z]/g,function(e){return e[1].toUpperCase()})}void 0!==e.setAttribute&&(e.setProperty=function(e,t){return this.setAttribute(n(e),String(t))},e.getPropertyValue=function(e){return this.getAttribute(n(e))||null},e.removeProperty=function(e){var t=this.getPropertyValue(e);return this.removeAttribute(n(e)),t})}(window.CSSStyleDeclaration.prototype)},{"class-list":36,"computed-style":37,punycode:44}],2:[function(e,t,n){"use strict";var u=e("../common");t.exports=function(e,t,n,i){n=n||"opaque";var r="obj"+(""+Math.random()).slice(2,15),o='<object class="fp-engine" id="'+r+'" name="'+r+'" ',a=-1<navigator.userAgent.indexOf("MSIE");o+=a?'classid="clsid:D27CDB6E-AE6D-11cf-96B8-444553540000">':' data="'+e+'" type="application/x-shockwave-flash">';var s={width:"100%",height:"100%",allowscriptaccess:"always",wmode:n,quality:"high",flashvars:"",movie:e+(a?"?"+r:""),name:r};"transparent"!==n&&(s.bgcolor=i||"#333333"),Object.keys(t).forEach(function(e){s.flashvars+=e+"="+t[e]+"&"}),Object.keys(s).forEach(function(e){o+='<param name="'+e+'" value="'+s[e]+'"/>'}),o+="</object>";var l=u.createElement("div",{},o);return u.find("object",l)},window.attachEvent&&window.attachEvent("onbeforeunload",function(){window.__flash_savedUnloadHandler=window.__flash_unloadHandler=function(){}})},{"../common":1}],3:[function(e,t,n){"use strict";var i,S=e("../flowplayer"),E=e("../common"),D=e("./embed"),N=e("extend-object"),j=e("bean");function L(e){return/^https?:/.test(e)}(i=function(g,m){var v,y,w,b,I=g.conf,M=[],C={engineName:i.engineName,pick:function(i){var e=N({},function(){if(S.support.flashVideo){for(var e,t,n=0;n<i.length;n++)if(t=i[n],/mp4|flv|flash/i.test(t.type)&&(e=t),g.conf.swfHls&&/mpegurl/i.test(t.type)&&(e=t),e&&!/mp4/i.test(e.type))return e;return e}}());if(e)return!e.src||L(e.src)||g.conf.rtmp||e.rtmp||(e.src=E.createAbsoluteUrl(e.src)),e},suspendEngine:function(){b=!0},resumeEngine:function(){b=!1},load:function(n){function e(e){return e.replace(/&amp;/g,"%26").replace(/&/g,"%26").replace(/=/g,"%3D")}v=n,M.forEach(function(e){clearTimeout(e)});var t=E.findDirect("video",m)[0]||E.find(".fp-player > video",m)[0],i=n.src,r=L(i),o=function(){E.removeNode(t)};S.support.video&&E.prop(t,"autoplay")&&n.sources.some(function(e){return!!t.canPlayType(e.type)})?j.one(t,"timeupdate",o):o();var a,s=n.rtmp||I.rtmp;if(r||s||(i=E.createAbsoluteUrl(i)),w&&A(n)&&w.data!==E.createAbsoluteUrl(I.swfHls)&&C.unload(),w){["live","preload","loop"].forEach(function(e){n.hasOwnProperty(e)&&w.__set(e,n[e])}),Object.keys(n.flashls||{}).forEach(function(e){w.__set("hls_"+e,n.flashls[e])});var l=!1;if(!r&&s)w.__set("rtmp",s.url||s);else l=!!w.__get("rtmp"),w.__set("rtmp",null);w.__play(i,l||n.rtmp&&n.rtmp!==I.rtmp)}else{y="fpCallback"+(""+Math.random()).slice(3,15),i=e(i);var u={hostname:I.embedded?E.hostname(I.hostname):E.hostname(location.hostname),url:i,callback:y};m.getAttribute("data-origin")&&(u.origin=m.getAttribute("data-origin")),["proxy","key","autoplay","preload","subscribe","live","loop","debug","splash","poster","rtmpt"].forEach(function(e){I.hasOwnProperty(e)&&(u[e]=I[e]),n.hasOwnProperty(e)&&(u[e]=n[e]),(I.rtmp||{}).hasOwnProperty(e)&&(u[e]=(I.rtmp||{})[e]),(n.rtmp||{}).hasOwnProperty(e)&&(u[e]=(n.rtmp||{})[e])}),I.splash&&(u.autoplay=!0),I.rtmp&&(u.rtmp=I.rtmp.url||I.rtmp),n.rtmp&&(u.rtmp=n.rtmp.url||n.rtmp),Object.keys(n.flashls||{}).forEach(function(e){var t=n.flashls[e];u["hls_"+e]=t});var c=void 0!==n.hlsQualities?n.hlsQualities:I.hlsQualities;void 0!==c&&(u.hlsQualities=c?encodeURIComponent(JSON.stringify(c)):c),void 0!==I.bufferTime&&(u.bufferTime=I.bufferTime),void 0!==I.bufferTimeMax&&(u.bufferTimeMax=I.bufferTimeMax),r&&delete u.rtmp,u.rtmp&&(u.rtmp=e(u.rtmp));var f,d=I.bgcolor||E.css(m,"background-color")||"";0===d.indexOf("rgb")?f=function(e){function t(e){return("0"+parseInt(e).toString(16)).slice(-2)}if(!(e=e.match(/^rgb\((\d+),\s*(\d+),\s*(\d+)\)$/)))return;return"#"+t(e[1])+t(e[2])+t(e[3])}(d):0===d.indexOf("#")&&(f=7===(a=d).length?a:"#"+a.split("").slice(1).map(function(e){return e+e}).join("")),u.initialVolume=g.volumeLevel;var p=A(n)?I.swfHls:I.swf;w=D(p,u,I.wmode,f)[0];var h=E.find(".fp-player",m)[0];E.prepend(h,w),g.off("quality.flashengine").on("quality.flashengine",function(e,t,n){if(void 0!==g.video.hlsQualities?g.video.hlsQualities:g.conf.hlsQualities)try{w.__quality(n)}catch(e){g.debug("Error changing quality in flash engine",e)}}),setTimeout(function(){try{if(!w.PercentLoaded())return g.trigger("error",[g,{code:7,url:I.swf}])}catch(e){}},5e3),M.push(setTimeout(function(){void 0===w.PercentLoaded&&g.trigger("flashdisabled",[g])},15e3)),M.push(setTimeout(function(){void 0===w.PercentLoaded&&g.trigger("flashdisabled",[g,!1])},500)),g.off("resume.flashhack").on("resume.flashhack",function(){var e=setTimeout(function(){var e=w.__status().time,t=setTimeout(function(){g.playing&&!g.loading&&w.__status().time===e&&g.trigger("flashdisabled",[g])},400);M.push(t),g.one("seek.flashhack pause.flashhack load.flashack",function(){clearTimeout(t)})},800);M.push(e),g.one("progress",function(){clearTimeout(e)})}),w.pollInterval=setInterval(function(){if(w&&!b){var e=w.__status?w.__status():null;e&&((g.conf.live||g.live||n.live)&&(n.seekOffset=e.seekOffset,n.duration=e.duration+e.seekOffset),g.playing&&e.time&&e.time!==g.video.time&&g.trigger("progress",[g,e.time]),n.buffer=e.buffer/n.bytes*n.duration,g.trigger("buffer",[g,n.buffer]),!n.buffered&&0<e.time&&(n.buffered=!0,g.trigger("buffered",[g])))}},250),window[y]=function(e,t){var n=v;I.debug&&(0===e.indexOf("debug")&&t&&t.length?console.log.apply(console,["-- "+e].concat(t)):console.log("--",e,t));var i={type:e};switch(e){case"ready":t=N(n,t);break;case"click":i.flash=!0;break;case"keydown":i.which=t;break;case"seek":n.time=t;break;case"status":g.trigger("progress",[g,t.time]),t.buffer<n.bytes&&!n.buffered?(n.buffer=t.buffer/n.bytes*n.duration,g.trigger("buffer",n.buffer)):n.buffered||(n.buffered=!0,g.trigger("buffered"));break;case"metadata":var r=atob(t);t={key:r.substr(10,4),data:r.substr(21)}}"click"===e||"keydown"===e?(i.target=m,j.fire(m,e,[i])):"buffered"!=e&&"unload"!==e?setTimeout(function(){g.trigger(i,[g,t])},1):"unload"===e&&g.trigger(i,[g,t])}}},speed:E.noop,unload:function(){w&&w.__unload&&w.__unload();try{y&&window[y]&&delete window[y]}catch(e){}E.find("object",m).forEach(E.removeNode),w=0,g.off(".flashengine"),g.off(".flashhack"),clearInterval(w.pollInterval),M.forEach(function(e){clearTimeout(e)})}};function A(e){return/application\/x-mpegurl/i.test(e.type)}return["pause","resume","seek","volume"].forEach(function(t){C[t]=function(e){try{g.ready&&(void 0===e?w["__"+t]():w["__"+t](e))}catch(e){if(void 0===w["__"+t])return g.trigger("flashdisabled",[g]);throw e}}}),C}).engineName="flash",i.canPlay=function(e,t){return S.support.flashVideo&&/video\/(mp4|flash|flv)/i.test(e)||S.support.flashVideo&&t.swfHls&&/mpegurl/i.test(e)},S.engines.push(i)},{"../common":1,"../flowplayer":31,"./embed":2,bean:34,"extend-object":39}],4:[function(e,t,n){"use strict";var l,g=e("../flowplayer"),i=g.support,m=g.common,v=g.bean,r=e("./html5-factory");function o(e){return void 0!==window.Hls&&(/mpegurl/.test(e)&&window.Hls.isSupported())}(l=function(d,a){var p,h,s=window.Hls;return r("hlsjs-lite",d,a,o,function(u,c,e){var i=g.extend({recoverMediaError:!0},d.conf.hlsjs,u.hlsjs);d.engine.hls&&d.engine.hls.destroy();var n,r,f=d.engine.hls=new s(i);l.extensions.forEach(function(e){e({hls:f,player:d,root:a,videoTag:c})}),f.loadSource(u.src),e.resume=function(){d.live&&!d.dvr&&(c.currentTime=f.liveSyncPosition||0),c.play()},e.seek=function(t){try{d.live||d.dvr?c.currentTime=Math.min(t,f.liveSyncPosition||c.duration-i.livePositionOffset):c.currentTime=t}catch(e){d.debug("Failed to seek to ",t,e)}},!1===i.bufferWhilePaused&&d.on("pause",function(){f.stopLoad(),d.one("resume",function(){f.startLoad()})}),d.on("quality",function(e,t,n){f.nextLevel=p=n});var o=function(e){if(d.debug("hlsjs - recovery"),m.removeClass(a,"is-paused"),m.addClass(a,"is-seeking"),v.one(c,"seeked",function(){c.paused&&(m.removeClass(a,"is-poster"),d.poster=!1,c.play()),m.removeClass(a,"is-seeking")}),e)return f.startLoad();var t=performance.now();!n||3e3<t-n?(n=performance.now(),f.recoverMediaError()):(!r||3e3<t-r)&&(r=performance.now(),f.swapAudioCodec(),f.recoverMediaError())};return f.on(s.Events.MANIFEST_PARSED,function(e,t){var i,r=u.hlsQualities||d.conf.hlsQualities,o={},n=t.levels;if(!1===r)return f.attachMedia(c);if("drive"===r)switch(n.length){case 4:i=[1,2,3];break;case 5:i=[1,2,3,4];break;case 6:i=[1,3,4,5];break;case 7:i=[1,3,5,6];break;case 8:i=[1,3,6,7];break;default:i=n.length<3||n[0].height&&n[2].height&&n[0].height===n[2].height?[]:[1,2]}if(u.qualities=[{value:-1,label:"Auto"}],Array.isArray(r)){var a=r.find(function(e){return-1===e||e.level&&-1===e.level});a?u.qualities[0].label="number"!=typeof a?a.label:u.qualities[0].label:u.qualities=[],i=r.map(function(e){return void 0!==e.level&&(o[e.level]=e.label),void 0!==e.level?e.level:e})}var s=-2;u.qualities=u.qualities.concat(n.map(function(e,t){if(i&&-1===i.indexOf(t))return!1;var n=o[t]||Math.min(e.width,e.height)+"p";return o[t]||"drive"===r||(n+=" ("+Math.round(e.bitrate/1e3)+"k)"),t===p&&(s=t),{value:t,label:n}})).filter(m.identity);var l=u.quality=-2===s?u.qualities[0].value||-1:s;l!==f.currentLevel&&(f.currentLevel=l),f.attachMedia(c),h&&u.src!==h&&c.play(),h=u.src}),f.on(s.Events.ERROR,function(e,t){if(t.fatal)if(i.recoverNetworkError&&t.type===s.ErrorTypes.NETWORK_ERROR)o(!0);else if(i.recoverMediaError&&t.type===s.ErrorTypes.MEDIA_ERROR)o(!1);else{var n=5;t.type===s.ErrorTypes.NETWORK_ERROR&&(n=2),t.type===s.ErrorTypes.MEDIA_ERROR&&(n=3),f.destroy(),d.trigger("error",[d,{code:n}])}}),d.one("unload",function(){f.destroy()}),{handlers:{error:function(e,t){var n=t.error&&t.error.code;return i.recoverMediaError&&3===n||!n?(e.preventDefault(),o(!1),!0):i.recoverNetworkError&&2===n?(e.preventDefault(),o(!0),!0):void 0}}}})}).canPlay=function(e,t){return!(!1===t.hlsjs||t.clip&&!1===t.clip.hlsjs)&&(!(i.browser.safari&&!(t.clip&&t.clip.hlsjs||t.hlsjs||{}).safari)&&(g.support.video&&o(e)))},l.engineName="hlsjs-lite",l.plugin=function(e){l.extensions.push(e)},l.extensions=[],g.engines.push(l)},{"../flowplayer":31,"./html5-factory":5}],5:[function(e,t,n){var i=e("../flowplayer"),m=i.common,v=i.support,y=i.bean,w=i.extend,r=v.browser.safari&&!v.iOS,b={ended:"finish",pause:"pause",play:"resume",timeupdate:"progress",volumechange:"volume",ratechange:"speed",seeked:"seek",loadedmetadata:r?0:"ready",canplaythrough:r?"ready":0,durationchange:"ready",error:"error",dataunavailable:"error",webkitendfullscreen:!i.support.inlineVideo&&"unload",progress:"buffer"};function I(e,t){return t=t||100,Math.round(e*t)/t}t.exports=function(e,d,p,h,l){var t,u,c,f=m.findDirect("video",p)[0]||m.find(".fp-player > video",p)[0],g=d.conf;return c={engineName:e,pick:function(e){var t=v.video&&e.filter(function(e){return h(e.type)})[0];if(t)return"string"==typeof t.src&&(t.src=m.createAbsoluteUrl(t.src)),t},load:function(e){var t=m.find(".fp-player",p)[0],n=!1;if(f||(f=document.createElement("video"),m.prepend(t,f),f.autoplay=!!g.splash,n=!0),m.addClass(f,"fp-engine"),m.find("track",f).forEach(m.removeNode),f.preload="none",g.nativesubtitles||m.attr(f,"crossorigin",!1),g.disableInline||(f.setAttribute("webkit-playsinline","true"),f.setAttribute("playsinline","true")),v.inlineVideo||m.css(f,{position:"absolute",top:"-9999em"}),v.subtitles&&g.nativesubtitles&&e.subtitles&&e.subtitles.length){m.addClass(f,"native-subtitles");var i=e.subtitles,r=function(e){var t=f.textTracks;t.length&&(t[0].mode=e)};i.some(function(e){return!m.isSameDomain(e.src)})&&m.attr(f,"crossorigin","anonymous"),"function"==typeof f.textTracks.addEventListener&&f.textTracks.addEventListener("addtrack",function(){r("disabled"),r("showing")}),i.forEach(function(e){f.appendChild(m.createElement("track",{kind:"subtitles",srclang:e.srclang||"en",label:e.label||"en",src:e.src,default:e.default}))})}y.off(f,"timeupdate",m.noop),y.on(f,"timeupdate",m.noop),m.prop(f,"loop",!1),d.off(".loophack"),(e.loop||g.loop)&&d.on("finish.loophack",function(){d.resume()}),void 0!==u&&(f.volume=u);var o=l(e,f,c);if(g.autoplay||g.splash||e.autoplay){d.debug("Autoplay / Splash setup, try to start video"),f.load();var a=function(){try{var e=f.play();if(e&&e.catch){var t=function(e){if("AbortError"===e.name&&20===e.code)return n?void 0:f.play().catch(t);if(!g.mutedAutoplay)throw new Error("Unable to autoplay");return d.debug("Play errored, trying muted",e),d.mute(!0,!0),f.play()};e.catch(t).catch(function(){g.autoplay=!1,d.mute(!1,!0),d.trigger("stop",[d])})}}catch(e){d.debug("play() error thrown",e)}};0<f.readyState?a():y.one(f,"canplay",a)}if(c._listeners=function(l,e,u,c){var f=p.getAttribute("data-flowplayer-instance-id");if(!l.listeners||!l.listeners.hasOwnProperty(f)){(l.listeners||(l.listeners={}))[f]=u,y.on(e,"error",function(e){try{h(e.target.getAttribute("type"))&&d.trigger("error",[d,{code:4,video:w(u,{src:l.src,url:l.src})}])}catch(e){}}),d.on("shutdown",function(){y.off(e),y.off(l,".dvrhack"),d.off(".loophack")});var t={},n=function(e){"metadata"===e.kind&&(e.mode="hidden",e.addEventListener("cuechange",function(){e.activeCues.length&&d.trigger("metadata",[d,e.activeCues[0].value])},!1))};return l&&l.textTracks&&l.textTracks.length&&Array.prototype.forEach.call(l.textTracks,n),l&&l.textTracks&&"function"==typeof l.textTracks.addEventListener&&l.textTracks.addEventListener("addtrack",function(e){n(e.track)},!1),(d.conf.dvr||d.dvr||u.dvr)&&y.on(l,"progress.dvrhack",function(){l.seekable.length&&(d.video.duration=l.seekable.end(null),d.video.seekOffset=l.seekable.start(null),d.trigger("dvrwindow",[d,{start:l.seekable.start(null),end:l.seekable.end(null)}]),l.currentTime>=l.seekable.start(null)||(l.currentTime=l.seekable.start(null)))}),Object.keys(b).forEach(function(a){var s=b[a];if("webkitendfullscreen"===a&&d.conf.disableInline&&(s="unload"),s){var e=function(e){if(u=l.listeners[f],e.target&&m.hasClass(e.target,"fp-engine")){/progress/.test(s)||d.debug(a,"->",s,e);var t,n=function(e){d.trigger(e||s,[d,t])};if((d.ready||/ready|error/.test(s))&&s&&m.find("video",p).length)if("unload"!==s){switch(s){case"ready":if(d.ready)return d.debug("Player already ready, not sending duplicate ready event");if(!(l.duration&&l.duration!==1/0||d.live))return d.debug("No duration and VOD setup, not sending ready event");if((t=w(u,{duration:l.duration<Number.MAX_VALUE?l.duration:0,width:l.videoWidth,height:l.videoHeight,url:l.currentSrc})).seekable=t.duration,d.debug("Ready: ",t),!d.live&&!t.duration&&!v.hlsDuration&&"loadeddata"===a){var i=function(){t.duration=l.duration;try{t.seekable=l.seekable&&l.seekable.end(null)}catch(e){}n(),l.removeEventListener("durationchange",i),m.toggleClass(p,"is-live",!1)};l.addEventListener("durationchange",i);var r=function(){d.ready||l.duration||(t.duration=0,m.addClass(p,"is-live"),n()),l.removeEventListener("timeupdate",r)};return void l.addEventListener("timeupdate",r)}break;case"progress":case"seek":if(0<l.currentTime||d.live)t=Math.max(l.currentTime,0);else if("seek"===s&&0===l.currentTime)t=0;else if("progress"==s)return;break;case"buffer":t=[];for(var o=0;o<l.buffered.length;o++)t.push({start:l.buffered.start(o),end:l.buffered.end(o)});l.buffered.length&&l.buffered.end(null)===l.duration&&n("buffered");break;case"speed":t=I(l.playbackRate);break;case"volume":t=I(l.muted?0:l.volume);break;case"error":try{if(c&&c.handlers&&c.handlers.error&&c.handlers.error(e,l))return;(t=(e.srcElement||e.originalTarget).error).video=w(u,{src:l.src,url:l.src})}catch(e){return}}n()}else d.unload();else"resume"===s&&d.one("ready",function(){setTimeout(function(){n()})})}};p.addEventListener(a,e,!0),t[a]||(t[a]=[]),t[a].push(e)}}),t}l.listeners[f]=u}(f,m.find("source",f).concat(f),e,o)||c._listeners,!(g.autoplay||g.splash||e.autoplay)){var s=function(){var e;0<=(e=p.getBoundingClientRect()).top&&0<=e.left&&e.bottom<=(window.innerHeight||document.documentElement.clientHeight)+e.height&&e.right<=(window.innerWidth||document.documentElement.clientWidth)+e.width&&(d.debug("player is in viewport, preload"),v.preloadMetadata?f.preload="metadata":f.load(),y.off(document,"scroll.preloadviewport"))};y.off(document,"scroll.preloadviewport"),y.on(document,"scroll.preloadviewport",function(){window.requestAnimationFrame(s)}),s()}},mute:function(e){f.muted=!!e,d.trigger("mute",[d,e]),d.trigger("volume",[d,e?0:f.volume])},pause:function(){f.pause()},resume:function(){f.play()},speed:function(e){f.playbackRate=e},seek:function(e){var t=f.paused||d.finished;try{f.currentTime=e,t&&y.one(f,"seeked",function(){f.pause()})}catch(e){}},volume:function(e){u=e,f&&(f.volume=e)&&c.mute(!1)},unload:function(){y.off(document,"scroll.preloadviewport"),m.find("video.fp-engine",p).forEach(function(e){"MediaSource"in window?e.src=URL.createObjectURL(new MediaSource):e.src="",m.removeNode(e)}),t=clearInterval(t);var e=p.getAttribute("data-flowplayer-instance-id");delete f.listeners[e],f=0,c._listeners&&Object.keys(c._listeners).forEach(function(t){c._listeners[t].forEach(function(e){p.removeEventListener(t,e,!0)})})}}}},{"../flowplayer":31}],6:[function(e,t,n){"use strict";var i,r=e("../flowplayer"),o=r.common,a=e("./html5-factory"),s=document.createElement("video");function l(e){var t;return/^(video|application)/i.test(e)||(e=/mpegurl/i.test(t=e)?"application/x-mpegurl":t),!!s.canPlayType(e).replace("no","")}(i=function(e,t){return a("html5",e,t,l,function(e,t){t.currentSrc!==e.src?(o.find("source",t).forEach(o.removeNode),t.src=e.src,t.type=e.type):e.autoplay&&t.load()})}).canPlay=function(e){return r.support.video&&l(e)},i.engineName="html5",r.engines.push(i)},{"../flowplayer":31,"./html5-factory":5}],7:[function(e,t,n){"use strict";var i=e("../flowplayer"),r=e("../common"),o=e("bean");i(function(e,i){e.on("ready",function(){var t=r.find("video.fp-engine",i)[0];t&&(t.setAttribute("x-webkit-airplay","allow"),window.WebKitPlaybackTargetAvailabilityEvent&&(t.addEventListener("webkitplaybacktargetavailabilitychanged",function(e){if("available"===e.availability){var t=r.find(".fp-header",i)[0];r.find(".fp-airplay",t).forEach(r.removeNode);var n=r.createElement("a",{class:"fp-airplay fp-icon",title:"Play on AirPlay device"});t.insertBefore(n,r.find(".fp-fullscreen",t)[0])}}),t.addEventListener("webkitcurrentplaybacktargetiswirelesschanged",function(){var e=r.find(".fp-airplay",i)[0];e&&r.toggleClass(e,"fp-active",t.webkitCurrentPlaybackTargetIsWireless)})))}),o.on(i,"click",".fp-airplay",function(e){e.preventDefault(),r.find("video.fp-engine",i)[0].webkitShowPlaybackTargetPicker()})})},{"../common":1,"../flowplayer":31,bean:34}],8:[function(e,t,n){"use strict";var i=e("../flowplayer"),l=e("./resolve").TYPE_RE,u=e("scriptjs"),c=e("bean");i(function(i,r){var o,t=i.conf.analytics,a=0,e=0;if(t){"undefined"==typeof _gat&&u("//google-analytics.com/ga.js");var s=function(){var e=_gat._getTracker(t);return e._setAllowLinker(!0),e},n=function(e,t,n){(n=n||i.video,a&&"undefined"!=typeof _gat)&&(s()._trackEvent("Video / Seconds played",i.engine.engineName+"/"+n.type,n.title||r.getAttribute("title")||n.src.split("/").slice(-1)[0].replace(l,""),Math.round(a/1e3)),a=0,o&&(clearTimeout(o),o=null))};i.bind("load unload",n).bind("progress",function(){i.seeking||(a+=e?+new Date-e:0,e=+new Date),o||(o=setTimeout(function(){o=null,s()._trackEvent("Flowplayer heartbeat","Heartbeat","",0,!0)},6e5))}).bind("pause",function(){e=0}),i.bind("shutdown",function(){c.off(window,"unload",n)}),c.on(window,"unload",n)}})},{"../flowplayer":31,"./resolve":21,bean:34,scriptjs:45}],9:[function(e,t,n){"use strict";var i=e("../flowplayer"),g=e("../common"),m=e("bean"),v=e("scriptjs");i(function(r,o){if(!1!==r.conf.chromecast){v("https://www.gstatic.com/cv/js/sender/v1/cast_sender.js"),window.__onGCastApiAvailable=function(e){var t,n,i;e&&(t=u.applicationId||chrome.cast.media.DEFAULT_MEDIA_RECEIVER_APP_ID,n=new chrome.cast.SessionRequest(t),i=new chrome.cast.ApiConfig(n,c,f),chrome.cast.initialize(i,d,p))};var a,s,l,u=r.conf.chromecast||{};m.on(o,"click",".fp-chromecast",function(e){if(e.preventDefault(),a)return r.trigger("pause",[r]),a.stop(),a=null,void h();r.playing&&r.pause(),chrome.cast.requestSession(function(e){var t=(a=e).receiver.friendlyName;g.html(g.find(".fp-chromecast-engine-status")[0],"Playing on device "+t);var n=new chrome.cast.media.MediaInfo(r.video.src),i=new chrome.cast.media.LoadRequest(n);a.loadMedia(i,function(n){n.addUpdateListener(function(e){if(a){s=s||setInterval(function(){r.trigger("progress",[r,n.getEstimatedTime()])},500),e?(g.toggleClass(o,"is-chromecast",!0),g.toggleClass(l,"fp-active",!0),r.hijack({pause:function(){n.pause()},resume:function(){n.play()},seek:function(e){var t=new chrome.cast.media.SeekRequest;t.currentTime=e,n.seek(t)}})):(h(),r.trigger("finish",[r]));var t=n.playerState;r.paused&&t===chrome.cast.media.PlayerState.PLAYING&&r.trigger("resume",[r]),r.playing&&t===chrome.cast.media.PlayerState.PAUSED&&r.trigger("pause",[r]),g.toggleClass(o,"is-loading",t===chrome.cast.media.PlayerState.BUFFERING)}})},function(){})},function(e){console.error("requestSession error",e)})})}function c(){console.log("sessionListener")}function f(e){e===chrome.cast.ReceiverAvailability.AVAILABLE&&function(){var e=g.find(".fp-header",o)[0];if(!e)return;g.find(".fp-chromecast",e).forEach(g.removeNode),g.find(".fp-chromecast-engine",o).forEach(g.removeNode),l=g.createElement("a",{class:"fp-chromecast fp-icon",title:"Play on Cast device"}),e.insertBefore(l,g.find(".fp-fullscreen",e)[0]);var t=g.createElement("div",{class:"fp-chromecast-engine"}),n=g.createElement("p",{class:"fp-chromecast-engine-status"}),i=g.createElement("p",{class:"fp-chromecast-engine-icon"});t.appendChild(i),t.appendChild(n);var r=g.find(".fp-engine",o)[0];r?r.parentNode.insertBefore(t,r):g.prepend(g.find(".fp-player",o)[0]||o,t)}()}function d(){}function p(){console.log("onError")}function h(){clearInterval(s),s=null,r.release(),g.toggleClass(o,"is-chromecast",!1),g.toggleClass(l,"fp-active",!1)}})},{"../common":1,"../flowplayer":31,bean:34,scriptjs:45}],10:[function(e,t,n){"use strict";var i=e("../flowplayer"),f=e("../common"),d=e("bean");i(function(a,s){var t=/ ?cue\d+ ?/,r=!1;function i(e){s.className=s.className.replace(t," "),0<=e&&f.addClass(s,"cue"+e)}var l={},o=-.125,u=function(e){i(e.index),a.trigger("cuepoint",[a,e])};function c(e){var t=e&&!isNaN(e.time)?e.time:e;return t<0&&(t=a.video.duration+t),.125*Math.round(t/.125)}a.on("progress",function(e,t,n){if(!r)for(var i=c(n);o<i;)l[o+=.125]&&l[o].forEach(u)}).on("unload",i).on("beforeseek",function(e){setTimeout(function(){e.defaultPrevented||(r=!0)})}).on("seek",function(e,t,n){i(),o=c(n||0)-.125,r=!1,!n&&l[0]&&l[0].forEach(u)}).on("ready",function(e,t,n){o=-.125;var i=n.cuepoints||a.conf.cuepoints||[];a.setCuepoints(i)}).on("finish",function(){for(var e=c(a.video.duration);o<e;)l[o+=.125]&&l[o].forEach(u);o=-.125}),a.conf.generate_cuepoints&&a.bind("load",function(){f.find(".fp-cuepoint",s).forEach(f.removeNode)}),a.setCuepoints=function(e){return a.cuepoints=[],l={},e.forEach(a.addCuepoint),a},a.addCuepoint=function(e){a.cuepoints||(a.cuepoints=[]),"number"==typeof e&&(e={time:e}),e.index=0;var t=c(e);if(l[t]||(l[t]=[]),l[t].push(e),a.cuepoints.length&&(e.index=Math.max.apply(null,a.cuepoints.map(function(e){return e.index}))+1),a.cuepoints.push(e),a.conf.generate_cuepoints&&!1!==e.visible){var n=a.video.duration,i=f.find(".fp-timeline",s)[0];f.css(i,"overflow","visible");var r=e.time||e;r<0&&(r=n+r);var o=f.createElement("a",{className:"fp-cuepoint fp-cuepoint"+e.index});f.css(o,"left",r/n*100+"%"),i.appendChild(o),d.on(o,"mousedown",function(e){e.preventDefault(),e.stopPropagation(),a.seek(r)})}return a},a.removeCuepoint=function(t){"number"==typeof t&&(t=a.cuepoints.filter(function(e){return e.index===t})[0]);var e=a.cuepoints.indexOf(t),n=c(t);if(-1!==e){a.cuepoints=a.cuepoints.slice(0,e).concat(a.cuepoints.slice(e+1));var i=f.find(".fp-timeline",s)[0];f.find(".fp-cuepoint"+t.index,i).forEach(f.removeNode);var r=l[n].indexOf(t);if(-1!==r)return l[n]=l[n].slice(0,r).concat(l[n].slice(r+1)),a}}})},{"../common":1,"../flowplayer":31,bean:34}],11:[function(e,t,n){"use strict";var i=e("../flowplayer"),r=e("bean"),l=e("../common"),o=e("./util/clipboard");i(function(a,s){if(!1!==a.conf.embed&&!1!==a.conf.share){var e=l.find(".fp-share-menu",s)[0],t=l.createElement("a",{class:"fp-icon fp-embed",title:"Copy to your site"},"Embed");l.append(e,t),a.embedCode=function(){var e=a.conf.embed||{},t=a.video,n=e.width||t.width||l.width(s),i=e.height||t.height||l.height(s),r=a.conf.ratio,o='<iframe src="'+a.shareUrl(!0)+'" allowfullscreen style="border:none;';return e.width||e.height?(isNaN(n)||(n+="px"),isNaN(i)||(i+="px"),o+"width:"+n+";height:"+i+';"></iframe>'):(r&&!a.conf.adaptiveRatio||(r=i/n),'<div style="position:relative;width:100%;display:inline-block;">'+o+'position:absolute;top:0;left:0;width:100%;height:100%;"></iframe><div style="padding-top:'+100*r+'%;"></div></div>')},r.on(s,"click",".fp-embed",function(){o(a.embedCode(),function(){a.message("The embed code is now on your clipboard",2e3)},function(){a.textarea(a.embedCode(),"Copy the code below to embed your video")})})}})},{"../common":1,"../flowplayer":31,"./util/clipboard":30,bean:34}],12:[function(e,t,n){"use strict";t.exports=function(o,s){s||(s=document.createElement("div"));var l={},u={},n=function(n,i,r){var o=n.split(".")[0],a=function(e){r&&(s.removeEventListener(o,a),l[n].splice(l[n].indexOf(a),1));var t=[e].concat(u[e.timeStamp+e.type]||[]);i&&i.apply(void 0,t)};s.addEventListener(o,a),l[n]||(l[n]=[]),l[n].push(a)};o.on=o.bind=function(e,t){return e.split(" ").forEach(function(e){n(e,t)}),o},o.one=function(e,t){return e.split(" ").forEach(function(e){n(e,t,!0)}),o};o.off=o.unbind=function(e){return e.split(" ").forEach(function(e){var i=e.split(".").slice(1),r=e.split(".")[0];Object.keys(l).filter(function(e){var t,n=e.split(".").slice(1);return(!r||0===e.indexOf(r))&&(t=n,0===i.filter(function(e){return-1===t.indexOf(e)}).length)}).forEach(function(e){var t=l[e],n=e.split(".")[0];l[e]=t.filter(function(e){return s.removeEventListener(n,e),!1})})}),o},o.trigger=function(e,t,n){if(e){t=(t||[]).length?t||[]:[t];var i,r=document.createEvent("Event");return i=e.type||e,r.initEvent(i,!1,!0),Object.defineProperty&&(r.preventDefault=function(){Object.defineProperty(this,"defaultPrevented",{get:function(){return!0}})}),u[r.timeStamp+r.type]=t,s.dispatchEvent(r),n?r:o}}},t.exports.EVENTS=["beforeseek","disable","error","finish","fullscreen","fullscreen-exit","load","mute","pause","progress","ready","resume","seek","speed","stop","unload","volume","boot","shutdown"]},{}],13:[function(e,t,n){"use strict";var i=e("../flowplayer"),r=e("../common"),a=e("bean");i(function(e,t){var o=e.conf;if(!1!==o.share&&o.facebook){e.facebook=function(){var e,t,n=screen.height,i=screen.width,r="string"==typeof o.facebook?o.facebook:window.location.toString();e=Math.round(i/2-275),t=0,420<n&&(t=Math.round(n/2-210)),window.open("https://www.facebook.com/sharer.php?s=100&p[url]="+encodeURIComponent(r),"sharer","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,left="+e+",top="+t)};var n=r.find(".fp-share-menu",t)[0],i=r.createElement("a",{class:"fp-icon fp-facebook"},"Facebook");r.append(n,i),a.on(t,"click",".fp-facebook",function(){e.facebook()})}})},{"../common":1,"../flowplayer":31,bean:34}],14:[function(e,t,n){"use strict";var s,i=e("../flowplayer"),r=e("bean"),l=e("../common"),u="fullscreen",c="fullscreen-exit",f=i.support.fullscreen;r.on(document,"fullscreenchange.ffscr webkitfullscreenchange.ffscr mozfullscreenchange.ffscr MSFullscreenChange.ffscr",function(e){var t=document.webkitCurrentFullScreenElement||document.mozFullScreenElement||document.fullscreenElement||document.msFullscreenElement||e.target;if(s||t.parentNode&&t.parentNode.getAttribute("data-flowplayer-instance-id")){var n=s||i(t.parentNode);t&&!s?s=n.trigger(u,[n]):(s.trigger(c,[s]),s=null)}}),i(function(t,n){var i=l.createElement("div",{className:"fp-player"});if(Array.prototype.map.call(n.children,l.identity).forEach(function(e){l.matches(e,".fp-ratio,script")||i.appendChild(e)}),n.appendChild(i),t.conf.fullscreen){var r,o,e,a=window;t.isFullscreen=!1,t.fullscreen=function(e){if(!t.disabled)return void 0===e&&(e=!t.isFullscreen),e&&(r=a.scrollY,o=a.scrollX),f?e?["requestFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].forEach(function(e){"function"==typeof i[e]&&(i[e](Element.ALLOW_KEYBOARD_INPUT),"webkitRequestFullScreen"!==e||document.webkitFullscreenElement||i[e]())}):["exitFullscreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].forEach(function(e){"function"==typeof document[e]&&document[e]()}):t.trigger(e?u:c,[t]),t},t.on("mousedown.fs",function(){+new Date-e<150&&t.ready&&t.fullscreen(),e=+new Date}),t.on(u,function(){l.addClass(n,"is-fullscreen"),l.toggleClass(n,"fp-minimal-fullscreen",l.hasClass(n,"fp-minimal")),l.removeClass(n,"fp-minimal"),f||l.css(n,"position","fixed"),t.isFullscreen=!0}).on(c,function(){var e;l.toggleClass(n,"fp-minimal",l.hasClass(n,"fp-minimal-fullscreen")),l.removeClass(n,"fp-minimal-fullscreen"),f||"html5"!==t.engine||(e=n.css("opacity")||"",l.css(n,"opacity",0)),f||l.css(n,"position",""),l.removeClass(n,"is-fullscreen"),f||"html5"!==t.engine||setTimeout(function(){n.css("opacity",e)}),t.isFullscreen=!1,a.scrollTo(o,r)}).on("unload",function(){t.isFullscreen&&t.fullscreen()}),t.on("shutdown",function(){s=null,l.removeNode(i)})}})},{"../common":1,"../flowplayer":31,bean:34}],15:[function(e,t,n){"use strict";var o,i,r=e("../flowplayer"),a=e("bean"),s=e("../common");a.on(document,"keydown.fp",function(e){var t=o,n=e.ctrlKey||e.metaKey||e.altKey,i=e.which,r=t&&t.conf;if(t&&r.keyboard&&!t.disabled&&!n&&t.ready){if(e.shiftKey)return 39==i?t.speed(!0):37==i&&t.speed(!1),e.preventDefault();if(i<58&&47<i)return e.preventDefault(),t.seekTo(i-48);(function(){switch(i){case 38:case 75:return t.volume(t.volumeLevel+.15),!0;case 40:case 74:return t.volume(t.volumeLevel-.15),!0;case 39:case 76:return t.seeking=!0,t.seek(!0),!0;case 37:case 72:return t.seeking=!0,t.seek(!1),!0;case 190:return t.seekTo(),!0;case 32:return t.toggle(),!0;case 70:return r.fullscreen&&t.fullscreen(),!0;case 77:return t.mute(),!0;case 81:return t.unload(),!0}})()&&e.preventDefault()}}),r(function(t,n){t.conf.keyboard&&(a.on(document,"click",function(e){if(s.hasParent(e.target,n))o=t.disabled?0:t;else{if(o!==t)return;o=0}o&&(i=n)}),t.bind("shutdown",function(){i==n&&(i=null)}))})},{"../common":1,"../flowplayer":31,bean:34}],16:[function(e,t,n){var i=e("../flowplayer"),l=e("../common"),u=e("bean");i(function(a,s){a.showMenu=function(e,t){var n=l.find(".fp-ui",s)[0];l.toggleClass(e,"fp-active",!0),setTimeout(function(){u.one(document,"click",function(){a.hideMenu(e)})});var i=t;if(t&&t.tagName&&(i={left:l.offset(t).left,rightFallbackOffset:l.width(t),top:l.offset(t).top+l.height(t)}),!i)return l.css(e,"top","auto");i.rightFallbackOffset=i.rightFallbackOffset||0;var r=i.top-l.offset(n).top,o=i.left-l.offset(n).left;l.width(e)+o>l.width(n)&&(o=o-l.width(e)+i.rightFallbackOffset),l.height(e)+r>l.height(n)&&(r-=l.height(e)),l.css(e,{top:r+"px",left:o+"px",right:"auto"})},a.hideMenu=function(e){l.toggleClass(e,"fp-active",!1),l.css(e,{top:"-9999em"})}})},{"../common":1,"../flowplayer":31,bean:34}],17:[function(e,t,n){var i=e("../flowplayer"),l=e("../common"),r=e("bean");i(function(e,t){var a=l.find(".fp-header",t)[0],s=l.find(".fp-ui",t)[0];e.message=function(e,t){var n,i,r=(n=e,i=l.createElement("div",{className:"fp-message"},n),s.insertBefore(i,a),setTimeout(function(){l.toggleClass(i,"fp-shown")}),i),o=function(){l.toggleClass(r,"fp-shown"),setTimeout(function(){var e;e=r,l.removeNode(e)},500)};return t&&setTimeout(o,t),o},e.textarea=function(e){var t=document.createElement("textarea");t.value=e,t.className="fp-textarea",s.appendChild(t),r.on(document,"click.fptextarea",function(e){if(e.target===t)return t.select();e.stopPropagation(),e.preventDefault(),l.removeNode(t),r.off(document,"click.fptextarea")})}})},{"../common":1,"../flowplayer":31,bean:34}],18:[function(e,t,n){"use strict";var i=e("../flowplayer"),p=/IEMobile/.test(window.navigator.userAgent),h=e("../common"),g=e("bean"),m=e("./ui").format,v=i.support,y=window.navigator.userAgent;(v.touch||p)&&i(function(n,i){var e=v.android,t=e&&!e.firefox,r=/Silk/.test(y),o=e.version||0;if(t&&!p){if(!/Chrome/.test(y)&&o<4||e.samsung&&o<5){var a=n.load;n.load=function(){var e=a.apply(n,arguments);return h.find("video.fp-engine",i)[0].load(),n.trigger("ready",[n,n.video]),e}}var s,l=0,u=function(e){s=setInterval(function(){e.video.time=++l,e.trigger("progress",[e,l])},1e3)};n.on("ready pause unload",function(){s&&(clearInterval(s),s=null)}),n.on("ready",function(){l=0}),n.on("resume",function(e,t){if(t.live)return l?u(t):void n.one("progress",function(e,t,n){0===n&&u(t)})})}v.volume||(h.removeClass(i,"fp-mute"),h.addClass(i,"no-volume")),v.iOS&&h.addClass(i,"fp-mute"),h.addClass(i,"is-touch"),n.sliders&&n.sliders.timeline&&n.sliders.timeline.disableAnimation();var c=!1;g.on(i,"touchmove",function(){c=!0});var f=!0;if(g.on(i,"touchend click",function(e){if(c)c=!1;else{var t=h.find("video.fp-engine",i)[0];if(f&&n.conf.clickToUnMute&&t&&t.muted&&n.conf.autoplay&&(t.muted=!1),f=!1,n.playing&&!h.hasClass(i,"is-mouseover"))return h.addClass(i,"is-mouseover"),h.removeClass(i,"is-mouseout"),e.preventDefault(),void e.stopPropagation();n.playing||n.splash||!h.hasClass(i,"is-mouseout")||h.hasClass(i,"is-mouseover")||setTimeout(function(){n.disabled||n.playing||n.splash||h.find("video.fp-engine",i)[0].play()},400)}}),!v.fullscreen&&n.conf.native_fullscreen&&"function"==typeof h.createElement("video").webkitEnterFullScreen){var d=n.fullscreen;n.fullscreen=function(){var e=h.find("video.fp-engine",i)[0];if(!e)return d.apply(n);n.trigger("fullscreen",[n]),g.on(document,"webkitfullscreenchange.nativefullscreen",function(){document.webkitFullscreenElement===e&&(g.off(document,".nativefullscreen"),g.on(document,"webkitfullscreenchange.nativefullscreen",function(){document.webkitFullscreenElement||(g.off(document,".nativefullscreen"),n.trigger("fullscreen-exit",[n]))}))}),e.webkitEnterFullScreen(),g.one(e,"webkitendfullscreen",function(){g.off(document,"fullscreenchange.nativefullscreen"),n.trigger("fullscreen-exit",[n]),h.prop(e,"controls",!0),h.prop(e,"controls",!1)})}}(t||r)&&n.bind("ready",function(){var t=h.find("video.fp-engine",i)[0];n.conf.splash&&t.paused&&"hlsjs-lite"!==n.engine.engineName&&(g.one(t,"canplay",function(){t.play()}),t.load()),n.bind("progress.dur",function(){if(!n.live&&!n.conf.live){var e=t.duration;1!==e&&(n.video.duration=e,h.find(".fp-duration",i)[0].innerHTML=m(e),n.unbind("progress.dur"))}})})})},{"../common":1,"../flowplayer":31,"./ui":27,bean:34}],19:[function(e,t,n){"use strict";var i=e("../flowplayer"),o=e("extend-object"),a=e("bean"),g=e("../common"),s=e("./resolve"),l=new s,m=window.jQuery,v=/^#/;i(function(u,c){var f=o({active:"is-active",advance:!0,query:".fp-playlist a"},u.conf),d=f.active,t=g.find(".fp-ui",c)[0],e=g.hasClass(c,"fp-custom-playlist")||!!f.customPlaylist;function n(){return g.find(f.query,p())}function p(){if(!v.test(f.query))return c}function h(){return g.find(f.query+"."+d,p())}function i(){var i=g.find(".fp-playlist",c)[0];if(!i){i=g.createElement("div",{className:"fp-playlist"});var e=g.find(".fp-next,.fp-prev",c);e.length?e[0].parentElement.insertBefore(i,e[0]):g.insertAfter(c,g.find("video",c)[0],i)}i.innerHTML="",u.conf.playlist[0].length&&(u.conf.playlist=u.conf.playlist.map(function(e){if("string"==typeof e){var t=e.split(s.TYPE_RE)[1];return{sources:[{type:"m3u8"===t.toLowerCase()?"application/x-mpegurl":"video/"+t,src:e}]}}return{sources:e.map(function(t){var n={};return Object.keys(t).forEach(function(e){n.type=/mpegurl/i.test(e)?"application/x-mpegurl":"video/"+e,n.src=t[e]}),n})}})),u.conf.playlist.forEach(function(e,t){var n=e.sources[0].src;i.appendChild(g.createElement("a",{href:n,className:u.video.index===t?d:void 0,"data-index":t}))})}g.toggleClass(c,"fp-custom-playlist",e),g.toggleClass(c,"fp-default-playlist",!e),u.play=function(e){if(void 0===e)return u.resume();if("number"==typeof e&&!u.conf.playlist[e])return u;if("number"!=typeof e)return u.load.apply(null,arguments);var t=o({index:e},u.conf.playlist[e]);return u.off("beforeresume.fromfirst"),"number"==typeof e&&e===u.video.index?u.seek(0,function(){u.resume()}):(u.load(t,function(){u.video.index=e}),u)},u.next=function(e){e&&e.preventDefault();var t=u.video.index;return-1!=t&&(t=t===u.conf.playlist.length-1?0:t+1,u.play(t)),u},u.prev=function(e){e&&e.preventDefault();var t=u.video.index;return-1!=t&&(t=0===t?u.conf.playlist.length-1:t-1,u.play(t)),u},u.setPlaylist=function(e,t){return u.conf.playlist=e,t||delete u.video.index,i(),u},u.addPlaylistItem=function(e){return delete u.video.is_last,u.setPlaylist(u.conf.playlist.concat([e]),!0)},u.removePlaylistItem=function(e){var t=u.conf.playlist;return u.setPlaylist(t.slice(0,e).concat(t.slice(e+1)))},a.on(c,"click",".fp-next",u.next),a.on(c,"click",".fp-prev",u.prev),u.off("finish.pl").on("finish.pl",function(e,t){if(void 0===t.conf.advance||t.conf.advance){if(t.video.loop)return t.seek(0,function(){t.resume()});var n=0<=t.video.index?t.video.index+1:void 0;n<t.conf.playlist.length||f.loop?(n=n===t.conf.playlist.length?0:n,g.removeClass(c,"is-finished"),setTimeout(function(){t.play(n)})):1<t.conf.playlist.length&&(t.one("beforeresume.fromfirst",function(e){e.preventDefault(),t.play(0)}),t.one("seek",function(){t.off("beforeresume.fromfirst")}))}});var r=!1;u.conf.playlist.length&&(r=!0,i(),u.conf.clip&&u.conf.clip.sources.length||(u.conf.clip=u.conf.playlist[u.conf.startIndex||0])),n().length&&!r&&(u.conf.playlist=[],delete u.conf.startIndex,n().forEach(function(e){var t=e.href;e.setAttribute("data-index",u.conf.playlist.length);var n=l.resolve(t,u.conf.clip.sources);m&&o(n,m(e).data()),u.conf.playlist.push(n)})),g.find(".fp-prev,.fp-next,.fp-playlist",c).forEach(function(e){t.appendChild(e)}),a.on(v.test(f.query)?document:c,"click",f.query,function(e){e.preventDefault();var t=e.currentTarget,n=Number(t.getAttribute("data-index"));-1!=n&&u.play(n)}),u.on("load",function(e,t,n){if(u.conf.playlist.length){var i,r=h()[0],o=r&&r.getAttribute("data-index"),a=n.index=void 0!==(i=n).index?i.index:void 0!==u.video.index?u.video.index:u.conf.startIndex||0,s=g.find(f.query+'[data-index="'+a+'"]',p())[0],l=a==u.conf.playlist.length-1;r&&g.removeClass(r,d),s&&g.addClass(s,d),g.removeClass(c,"video"+o),g.addClass(c,"video"+a),g.toggleClass(c,"last-video",l),n.index=t.video.index=a,n.is_last=t.video.is_last=l}}).on("unload.pl",function(){u.conf.playlist.length&&(h().forEach(function(e){g.toggleClass(e,d)}),u.conf.playlist.forEach(function(e,t){g.removeClass(c,"video"+t)}),delete u.video.index)}),u.conf.playlist.length&&(u.conf.loop=!1)})},{"../common":1,"../flowplayer":31,"./resolve":21,bean:34,"extend-object":39}],20:[function(e,t,n){var i=e("../flowplayer"),s=e("../common"),l=e("bean");i(function(n,i){var r=s.find(".fp-ui",i)[0],o=s.find(".fp-controls",r)[0];function a(t){s.find(".fp-qsel-menu a",i).forEach(function(e){s.toggleClass(e,"fp-selected",e.getAttribute("data-quality")==t),s.toggleClass(e,"fp-color",e.getAttribute("data-quality")==t)})}l.on(i,"click",".fp-qsel",function(){var e=s.find(".fp-qsel-menu",i)[0];s.hasClass(e,"fp-active")?n.hideMenu():n.showMenu(e)}),l.on(i,"click",".fp-qsel-menu a",function(e){var t=e.target.getAttribute("data-quality");n.quality(t)}),n.quality=function(e){e=isNaN(Number(e))?e:Number(e),n.trigger("quality",[n,e])},n.on("quality",function(e,t,n){a(n,t.video.qualities)}),n.on("ready",function(e,t,n){s.find(".fp-qsel-menu",i).forEach(s.removeNode),s.find(".fp-qsel",i).forEach(s.removeNode),!n.qualities||n.qualities.filter(function(e){return void 0===e.value||-1<e.value}).length<2||(!function(e){o.appendChild(s.createElement("strong",{className:"fp-qsel"},"HD"));var i=s.createElement("div",{className:"fp-menu fp-qsel-menu"},"<strong>Quality</strong>");e.forEach(function(e){var t=document.createElement("a"),n=void 0!==e.value?e.value:e;t.setAttribute("data-quality",n),t.innerHTML=e.label||e,i.appendChild(t)}),r.appendChild(i)}(n.qualities,n.quality),a(n.quality,n.qualities))})})},{"../common":1,"../flowplayer":31,bean:34}],21:[function(e,t,n){"use strict";var r=/\.(\w{3,4})(\?.*)?$/i,o=e("extend-object");function i(e){var t=e.attr("src"),n=e.attr("type")||"",i=t.split(r)[1];return n=n.toLowerCase(),o(e.data(),{src:t,suffix:i||n,type:n||i})}t.exports=function(){this.sourcesFromVideoTag=function(e,t){var n=[];return t("source",e).each(function(){n.push(i(t(this)))}),!n.length&&e.length&&n.push(i(e)),n},this.resolve=function(n,e){return n?("string"==typeof n&&((n={src:n,sources:[]}).sources=(e||[]).map(function(e){var t=e.src.split(r)[1];return{type:e.type,src:n.src.replace(r,"."+t+"$2")}})),n instanceof Array&&(n={sources:n.map(function(i){return i.type&&i.src?i:Object.keys(i).reduce(function(e,t){return o(e,{type:(n=t,/mpegurl/i.test(n)?"application/x-mpegurl":"video/"+n),src:i[t]});var n},{})})}),n):{sources:e}}},t.exports.TYPE_RE=r},{"extend-object":39}],22:[function(e,t,n){"use strict";var i=e("../flowplayer"),o=e("../common"),a=e("extend-object"),s=e("bean");i(function(t,e){var n=t.conf;if(!1!==n.share){t.shareUrl=function(e){return e&&n.embed&&n.embed.iframe?n.embed.iframe:"string"==typeof t.conf.share?t.conf.share:(e?"https://flowplayer.com/e/":"https://flowplayer.com/s/")+"?t="+encodeURIComponent(t.video.title||(o.find("title")[0]||{}).innerHTML||"Flowplayer video")+"&c="+encodeURIComponent(btoa(JSON.stringify(a({},t.conf,t.extensions)).replace(/[\u007F-\uFFFF]/g,function(e){return"\\u"+("0000"+e.charCodeAt(0).toString(16)).substr(-4)})))+"&r="+encodeURIComponent(window.location.toString())};var i=o.createElement("div",{className:"fp-menu fp-share-menu"},"<strong>Share</strong>");o.find(".fp-ui",e)[0].appendChild(i);var r=o.find(".fp-share",e)[0];s.on(e,"click",".fp-share",function(e){e.preventDefault(),o.hasClass(i,"fp-active")?t.hideMenu():t.showMenu(i,r)})}else o.find(".fp-share",e).forEach(o.removeNode)})},{"../common":1,"../flowplayer":31,bean:34,"extend-object":39}],23:[function(e,t,n){"use strict";var f=e("../flowplayer"),d=e("../common"),p=e("bean"),i=e("./subtitles/parser");f.defaults.subtitleParser=i,f(function(r,o){var a,s,n,l,i;(!f.support.inlineVideo||!f.support.fullscreen&&r.conf.native_fullscreen)&&(r.conf.nativesubtitles=!0),r.ui||(r.ui={}),r.ui.createSubtitleControl=function(e,t){return i=t,n=n||d.createElement("strong",{className:"fp-cc"},"CC"),l=l||d.createElement("div",{className:"fp-menu fp-subtitle-menu"},"<strong>Closed Captions</strong>"),d.find("a",l).forEach(d.removeNode),l.appendChild(d.createElement("a",{"data-subtitle-index":-1},"No subtitles")),(e||[]).forEach(function(e,t){var n=e.srclang||"en",i=e.label||"Default ("+n+")",r=d.createElement("a",{"data-subtitle-index":t},i);l.appendChild(r)}),d.find(".fp-ui",o)[0].appendChild(l),d.find(".fp-controls",o)[0].appendChild(n),d.toggleClass(n,"fp-hidden",!e||!e.length),n},r.ui.setActiveSubtitleItem=function(e){u(e)},p.on(o,"click",".fp-cc",function(){d.hasClass(l,"fp-active")?r.hideMenu():r.showMenu(l)}),p.on(o,"click",".fp-subtitle-menu [data-subtitle-index]",function(e){e.preventDefault();var t=e.target.getAttribute("data-subtitle-index");return i?i(t):"-1"===t?r.disableSubtitles():void r.loadSubtitles(t)});r.on("ready",function(e,t,n){if(t.subtitles=[],s=(s=d.find(".fp-captions",o)[0])||d.appendTo(d.createElement("div",{class:"fp-captions"}),d.find(".fp-player",o)[0]),Array.prototype.forEach.call(s.children,d.removeNode),r.ui.createSubtitleControl(r.video.subtitles),d.removeClass(o,"has-menu"),r.disableSubtitles(),n.subtitles&&n.subtitles.length){var i=n.subtitles.filter(function(e){return e.default})[0];i&&t.loadSubtitles(n.subtitles.indexOf(i))}}),r.showSubtitle=function(e){d.html(s,e),d.addClass(s,"fp-shown")},r.hideSubtitle=function(){d.removeClass(s,"fp-shown")},r.bind("cuepoint",function(e,t,n){n.subtitle?(a=n.index,r.showSubtitle(n.subtitle.text)):n.subtitleEnd&&(r.hideSubtitle(),a=n.index)}),r.bind("seek",function(e,t,i){a&&r.cuepoints[a]&&r.cuepoints[a].time>i&&(d.removeClass(s,"fp-shown"),a=null),(r.cuepoints||[]).forEach(function(e,t){var n=e.subtitle;n&&a!=t?i>=e.time&&(!n.endTime||i<=n.endTime)&&r.trigger("cuepoint",[r,e]):e.subtitleEnd&&i>=e.time&&t==a+1&&r.trigger("cuepoint",[r,e])})}),r.on("unload",function(){d.find(".fp-captions",o).forEach(d.removeNode)});var u=function(e){d.toggleClass(d.find("a.fp-selected",l)[0],"fp-selected"),d.toggleClass(d.find('a[data-subtitle-index="'+e+'"]',l)[0],"fp-selected")},c=function(e,t){var n=d.find("video.fp-engine",o)[0].textTracks;n.length&&(null===e?[].forEach.call(n,function(e){e.mode=t}):n[e].mode=t)};r.disableSubtitles=function(){return r.subtitles=[],(r.cuepoints||[]).forEach(function(e){(e.subtitle||e.subtitleEnd)&&r.removeCuepoint(e)}),s&&Array.prototype.forEach.call(s.children,d.removeNode),u(-1),f.support.subtitles&&r.conf.nativesubtitles&&"html5"==r.engine.engineName&&c(null,"disabled"),r},r.loadSubtitles=function(e){r.disableSubtitles();var t=r.video.subtitles[e].src;if(t){if(u(e),!f.support.subtitles||!r.conf.nativesubtitles||"html5"!=r.engine.engineName)return d.xhrGet(t,function(e){r.conf.subtitleParser(e).forEach(function(e,t){e.title||(e.title="subtitle"+t);var n={time:e.startTime,subtitle:e,visible:!1};r.subtitles.push(e),r.addCuepoint(n),r.addCuepoint({time:e.endTime,subtitleEnd:e.title,visible:!1}),0!==e.startTime||r.video.time||r.splash||r.trigger("cuepoint",[r,f.extend({},n,{index:0})]),r.splash&&r.one("ready",function(){r.trigger("cuepoint",[r,n])})})},function(){return r.trigger("error",{code:8,url:t}),!1}),r;c(e,"showing")}}})},{"../common":1,"../flowplayer":31,"./subtitles/parser":24,bean:34}],24:[function(e,t,n){t.exports=function(e){var t=/^(([0-9]+:){1,2}[0-9]{2}[,.][0-9]{3}) --\> (([0-9]+:){1,2}[0-9]{2}[,.][0-9]{3})(.*)/;function n(e){var t=e.split(":");return 2==t.length&&t.unshift(0),60*t[0]*60+60*t[1]+parseFloat(t[2].replace(",","."))}for(var i,r,o,a=[],s=0,l=e.split("\n"),u=l.length,c={};s<u;s++)if(r=t.exec(l[s])){for(i=l[s-1],o="<p>"+l[++s]+"</p><br/>";"string"==typeof l[++s]&&l[s].trim()&&s<l.length;)o+="<p>"+l[s]+"</p><br/>";c={title:i,startTime:n(r[1]),endTime:n(r[3]),text:o},a.push(c)}return a}},{}],25:[function(e,t,n){"use strict";var S=e("../flowplayer"),E=e("extend-object");!function(){var e={},t=document.documentElement.style,n=navigator.userAgent.toLowerCase(),i=/(chrome)[ \/]([\w.]+)/.exec(n)||/(safari)[ \/]([\w.]+)/.exec(n)||/(webkit)[ \/]([\w.]+)/.exec(n)||/(opera)(?:.*version|)[ \/]([\w.]+)/.exec(n)||/(msie) ([\w.]+)/.exec(n)||n.indexOf("compatible")<0&&/(mozilla)(?:.*? rv:([\w.]+)|)/.exec(n)||[];i[1]&&(e[i[1]]=!0,e.version=i[2]||"0"),e.safari&&(e.version=(/version\/([\w.]+)/.exec(n)||[])[1]);var r,o,a=((o=document.createElement("video")).loop=!0,o.autoplay=!0,o.preload=!0,o),s=navigator.userAgent,l=e.msie||/Trident\/7/.test(s),u=/iPad|MeeGo/.test(s)&&!/CriOS/.test(s),c=/iPad/.test(s)&&/CriOS/.test(s),f=/iP(hone|od)/i.test(s)&&!/iPad/.test(s)&&!/IEMobile/i.test(s),d=/Android/.test(s),p=d&&/Firefox/.test(s),h=d&&/SAMSUNG/.test(s),g=/Silk/.test(s),m=/IEMobile/.test(s),v=m?parseFloat(/Windows\ Phone\ (\d+\.\d+)/.exec(s)[1],10):0,y=m?parseFloat(/IEMobile\/(\d+\.\d+)/.exec(s)[1],10):0,w=u||f?(r=/iP(ad|hone)(; CPU)? OS (\d+_\d)/.exec(s))&&1<r.length?parseFloat(r[r.length-1].replace("_","."),10):0:0,b=d?parseFloat(/Android\ (\d+(\.\d+)?)/.exec(s)[1],10):0,I=(f||u||c)&&{iPhone:f,iPad:u||c,version:w,chrome:c},M=E(S.support,{browser:e,iOS:I,android:!!d&&{firefox:p,opera:/Opera/.test(s),samsung:h,version:b},subtitles:!!a.addTextTrack,fullscreen:"boolean"==typeof document.webkitFullscreenEnabled?document.webkitFullscreenEnabled:"function"==typeof document.webkitCancelFullScreen&&!/Mac OS X 10_5.+Version\/5\.0\.\d Safari/.test(s)||document.mozFullScreenEnabled||"function"==typeof document.exitFullscreen||"function"==typeof document.msExitFullscreen,inlineBlock:!(l&&e.version<8),touch:"ontouchstart"in window,dataload:!u&&!f&&!m,flex:"flexWrap"in t||"WebkitFlexWrap"in t||"msFlexWrap"in t,svg:!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,zeropreload:!l&&!d,volume:!(u||f||g||c),cachedVideoTag:!(u||f||c||m),firstframe:!(g||m||p||h||w&&w<10||d&&b<4.4),inlineVideo:(!f||10<=w)&&(!m||8.1<=v&&11<=y)&&(!d||3<=b),hlsDuration:!d&&(!e.safari||u||f||c),seekable:!u&&!c,preloadMetadata:!I&&!e.safari});M.autoplay=M.firstframe,m&&(M.browser.safari=!1);try{var C=navigator.plugins["Shockwave Flash"],A=l?new ActiveXObject("ShockwaveFlash.ShockwaveFlash").GetVariable("$version"):C.description;l||C[0].enabledPlugin?((A=A.split(/\D+/)).length&&!A[0]&&(A=A.slice(1)),M.flashVideo=9<A[0]||9==A[0]&&115<=A[3]):M.flashVideo=!1}catch(e){}try{M.video=!!a.canPlayType,M.video&&a.canPlayType("video/mp4")}catch(e){M.video=!1}M.animation=function(){for(var e=["","Webkit","Moz","O","ms","Khtml"],t=document.createElement("p"),n=0;n<e.length;n++)if(void 0!==t.style[e[n]+"AnimationName"])return!0}()}()},{"../flowplayer":31,"extend-object":39}],26:[function(e,t,n){"use strict";var i=e("../flowplayer"),r=e("../common"),s=e("bean");i(function(o,e){var a=o.conf;if(!1!==a.share&&!1!==a.twitter){o.tweet=function(){var e,t,n=screen.height,i=screen.width,r="string"==typeof a.twitter?a.twitter:o.shareUrl();e=Math.round(i/2-275),t=0,420<n&&(t=Math.round(n/2-210)),window.open("https://twitter.com/intent/tweet?url="+encodeURIComponent(r),"intent","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,left="+e+",top="+t)};var t=r.find(".fp-share-menu",e)[0],n=r.createElement("a",{class:"fp-icon fp-twitter"},"Twitter");r.append(t,n),s.on(e,"click",".fp-twitter",function(){o.tweet()})}})},{"../common":1,"../flowplayer":31,bean:34}],27:[function(e,t,n){(function(Z){"use strict";var P=e("../flowplayer"),k=e("../common"),Y=e("bean"),z=e("./ui/slider"),G=e("./ui/bar-slider");function r(e){return 10<=(e=parseInt(e,10))?e:"0"+e}function R(e,t){e=Math.max(e||0,0),e=t?Math.ceil(e):Math.floor(e);var n=Math.floor(e/3600),i=Math.floor(e/60);return e-=60*i,1<=n?n+":"+r(i-=60*n)+":"+r(e):r(i)+":"+r(e)}var O=Z("PHN2ZyBjbGFzcz0iZnAtcGxheS1yb3VuZGVkLW91dGxpbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDk5Ljg0NCA5OS44NDM0Ij48ZGVmcz48c3R5bGU+LmZwLWNvbG9yLXBsYXl7b3BhY2l0eTowLjY1O30uY29udHJvbGJ1dHRvbntmaWxsOiNmZmY7fTwvc3R5bGU+CjwvZGVmcz4KPHRpdGxlPnBsYXktcm91bmRlZC1vdXRsaW5lPC90aXRsZT48cGF0aCBjbGFzcz0iZnAtY29sb3ItcGxheSIgZD0iTTQ5LjkyMTctLjA3OGE1MCw1MCwwLDEsMCw1MCw1MEE1MC4wNTY0LDUwLjA1NjQsMCwwLDAsNDkuOTIxNy0uMDc4WiIvPjxwYXRoIGNsYXNzPSJjb250cm9sYnV0dG9uIiBkPSJNNDEuMDM1OSw3MS4xOWE1LjA0OTIsNS4wNDkyLDAsMCwxLTIuNTU3NS0uNjY3M2MtMS44MDMxLTEuMDQxLTIuNzk1OC0zLjEyNDgtMi43OTU4LTUuODY2NFYzNS4xODg3YzAtMi43NDI5Ljk5MzMtNC44MjcyLDIuNzk3LTUuODY3NiwxLjgwMjUtMS4wNDIyLDQuMTAzNC0uODYsNi40OC41MTQzTDcwLjQ3ODIsNDQuNTY3MmMyLjM3NTEsMS4zNzExLDMuNjgyNiwzLjI3MjUsMy42ODMyLDUuMzU0NXMtMS4zMDc2LDMuOTg0NS0zLjY4MzIsNS4zNTYyTDQ0Ljk1OTIsNzAuMDExNEE3LjkzODQsNy45Mzg0LDAsMCwxLDQxLjAzNTksNzEuMTlabS4wMDY1LTQwLjEyM2EyLjY3OTQsMi42Nzk0LDAsMCwwLTEuMzU4Mi4zNDEzYy0xLjAyNjMuNTkyNi0xLjU5MTIsMS45MzQ5LTEuNTkxMiwzLjc4VjY0LjY1NjNjMCwxLjg0NDkuNTY0OSwzLjE4NjYsMS41OTA2LDMuNzc5MSwxLjAyODEuNTkzMiwyLjQ3MzMuNDEwOCw0LjA3LS41MTJMNjkuMjczLDUzLjE5MDZjMS41OTgzLS45MjI3LDIuNDc4LTIuMDgzOCwyLjQ3OC0zLjI2ODlzLS44OC0yLjM0NDUtMi40NzgtMy4yNjY2TDQzLjc1NCwzMS45MjI3QTUuNTY4NSw1LjU2ODUsMCwwLDAsNDEuMDQyMywzMS4wNjcxWiIgZmlsdGVyPSJ1cmwoI2YxKSIvPjwvc3ZnPgo=","base64"),W=Z("PHN2ZyBjbGFzcz0iZnAtcGxheS1yb3VuZGVkLWZpbGwiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDEwMCAxMDAiPgogIDxkZWZzPjxzdHlsZT4uYXtmaWxsOiMwMDA7b3BhY2l0eTowLjY1O30uYntmaWxsOiNmZmY7b3BhY2l0eToxLjA7fTwvc3R5bGU+CiAgPC9kZWZzPjx0aXRsZT5wbGF5LXJvdW5kZWQtZmlsbDwvdGl0bGU+CiAgPHBhdGggY2xhc3M9ImZwLWNvbG9yLXBsYXkiIGQ9Ik00OS45MjE3LS4wNzhhNTAsNTAsMCwxLDAsNTAsNTBBNTAuMDU2NCw1MC4wNTY0LDAsMCwwLDQ5LjkyMTctLjA3OFoiLz4KICA8cGF0aCBjbGFzcz0iYiIgZD0iTTM1Ljk0MiwzNS4yMzIzYzAtNC43Mjg5LDMuMzUwNi02LjY2MzcsNy40NDYtNC4yOTcxTDY4LjgzLDQ1LjYyMzVjNC4wOTU2LDIuMzY0LDQuMDk1Niw2LjIzMTksMCw4LjU5NzdMNDMuMzg4LDY4LjkxYy00LjA5NTQsMi4zNjQtNy40NDYuNDMtNy40NDYtNC4yOTc5WiIgZmlsdGVyPSJ1cmwoI2YxKSIvPgogIDwvc3ZnPgogIAo=","base64"),U=Z("PHN2ZyBjbGFzcz0iZnAtcGxheS1zaGFycC1maWxsIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj4KICA8ZGVmcz4KICAgIDxzdHlsZT4uZnAtY29sb3ItcGxheXtvcGFjaXR5OjAuNjU7fS5jb250cm9sYnV0dG9ue2ZpbGw6I2ZmZjt9PC9zdHlsZT4KICA8L2RlZnM+CiAgPHRpdGxlPnBsYXktc2hhcnAtZmlsbDwvdGl0bGU+CiAgPHBhdGggY2xhc3M9ImZwLWNvbG9yLXBsYXkiIGQ9Ik00OS45MjE3LS4wNzhhNTAsNTAsMCwxLDAsNTAsNTBBNTAuMDU2NCw1MC4wNTY0LDAsMCwwLDQ5LjkyMTctLjA3OFoiLz4KICA8cG9seWdvbiBjbGFzcz0iY29udHJvbGJ1dHRvbiIgcG9pbnRzPSI3My42MDEgNTAgMzcuOTY4IDcwLjU3MyAzNy45NjggMjkuNDI3IDczLjYwMSA1MCIgZmlsdGVyPSJ1cmwoI2YxKSIvPgo8L3N2Zz4K","base64"),J=Z("PHN2ZyBjbGFzcz0iZnAtcGxheS1zaGFycC1vdXRsaW5lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA5OS44NDQgOTkuODQzNCI+PGRlZnM+PHN0eWxlPi5jb250cm9sYnV0dG9uYmd7b3BhY2l0eTowLjY1O30uY29udHJvbGJ1dHRvbntmaWxsOiNmZmY7fTwvc3R5bGU+CjwvZGVmcz48dGl0bGU+cGxheS1zaGFycC1vdXRsaW5lPC90aXRsZT48cGF0aCBjbGFzcz0iZnAtY29sb3ItcGxheSIgZD0iTTQ5LjkyMTctLjA3OGE1MCw1MCwwLDEsMCw1MCw1MEE1MC4wNTY0LDUwLjA1NjQsMCwwLDAsNDkuOTIxNy0uMDc4WiIvPjxwYXRoIGNsYXNzPSJjb250cm9sYnV0dG9uIiBkPSJNMzYuOTQ0Myw3Mi4yNDczVjI3LjI5MTZMNzUuODc3Niw0OS43N1ptMi4yLTQxLjE0NTVWNjguNDM3MUw3MS40Nzc2LDQ5Ljc3WiIgZmlsdGVyPSJ1cmwoI2YxKSIvPjwvc3ZnPgo=","base64"),B=Z("PHN2ZyBjbGFzcz0iZnAtcGF1c2Utcm91bmRlZC1vdXRsaW5lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCA5OS44NDM0IDk5Ljg0MzQiPjxkZWZzPjxzdHlsZT4uZnAtY29sb3ItcGxheXtvcGFjaXR5OjAuNjU7fS5yZWN0e2ZpbGw6I2ZmZjt9PC9zdHlsZT4KPC9kZWZzPjx0aXRsZT5wYXVzZS1yb3VuZGVkLW91dGxpbmU8L3RpdGxlPjxwYXRoIGNsYXNzPSJmcC1jb2xvci1wbGF5IiBkPSJNNDkuOTIxMi0uMDc4M2E1MCw1MCwwLDEsMCw1MC4wMDA2LDUwQTUwLjA1NjIsNTAuMDU2MiwwLDAsMCw0OS45MjEyLS4wNzgzWiIvPjxnIGNsYXNzPSJjb250cm9sYnV0dG9uIj48cGF0aCBjbGFzcz0icmVjdCIgZD0iTTM5LjAwMzYsNzEuOTcyNmE3LjU2NSw3LjU2NSwwLDAsMS03LjU1Ny03LjU1NnYtMjguOTlhNy41NTY1LDcuNTU2NSwwLDAsMSwxNS4xMTMsMHYyOC45OUE3LjU2NDgsNy41NjQ4LDAsMCwxLDM5LjAwMzYsNzEuOTcyNlptMC00MS45MDRhNS4zNjQ3LDUuMzY0NywwLDAsMC01LjM1OTMsNS4zNTgydjI4Ljk5YTUuMzU4Nyw1LjM1ODcsMCwwLDAsMTAuNzE3NCwwdi0yOC45OUE1LjM2NDUsNS4zNjQ1LDAsMCwwLDM5LjAwMzYsMzAuMDY4NloiIGZpbHRlcj0idXJsKCNmMSkiLz48cGF0aCBjbGFzcz0icmVjdCIgZD0iTTYwLjg0LDcxLjk3MjZhNy41NjQ4LDcuNTY0OCwwLDAsMS03LjU1Ni03LjU1NnYtMjguOTlhNy41NTY1LDcuNTU2NSwwLDAsMSwxNS4xMTMsMHYyOC45OUE3LjU2NSw3LjU2NSwwLDAsMSw2MC44NCw3MS45NzI2Wm0wLTQxLjkwNGE1LjM2NDUsNS4zNjQ1LDAsMCwwLTUuMzU4Miw1LjM1ODJ2MjguOTlhNS4zNTg3LDUuMzU4NywwLDAsMCwxMC43MTc0LDB2LTI4Ljk5QTUuMzY0Nyw1LjM2NDcsMCwwLDAsNjAuODQsMzAuMDY4NloiIGZpbHRlcj0idXJsKCNmMSkiLz48L2c+PC9zdmc+Cg==","base64"),F=Z("PHN2ZyBjbGFzcz0iZnAtcGF1c2Utcm91bmRlZC1maWxsIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAxMDAgMTAwIj48ZGVmcz48c3R5bGU+LmZwLWNvbG9yLXBsYXl7b3BhY2l0eTowLjY1O30ucmVjdHtmaWxsOiNmZmY7fTwvc3R5bGU+CjwvZGVmcz48dGl0bGU+cGF1c2Utcm91bmRlZC1maWxsPC90aXRsZT48cGF0aCBjbGFzcz0iZnAtY29sb3ItcGxheSIgZD0iTTQ5LjkyMTctLjA3OGE1MCw1MCwwLDEsMCw1MCw1MEE1MC4wNTY0LDUwLjA1NjQsMCwwLDAsNDkuOTIxNy0uMDc4WiIvPjxnIGNsYXNzPSJjb250cm9sYnV0dG9uIiBmaWx0ZXI9InVybCgjZjEpIj48cmVjdCBjbGFzcz0icmVjdCIgeD0iMzEuODQ0IiB5PSIyOC4xMjMxIiB3aWR0aD0iMTMuNDM2MiIgaGVpZ2h0PSI0My41OTczIiByeD0iNi43MTgxIiByeT0iNi43MTgxIi8+PHJlY3QgY2xhc3M9InJlY3QiIHg9IjU0LjU2MzgiIHk9IjI4LjEyMzEiIHdpZHRoPSIxMy40MzYyIiBoZWlnaHQ9IjQzLjU5NzMiIHJ4PSI2LjcxODEiIHJ5PSI2LjcxODEiLz48L2c+PC9zdmc+Cg==","base64"),H=Z("PHN2ZyBjbGFzcz0iZnAtcGF1c2Utc2hhcnAtZmlsbCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCI+PGRlZnM+PHN0eWxlPi5mcC1jb2xvci1wbGF5e29wYWNpdHk6MC42NTt9LnJlY3R7ZmlsbDojZmZmO308L3N0eWxlPgo8L2RlZnM+PHRpdGxlPnBhdXNlLXNoYXJwLWZpbGw8L3RpdGxlPjxwYXRoIGNsYXNzPSJmcC1jb2xvci1wbGF5IiBkPSJNNDkuOTIxNy0uMDc4YTUwLDUwLDAsMSwwLDUwLDUwQTUwLjA1NjQsNTAuMDU2NCwwLDAsMCw0OS45MjE3LS4wNzhaIi8+PGcgY2xhc3M9ImNvbnRyb2xidXR0b24iIGZpbHRlcj0idXJsKCNmMSkiPjxyZWN0IGNsYXNzPSJyZWN0IiB4PSIzMy41IiB5PSIzMC4xMDQyIiB3aWR0aD0iMTIuMjYzNCIgaGVpZ2h0PSIzOS43OTE3Ii8+PHJlY3QgY2xhc3M9InJlY3QiIHg9IjU0LjIzNjYiIHk9IjMwLjEwNDIiIHdpZHRoPSIxMi4yNjM0IiBoZWlnaHQ9IjM5Ljc5MTciLz48L2c+PC9zdmc+Cg==","base64"),V=Z("PHN2ZyBjbGFzcz0iZnAtcGF1c2Utc2hhcnAtb3V0bGluZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgOTkuODQzNCA5OS44NDM0Ij48ZGVmcz48c3R5bGU+LmZwLWNvbG9yLXBsYXl7b3BhY2l0eTowLjY1O30ucmVjdHtmaWxsOiNmZmY7fTwvc3R5bGU+CjwvZGVmcz48dGl0bGU+cGF1c2Utc2hhcnAtb3V0bGluZTwvdGl0bGU+PHBhdGggY2xhc3M9ImZwLWNvbG9yLXBsYXkiIGQ9Ik00OS45MjEyLS4wNzgzYTUwLDUwLDAsMSwwLDUwLjAwMDYsNTBBNTAuMDU2Miw1MC4wNTYyLDAsMCwwLDQ5LjkyMTItLjA3ODNaIi8+PGcgY2xhc3M9ImNvbnRyb2xidXR0b24iIGZpbHRlcj0idXJsKCNmMSkiPjxwYXRoIGNsYXNzPSJyZWN0IiBkPSJNNDYuODcwOSw2OS45NTMxSDMzLjEzODVWMjkuODlINDYuODcwOVpNMzUuMTQxNiw2Ny45NWg5LjcyNjJWMzEuODkzNUgzNS4xNDE2WiIvPjxwYXRoIGNsYXNzPSJyZWN0IiBkPSJNNjYuNzA0Nyw2OS45NTMxSDUyLjk3MjJWMjkuODlINjYuNzA0N1pNNTQuOTc1NCw2Ny45NWg5LjcyNjJWMzEuODkzNUg1NC45NzU0WiIvPjwvZz48L3N2Zz4K","base64"),X=Z("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","base64"),_=Z("PHN2ZyBjbGFzcz0iZnAtbG9hZGluZy1yb3VuZGVkLWZpbGwiIHdpZHRoPScxMTJweCcgaGVpZ2h0PScxMTJweCcgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwIDEwMCIgcHJlc2VydmVBc3BlY3RSYXRpbz0ieE1pZFlNaWQiPgogICAgPHJlY3QgeD0iMCIgeT0iMCIgd2lkdGg9Ijc2IiBoZWlnaHQ9Ijc2IiBmaWxsPSJyZ2JhKDAsMCwwLDApIiBjbGFzcz0iYmsiPjwvcmVjdD4KICAgIDxjaXJjbGUgY3g9IjAiIGN5PSIwIiByPSIxMCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjUgMjUpIiBmaWxsPSJyZ2JhKDAsMCwwLC41KSIgY2xhc3M9InNxIj4KICAgICAgPGFuaW1hdGUgYXR0cmlidXRlTmFtZT0iZmlsbCIgZnJvbT0icmdiYSgwLDAsMCwwKSIgdG89InJnYmEoMCwwLDAsLjUpIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgZHVyPSIxLjZzIiBiZWdpbj0iMC4wcyIgdmFsdWVzPSJyZ2JhKDAsMCwwLC41KTtyZ2JhKDAsMCwwLDApO3JnYmEoMCwwLDAsMCk7cmdiYSgwLDAsMCwuNSk7cmdiYSgwLDAsMCwuNSkiIGtleVRpbWVzPSIwOzAuMTswLjI7MC40OzEiPjwvYW5pbWF0ZT4KICAgICAgPGFuaW1hdGVUcmFuc2Zvcm0gYXR0cmlidXRlTmFtZT0idHJhbnNmb3JtIiB0eXBlPSJzY2FsZSIgYWRkaXRpdmU9InN1bSIgZnJvbT0iMC44IiB0bz0iMSIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiIGJlZ2luPSIwLjBzIiBkdXI9IjEuNnMiIHZhbHVlcz0iMTswLjg7MC44OzE7MSIga2V5VGltZXM9IjA7MC4xOzAuMjswLjQ7MSI+PC9hbmltYXRlVHJhbnNmb3JtPgogICAgPC9jaXJjbGU+CiAgICA8Y2lyY2xlIGN4PSIwIiBjeT0iMCIgcj0iMTAiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDUwIDI1KSIgZmlsbD0icmdiYSgwLDAsMCwuNSkiIGNsYXNzPSJzcSI+CiAgICAgIDxhbmltYXRlIGF0dHJpYnV0ZU5hbWU9ImZpbGwiIGZyb209InJnYmEoMCwwLDAsMCkiIHRvPSJyZ2JhKDAsMCwwLC41KSIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiIGR1cj0iMS42cyIgYmVnaW49IjAuNHMiIHZhbHVlcz0icmdiYSgwLDAsMCwuNSk7cmdiYSgwLDAsMCwwKTtyZ2JhKDAsMCwwLDApO3JnYmEoMCwwLDAsLjUpO3JnYmEoMCwwLDAsLjUpIiBrZXlUaW1lcz0iMDswLjE7MC4yOzAuNDsxIj48L2FuaW1hdGU+CiAgICAgIDxhbmltYXRlVHJhbnNmb3JtIGF0dHJpYnV0ZU5hbWU9InRyYW5zZm9ybSIgdHlwZT0ic2NhbGUiIGFkZGl0aXZlPSJzdW0iIGZyb209IjAuOCIgdG89IjEiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiBiZWdpbj0iMC40cyIgZHVyPSIxLjZzIiB2YWx1ZXM9IjE7MC44OzAuODsxOzEiIGtleVRpbWVzPSIwOzAuMTswLjI7MC40OzEiPjwvYW5pbWF0ZVRyYW5zZm9ybT4KICAgIDwvY2lyY2xlPgogICAgPGNpcmNsZSBjeD0iMCIgY3k9IjAiIHI9IjEwIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCA1MCkiIGZpbGw9InJnYmEoMCwwLDAsLjUpIiBjbGFzcz0ic3EiPgogICAgICA8YW5pbWF0ZSBhdHRyaWJ1dGVOYW1lPSJmaWxsIiBmcm9tPSJyZ2JhKDAsMCwwLDApIiB0bz0icmdiYSgwLDAsMCwuNSkiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiBkdXI9IjEuNnMiIGJlZ2luPSIwLjhzIiB2YWx1ZXM9InJnYmEoMCwwLDAsLjUpO3JnYmEoMCwwLDAsMCk7cmdiYSgwLDAsMCwwKTtyZ2JhKDAsMCwwLC41KTtyZ2JhKDAsMCwwLC41KSIga2V5VGltZXM9IjA7MC4xOzAuMjswLjQ7MSI+PC9hbmltYXRlPgogICAgICA8YW5pbWF0ZVRyYW5zZm9ybSBhdHRyaWJ1dGVOYW1lPSJ0cmFuc2Zvcm0iIHR5cGU9InNjYWxlIiBhZGRpdGl2ZT0ic3VtIiBmcm9tPSIwLjgiIHRvPSIxIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgYmVnaW49IjAuOHMiIGR1cj0iMS42cyIgdmFsdWVzPSIxOzAuODswLjg7MTsxIiBrZXlUaW1lcz0iMDswLjE7MC4yOzAuNDsxIj48L2FuaW1hdGVUcmFuc2Zvcm0+CiAgICA8L2NpcmNsZT4KICAgIDxjaXJjbGUgY3g9IjAiIGN5PSIwIiByPSIxMCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoMjUgNTApIiBmaWxsPSJyZ2JhKDAsMCwwLC41KSIgY2xhc3M9InNxIj4KICAgICAgPGFuaW1hdGUgYXR0cmlidXRlTmFtZT0iZmlsbCIgZnJvbT0icmdiYSgwLDAsMCwwKSIgdG89InJnYmEoMCwwLDAsLjUpIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgZHVyPSIxLjZzIiBiZWdpbj0iMS4ycyIgdmFsdWVzPSJyZ2JhKDAsMCwwLC41KTtyZ2JhKDAsMCwwLDApO3JnYmEoMCwwLDAsMCk7cmdiYSgwLDAsMCwuNSk7cmdiYSgwLDAsMCwuNSkiIGtleVRpbWVzPSIwOzAuMTswLjI7MC40OzEiPjwvYW5pbWF0ZT4KICAgICAgPGFuaW1hdGVUcmFuc2Zvcm0gYXR0cmlidXRlTmFtZT0idHJhbnNmb3JtIiB0eXBlPSJzY2FsZSIgYWRkaXRpdmU9InN1bSIgZnJvbT0iMC44IiB0bz0iMSIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiIGJlZ2luPSIxLjJzIiBkdXI9IjEuNnMiIHZhbHVlcz0iMTswLjg7MC44OzE7MSIga2V5VGltZXM9IjA7MC4xOzAuMjswLjQ7MSI+PC9hbmltYXRlVHJhbnNmb3JtPgogICAgPC9jaXJjbGU+Cjwvc3ZnPgo=","base64"),K=Z("PHN2ZyBjbGFzcz0iZnAtbG9hZGluZy1zaGFycC1maWxsIiB3aWR0aD0nMTEycHgnIGhlaWdodD0nMTEycHgnIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgdmlld0JveD0iMCAwIDEwMCAxMDAiIHByZXNlcnZlQXNwZWN0UmF0aW89InhNaWRZTWlkIj4KICA8cmVjdCB4PSIwIiB5PSIwIiB3aWR0aD0iNzYiIGhlaWdodD0iNzYiIGZpbGw9InJnYmEoMCwwLDAsMCkiIGNsYXNzPSJiayI+PC9yZWN0PgogIDxyZWN0IHg9Ii0xMCIgeT0iLTEwIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDI1IDI1KSIgZmlsbD0icmdiYSgwLDAsMCwuNSkiIGNsYXNzPSJzcSI+CiAgICA8YW5pbWF0ZSBhdHRyaWJ1dGVOYW1lPSJmaWxsIiBmcm9tPSJyZ2JhKDAsMCwwLDApIiB0bz0icmdiYSgwLDAsMCwuNSkiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiBkdXI9IjEuNnMiIGJlZ2luPSIwLjBzIiB2YWx1ZXM9InJnYmEoMCwwLDAsLjUpO3JnYmEoMCwwLDAsMCk7cmdiYSgwLDAsMCwwKTtyZ2JhKDAsMCwwLC41KTtyZ2JhKDAsMCwwLC41KSIga2V5VGltZXM9IjA7MC4xOzAuMjswLjQ7MSI+PC9hbmltYXRlPgogICAgPGFuaW1hdGVUcmFuc2Zvcm0gYXR0cmlidXRlTmFtZT0idHJhbnNmb3JtIiB0eXBlPSJzY2FsZSIgYWRkaXRpdmU9InN1bSIgZnJvbT0iMC44IiB0bz0iMSIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiIGJlZ2luPSIwLjBzIiBkdXI9IjEuNnMiIHZhbHVlcz0iMTswLjg7MC44OzE7MSIga2V5VGltZXM9IjA7MC4xOzAuMjswLjQ7MSI+PC9hbmltYXRlVHJhbnNmb3JtPgogIDwvcmVjdD4KICA8cmVjdCB4PSItMTAiIHk9Ii0xMCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjIwIiB0cmFuc2Zvcm09InRyYW5zbGF0ZSg1MCAyNSkiIGZpbGw9InJnYmEoMCwwLDAsLjUpIiBjbGFzcz0ic3EiPgogICAgPGFuaW1hdGUgYXR0cmlidXRlTmFtZT0iZmlsbCIgZnJvbT0icmdiYSgwLDAsMCwwKSIgdG89InJnYmEoMCwwLDAsLjUpIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgZHVyPSIxLjZzIiBiZWdpbj0iMC40cyIgdmFsdWVzPSJyZ2JhKDAsMCwwLC41KTtyZ2JhKDAsMCwwLDApO3JnYmEoMCwwLDAsMCk7cmdiYSgwLDAsMCwuNSk7cmdiYSgwLDAsMCwuNSkiIGtleVRpbWVzPSIwOzAuMTswLjI7MC40OzEiPjwvYW5pbWF0ZT4KICAgIDxhbmltYXRlVHJhbnNmb3JtIGF0dHJpYnV0ZU5hbWU9InRyYW5zZm9ybSIgdHlwZT0ic2NhbGUiIGFkZGl0aXZlPSJzdW0iIGZyb209IjAuOCIgdG89IjEiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiBiZWdpbj0iMC40cyIgZHVyPSIxLjZzIiB2YWx1ZXM9IjE7MC44OzAuODsxOzEiIGtleVRpbWVzPSIwOzAuMTswLjI7MC40OzEiPjwvYW5pbWF0ZVRyYW5zZm9ybT4KICA8L3JlY3Q+CiAgPHJlY3QgeD0iLTEwIiB5PSItMTAiIHdpZHRoPSIyMCIgaGVpZ2h0PSIyMCIgdHJhbnNmb3JtPSJ0cmFuc2xhdGUoNTAgNTApIiBmaWxsPSJyZ2JhKDAsMCwwLC41KSIgY2xhc3M9InNxIj4KICAgIDxhbmltYXRlIGF0dHJpYnV0ZU5hbWU9ImZpbGwiIGZyb209InJnYmEoMCwwLDAsMCkiIHRvPSJyZ2JhKDAsMCwwLC41KSIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiIGR1cj0iMS42cyIgYmVnaW49IjAuOHMiIHZhbHVlcz0icmdiYSgwLDAsMCwuNSk7cmdiYSgwLDAsMCwwKTtyZ2JhKDAsMCwwLDApO3JnYmEoMCwwLDAsLjUpO3JnYmEoMCwwLDAsLjUpIiBrZXlUaW1lcz0iMDswLjE7MC4yOzAuNDsxIj48L2FuaW1hdGU+CiAgICA8YW5pbWF0ZVRyYW5zZm9ybSBhdHRyaWJ1dGVOYW1lPSJ0cmFuc2Zvcm0iIHR5cGU9InNjYWxlIiBhZGRpdGl2ZT0ic3VtIiBmcm9tPSIwLjgiIHRvPSIxIiByZXBlYXRDb3VudD0iaW5kZWZpbml0ZSIgYmVnaW49IjAuOHMiIGR1cj0iMS42cyIgdmFsdWVzPSIxOzAuODswLjg7MTsxIiBrZXlUaW1lcz0iMDswLjE7MC4yOzAuNDsxIj48L2FuaW1hdGVUcmFuc2Zvcm0+CiAgPC9yZWN0PgogIDxyZWN0IHg9Ii0xMCIgeT0iLTEwIiB3aWR0aD0iMjAiIGhlaWdodD0iMjAiIHRyYW5zZm9ybT0idHJhbnNsYXRlKDI1IDUwKSIgZmlsbD0icmdiYSgwLDAsMCwuNSkiIGNsYXNzPSJzcSI+CiAgICA8YW5pbWF0ZSBhdHRyaWJ1dGVOYW1lPSJmaWxsIiBmcm9tPSJyZ2JhKDAsMCwwLDApIiB0bz0icmdiYSgwLDAsMCwuNSkiIHJlcGVhdENvdW50PSJpbmRlZmluaXRlIiBkdXI9IjEuNnMiIGJlZ2luPSIxLjJzIiB2YWx1ZXM9InJnYmEoMCwwLDAsLjUpO3JnYmEoMCwwLDAsMCk7cmdiYSgwLDAsMCwwKTtyZ2JhKDAsMCwwLC41KTtyZ2JhKDAsMCwwLC41KSIga2V5VGltZXM9IjA7MC4xOzAuMjswLjQ7MSI+PC9hbmltYXRlPgogICAgPGFuaW1hdGVUcmFuc2Zvcm0gYXR0cmlidXRlTmFtZT0idHJhbnNmb3JtIiB0eXBlPSJzY2FsZSIgYWRkaXRpdmU9InN1bSIgZnJvbT0iMC44IiB0bz0iMSIgcmVwZWF0Q291bnQ9ImluZGVmaW5pdGUiIGJlZ2luPSIxLjJzIiBkdXI9IjEuNnMiIHZhbHVlcz0iMTswLjg7MC44OzE7MSIga2V5VGltZXM9IjA7MC4xOzAuMjswLjQ7MSI+PC9hbmltYXRlVHJhbnNmb3JtPgogIDwvcmVjdD4KPC9zdmc+Cg==","base64"),Q=Z("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","base64");P(function(s,a){k.find(".fp-filters").forEach(k.removeNode);try{var e;document.body.appendChild(e=k.createElement("div",{},Z("PHN2ZyBjbGFzcz0iZnAtZmlsdGVycyIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMCAwIj4KICA8ZGVmcz4KICAgIDxmaWx0ZXIgaWQ9ImYxIiB4PSItMjAlIiB5PSItMjAlIiB3aWR0aD0iMjAwJSIgaGVpZ2h0PSIyMDAlIj4KICAgICAgPGZlT2Zmc2V0IHJlc3VsdD0ib2ZmT3V0IiBpbj0iU291cmNlQWxwaGEiIGR4PSIwIiBkeT0iMCIgLz4KICAgICAgPGZlQ29sb3JNYXRyaXggcmVzdWx0PSJtYXRyaXhPdXQiIGluPSJvZmZPdXQiIHR5cGU9Im1hdHJpeCIKICAgICAgdmFsdWVzPSIwLjMgMCAwIDAgMCAwIDAuMyAwIDAgMCAwIDAgMC4zIDAgMCAwIDAgMCAwLjQgMCIgLz4KICAgICAgPGZlR2F1c3NpYW5CbHVyIHJlc3VsdD0iYmx1ck91dCIgaW49Im1hdHJpeE91dCIgc3RkRGV2aWF0aW9uPSI0IiAvPgogICAgICA8ZmVCbGVuZCBpbj0iU291cmNlR3JhcGhpYyIgaW4yPSJibHVyT3V0IiBtb2RlPSJub3JtYWwiIC8+CiAgICA8L2ZpbHRlcj4KICA8L2RlZnM+Cjwvc3ZnPgo=","base64"))),k.css(e,{width:0,height:0,overflow:"hidden",position:"absolute",margin:0,padding:0})}catch(e){}var r,l=s.conf,u=P.support;k.find(".fp-ratio,.fp-ui",a).forEach(k.removeNode),k.addClass(a,"flowplayer"),a.appendChild(k.createElement("div",{className:"fp-ratio"}));var o=k.createElement("div",{className:"fp-ui"},'         <div class="fp-waiting">           {{ LOADING_SHARP_OUTLINE }}           {{ LOADING_SHARP_FILL }}           {{ LOADING_ROUNDED_FILL }}           {{ LOADING_ROUNDED_OUTLINE }}         </div>         <div class="fp-header">           <a class="fp-share fp-icon"></a>           <a class="fp-fullscreen fp-icon"></a>           <a class="fp-unload fp-icon"></a>         </div>         <p class="fp-speed-flash"></p>         <div class="fp-play fp-visible">           <a class="fp-icon fp-playbtn"></a>           {{ PLAY_ROUNDED_FILL }}           {{ PLAY_ROUNDED_OUTLINE }}           {{ PLAY_SHARP_FILL }}           {{ PLAY_SHARP_OUTLINE }}         </div>         <div class="fp-pause">           <a class="fp-icon fp-playbtn"></a>           {{ PAUSE_SHARP_OUTLINE }}           {{ PAUSE_SHARP_FILL }}           {{ PAUSE_ROUNDED_OUTLINE }}           {{ PAUSE_ROUNDED_FILL }}         </div>         <div class="fp-controls">            <a class="fp-icon fp-playbtn"></a>            <span class="fp-elapsed">00:00</span>            <div class="fp-timeline fp-bar">               <span class="fp-timestamp"></span>               <div class="fp-progress fp-color"></div>            </div>            <span class="fp-duration"></span>            <span class="fp-remaining"></span>            <div class="fp-volume">               <a class="fp-icon fp-volumebtn"></a>               <div class="fp-volumebar fp-bar-slider">                 <em></em><em></em><em></em><em></em><em></em><em></em><em></em>               </div>            </div>            <strong class="fp-speed fp-hidden"></strong>         </div>'.replace("{{ PAUSE_ROUNDED_FILL }}",F).replace("{{ PAUSE_ROUNDED_OUTLINE }}",B).replace("{{ PAUSE_SHARP_FILL }}",H).replace("{{ PAUSE_SHARP_OUTLINE }}",V).replace("{{ PLAY_SHARP_OUTLINE }}",J).replace("{{ PLAY_SHARP_FILL }}",U).replace("{{ PLAY_ROUNDED_OUTLINE }}",O).replace("{{ PLAY_ROUNDED_FILL }}",W).replace("{{ LOADING_ROUNDED_OUTLINE }}",X).replace("{{ LOADING_ROUNDED_FILL }}",_).replace("{{ LOADING_SHARP_FILL }}",K).replace("{{ LOADING_SHARP_OUTLINE }}",Q).replace(/url\(#/g,"url("+window.location.href.replace(window.location.hash,"").replace(/\#$/g,"")+"#"));function t(e){return k.find(".fp-"+e,a)[0]}a.appendChild(o);var n=t("waiting"),c=t("elapsed"),i=t("ratio"),f=t("speed-flash"),d=t("duration"),p=t("remaining"),h=t("timestamp"),g=k.css(i,"padding-top"),m=t("play"),v=t("pause"),y=t("timeline"),w=z(y,s.rtl),b=t("fullscreen"),I=t("volumebar"),M=G(I,{rtl:s.rtl}),C=k.hasClass(a,"no-toggle");w.disableAnimation(k.hasClass(a,"is-touch")),s.sliders=s.sliders||{},s.sliders.timeline=w,s.sliders.volume=M;var A=[];function S(e){k.css(i,"padding-top",100*e+"%"),u.inlineBlock||k.height(k.find("object",a)[0],k.height(a))}function E(e){e?(k.addClass(a,"is-mouseover"),k.removeClass(a,"is-mouseout")):(k.addClass(a,"is-mouseout"),k.removeClass(a,"is-mouseover"))}u.svg||k.html(n,"<p>loading &hellip;</p>"),l.ratio&&S(l.ratio);try{l.fullscreen||k.removeNode(b)}catch(e){k.removeNode(b)}s.on("dvrwindow",function(){w.disable(!1)}),s.on("ready",function(e,t,n){var i=t.video.duration;w.disable(t.disabled||!i),l.adaptiveRatio&&!isNaN(n.height/n.width)&&S(n.height/n.width),k.html([d,p],t.live?"Live":R(i)),k.toggleClass(a,"is-long",3600<=i),M.slide(t.volumeLevel),"flash"===t.engine.engineName?w.disableAnimation(!0,!0):w.disableAnimation(!1),k.find(".fp-title",o).forEach(k.removeNode),n.title&&k.prepend(o,k.createElement("div",{className:"fp-message fp-title"},n.title)),k.toggleClass(a,"has-title",!!n.title)}).on("unload",function(){g||l.splash||k.css(i,"paddingTop",""),w.slide(0),k.addClass(m,"fp-visible")}).on("buffer",function(e,t,n){var i=t.video,r=i.buffer/i.duration;!i.seekable&&u.seekable&&w.max(t.conf.live?1/0:r),n&&"number"!=typeof n||(n=[{start:0,end:i.buffer}]);var o=k.find(".fp-buffer",y);o.length!==n.length&&(o.forEach(k.removeNode),o=[]),n.forEach(function(e,t){var n=o[t]||k.createElement("div",{className:"fp-buffer"});k.css(n,{left:100*e.start/i.duration+"%",width:100*(e.end-e.start)/i.duration+"%"}),k.prepend(y,n)})}).on("speed",function(e,t,n){t.video.time&&(k.text(f,n+"x"),k.addClass(f,"fp-shown"),(A=A.filter(function(e){return clearTimeout(e),!1})).push(setTimeout(function(){k.addClass(f,"fp-hilite"),A.push(setTimeout(function(){k.removeClass(f,"fp-hilite"),A.push(setTimeout(function(){k.removeClass(f,"fp-shown")},300))},1e3))})))}).on("buffered",function(){w.max(1)}).on("progress seek",function(e,t,n){var i=s.video.duration,r=s.video.seekOffset||0,o=((n=n||s.video.time)-r)/(i-r);w.dragging||w.slide(o,s.seeking?0:250),k.toggleClass(a,"is-live-position",i-n<l.livePositionOffset),k.html(c,R(n)),k.html(p,R(i-n,!0))}).on("finish resume seek",function(e){k.toggleClass(a,"is-finished","finish"==e.type)}).on("resume",function(){k.addClass(m,"fp-visible"),setTimeout(function(){k.removeClass(m,"fp-visible")},300)}).on("pause",function(){k.addClass(v,"fp-visible"),setTimeout(function(){k.removeClass(v,"fp-visible")},300)}).on("stop",function(){k.html(c,R(0)),w.slide(0,100)}).on("finish",function(){k.html(c,R(s.video.duration)),w.slide(1,100),k.removeClass(a,"is-seeking")}).on("beforeseek",function(){}).on("volume",function(){M.slide(s.volumeLevel)}).on("disable",function(){var e=s.disabled;w.disable(e),M.disable(e),k.toggleClass(a,"is-disabled",s.disabled)}).on("mute",function(e,t,n){k.toggleClass(a,"is-muted",n)}).on("error",function(e,t,n){if(k.removeClass(a,"is-loading"),k.removeClass(a,"is-seeking"),k.addClass(a,"is-error"),n){t.error=!0;var i=n.code;(n.message||"").match(/DECODER_ERROR_NOT_SUPPORTED/)&&(i=3);var r=t.message((t.engine&&t.engine.engineName||"html5")+": "+l.errors[i]);k.removeClass(a,"is-mouseover"),t.one("load progress",function(){r()})}}).one("resume ready",function(){var e=k.find("video.fp-engine",a)[0];if(e&&(!k.width(e)||!k.height(e))){var t=a.style.overflow;a.style.overflow="visible",setTimeout(function(){t?a.style.overflow=t:a.style.removeProperty("overflow")})}}),Y.on(a,"mouseenter mouseleave",function(e){if(!C){var t,n="mouseover"==e.type;if(E(n),n){var i=function(){E(!0),t=new Date};s.on("pause.x volume.x",i),Y.on(a,"mousemove.x",i),r=setInterval(function(){new Date-t>l.mouseoutTimeout&&(E(!1),t=new Date)},100)}else Y.off(a,"mousemove.x"),s.off("pause.x volume.x"),clearInterval(r)}}),Y.on(a,"mouseleave",function(){(w.dragging||M.dragging)&&(k.addClass(a,"is-mouseover"),k.removeClass(a,"is-mouseout"))}),Y.on(a,"click.player",function(e){if(!s.disabled)return k.hasClass(e.target,"fp-ui")||k.hasClass(e.target,"fp-engine")||e.flash||k.hasParent(e.target,".fp-play,.fp-pause")?(e.preventDefault&&e.preventDefault(),s.toggle()):void 0}),Y.on(a,"mousemove",".fp-timeline",function(e){var t=(e.pageX||e.clientX)-k.offset(y).left,n=t/k.width(y),i=s.video,r=i.duration-(void 0===i.seekOffset?0:i.seekOffset),o=(s.rtl?1-n:n)*r;if(!(n<0)){k.html(h,R(o));var a=t-k.width(h)/2;a<0&&(a=0),a>k.width(y)-k.width(h)&&(a=!1),!1!==a?k.css(h,{left:a+"px",right:"auto"}):k.css(h,{left:"auto",right:"0px"})}}),Y.on(a,"contextmenu",function(e){var t=window;if(!k.hasClass(a,"is-flash-disabled")){var n=k.find(".fp-context-menu",a)[0];n&&(e.preventDefault(),s.showMenu(n,{left:e.clientX-t.scrollX,top:e.clientY-t.scrollY}),Y.on(a,"click",".fp-context-menu",function(e){e.stopPropagation()}))}}),s.on("flashdisabled",function(e,t,n){var i;k.addClass(a,"is-flash-disabled"),!1!==n&&(i=s.message("Seems something is blocking Adobe Flash from running")),s.one("ready progress",function(){k.removeClass(a,"is-flash-disabled"),i&&i()})}),l.poster&&k.css(a,"background-image","url("+l.poster+")");var D,N=k.css(a,"background-color"),j="none"!=k.css(a,"background-image")||N&&"rgba(0, 0, 0, 0)"!=N&&"transparent"!=N;if(j&&!l.splash){l.poster||(l.poster=!0);var L=function(){k.addClass(a,"is-poster"),k.addClass(m,"fp-visible"),s.poster=!0,s.on("resume.poster progress.poster beforeseek.poster",function(e){("beforeseek"===e.type||s.playing)&&(k.removeClass(a,"is-poster"),k.removeClass(m,"fp-visible"),s.poster=!1,s.off(".poster"))})};s.on("stop",function(){L()}),s.on("ready",function(e,t,n){n.index||n.autoplay||L()})}if("string"==typeof l.splash&&k.css(a,"background-image","url('"+l.splash+"')"),!j&&s.forcedSplash&&k.css(a,"background-color","#555"),Y.on(a,"click",".fp-toggle, .fp-play, .fp-playbtn",function(){s.disabled||s.toggle()}),Y.on(a,"click",".fp-volumebtn",function(){s.mute()}),Y.on(a,"click",".fp-fullscreen",function(){s.fullscreen()}),Y.on(a,"click",".fp-unload",function(){s.unload()}),Y.on(y,"slide",function(e){s.seeking=!0,s.seekTo(10*e)}),Y.on(I,"slide",function(e){s.volume(e)}),Y.on(a,"click",".fp-duration,.fp-remaining",function(){if(s.dvr)return s.seekTo(10);k.toggleClass(a,"is-inverted")}),E(C),s.on("shutdown",function(){Y.off(y),Y.off(I),D&&window.cancelAnimationFrame(D),k.removeNode(o),k.find(".fp-ratio",a).forEach(k.removeNode)}),"function"==typeof window.requestAnimationFrame){var x=k.find(".fp-player",a)[0]||a,T=function(){k.toggleClass(a,"is-tiny",x.clientWidth<400),k.toggleClass(a,"is-small",x.clientWidth<600&&400<=x.clientWidth),D=window.requestAnimationFrame(T)};D=window.requestAnimationFrame(T)}}),t.exports.format=R}).call(this,e("buffer").Buffer)},{"../common":1,"../flowplayer":31,"./ui/bar-slider":28,"./ui/slider":29,bean:34,buffer:35}],28:[function(e,t,n){var c=e("bean"),f=e("../../common");t.exports=function(a,e){var r=(e=e||{}).activeClass||"fp-color",o=e.inactiveClass||"fp-grey",t=e.childSelector||"em",s=!!e.rtl,n=!1,l=f.find(t,a).length,i={unload:function(){c.off(a,".barslider")},slide:function(i,e){f.find(t,a).forEach(function(e,t){var n=t/l<i;f.toggleClass(e,r,n),f.toggleClass(e,o,!n)}),e&&c.fire(a,"slide",[i])},disable:function(e){n=e}};return c.on(a,"mousedown.sld touchstart.sld",function(e){e.preventDefault(),n||(i.slide(u(e),!0),c.on(a,"mousemove.sld touchmove.sld",function(e){e.preventDefault(),i.slide(u(e),!0)}),c.one(document,"mouseup.sld touchup.sld",function(){c.off(a,"mousemove.sld touchmove.sld")}))}),i;function u(e){var t=e.pageX||e.clientX,n=f.offset(a),i=f.width(a);!t&&e.originalEvent&&e.originalEvent.touches&&e.originalEvent.touches.length&&(t=e.originalEvent.touches[0].pageX);var r=t-n.left,o=(r=Math.max(0,Math.min(i,r)))/i;return s&&(o=1-o),o}}},{"../../common":1,bean:34}],29:[function(e,t,n){"use strict";var v=e("bean"),y=e("../../common");t.exports=function(o,r){var a,s,t,l,i,u,c=y.lastChild(o),f=!1,d=function(){s=y.offset(o),t=y.width(o),y.height(o),l=t,u=e(i)},p=function(e){a||e==m.value||i&&!(e<i)||(v.fire(o,"slide",[e]),m.value=e)},h=function(e){var t=e.pageX||e.clientX;!t&&e.originalEvent&&e.originalEvent.touches&&e.originalEvent.touches.length&&(t=e.originalEvent.touches[0].pageX);var n=t-s.left,i=(n=Math.max(0,Math.min(u||l,n)))/l;return r&&(i=1-i),g(i,0,!0)},g=function(e,t){void 0===t&&(t=0),1<e&&(e=1);var n=Math.round(1e3*e)/10+"%";return(!i||e<=i)&&(f?y.removeClass(c,"animated"):(y.addClass(c,"animated"),y.css(c,"transition-duration",(t||0)+"ms")),y.css(c,"width",n)),e},e=function(e){return Math.max(0,Math.min(l,e*t))},m={max:function(e){i=e},disable:function(e){a=e},slide:function(e,t,n){d(),n&&p(e),g(e,t)},disableAnimation:function(e,t){f=!1!==e,y.toggleClass(o,"no-animation",!!t)}};return d(),v.on(o,"mousedown.sld touchstart",function(e){if(e.preventDefault(),!a){var t=(n=p,i=100,function(){r||(n.apply(this,arguments),r=1,setTimeout(function(){r=0},i))});d(),m.dragging=!0,y.addClass(o,"is-dragging"),p(h(e)),v.on(document,"mousemove.sld touchmove.sld",function(e){e.preventDefault(),t(h(e))}),v.one(document,"mouseup touchend",function(){m.dragging=!1,y.removeClass(o,"is-dragging"),v.off(document,"mousemove.sld touchmove.sld")})}var n,i,r}),m}},{"../../common":1,bean:34}],30:[function(e,t,n){t.exports=function(e,t,n){try{!function(e){var t=document.createElement("textarea");t.value=e,t.style.opacity=0,t.style.position="absolute",document.body.appendChild(t),t.select();var n=document.execCommand("copy");if(document.body.removeChild(t),!n)throw new Error("Unsuccessfull")}(e),t()}catch(e){n(e)}}},{}],31:[function(e,t,n){"use strict";var g=e("extend-object"),m=e("is-function"),v=e("bean"),i=e("./ext/ui/slider"),r=e("./ext/ui/bar-slider"),y=e("./common"),w=e("./ext/events"),b=[],I=[],o=window.onbeforeunload;window.onbeforeunload=function(e){if(b.forEach(function(e){e.conf.splash?e.unload():e.bind("error",function(){y.find(".flowplayer.is-error .fp-message").forEach(y.removeNode)})}),o)return o(e)};var a=/Safari/.exec(navigator.userAgent)&&!/Chrome/.exec(navigator.userAgent),s=/(\d+\.\d+) Safari/.exec(navigator.userAgent),l=s?Number(s[1]):100,M=t.exports=function(e,t,n){if(m(e))return I.push(e);if("number"==typeof e||void 0===e)return b[e||0];if(e.nodeType){if(null!==e.getAttribute("data-flowplayer-instance-id"))return b[e.getAttribute("data-flowplayer-instance-id")];if(!t)return;return c(e,t,n)}if(e.jquery)return M(e[0],t,n);if("string"==typeof e){var i=y.find(e)[0];return i&&M(i,t,n)}};g(M,{version:"7.2.7",engines:[],engine:function(t){return M.engines.filter(function(e){return e.engineName===t})[0]},extensions:[],conf:{},set:function(e,t){"string"==typeof e?M.conf[e]=t:g(M.conf,e)},registerExtension:function(e,t){M.extensions.push([e,t])},support:{},defaults:{debug:!1,disabled:!1,fullscreen:window==window.top,keyboard:!0,ratio:9/16,adaptiveRatio:!1,rtmp:0,proxy:"best",hlsQualities:!0,seekStep:!1,splash:!1,live:!1,livePositionOffset:120,swf:"//releases.flowplayer.org/7.2.7/flowplayer.swf",swfHls:"//releases.flowplayer.org/7.2.7/flowplayerhls.swf",speeds:[.25,.5,1,1.5,2],tooltip:!0,mouseoutTimeout:5e3,mutedAutoplay:!0,clickToUnMute:!0,volume:1,errors:["","Video loading aborted","Network error","Video not properly encoded","Video file not found","Unsupported video","Skin not found","SWF file not found","Subtitles not found","Invalid RTMP URL","Unsupported video format. Try installing Adobe Flash."],errorUrls:["","","","","","","","","","","http://get.adobe.com/flashplayer/"],playlist:[],hlsFix:a&&l<8,disableInline:!1},bean:v,common:y,slider:i,barSlider:r,extend:g});var C=0,A=e("./ext/resolve");if(void 0!==window.jQuery){var u=window.jQuery;u(function(){"function"==typeof u.fn.flowplayer&&u('.flowplayer:has(video:not(.fp-engine),script[type="application/json"])').flowplayer()});u.fn.flowplayer=function(r,o){return this.each(function(){"string"==typeof r&&(r={swf:r}),m(r)&&(o=r,r={});var t=u(this),e=t.find('script[type="application/json"]'),n=e.length?JSON.parse(e.text()):function(i){if(!i.length)return{};var r=i.data()||{},o={};return u.each(["autoplay","loop","preload","poster"],function(e,t){var n=i.attr(t);void 0!==n&&-1!==["autoplay","poster"].indexOf(t)?o[t]=n||!0:void 0!==n&&(r[t]=n||!0)}),i[0].autoplay=i[0].preload=!1,r.subtitles=i.find("track").map(function(){var e=u(this);return{src:e.attr("src"),kind:e.attr("kind"),label:e.attr("label"),srclang:e.attr("srclang"),default:e.prop("default")}}).get(),r.sources=(new A).sourcesFromVideoTag(i,u),g(o,{clip:r})}(t.find("video")),i=c(this,u.extend({},r||{},n,t.data()),o);w.EVENTS.forEach(function(e){i.on(e+".jquery",function(e){t.trigger.call(t,e.type,e.detail&&e.detail.args)})}),t.data("flowplayer",i)})}}function c(e,t,n){t&&t.embed&&(t.embed=g({},M.defaults.embed,t.embed));var i=!1;try{void 0===M.conf.storage&&"object"==typeof window.localStorage&&(window.localStorage.flowplayerTestStorage="test",i=!0)}catch(e){}var r,o,a=e,s=g({},M.defaults,M.conf,t),l={},u=a.className,c=new A;y.addClass(a,"is-loading"),y.toggleClass(a,"no-flex",!M.support.flex),y.toggleClass(a,"no-svg",!M.support.svg);try{l=M.conf.storage||(i?window.localStorage:l)}catch(e){}if(s.volume="true"===l.muted?0:s.volume!==M.defaults.volume?s.volume:isNaN(l.volume)?s.volume:l.volume,s.debug=!!l.flowplayerDebug||s.debug,s.aspectRatio&&"string"==typeof s.aspectRatio){var f=s.aspectRatio.split(/[:\/]/);s.ratio=f[1]/f[0]}var d=a.currentStyle&&"rtl"===a.currentStyle.direction||window.getComputedStyle&&null!==window.getComputedStyle(a,null)&&"rtl"===window.getComputedStyle(a,null).getPropertyValue("direction");d&&y.addClass(a,"is-rtl");var p={conf:s,currentSpeed:1,volumeLevel:s.muted?0:void 0===s.volume?1*l.volume:s.volume,video:{},disabled:!1,finished:!1,loading:!1,muted:"true"==l.muted||s.muted,paused:!1,playing:!1,ready:!1,splash:!1,rtl:d,hijack:function(e){try{p.engine.suspendEngine()}catch(e){}p.hijacked=e},release:function(){try{p.engine.resumeEngine()}catch(e){}p.hijacked=!1},debug:function(){s.debug&&console.log.apply(console,["DEBUG"].concat([].slice.call(arguments)))},load:function(e,t){if(!p.error&&!p.loading){p.video={},p.finished=!1,e=e||s.clip,e=g({},c.resolve(e,s.clip.sources)),(p.playing||p.engine)&&(e.autoplay=!0);var n=h(e);if(!n)return setTimeout(function(){p.trigger("error",[p,{code:M.support.flashVideo?5:10}])})&&p;if(!n.engineName)throw new Error("engineName property of factory should be exposed");if(p.engine&&n.engineName===p.engine.engineName||(p.ready=!1,p.engine&&(p.engine.unload(),p.conf.autoplay=!0),o=p.engine=n(p,a),p.one("ready",function(){setTimeout(function(){p.muted?p.mute(!0,!0):o.volume(p.volumeLevel)})})),g(e,o.pick(e.sources.filter(function(e){return!e.engine||e.engine===o.engineName}))),e.src)p.trigger("load",[p,e,o],!0).defaultPrevented?p.loading=!1:(p.ready=!1,o.load(e),m(e)&&(t=e),t&&p.one("ready",t));return p}},pause:function(e){return p.hijacked?p.hijacked.pause(e)|p:(!p.ready||p.seeking||p.loading||(o.pause(),p.one("pause",e)),p)},resume:function(){if(!p.trigger("beforeresume",[p],!0).defaultPrevented)return p.hijacked?p.hijacked.resume()|p:(p.ready&&p.paused&&(o.resume(),p.finished&&(p.trigger("resume",[p]),p.finished=!1)),p)},toggle:function(){return p.ready?p.paused?p.resume():p.pause():p.load()},seek:function(e,t){if("boolean"==typeof e){var n=p.conf.seekStep||.1*p.video.duration;e=p.video.time+(e?n:-n),e=Math.min(Math.max(e,0),p.video.duration-.1)}if(void 0===e)return p;if(p.hijacked)return p.hijacked.seek(e,t)|p;p.ready&&(r=e,p.trigger("beforeseek",[p,e],!0).defaultPrevented?(p.seeking=!1,y.toggleClass(a,"is-seeking",p.seeking)):(o.seek(e),m(t)&&p.one("seek",t)));return p},seekTo:function(e,t){return void 0===e?p.seek(r,t):void 0!==p.video.seekOffset?p.seek(p.video.seekOffset+.1*(p.video.duration-p.video.seekOffset)*e,t):p.seek(.1*p.video.duration*e,t)},mute:function(e,t){return void 0===e&&(e=!p.muted),p.muted=e,t||(l.muted=e,l.volume=isNaN(l.volume)?s.volume:l.volume),void 0!==o.mute?o.mute(e):(p.volume(e?0:l.volume,!0),p.trigger("mute",[p,e])),p},volume:function(e,t){return p.ready&&(e=Math.min(Math.max(e,0),1),t||(l.volume=e),o.volume(e)),p},speed:function(e,t){return p.ready&&("boolean"==typeof e&&(e=s.speeds[s.speeds.indexOf(p.currentSpeed)+(e?1:-1)]||p.currentSpeed),o.speed(e),t&&a.one("speed",t)),p},stop:function(){return p.ready&&(p.pause(),!p.live||p.dvr?p.seek(0,function(){p.trigger("stop",[p])}):p.trigger("stop",[p])),p},unload:function(){return s.splash?(p.trigger("unload",[p]),o&&(o.unload(),p.engine=o=0)):p.stop(),p},shutdown:function(){p.unload(),p.trigger("shutdown",[p]),v.off(a),delete b[a.getAttribute("data-flowplayer-instance-id")],a.removeAttribute("data-flowplayer-instance-id")},disable:function(e){return void 0===e&&(e=!p.disabled),e!=p.disabled&&(p.disabled=e,p.trigger("disable",e)),p},registerExtension:function(e,t){"string"==typeof(e=e||[])&&(e=[e]),"string"==typeof(t=t||[])&&(t=[t]),e.forEach(function(e){p.extensions.js.push(e)}),t.forEach(function(e){p.extensions.css.push(e)})}};p.conf=g(p.conf,s),p.extensions={js:[],css:[]},M.extensions.forEach(function(e){p.registerExtension(e[0],e[1])}),w(p);var h=function(e){var n,i=M.engines;if(s.engine){var t=i.filter(function(e){return e.engineName===s.engine})[0];if(t&&e.sources.some(function(e){return(!e.engine||e.engine===t.engineName)&&t.canPlay(e.type,p.conf)}))return t}return s.enginePreference&&(i=M.engines.filter(function(e){return-1<s.enginePreference.indexOf(e.engineName)}).sort(function(e,t){return s.enginePreference.indexOf(e.engineName)-s.enginePreference.indexOf(t.engineName)})),e.sources.some(function(t){var e=i.filter(function(e){return(!t.engine||t.engine===e.engineName)&&e.canPlay(t.type,p.conf)}).shift();return e&&(n=e),!!e}),n};return a.getAttribute("data-flowplayer-instance-id")||(a.setAttribute("data-flowplayer-instance-id",C++),p.on("boot",function(){var e=M.support;(s.splash||y.hasClass(a,"is-splash")||!e.firstframe)&&(p.forcedSplash=!s.splash&&!y.hasClass(a,"is-splash"),p.splash=!0,s.splash||(s.splash=!0),y.addClass(a,"is-splash")),s.splash&&y.find("video",a).forEach(y.removeNode),(s.dvr||s.live||y.hasClass(a,"is-live"))&&(p.live=s.live=!0,p.dvr=s.dvr=!!s.dvr||y.hasClass(a,"is-dvr"),y.addClass(a,"is-live"),y.toggleClass(a,"is-dvr",p.dvr)),I.forEach(function(e){e(p,a)}),b.push(p),s.splash?p.unload():p.load(),s.disabled&&p.disable(),p.one("ready",n),p.one("shutdown",function(){a.className=u})}).on("load",function(e,t,n){s.splash&&y.find(".flowplayer.is-ready,.flowplayer.is-loading").forEach(function(e){var t=e.getAttribute("data-flowplayer-instance-id");if(t!==a.getAttribute("data-flowplayer-instance-id")){var n=b[Number(t)];n&&n.conf.splash&&n.unload()}}),y.addClass(a,"is-loading"),t.loading=!0,void 0===n.live&&void 0===n.dvr||(y.toggleClass(a,"is-live",n.dvr||n.live),y.toggleClass(a,"is-dvr",!!n.dvr),t.live=n.dvr||n.live,t.dvr=!!n.dvr)}).on("ready",function(e,t,n){n.time=0,t.video=n,y.removeClass(a,"is-loading"),t.loading=!1,t.muted?t.mute(!0,!0):t.volume(t.volumeLevel);var i=t.conf.hlsFix&&/mpegurl/i.exec(n.type);y.toggleClass(a,"hls-fix",!!i)}).on("unload",function(){y.removeClass(a,"is-loading"),p.loading=!1}).on("ready unload",function(e){var t="ready"==e.type;y.toggleClass(a,"is-splash",!t),y.toggleClass(a,"is-ready",t),p.ready=t,p.splash=!t}).on("progress",function(e,t,n){t.video.time=n}).on("buffer",function(e,t,n){t.video.buffer="number"==typeof n?n:n.length?n[n.length-1].end:0}).on("speed",function(e,t,n){t.currentSpeed=n}).on("volume",function(e,t,n){t.volumeLevel=Math.round(100*n)/100,t.muted&&n&&t.mute(!1)}).on("beforeseek seek",function(e){p.seeking="beforeseek"==e.type,y.toggleClass(a,"is-seeking",p.seeking)}).on("ready pause resume unload finish stop",function(e){p.paused=/pause|finish|unload|stop/.test(e.type),p.paused=p.paused||"ready"===e.type&&!s.autoplay&&!p.playing,p.playing=!p.paused,y.toggleClass(a,"is-paused",p.paused),y.toggleClass(a,"is-playing",p.playing),p.load.ed||p.pause()}).on("finish",function(){p.finished=!0}).on("error",function(){})),p.trigger("boot",[p,a]),p}},{"./common":1,"./ext/events":12,"./ext/resolve":21,"./ext/ui/bar-slider":28,"./ext/ui/slider":29,bean:34,"extend-object":39,"is-function":42}],32:[function(e,t,n){e("es5-shim");var w=t.exports=e("./flowplayer");e("./ext/support"),e("./engine/embed"),e("./engine/hlsjs"),e("./engine/html5"),e("./engine/flash"),e("./ext/ui"),e("./ext/message"),e("./ext/keyboard"),e("./ext/playlist"),e("./ext/cuepoint"),e("./ext/subtitle"),e("./ext/analytics"),e("./ext/share"),e("./ext/facebook"),e("./ext/twitter"),e("./ext/embed"),e("./ext/airplay"),e("./ext/chromecast"),e("./ext/qsel"),e("./ext/menu"),e("./ext/fullscreen"),e("./ext/mobile"),w(function(e,l){var t,n,i,r,o=e.conf,a=w.common,s=a.createElement,u=o.swf.indexOf("flowplayer.org")&&o.e&&l.getAttribute("data-origin"),c=u?(t=u,(n=document.createElement("a")).href=t,a.hostname(n.hostname)):a.hostname(),f=(document,o.key);if("file:"==location.protocol&&(c="localhost"),e.load.ed=1,o.hostname=c,o.origin=u||location.href,u&&(r="is-embedded",-1===(i=l).className.split(" ").indexOf(r)&&(i.className+=" "+r)),"string"==typeof f&&(f=f.split(/,\s*/)),f&&"function"==typeof key_check&&key_check(f,c)){if(o.logo){var d=a.find(".fp-player",l)[0],p=o.logo.href||"",h=o.logo.src||o.logo,g=s("a",{className:"fp-logo",href:p});u&&(g.href=g.href||u),o.embed&&o.embed.popup&&(g.target="_blank");var m=s("img",{src:h});g.appendChild(m),(d||l).appendChild(g)}}else{g=s("a",{href:"https://flowplayer.com/hello/?from=player"});((d=a.find(".fp-player",l)[0])||l).appendChild(g);var v=s("div",{className:"fp-context-menu fp-menu"},'<strong>&copy; 2018 Flowplayer AB</strong><a href="https://flowplayer.com/hello/?from=player">About Flowplayer</a><a href="https://flowplayer.com/license">GPL based license</a>'),y=window.location.href.indexOf("localhost");7!==y&&(d||l).appendChild(v),e.on("pause resume finish unload ready",function(e,t){var n,i=-1;if(t.video.src)for(var r=[["org","flowplayer","drive"],["org","flowplayer","my"],["org","flowplayer","cdn"],["com","flowplayer","cdn"]],o=0;o<r.length&&-1===(i=t.video.src.indexOf("://"+r[o].reverse().join(".")));o++);if(/pause|resume/.test(e.type)&&"flash"!=t.engine.engineName&&4!=i&&5!=i){var a={display:"block",position:"absolute",left:"16px",bottom:"70px",zIndex:99999,width:"100px",height:"20px",backgroundImage:"url("+[".png","logo","/",".net",".cloudfront","d32wqyuo10o653","//","https:"].reverse().join("")+")"};for(var s in a)a.hasOwnProperty(s)&&(g.style[s]=a[s]);t.load.ed=(n=g,"none"!==window.getComputedStyle(n).display&&(7===y||v.parentNode==l||v.parentNode==d)),t.load.ed||t.pause()}else g.style.display="none"})}})},{"./engine/embed":2,"./engine/flash":3,"./engine/hlsjs":4,"./engine/html5":6,"./ext/airplay":7,"./ext/analytics":8,"./ext/chromecast":9,"./ext/cuepoint":10,"./ext/embed":11,"./ext/facebook":13,"./ext/fullscreen":14,"./ext/keyboard":15,"./ext/menu":16,"./ext/message":17,"./ext/mobile":18,"./ext/playlist":19,"./ext/qsel":20,"./ext/share":22,"./ext/subtitle":23,"./ext/support":25,"./ext/twitter":26,"./ext/ui":27,"./flowplayer":31,"es5-shim":38}],33:[function(e,t,n){"use strict";n.byteLength=function(e){var t=p(e),n=t[0],i=t[1];return 3*(n+i)/4-i},n.toByteArray=function(e){for(var t,n=p(e),i=n[0],r=n[1],o=new d((u=i,c=r,3*(u+c)/4-c)),a=0,s=0<r?i-4:i,l=0;l<s;l+=4)t=f[e.charCodeAt(l)]<<18|f[e.charCodeAt(l+1)]<<12|f[e.charCodeAt(l+2)]<<6|f[e.charCodeAt(l+3)],o[a++]=t>>16&255,o[a++]=t>>8&255,o[a++]=255&t;var u,c;2===r&&(t=f[e.charCodeAt(l)]<<2|f[e.charCodeAt(l+1)]>>4,o[a++]=255&t);1===r&&(t=f[e.charCodeAt(l)]<<10|f[e.charCodeAt(l+1)]<<4|f[e.charCodeAt(l+2)]>>2,o[a++]=t>>8&255,o[a++]=255&t);return o},n.fromByteArray=function(e){for(var t,n=e.length,i=n%3,r=[],o=0,a=n-i;o<a;o+=16383)r.push(l(e,o,a<o+16383?a:o+16383));1===i?(t=e[n-1],r.push(s[t>>2]+s[t<<4&63]+"==")):2===i&&(t=(e[n-2]<<8)+e[n-1],r.push(s[t>>10]+s[t>>4&63]+s[t<<2&63]+"="));return r.join("")};for(var s=[],f=[],d="undefined"!=typeof Uint8Array?Uint8Array:Array,i="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",r=0,o=i.length;r<o;++r)s[r]=i[r],f[i.charCodeAt(r)]=r;function p(e){var t=e.length;if(0<t%4)throw new Error("Invalid string. Length must be a multiple of 4");var n=e.indexOf("=");return-1===n&&(n=t),[n,n===t?0:4-n%4]}function l(e,t,n){for(var i,r,o=[],a=t;a<n;a+=3)i=(e[a]<<16&16711680)+(e[a+1]<<8&65280)+(255&e[a+2]),o.push(s[(r=i)>>18&63]+s[r>>12&63]+s[r>>6&63]+s[63&r]);return o.join("")}f["-".charCodeAt(0)]=62,f["_".charCodeAt(0)]=63},{}],34:[function(e,t,n){var i,r;i=this,r=function(e,t){e=e||"bean",t=t||this;var g,d,p,u,n,i,r,o,a,s,l,c,f,h,m,v,y,w,b,I=window,M=t[e],C=/[^\.]*(?=\..*)\.|.*/,A=/\..*/,S="addEventListener",E=document||{},D=E.documentElement||{},N=D[S],j=N?S:"attachEvent",L={},x=Array.prototype.slice,T=function(e,t){return e.split(t||" ")},Z=function(e){return"string"==typeof e},P=function(e){return"function"==typeof e},k=function(e,t,n){for(n=0;n<t.length;n++)t[n]&&(e[t[n]]=1);return e}({},T("click dblclick mouseup mousedown contextmenu mousewheel mousemultiwheel DOMMouseScroll mouseover mouseout mousemove selectstart selectend keydown keypress keyup orientationchange focus blur change reset select submit load unload beforeunload resize move DOMContentLoaded readystatechange message error abort scroll "+(N?"show input invalid touchstart touchmove touchend touchcancel gesturestart gesturechange gestureend textinput readystatechange pageshow pagehide popstate hashchange offline online afterprint beforeprint dragstart dragenter dragover dragleave drag drop dragend loadstart progress suspend emptied stalled loadmetadata loadeddata canplay canplaythrough playing waiting seeking seeked ended durationchange timeupdate play pause ratechange volumechange cuechange checking noupdate downloading cached updateready obsolete ":""))),Y=(w="compareDocumentPosition"in D?function(e,t){return t.compareDocumentPosition&&16==(16&t.compareDocumentPosition(e))}:"contains"in D?function(e,t){return(t=9===t.nodeType||t===window?D:t)!==e&&t.contains(e)}:function(e,t){for(;e=e.parentNode;)if(e===t)return 1;return 0},{mouseenter:{base:"mouseover",condition:b=function(e){var t=e.relatedTarget;return t?t!==this&&"xul"!==t.prefix&&!/document/.test(this.toString())&&!w(t,this):null==t}},mouseleave:{base:"mouseout",condition:b},mousewheel:{base:/Firefox/.test(navigator.userAgent)?"DOMMouseScroll":"mousewheel"}}),z=(i=T("altKey attrChange attrName bubbles cancelable ctrlKey currentTarget detail eventPhase getModifierState isTrusted metaKey relatedNode relatedTarget shiftKey srcElement target timeStamp type view which propertyName"),r=i.concat(T("button buttons clientX clientY dataTransfer fromElement offsetX offsetY pageX pageY screenX screenY toElement")),o=r.concat(T("wheelDelta wheelDeltaX wheelDeltaY wheelDeltaZ axis")),a=i.concat(T("char charCode key keyCode keyIdentifier keyLocation location")),s=i.concat(T("data")),l=i.concat(T("touches targetTouches changedTouches scale rotation")),c=i.concat(T("data origin source")),f=i.concat(T("state")),h=/over|out/,m=[{reg:/key/i,fix:function(e,t){return t.keyCode=e.keyCode||e.which,a}},{reg:/click|mouse(?!(.*wheel|scroll))|menu|drag|drop/i,fix:function(e,t,n){return t.rightClick=3===e.which||2===e.button,t.pos={x:0,y:0},e.pageX||e.pageY?(t.clientX=e.pageX,t.clientY=e.pageY):(e.clientX||e.clientY)&&(t.clientX=e.clientX+E.body.scrollLeft+D.scrollLeft,t.clientY=e.clientY+E.body.scrollTop+D.scrollTop),h.test(n)&&(t.relatedTarget=e.relatedTarget||e[("mouseover"==n?"from":"to")+"Element"]),r}},{reg:/mouse.*(wheel|scroll)/i,fix:function(){return o}},{reg:/^text/i,fix:function(){return s}},{reg:/^touch|^gesture/i,fix:function(){return l}},{reg:/^message$/i,fix:function(){return c}},{reg:/^popstate$/i,fix:function(){return f}},{reg:/.*/,fix:function(){return i}}],v={},(y=function(e,t,n){if(arguments.length&&(e=e||((t.ownerDocument||t.document||t).parentWindow||I).event,this.originalEvent=e,this.isNative=n,this.isBean=!0,e)){var i,r,o,a,s,l=e.type,u=e.target||e.srcElement;if(this.target=u&&3===u.nodeType?u.parentNode:u,n){if(!(s=v[l]))for(i=0,r=m.length;i<r;i++)if(m[i].reg.test(l)){v[l]=s=m[i].fix;break}for(i=(a=s(e,this,l)).length;i--;)!((o=a[i])in this)&&o in e&&(this[o]=e[o])}}}).prototype.preventDefault=function(){this.originalEvent.preventDefault?this.originalEvent.preventDefault():this.originalEvent.returnValue=!1},y.prototype.stopPropagation=function(){this.originalEvent.stopPropagation?this.originalEvent.stopPropagation():this.originalEvent.cancelBubble=!0},y.prototype.stop=function(){this.preventDefault(),this.stopPropagation(),this.stopped=!0},y.prototype.stopImmediatePropagation=function(){this.originalEvent.stopImmediatePropagation&&this.originalEvent.stopImmediatePropagation(),this.isImmediatePropagationStopped=function(){return!0}},y.prototype.isImmediatePropagationStopped=function(){return this.originalEvent.isImmediatePropagationStopped&&this.originalEvent.isImmediatePropagationStopped()},y.prototype.clone=function(e){var t=new y(this,this.element,this.isNative);return t.currentTarget=e,t},y),G=function(e,t){return N||t||e!==E&&e!==I?e:D},R=(u=function(n,i,r,o){var a=function(e,t){return i.apply(n,o?x.call(t,e?0:1).concat(o):t)},s=function(e,t){return i.__beanDel?i.__beanDel.ft(e.target,n):t},e=r?function(e){var t=s(e,this);if(r.apply(t,arguments))return e&&(e.currentTarget=t),a(e,arguments)}:function(e){return i.__beanDel&&(e=e.clone(s(e))),a(e,arguments)};return e.__beanDel=i.__beanDel,e},(n=function(e,t,n,i,r,o,a){var s,l=Y[t];"unload"==t&&(n=B(F,e,t,n,i)),l&&(l.condition&&(n=u(e,n,l.condition,o)),t=l.base||t),this.isNative=s=k[t]&&!!e[j],this.customType=!N&&!s&&t,this.element=e,this.type=t,this.original=i,this.namespaces=r,this.eventType=N||s?t:"propertychange",this.target=G(e,s),this[j]=!!this.target[j],this.root=a,this.handler=u(e,n,null,o)}).prototype.inNamespaces=function(e){var t,n,i=0;if(!e)return!0;if(!this.namespaces)return!1;for(t=e.length;t--;)for(n=this.namespaces.length;n--;)e[t]==this.namespaces[n]&&i++;return e.length===i},n.prototype.matches=function(e,t,n){return!(this.element!==e||t&&this.original!==t||n&&this.handler!==n)},n),O=(d={},p=function(e,t,n,i,r,o){var a=r?"r":"$";if(t&&"*"!=t){var s,l=0,u=d[a+t],c="*"==e;if(!u)return;for(s=u.length;l<s;l++)if((c||u[l].matches(e,n,i))&&!o(u[l],u,l,t))return}else for(var f in d)f.charAt(0)==a&&p(e,f.substr(1),n,i,r,o)},{has:function(e,t,n,i){var r,o=d[(i?"r":"$")+t];if(o)for(r=o.length;r--;)if(!o[r].root&&o[r].matches(e,n,null))return!0;return!1},get:function(e,t,n,i){var r=[];return p(e,t,n,null,i,function(e){return r.push(e)}),r},put:function(e){var t=!e.root&&!this.has(e.element,e.type,null,!1),n=(e.root?"r":"$")+e.type;return(d[n]||(d[n]=[])).push(e),t},del:function(e){p(e.element,e.type,null,e.handler,e.root,function(e,t,n){return t.splice(n,1),e.removed=!0,0===t.length&&delete d[(e.root?"r":"$")+e.type],!1})},entries:function(){var e,t=[];for(e in d)"$"==e.charAt(0)&&(t=t.concat(d[e]));return t}}),W=function(e){g=arguments.length?e:E.querySelectorAll?function(e,t){return t.querySelectorAll(e)}:function(){throw new Error("Bean: No selector engine installed")}},U=function(e,t){if(N||!t||!e||e.propertyName=="_on"+t){var n=O.get(this,t||e.type,null,!1),i=n.length,r=0;for(e=new z(e,this,!0),t&&(e.type=t);r<i&&!e.isImmediatePropagationStopped();r++)n[r].removed||n[r].handler.call(this,e)}},J=N?function(e,t,n){e[n?S:"removeEventListener"](t,U,!1)}:function(t,e,n,i){var r;n?(O.put(r=new R(t,i||e,function(e){U.call(t,e,i)},U,null,null,!0)),i&&null==t["_on"+i]&&(t["_on"+i]=0),r.target.attachEvent("on"+r.eventType,r.handler)):(r=O.get(t,i||e,U,!0)[0])&&(r.target.detachEvent("on"+r.eventType,r.handler),O.del(r))},B=function(e,t,n,i,r){return function(){i.apply(this,arguments),e(t,n,r)}},F=function(e,t,n,i){var r,o,a=t&&t.replace(A,""),s=O.get(e,a,null,!1),l={};for(r=0,o=s.length;r<o;r++)n&&s[r].original!==n||!s[r].inNamespaces(i)||(O.del(s[r]),!l[s[r].eventType]&&s[r][j]&&(l[s[r].eventType]={t:s[r].eventType,c:s[r].type}));for(r in l)O.has(e,l[r].t,null,!1)||J(e,l[r].t,!1,l[r].c)},H=N?function(e,t,n){var i=E.createEvent(e?"HTMLEvents":"UIEvents");i[e?"initEvent":"initUIEvent"](t,!0,!0,I,1),n.dispatchEvent(i)}:function(e,t,n){n=G(n,e),e?n.fireEvent("on"+t,E.createEventObject()):n["_on"+t]++},V=function(e,t,n){var i,r,o,a,s=Z(t);if(s&&0<t.indexOf(" ")){for(a=(t=T(t)).length;a--;)V(e,t[a],n);return e}if((r=s&&t.replace(A,""))&&Y[r]&&(r=Y[r].base),!t||s)(o=s&&t.replace(C,""))&&(o=T(o,".")),F(e,r,n,o);else if(P(t))F(e,null,t);else for(i in t)t.hasOwnProperty(i)&&V(e,i,t[i]);return e},X=function(e,t,n,i){var r,o,a,s,l,u,c;if(void 0!==n||"object"!=typeof t){var f,d,p,h;for(P(n)?(l=x.call(arguments,3),i=r=n):(r=i,l=x.call(arguments,4),d=r,(h=function(e){var t=p(e.target,this);t&&d.apply(t,arguments)}).__beanDel={ft:p=function(e,t){for(var n,i=Z(f)?g(f,t):f;e&&e!==t;e=e.parentNode)for(n=i.length;n--;)if(i[n]===e)return e},selector:f=n},i=h),a=T(t),this===L&&(i=B(V,e,t,i,r)),s=a.length;s--;)c=O.put(u=new R(e,a[s].replace(A,""),i,r,T(a[s].replace(C,""),"."),l,!1)),u[j]&&c&&J(e,u.eventType,!0,u.customType);return e}for(o in t)t.hasOwnProperty(o)&&X.call(this,e,o,t[o])},_={on:X,add:function(e,t,n,i){return X.apply(null,Z(n)?[e,n,t,i].concat(3<arguments.length?x.call(arguments,5):[]):x.call(arguments))},one:function(){return X.apply(L,arguments)},off:V,remove:V,clone:function(e,t,n){for(var i,r,o=O.get(t,n,null,!1),a=o.length,s=0;s<a;s++)o[s].original&&(i=[e,o[s].type],(r=o[s].handler.__beanDel)&&i.push(r.selector),i.push(o[s].original),X.apply(null,i));return e},fire:function(e,t,n){var i,r,o,a,s,l=T(t);for(i=l.length;i--;)if(t=l[i].replace(A,""),(a=l[i].replace(C,""))&&(a=T(a,".")),a||n||!e[j])for(s=O.get(e,t,null,!1),n=[!1].concat(n),r=0,o=s.length;r<o;r++)s[r].inNamespaces(a)&&s[r].handler.apply(e,n);else H(k[t],t,e);return e},Event:z,setSelectorEngine:W,noConflict:function(){return t[e]=M,this}};if(I.attachEvent){var K=function(){var e,t=O.entries();for(e in t)t[e].type&&"unload"!==t[e].type&&V(t[e].element,t[e].type);I.detachEvent("onunload",K),I.CollectGarbage&&I.CollectGarbage()};I.attachEvent("onunload",K)}return W(),_},void 0!==t&&t.exports?t.exports=r():i.bean=r()},{}],35:[function(t,e,G){(function(e){"use strict";var i=t("base64-js"),o=t("ieee754"),a=t("isarray");function n(){return f.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function s(e,t){if(n()<t)throw new RangeError("Invalid typed array length");return f.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=f.prototype:(null===e&&(e=new f(t)),e.length=t),e}function f(e,t,n){if(!(f.TYPED_ARRAY_SUPPORT||this instanceof f))return new f(e,t,n);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return u(this,e)}return r(this,e,t,n)}function r(e,t,n,i){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,n,i){if(t.byteLength,n<0||t.byteLength<n)throw new RangeError("'offset' is out of bounds");if(t.byteLength<n+(i||0))throw new RangeError("'length' is out of bounds");t=void 0===n&&void 0===i?new Uint8Array(t):void 0===i?new Uint8Array(t,n):new Uint8Array(t,n,i);f.TYPED_ARRAY_SUPPORT?(e=t).__proto__=f.prototype:e=c(e,t);return e}(e,t,n,i):"string"==typeof t?function(e,t,n){"string"==typeof n&&""!==n||(n="utf8");if(!f.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var i=0|p(t,n),r=(e=s(e,i)).write(t,n);r!==i&&(e=e.slice(0,r));return e}(e,t,n):function(e,t){if(f.isBuffer(t)){var n=0|d(t.length);return 0===(e=s(e,n)).length||t.copy(e,0,0,n),e}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(i=t.length)!=i?s(e,0):c(e,t);if("Buffer"===t.type&&a(t.data))return c(e,t.data)}var i;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function l(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function u(e,t){if(l(t),e=s(e,t<0?0:0|d(t)),!f.TYPED_ARRAY_SUPPORT)for(var n=0;n<t;++n)e[n]=0;return e}function c(e,t){var n=t.length<0?0:0|d(t.length);e=s(e,n);for(var i=0;i<n;i+=1)e[i]=255&t[i];return e}function d(e){if(e>=n())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+n().toString(16)+" bytes");return 0|e}function p(e,t){if(f.isBuffer(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var n=e.length;if(0===n)return 0;for(var i=!1;;)switch(t){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return k(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return Y(e).length;default:if(i)return k(e).length;t=(""+t).toLowerCase(),i=!0}}function h(e,t,n){var i=e[t];e[t]=e[n],e[n]=i}function g(e,t,n,i,r){if(0===e.length)return-1;if("string"==typeof n?(i=n,n=0):2147483647<n?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=r?0:e.length-1),n<0&&(n=e.length+n),n>=e.length){if(r)return-1;n=e.length-1}else if(n<0){if(!r)return-1;n=0}if("string"==typeof t&&(t=f.from(t,i)),f.isBuffer(t))return 0===t.length?-1:m(e,t,n,i,r);if("number"==typeof t)return t&=255,f.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?r?Uint8Array.prototype.indexOf.call(e,t,n):Uint8Array.prototype.lastIndexOf.call(e,t,n):m(e,[t],n,i,r);throw new TypeError("val must be string, number or Buffer")}function m(e,t,n,i,r){var o,a=1,s=e.length,l=t.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return-1;s/=a=2,l/=2,n/=2}function u(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(r){var c=-1;for(o=n;o<s;o++)if(u(e,o)===u(t,-1===c?0:o-c)){if(-1===c&&(c=o),o-c+1===l)return c*a}else-1!==c&&(o-=o-c),c=-1}else for(s<n+l&&(n=s-l),o=n;0<=o;o--){for(var f=!0,d=0;d<l;d++)if(u(e,o+d)!==u(t,d)){f=!1;break}if(f)return o}return-1}function v(e,t,n,i){n=Number(n)||0;var r=e.length-n;i?r<(i=Number(i))&&(i=r):i=r;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");o/2<i&&(i=o/2);for(var a=0;a<i;++a){var s=parseInt(t.substr(2*a,2),16);if(isNaN(s))return a;e[n+a]=s}return a}function y(e,t,n,i){return z(function(e){for(var t=[],n=0;n<e.length;++n)t.push(255&e.charCodeAt(n));return t}(t),e,n,i)}function w(e,t,n){return 0===t&&n===e.length?i.fromByteArray(e):i.fromByteArray(e.slice(t,n))}function b(e,t,n){n=Math.min(e.length,n);for(var i=[],r=t;r<n;){var o,a,s,l,u=e[r],c=null,f=239<u?4:223<u?3:191<u?2:1;if(r+f<=n)switch(f){case 1:u<128&&(c=u);break;case 2:128==(192&(o=e[r+1]))&&127<(l=(31&u)<<6|63&o)&&(c=l);break;case 3:o=e[r+1],a=e[r+2],128==(192&o)&&128==(192&a)&&2047<(l=(15&u)<<12|(63&o)<<6|63&a)&&(l<55296||57343<l)&&(c=l);break;case 4:o=e[r+1],a=e[r+2],s=e[r+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&65535<(l=(15&u)<<18|(63&o)<<12|(63&a)<<6|63&s)&&l<1114112&&(c=l)}null===c?(c=65533,f=1):65535<c&&(c-=65536,i.push(c>>>10&1023|55296),c=56320|1023&c),i.push(c),r+=f}return function(e){var t=e.length;if(t<=I)return String.fromCharCode.apply(String,e);var n="",i=0;for(;i<t;)n+=String.fromCharCode.apply(String,e.slice(i,i+=I));return n}(i)}G.Buffer=f,G.SlowBuffer=function(e){+e!=e&&(e=0);return f.alloc(+e)},G.INSPECT_MAX_BYTES=50,f.TYPED_ARRAY_SUPPORT=void 0!==e.TYPED_ARRAY_SUPPORT?e.TYPED_ARRAY_SUPPORT:function(){try{var e=new Uint8Array(1);return e.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===e.foo()&&"function"==typeof e.subarray&&0===e.subarray(1,1).byteLength}catch(e){return!1}}(),G.kMaxLength=n(),f.poolSize=8192,f._augment=function(e){return e.__proto__=f.prototype,e},f.from=function(e,t,n){return r(null,e,t,n)},f.TYPED_ARRAY_SUPPORT&&(f.prototype.__proto__=Uint8Array.prototype,f.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&f[Symbol.species]===f&&Object.defineProperty(f,Symbol.species,{value:null,configurable:!0})),f.alloc=function(e,t,n){return i=null,o=t,a=n,l(r=e),r<=0?s(i,r):void 0!==o?"string"==typeof a?s(i,r).fill(o,a):s(i,r).fill(o):s(i,r);var i,r,o,a},f.allocUnsafe=function(e){return u(null,e)},f.allocUnsafeSlow=function(e){return u(null,e)},f.isBuffer=function(e){return!(null==e||!e._isBuffer)},f.compare=function(e,t){if(!f.isBuffer(e)||!f.isBuffer(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var n=e.length,i=t.length,r=0,o=Math.min(n,i);r<o;++r)if(e[r]!==t[r]){n=e[r],i=t[r];break}return n<i?-1:i<n?1:0},f.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},f.concat=function(e,t){if(!a(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return f.alloc(0);var n;if(void 0===t)for(n=t=0;n<e.length;++n)t+=e[n].length;var i=f.allocUnsafe(t),r=0;for(n=0;n<e.length;++n){var o=e[n];if(!f.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(i,r),r+=o.length}return i},f.byteLength=p,f.prototype._isBuffer=!0,f.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)h(this,t,t+1);return this},f.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)h(this,t,t+3),h(this,t+1,t+2);return this},f.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)h(this,t,t+7),h(this,t+1,t+6),h(this,t+2,t+5),h(this,t+3,t+4);return this},f.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?b(this,0,e):function(e,t,n){var i=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if((n>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return A(this,t,n);case"utf8":case"utf-8":return b(this,t,n);case"ascii":return M(this,t,n);case"latin1":case"binary":return C(this,t,n);case"base64":return w(this,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return S(this,t,n);default:if(i)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),i=!0}}.apply(this,arguments)},f.prototype.equals=function(e){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e||0===f.compare(this,e)},f.prototype.inspect=function(){var e="",t=G.INSPECT_MAX_BYTES;return 0<this.length&&(e=this.toString("hex",0,t).match(/.{2}/g).join(" "),this.length>t&&(e+=" ... ")),"<Buffer "+e+">"},f.prototype.compare=function(e,t,n,i,r){if(!f.isBuffer(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===n&&(n=e?e.length:0),void 0===i&&(i=0),void 0===r&&(r=this.length),t<0||n>e.length||i<0||r>this.length)throw new RangeError("out of range index");if(r<=i&&n<=t)return 0;if(r<=i)return-1;if(n<=t)return 1;if(this===e)return 0;for(var o=(r>>>=0)-(i>>>=0),a=(n>>>=0)-(t>>>=0),s=Math.min(o,a),l=this.slice(i,r),u=e.slice(t,n),c=0;c<s;++c)if(l[c]!==u[c]){o=l[c],a=u[c];break}return o<a?-1:a<o?1:0},f.prototype.includes=function(e,t,n){return-1!==this.indexOf(e,t,n)},f.prototype.indexOf=function(e,t,n){return g(this,e,t,n,!0)},f.prototype.lastIndexOf=function(e,t,n){return g(this,e,t,n,!1)},f.prototype.write=function(e,t,n,i){if(void 0===t)i="utf8",n=this.length,t=0;else if(void 0===n&&"string"==typeof t)i=t,n=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(n)?(n|=0,void 0===i&&(i="utf8")):(i=n,n=void 0)}var r=this.length-t;if((void 0===n||r<n)&&(n=r),0<e.length&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var o,a,s,l,u,c,f,d,p,h=!1;;)switch(i){case"hex":return v(this,e,t,n);case"utf8":case"utf-8":return d=t,p=n,z(k(e,(f=this).length-d),f,d,p);case"ascii":return y(this,e,t,n);case"latin1":case"binary":return y(this,e,t,n);case"base64":return l=this,u=t,c=n,z(Y(e),l,u,c);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return a=t,s=n,z(function(e,t){for(var n,i,r,o=[],a=0;a<e.length&&!((t-=2)<0);++a)n=e.charCodeAt(a),i=n>>8,r=n%256,o.push(r),o.push(i);return o}(e,(o=this).length-a),o,a,s);default:if(h)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),h=!0}},f.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var I=4096;function M(e,t,n){var i="";n=Math.min(e.length,n);for(var r=t;r<n;++r)i+=String.fromCharCode(127&e[r]);return i}function C(e,t,n){var i="";n=Math.min(e.length,n);for(var r=t;r<n;++r)i+=String.fromCharCode(e[r]);return i}function A(e,t,n){var i=e.length;(!t||t<0)&&(t=0),(!n||n<0||i<n)&&(n=i);for(var r="",o=t;o<n;++o)r+=P(e[o]);return r}function S(e,t,n){for(var i=e.slice(t,n),r="",o=0;o<i.length;o+=2)r+=String.fromCharCode(i[o]+256*i[o+1]);return r}function E(e,t,n){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(n<e+t)throw new RangeError("Trying to access beyond buffer length")}function D(e,t,n,i,r,o){if(!f.isBuffer(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(r<t||t<o)throw new RangeError('"value" argument is out of bounds');if(n+i>e.length)throw new RangeError("Index out of range")}function N(e,t,n,i){t<0&&(t=65535+t+1);for(var r=0,o=Math.min(e.length-n,2);r<o;++r)e[n+r]=(t&255<<8*(i?r:1-r))>>>8*(i?r:1-r)}function j(e,t,n,i){t<0&&(t=4294967295+t+1);for(var r=0,o=Math.min(e.length-n,4);r<o;++r)e[n+r]=t>>>8*(i?r:3-r)&255}function L(e,t,n,i,r,o){if(n+i>e.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function x(e,t,n,i,r){return r||L(e,0,n,4),o.write(e,t,n,i,23,4),n+4}function T(e,t,n,i,r){return r||L(e,0,n,8),o.write(e,t,n,i,52,8),n+8}f.prototype.slice=function(e,t){var n,i=this.length;if((e=~~e)<0?(e+=i)<0&&(e=0):i<e&&(e=i),(t=void 0===t?i:~~t)<0?(t+=i)<0&&(t=0):i<t&&(t=i),t<e&&(t=e),f.TYPED_ARRAY_SUPPORT)(n=this.subarray(e,t)).__proto__=f.prototype;else{var r=t-e;n=new f(r,void 0);for(var o=0;o<r;++o)n[o]=this[o+e]}return n},f.prototype.readUIntLE=function(e,t,n){e|=0,t|=0,n||E(e,t,this.length);for(var i=this[e],r=1,o=0;++o<t&&(r*=256);)i+=this[e+o]*r;return i},f.prototype.readUIntBE=function(e,t,n){e|=0,t|=0,n||E(e,t,this.length);for(var i=this[e+--t],r=1;0<t&&(r*=256);)i+=this[e+--t]*r;return i},f.prototype.readUInt8=function(e,t){return t||E(e,1,this.length),this[e]},f.prototype.readUInt16LE=function(e,t){return t||E(e,2,this.length),this[e]|this[e+1]<<8},f.prototype.readUInt16BE=function(e,t){return t||E(e,2,this.length),this[e]<<8|this[e+1]},f.prototype.readUInt32LE=function(e,t){return t||E(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},f.prototype.readUInt32BE=function(e,t){return t||E(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},f.prototype.readIntLE=function(e,t,n){e|=0,t|=0,n||E(e,t,this.length);for(var i=this[e],r=1,o=0;++o<t&&(r*=256);)i+=this[e+o]*r;return(r*=128)<=i&&(i-=Math.pow(2,8*t)),i},f.prototype.readIntBE=function(e,t,n){e|=0,t|=0,n||E(e,t,this.length);for(var i=t,r=1,o=this[e+--i];0<i&&(r*=256);)o+=this[e+--i]*r;return(r*=128)<=o&&(o-=Math.pow(2,8*t)),o},f.prototype.readInt8=function(e,t){return t||E(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},f.prototype.readInt16LE=function(e,t){t||E(e,2,this.length);var n=this[e]|this[e+1]<<8;return 32768&n?4294901760|n:n},f.prototype.readInt16BE=function(e,t){t||E(e,2,this.length);var n=this[e+1]|this[e]<<8;return 32768&n?4294901760|n:n},f.prototype.readInt32LE=function(e,t){return t||E(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},f.prototype.readInt32BE=function(e,t){return t||E(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},f.prototype.readFloatLE=function(e,t){return t||E(e,4,this.length),o.read(this,e,!0,23,4)},f.prototype.readFloatBE=function(e,t){return t||E(e,4,this.length),o.read(this,e,!1,23,4)},f.prototype.readDoubleLE=function(e,t){return t||E(e,8,this.length),o.read(this,e,!0,52,8)},f.prototype.readDoubleBE=function(e,t){return t||E(e,8,this.length),o.read(this,e,!1,52,8)},f.prototype.writeUIntLE=function(e,t,n,i){(e=+e,t|=0,n|=0,i)||D(this,e,t,n,Math.pow(2,8*n)-1,0);var r=1,o=0;for(this[t]=255&e;++o<n&&(r*=256);)this[t+o]=e/r&255;return t+n},f.prototype.writeUIntBE=function(e,t,n,i){(e=+e,t|=0,n|=0,i)||D(this,e,t,n,Math.pow(2,8*n)-1,0);var r=n-1,o=1;for(this[t+r]=255&e;0<=--r&&(o*=256);)this[t+r]=e/o&255;return t+n},f.prototype.writeUInt8=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,1,255,0),f.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},f.prototype.writeUInt16LE=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):N(this,e,t,!0),t+2},f.prototype.writeUInt16BE=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,2,65535,0),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):N(this,e,t,!1),t+2},f.prototype.writeUInt32LE=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):j(this,e,t,!0),t+4},f.prototype.writeUInt32BE=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,4,4294967295,0),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},f.prototype.writeIntLE=function(e,t,n,i){if(e=+e,t|=0,!i){var r=Math.pow(2,8*n-1);D(this,e,t,n,r-1,-r)}var o=0,a=1,s=0;for(this[t]=255&e;++o<n&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+n},f.prototype.writeIntBE=function(e,t,n,i){if(e=+e,t|=0,!i){var r=Math.pow(2,8*n-1);D(this,e,t,n,r-1,-r)}var o=n-1,a=1,s=0;for(this[t+o]=255&e;0<=--o&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+n},f.prototype.writeInt8=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,1,127,-128),f.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},f.prototype.writeInt16LE=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):N(this,e,t,!0),t+2},f.prototype.writeInt16BE=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,2,32767,-32768),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):N(this,e,t,!1),t+2},f.prototype.writeInt32LE=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,4,2147483647,-2147483648),f.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):j(this,e,t,!0),t+4},f.prototype.writeInt32BE=function(e,t,n){return e=+e,t|=0,n||D(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),f.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):j(this,e,t,!1),t+4},f.prototype.writeFloatLE=function(e,t,n){return x(this,e,t,!0,n)},f.prototype.writeFloatBE=function(e,t,n){return x(this,e,t,!1,n)},f.prototype.writeDoubleLE=function(e,t,n){return T(this,e,t,!0,n)},f.prototype.writeDoubleBE=function(e,t,n){return T(this,e,t,!1,n)},f.prototype.copy=function(e,t,n,i){if(n||(n=0),i||0===i||(i=this.length),t>=e.length&&(t=e.length),t||(t=0),0<i&&i<n&&(i=n),i===n)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-n&&(i=e.length-t+n);var r,o=i-n;if(this===e&&n<t&&t<i)for(r=o-1;0<=r;--r)e[r+t]=this[r+n];else if(o<1e3||!f.TYPED_ARRAY_SUPPORT)for(r=0;r<o;++r)e[r+t]=this[r+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),t);return o},f.prototype.fill=function(e,t,n,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,n=this.length):"string"==typeof n&&(i=n,n=this.length),1===e.length){var r=e.charCodeAt(0);r<256&&(e=r)}if(void 0!==i&&"string"!=typeof i)throw new TypeError("encoding must be a string");if("string"==typeof i&&!f.isEncoding(i))throw new TypeError("Unknown encoding: "+i)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;var o;if(t>>>=0,n=void 0===n?this.length:n>>>0,e||(e=0),"number"==typeof e)for(o=t;o<n;++o)this[o]=e;else{var a=f.isBuffer(e)?e:k(new f(e,i).toString()),s=a.length;for(o=0;o<n-t;++o)this[o+t]=a[o%s]}return this};var Z=/[^+\/0-9A-Za-z-_]/g;function P(e){return e<16?"0"+e.toString(16):e.toString(16)}function k(e,t){var n;t=t||1/0;for(var i=e.length,r=null,o=[],a=0;a<i;++a){if(55295<(n=e.charCodeAt(a))&&n<57344){if(!r){if(56319<n){-1<(t-=3)&&o.push(239,191,189);continue}if(a+1===i){-1<(t-=3)&&o.push(239,191,189);continue}r=n;continue}if(n<56320){-1<(t-=3)&&o.push(239,191,189),r=n;continue}n=65536+(r-55296<<10|n-56320)}else r&&-1<(t-=3)&&o.push(239,191,189);if(r=null,n<128){if((t-=1)<0)break;o.push(n)}else if(n<2048){if((t-=2)<0)break;o.push(n>>6|192,63&n|128)}else if(n<65536){if((t-=3)<0)break;o.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return o}function Y(e){return i.toByteArray(function(e){var t;if((e=(t=e,t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")).replace(Z,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function z(e,t,n,i){for(var r=0;r<i&&!(r+n>=t.length||r>=e.length);++r)t[r+n]=e[r];return r}}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{"base64-js":33,ieee754:40,isarray:43}],36:[function(e,t,n){var l=e("indexof");function u(e){return!!e}t.exports=function(i){var e=i.classList;if(e)return e;var r={add:t,remove:n,contains:o,toggle:function(e){return o(e)?(n(e),!1):(t(e),!0)},toString:function(){return i.className},length:0,item:function(e){return a()[e]||null}};return r;function t(e){var t=a();-1<l(t,e)||(t.push(e),s(t))}function n(e){var t=a(),n=l(t,e);-1!==n&&(t.splice(n,1),s(t))}function o(e){return-1<l(a(),e)}function a(){var e=i.className;return function(e,t){for(var n=[],i=0;i<e.length;i++)t(e[i])&&n.push(e[i]);return n}(e.split(" "),u)}function s(e){var t=e.length;i.className=e.join(" "),r.length=t;for(var n=0;n<e.length;n++)r[n]=e[n];delete e[t]}}},{indexof:41}],37:[function(e,t,n){t.exports=function(e,t,n,i){if(i=(n=window.getComputedStyle)?n(e):e.currentStyle)return i[t.replace(/-(\w)/gi,function(e,t){return t.toUpperCase()})]}},{}],38:[function(e,n,i){!function(e,t){"use strict";"object"==typeof i?n.exports=t():e.returnExports=t()}(this,function(){var d,p,s=Array,e=s.prototype,l=Object,t=l.prototype,u=Function,n=u.prototype,v=String,i=v.prototype,y=Number,r=y.prototype,c=e.slice,o=e.splice,h=e.push,a=e.unshift,f=e.concat,g=e.join,m=n.call,w=n.apply,b=Math.max,I=Math.min,M=t.toString,C="function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag,A=Function.prototype.toString,S=/^\s*class /,E=function(e){try{var t=A.call(e).replace(/\/\/.*\n/g,"").replace(/\/\*[.\s\S]*\*\//g,"").replace(/\n/gm," ").replace(/ {2}/g," ");return S.test(t)}catch(e){return!1}},D=function(e){if(!e)return!1;if("function"!=typeof e&&"object"!=typeof e)return!1;if(C)return function(e){try{return!E(e)&&(A.call(e),!0)}catch(e){return!1}}(e);if(E(e))return!1;var t=M.call(e);return"[object Function]"===t||"[object GeneratorFunction]"===t},N=RegExp.prototype.exec;d=function(e){return"object"==typeof e&&(C?function(e){try{return N.call(e),!0}catch(e){return!1}}(e):"[object RegExp]"===M.call(e))};var j=String.prototype.valueOf;p=function(e){return"string"==typeof e||"object"==typeof e&&(C?function(e){try{return j.call(e),!0}catch(e){return!1}}(e):"[object String]"===M.call(e))};var L,x,T=l.defineProperty&&function(){try{var e={};for(var t in l.defineProperty(e,"x",{enumerable:!1,value:e}),e)return!1;return e.x===e}catch(e){return!1}}(),Z=(L=t.hasOwnProperty,x=T?function(e,t,n,i){!i&&t in e||l.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:!0,value:n})}:function(e,t,n,i){!i&&t in e||(e[t]=n)},function(e,t,n){for(var i in t)L.call(t,i)&&x(e,i,t[i],n)}),P=function(e){var t=typeof e;return null===e||"object"!==t&&"function"!==t},k=y.isNaN||function(e){return e!=e},Y=function(e){var t=+e;return k(t)?t=0:0!==t&&t!==1/0&&t!==-1/0&&(t=(0<t||-1)*Math.floor(Math.abs(t))),t},z=function(e){var t,n,i;if(P(e))return e;if(n=e.valueOf,D(n)&&(t=n.call(e),P(t)))return t;if(i=e.toString,D(i)&&(t=i.call(e),P(t)))return t;throw new TypeError},G=function(e){if(null==e)throw new TypeError("can't convert "+e+" to object");return l(e)},R=function(e){return e>>>0},O=function(){};Z(n,{bind:function(t){var n=this;if(!D(n))throw new TypeError("Function.prototype.bind called on incompatible "+n);for(var i,r=c.call(arguments,1),e=b(0,n.length-r.length),o=[],a=0;a<e;a++)h.call(o,"$"+a);return i=u("binder","return function ("+g.call(o,",")+"){ return binder.apply(this, arguments); }")(function(){if(this instanceof i){var e=w.call(n,this,f.call(r,c.call(arguments)));return l(e)===e?e:this}return w.call(n,t,f.call(r,c.call(arguments)))}),n.prototype&&(O.prototype=n.prototype,i.prototype=new O,O.prototype=null),i}});var W=m.bind(t.hasOwnProperty),U=m.bind(t.toString),J=m.bind(c),B=w.bind(c);if("object"==typeof document&&document&&document.documentElement)try{J(document.documentElement.childNodes)}catch(e){var F=J,H=B;J=function(e){for(var t=[],n=e.length;0<n--;)t[n]=e[n];return H(t,F(arguments,1))},B=function(e,t){return H(J(e),t)}}var V=m.bind(i.slice),X=m.bind(i.split),_=m.bind(i.indexOf),K=m.bind(h),Q=m.bind(t.propertyIsEnumerable),q=m.bind(e.sort),$=s.isArray||function(e){return"[object Array]"===U(e)},ee=1!==[].unshift(0);Z(e,{unshift:function(){return a.apply(this,arguments),this.length}},ee),Z(s,{isArray:$});var te=l("a"),ne="a"!==te[0]||!(0 in te),ie=function(e){var i=!0,t=!0,n=!1;if(e)try{e.call("foo",function(e,t,n){"object"!=typeof n&&(i=!1)}),e.call([1],function(){"use strict";t="string"==typeof this},"x")}catch(e){n=!0}return!!e&&!n&&i&&t};Z(e,{forEach:function(e){var t,n=G(this),i=ne&&p(this)?X(this,""):n,r=-1,o=R(i.length);if(1<arguments.length&&(t=arguments[1]),!D(e))throw new TypeError("Array.prototype.forEach callback must be a function");for(;++r<o;)r in i&&(void 0===t?e(i[r],r,n):e.call(t,i[r],r,n))}},!ie(e.forEach)),Z(e,{map:function(e){var t,n=G(this),i=ne&&p(this)?X(this,""):n,r=R(i.length),o=s(r);if(1<arguments.length&&(t=arguments[1]),!D(e))throw new TypeError("Array.prototype.map callback must be a function");for(var a=0;a<r;a++)a in i&&(o[a]=void 0===t?e(i[a],a,n):e.call(t,i[a],a,n));return o}},!ie(e.map)),Z(e,{filter:function(e){var t,n,i=G(this),r=ne&&p(this)?X(this,""):i,o=R(r.length),a=[];if(1<arguments.length&&(n=arguments[1]),!D(e))throw new TypeError("Array.prototype.filter callback must be a function");for(var s=0;s<o;s++)s in r&&(t=r[s],(void 0===n?e(t,s,i):e.call(n,t,s,i))&&K(a,t));return a}},!ie(e.filter)),Z(e,{every:function(e){var t,n=G(this),i=ne&&p(this)?X(this,""):n,r=R(i.length);if(1<arguments.length&&(t=arguments[1]),!D(e))throw new TypeError("Array.prototype.every callback must be a function");for(var o=0;o<r;o++)if(o in i&&!(void 0===t?e(i[o],o,n):e.call(t,i[o],o,n)))return!1;return!0}},!ie(e.every)),Z(e,{some:function(e){var t,n=G(this),i=ne&&p(this)?X(this,""):n,r=R(i.length);if(1<arguments.length&&(t=arguments[1]),!D(e))throw new TypeError("Array.prototype.some callback must be a function");for(var o=0;o<r;o++)if(o in i&&(void 0===t?e(i[o],o,n):e.call(t,i[o],o,n)))return!0;return!1}},!ie(e.some));var re=!1;e.reduce&&(re="object"==typeof e.reduce.call("es5",function(e,t,n,i){return i})),Z(e,{reduce:function(e){var t=G(this),n=ne&&p(this)?X(this,""):t,i=R(n.length);if(!D(e))throw new TypeError("Array.prototype.reduce callback must be a function");if(0===i&&1===arguments.length)throw new TypeError("reduce of empty array with no initial value");var r,o=0;if(2<=arguments.length)r=arguments[1];else for(;;){if(o in n){r=n[o++];break}if(++o>=i)throw new TypeError("reduce of empty array with no initial value")}for(;o<i;o++)o in n&&(r=e(r,n[o],o,t));return r}},!re);var oe=!1;e.reduceRight&&(oe="object"==typeof e.reduceRight.call("es5",function(e,t,n,i){return i})),Z(e,{reduceRight:function(e){var t,n=G(this),i=ne&&p(this)?X(this,""):n,r=R(i.length);if(!D(e))throw new TypeError("Array.prototype.reduceRight callback must be a function");if(0===r&&1===arguments.length)throw new TypeError("reduceRight of empty array with no initial value");var o=r-1;if(2<=arguments.length)t=arguments[1];else for(;;){if(o in i){t=i[o--];break}if(--o<0)throw new TypeError("reduceRight of empty array with no initial value")}if(o<0)return t;for(;o in i&&(t=e(t,i[o],o,n)),o--;);return t}},!oe);var ae=e.indexOf&&-1!==[0,1].indexOf(1,2);Z(e,{indexOf:function(e){var t=ne&&p(this)?X(this,""):G(this),n=R(t.length);if(0===n)return-1;var i=0;for(1<arguments.length&&(i=Y(arguments[1])),i=0<=i?i:b(0,n+i);i<n;i++)if(i in t&&t[i]===e)return i;return-1}},ae);var se=e.lastIndexOf&&-1!==[0,1].lastIndexOf(0,-3);Z(e,{lastIndexOf:function(e){var t=ne&&p(this)?X(this,""):G(this),n=R(t.length);if(0===n)return-1;var i=n-1;for(1<arguments.length&&(i=I(i,Y(arguments[1]))),i=0<=i?i:n-Math.abs(i);0<=i;i--)if(i in t&&e===t[i])return i;return-1}},se);var le,ue,ce=(ue=(le=[1,2]).splice(),2===le.length&&$(ue)&&0===ue.length);Z(e,{splice:function(e,t){return 0===arguments.length?[]:o.apply(this,arguments)}},!ce);var fe,de=(fe={},e.splice.call(fe,0,0,1),1===fe.length);Z(e,{splice:function(e,t){if(0===arguments.length)return[];var n=arguments;return this.length=b(Y(this.length),0),0<arguments.length&&"number"!=typeof t&&((n=J(arguments)).length<2?K(n,this.length-e):n[1]=Y(t)),o.apply(this,n)}},!de);var pe,he,ge=((pe=new s(1e5))[8]="x",pe.splice(1,1),7===pe.indexOf("x")),me=((he=[])[256]="a",he.splice(257,0,"b"),"a"===he[256]);Z(e,{splice:function(e,t){for(var n,i=G(this),r=[],o=R(i.length),a=Y(e),s=a<0?b(o+a,0):I(a,o),l=I(b(Y(t),0),o-s),u=0;u<l;)n=v(s+u),W(i,n)&&(r[u]=i[n]),u+=1;var c,f=J(arguments,2),d=f.length;if(d<l){u=s;for(var p=o-l;u<p;)n=v(u+l),c=v(u+d),W(i,n)?i[c]=i[n]:delete i[c],u+=1;for(var h=(u=o)-l+d;h<u;)delete i[u-1],u-=1}else if(l<d)for(u=o-l;s<u;)n=v(u+l-1),c=v(u+d-1),W(i,n)?i[c]=i[n]:delete i[c],u-=1;u=s;for(var g=0;g<f.length;++g)i[u]=f[g],u+=1;return i.length=o-l+d,r}},!ge||!me);var ve,ye=e.join;try{ve="1,2,3"!==Array.prototype.join.call("123",",")}catch(e){ve=!0}ve&&Z(e,{join:function(e){var t=void 0===e?",":e;return ye.call(p(this)?X(this,""):this,t)}},ve);var we="1,2"!==[1,2].join(void 0);we&&Z(e,{join:function(e){var t=void 0===e?",":e;return ye.call(this,t)}},we);var be,Ie=function(e){for(var t=G(this),n=R(t.length),i=0;i<arguments.length;)t[n+i]=arguments[i],i+=1;return t.length=n+i,n+i},Me=(be={},1!==Array.prototype.push.call(be,void 0)||1!==be.length||void 0!==be[0]||!W(be,0));Z(e,{push:function(e){return $(this)?h.apply(this,arguments):Ie.apply(this,arguments)}},Me);var Ce,Ae=1!==(Ce=[]).push(void 0)||1!==Ce.length||void 0!==Ce[0]||!W(Ce,0);Z(e,{push:Ie},Ae),Z(e,{slice:function(e,t){var n=p(this)?X(this,""):this;return B(n,arguments)}},ne);var Se=function(){try{[1,2].sort(null)}catch(e){try{[1,2].sort({})}catch(e){return!1}}return!0}(),Ee=function(){try{return[1,2].sort(/a/),!1}catch(e){}return!0}(),De=function(){try{return[1,2].sort(void 0),!0}catch(e){}return!1}();Z(e,{sort:function(e){if(void 0===e)return q(this);if(!D(e))throw new TypeError("Array.prototype.sort callback must be a function");return q(this,e)}},Se||!De||!Ee);var Ne=!Q({toString:null},"toString"),je=Q(function(){},"prototype"),Le=!W("x","0"),xe=function(e){var t=e.constructor;return t&&t.prototype===e},Te={$window:!0,$console:!0,$parent:!0,$self:!0,$frame:!0,$frames:!0,$frameElement:!0,$webkitIndexedDB:!0,$webkitStorageInfo:!0,$external:!0,$width:!0,$height:!0,$top:!0,$localStorage:!0},Ze=function(){if("undefined"==typeof window)return!1;for(var e in window)try{!Te["$"+e]&&W(window,e)&&null!==window[e]&&"object"==typeof window[e]&&xe(window[e])}catch(e){return!0}return!1}(),Pe=["toString","toLocaleString","valueOf","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","constructor"],ke=Pe.length,Ye=function(e){return"[object Arguments]"===U(e)},ze=Ye(arguments)?Ye:function(e){return null!==e&&"object"==typeof e&&"number"==typeof e.length&&0<=e.length&&!$(e)&&D(e.callee)};Z(l,{keys:function(e){var t=D(e),n=ze(e),i=null!==e&&"object"==typeof e,r=i&&p(e);if(!i&&!t&&!n)throw new TypeError("Object.keys called on a non-object");var o=[],a=je&&t;if(r&&Le||n)for(var s=0;s<e.length;++s)K(o,v(s));if(!n)for(var l in e)a&&"prototype"===l||!W(e,l)||K(o,v(l));if(Ne)for(var u=function(e){if("undefined"==typeof window||!Ze)return xe(e);try{return xe(e)}catch(e){return!1}}(e),c=0;c<ke;c++){var f=Pe[c];u&&"constructor"===f||!W(e,f)||K(o,f)}return o}});var Ge=l.keys&&function(){return 2===l.keys(arguments).length}(1,2),Re=l.keys&&function(){var e=l.keys(arguments);return 1!==arguments.length||1!==e.length||1!==e[0]}(1),Oe=l.keys;Z(l,{keys:function(e){return ze(e)?Oe(J(e)):Oe(e)}},!Ge||Re);var We,Ue,Je=0!==new Date(-0xc782b5b342b24).getUTCMonth(),Be=new Date(-0x55d318d56a724),Fe=new Date(14496624e5),He="Mon, 01 Jan -45875 11:59:59 GMT"!==Be.toUTCString();Be.getTimezoneOffset()<-720?(We="Tue Jan 02 -45875"!==Be.toDateString(),Ue=!/^Thu Dec 10 2015 \d\d:\d\d:\d\d GMT[-+]\d\d\d\d(?: |$)/.test(String(Fe))):(We="Mon Jan 01 -45875"!==Be.toDateString(),Ue=!/^Wed Dec 09 2015 \d\d:\d\d:\d\d GMT[-+]\d\d\d\d(?: |$)/.test(String(Fe)));var Ve=m.bind(Date.prototype.getFullYear),Xe=m.bind(Date.prototype.getMonth),_e=m.bind(Date.prototype.getDate),Ke=m.bind(Date.prototype.getUTCFullYear),Qe=m.bind(Date.prototype.getUTCMonth),qe=m.bind(Date.prototype.getUTCDate),$e=m.bind(Date.prototype.getUTCDay),et=m.bind(Date.prototype.getUTCHours),tt=m.bind(Date.prototype.getUTCMinutes),nt=m.bind(Date.prototype.getUTCSeconds),it=m.bind(Date.prototype.getUTCMilliseconds),rt=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],ot=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],at=function(e,t){return _e(new Date(t,e,0))};Z(Date.prototype,{getFullYear:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ve(this);return e<0&&11<Xe(this)?e+1:e},getMonth:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ve(this),t=Xe(this);return e<0&&11<t?0:t},getDate:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ve(this),t=Xe(this),n=_e(this);return e<0&&11<t?12===t?n:at(0,e+1)-n+1:n},getUTCFullYear:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ke(this);return e<0&&11<Qe(this)?e+1:e},getUTCMonth:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ke(this),t=Qe(this);return e<0&&11<t?0:t},getUTCDate:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=Ke(this),t=Qe(this),n=qe(this);return e<0&&11<t?12===t?n:at(0,e+1)-n+1:n}},Je),Z(Date.prototype,{toUTCString:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=$e(this),t=qe(this),n=Qe(this),i=Ke(this),r=et(this),o=tt(this),a=nt(this);return rt[e]+", "+(t<10?"0"+t:t)+" "+ot[n]+" "+i+" "+(r<10?"0"+r:r)+":"+(o<10?"0"+o:o)+":"+(a<10?"0"+a:a)+" GMT"}},Je||He),Z(Date.prototype,{toDateString:function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=this.getDay(),t=this.getDate(),n=this.getMonth(),i=this.getFullYear();return rt[e]+" "+ot[n]+" "+(t<10?"0"+t:t)+" "+i}},Je||We),(Je||Ue)&&(Date.prototype.toString=function(){if(!(this&&this instanceof Date))throw new TypeError("this is not a Date object.");var e=this.getDay(),t=this.getDate(),n=this.getMonth(),i=this.getFullYear(),r=this.getHours(),o=this.getMinutes(),a=this.getSeconds(),s=this.getTimezoneOffset(),l=Math.floor(Math.abs(s)/60),u=Math.floor(Math.abs(s)%60);return rt[e]+" "+ot[n]+" "+(t<10?"0"+t:t)+" "+i+" "+(r<10?"0"+r:r)+":"+(o<10?"0"+o:o)+":"+(a<10?"0"+a:a)+" GMT"+(0<s?"-":"+")+(l<10?"0"+l:l)+(u<10?"0"+u:u)},T&&l.defineProperty(Date.prototype,"toString",{configurable:!0,enumerable:!1,writable:!0}));var st=-621987552e5,lt="-000001",ut=Date.prototype.toISOString&&-1===new Date(st).toISOString().indexOf(lt),ct=Date.prototype.toISOString&&"1969-12-31T23:59:59.999Z"!==new Date(-1).toISOString(),ft=m.bind(Date.prototype.getTime);Z(Date.prototype,{toISOString:function(){if(!isFinite(this)||!isFinite(ft(this)))throw new RangeError("Date.prototype.toISOString called on non-finite value.");var e=Ke(this),t=Qe(this);e+=Math.floor(t/12);var n=[(t=(t%12+12)%12)+1,qe(this),et(this),tt(this),nt(this)];e=(e<0?"-":9999<e?"+":"")+V("00000"+Math.abs(e),0<=e&&e<=9999?-4:-6);for(var i=0;i<n.length;++i)n[i]=V("00"+n[i],-2);return e+"-"+J(n,0,2).join("-")+"T"+J(n,2).join(":")+"."+V("000"+it(this),-3)+"Z"}},ut||ct),function(){try{return Date.prototype.toJSON&&null===new Date(NaN).toJSON()&&-1!==new Date(st).toJSON().indexOf(lt)&&Date.prototype.toJSON.call({toISOString:function(){return!0}})}catch(e){return!1}}()||(Date.prototype.toJSON=function(e){var t=l(this),n=z(t);if("number"==typeof n&&!isFinite(n))return null;var i=t.toISOString;if(!D(i))throw new TypeError("toISOString property is not callable");return i.call(t)});var dt=1e15===Date.parse("+033658-09-27T01:46:40.000Z"),pt=!isNaN(Date.parse("2012-04-04T24:00:00.500Z"))||!isNaN(Date.parse("2012-11-31T23:59:59.000Z"))||!isNaN(Date.parse("2012-12-31T23:59:60.000Z"));if(isNaN(Date.parse("2000-01-01T00:00:00.000Z"))||pt||!dt){var ht=Math.pow(2,31)-1,gt=k(new Date(1970,0,1,0,0,0,ht+1).getTime());Date=function(h){var p=function(e,t,n,i,r,o,a){var s,l=arguments.length;if(this instanceof h){var u=o,c=a;if(gt&&7<=l&&ht<a){var f=Math.floor(a/ht)*ht,d=Math.floor(f/1e3);u+=d,c-=1e3*d}s=1===l&&v(e)===e?new h(p.parse(e)):7<=l?new h(e,t,n,i,r,u,c):6<=l?new h(e,t,n,i,r,u):5<=l?new h(e,t,n,i,r):4<=l?new h(e,t,n,i):3<=l?new h(e,t,n):2<=l?new h(e,t):1<=l?new h(e instanceof h?+e:e):new h}else s=h.apply(this,arguments);return P(s)||Z(s,{constructor:p},!0),s},g=new RegExp("^(\\d{4}|[+-]\\d{6})(?:-(\\d{2})(?:-(\\d{2})(?:T(\\d{2}):(\\d{2})(?::(\\d{2})(?:(\\.\\d{1,}))?)?(Z|(?:([-+])(\\d{2}):(\\d{2})))?)?)?)?$"),i=[0,31,59,90,120,151,181,212,243,273,304,334,365],m=function(e,t){var n=1<t?1:0;return i[t]+Math.floor((e-1969+n)/4)-Math.floor((e-1901+n)/100)+Math.floor((e-1601+n)/400)+365*(e-1970)};for(var e in h)W(h,e)&&(p[e]=h[e]);Z(p,{now:h.now,UTC:h.UTC},!0),p.prototype=h.prototype,Z(p.prototype,{constructor:p},!0);return Z(p,{parse:function(e){var t=g.exec(e);if(t){var n,i=y(t[1]),r=y(t[2]||1)-1,o=y(t[3]||1)-1,a=y(t[4]||0),s=y(t[5]||0),l=y(t[6]||0),u=Math.floor(1e3*y(t[7]||0)),c=Boolean(t[4]&&!t[8]),f="-"===t[9]?1:-1,d=y(t[10]||0),p=y(t[11]||0);return a<(0<s||0<l||0<u?24:25)&&s<60&&l<60&&u<1e3&&-1<r&&r<12&&d<24&&p<60&&-1<o&&o<m(i,r+1)-m(i,r)&&(n=1e3*(60*((n=60*(24*(m(i,r)+o)+a+d*f))+s+p*f)+l)+u,c&&(n=function(e){var t=0,n=e;if(gt&&ht<n){var i=Math.floor(n/ht)*ht,r=Math.floor(i/1e3);t+=r,n-=1e3*r}return y(new h(1970,0,1,0,0,t,n))}(n)),-864e13<=n&&n<=864e13)?n:NaN}return h.parse.apply(this,arguments)}}),p}(Date)}Date.now||(Date.now=function(){return(new Date).getTime()});var mt=r.toFixed&&("0.000"!==8e-5.toFixed(3)||"1"!==.9.toFixed(0)||"1.25"!==1.255.toFixed(2)||"1000000000000000128"!==(0xde0b6b3a7640080).toFixed(0)),vt={base:1e7,size:6,data:[0,0,0,0,0,0],multiply:function(e,t){for(var n=-1,i=t;++n<vt.size;)i+=e*vt.data[n],vt.data[n]=i%vt.base,i=Math.floor(i/vt.base)},divide:function(e){for(var t=vt.size,n=0;0<=--t;)n+=vt.data[t],vt.data[t]=Math.floor(n/e),n=n%e*vt.base},numToString:function(){for(var e=vt.size,t="";0<=--e;)if(""!==t||0===e||0!==vt.data[e]){var n=v(vt.data[e]);""===t?t=n:t+=V("0000000",0,7-n.length)+n}return t},pow:function e(t,n,i){return 0===n?i:n%2==1?e(t,n-1,i*t):e(t*t,n/2,i)},log:function(e){for(var t=0,n=e;4096<=n;)t+=12,n/=4096;for(;2<=n;)t+=1,n/=2;return t}};Z(r,{toFixed:function(e){var t,n,i,r,o,a,s,l;if(t=y(e),(t=k(t)?0:Math.floor(t))<0||20<t)throw new RangeError("Number.toFixed called with invalid number of decimals");if(n=y(this),k(n))return"NaN";if(n<=-1e21||1e21<=n)return v(n);if(i="",n<0&&(i="-",n=-n),r="0",1e-21<n)if(a=(o=vt.log(n*vt.pow(2,69,1))-69)<0?n*vt.pow(2,-o,1):n/vt.pow(2,o,1),a*=4503599627370496,0<(o=52-o)){for(vt.multiply(0,a),s=t;7<=s;)vt.multiply(1e7,0),s-=7;for(vt.multiply(vt.pow(10,s,1),0),s=o-1;23<=s;)vt.divide(1<<23),s-=23;vt.divide(1<<s),vt.multiply(1,1),vt.divide(2),r=vt.numToString()}else vt.multiply(0,a),vt.multiply(1<<-o,0),r=vt.numToString()+V("0.00000000000000000000",2,2+t);return r=0<t?(l=r.length)<=t?i+V("0.0000000000000000000",0,t-l+2)+r:i+V(r,0,l-t)+"."+V(r,l-t):i+r}},mt);var yt,wt,bt=function(){try{return"1"===1..toPrecision(void 0)}catch(e){return!0}}(),It=r.toPrecision;Z(r,{toPrecision:function(e){return void 0===e?It.call(this):It.call(this,e)}},bt),2!=="ab".split(/(?:ab)*/).length||4!==".".split(/(.?)(.?)/).length||"t"==="tesst".split(/(s)*/)[1]||4!=="test".split(/(?:)/,-1).length||"".split(/.?/).length||1<".".split(/()()/).length?(yt=void 0===/()??/.exec("")[1],wt=Math.pow(2,32)-1,i.split=function(e,t){var n=String(this);if(void 0===e&&0===t)return[];if(!d(e))return X(this,e,t);var i,r,o,a,s=[],l=(e.ignoreCase?"i":"")+(e.multiline?"m":"")+(e.unicode?"u":"")+(e.sticky?"y":""),u=0,c=new RegExp(e.source,l+"g");yt||(i=new RegExp("^"+c.source+"$(?!\\s)",l));var f=void 0===t?wt:R(t);for(r=c.exec(n);r&&!(u<(o=r.index+r[0].length)&&(K(s,V(n,u,r.index)),!yt&&1<r.length&&r[0].replace(i,function(){for(var e=1;e<arguments.length-2;e++)void 0===arguments[e]&&(r[e]=void 0)}),1<r.length&&r.index<n.length&&h.apply(s,J(r,1)),a=r[0].length,u=o,f<=s.length));)c.lastIndex===r.index&&c.lastIndex++,r=c.exec(n);return u===n.length?!a&&c.test("")||K(s,""):K(s,V(n,u)),f<s.length?J(s,0,f):s}):"0".split(void 0,0).length&&(i.split=function(e,t){return void 0===e&&0===t?[]:X(this,e,t)});var Mt,Ct=i.replace;(Mt=[],"x".replace(/x(.)?/g,function(e,t){K(Mt,t)}),1===Mt.length&&void 0===Mt[0])||(i.replace=function(r,o){var e=D(o),t=d(r)&&/\)[*?]/.test(r.source);if(e&&t){return Ct.call(this,r,function(e){var t=arguments.length,n=r.lastIndex;r.lastIndex=0;var i=r.exec(e)||[];return r.lastIndex=n,K(i,arguments[t-2],arguments[t-1]),o.apply(this,i)})}return Ct.call(this,r,o)});var At=i.substr,St="".substr&&"b"!=="0b".substr(-1);Z(i,{substr:function(e,t){var n=e;return e<0&&(n=b(this.length+e,0)),At.call(this,n,t)}},St);var Et="\t\n\v\f\r   ᠎             　\u2028\u2029\ufeff",Dt="["+Et+"]",Nt=new RegExp("^"+Dt+Dt+"*"),jt=new RegExp(Dt+Dt+"*$"),Lt=i.trim&&(Et.trim()||!"​".trim());Z(i,{trim:function(){if(null==this)throw new TypeError("can't convert "+this+" to object");return v(this).replace(Nt,"").replace(jt,"")}},Lt);var xt=m.bind(String.prototype.trim),Tt=i.lastIndexOf&&-1!=="abcあい".lastIndexOf("あい",2);Z(i,{lastIndexOf:function(e){if(null==this)throw new TypeError("can't convert "+this+" to object");for(var t=v(this),n=v(e),i=1<arguments.length?y(arguments[1]):NaN,r=k(i)?1/0:Y(i),o=I(b(r,0),t.length),a=n.length,s=o+a;0<s;){s=b(0,s-a);var l=_(V(t,s,o+a),n);if(-1!==l)return s+l}return-1}},Tt);var Zt,Pt,kt,Yt=i.lastIndexOf;if(Z(i,{lastIndexOf:function(e){return Yt.apply(this,arguments)}},1!==i.lastIndexOf.length),8===parseInt(Et+"08")&&22===parseInt(Et+"0x16")||(parseInt=(Zt=parseInt,Pt=/^[-+]?0[xX]/,function(e,t){var n=xt(String(e)),i=y(t)||(Pt.test(n)?16:10);return Zt(n,i)})),1/parseFloat("-0")!=-1/0&&(parseFloat=(kt=parseFloat,function(e){var t=xt(String(e)),n=kt(t);return 0===n&&"-"===V(t,0,1)?-0:n})),"RangeError: test"!==String(new RangeError("test"))){Error.prototype.toString=function(){if(null==this)throw new TypeError("can't convert "+this+" to object");var e=this.name;void 0===e?e="Error":"string"!=typeof e&&(e=v(e));var t=this.message;return void 0===t?t="":"string"!=typeof t&&(t=v(t)),e?t?e+": "+t:e:t}}if(T){var zt=function(e,t){if(Q(e,t)){var n=Object.getOwnPropertyDescriptor(e,t);n.configurable&&(n.enumerable=!1,Object.defineProperty(e,t,n))}};zt(Error.prototype,"message"),""!==Error.prototype.message&&(Error.prototype.message=""),zt(Error.prototype,"name")}if("/a/gim"!==String(/a/gim)){RegExp.prototype.toString=function(){var e="/"+this.source+"/";return this.global&&(e+="g"),this.ignoreCase&&(e+="i"),this.multiline&&(e+="m"),e}}})},{}],39:[function(e,t,n){var i=[],r=i.forEach,o=i.slice;t.exports=function(n){return r.call(o.call(arguments,1),function(e){if(e)for(var t in e)n[t]=e[t]}),n}},{}],40:[function(e,t,n){n.read=function(e,t,n,i,r){var o,a,s=8*r-i-1,l=(1<<s)-1,u=l>>1,c=-7,f=n?r-1:0,d=n?-1:1,p=e[t+f];for(f+=d,o=p&(1<<-c)-1,p>>=-c,c+=s;0<c;o=256*o+e[t+f],f+=d,c-=8);for(a=o&(1<<-c)-1,o>>=-c,c+=i;0<c;a=256*a+e[t+f],f+=d,c-=8);if(0===o)o=1-u;else{if(o===l)return a?NaN:1/0*(p?-1:1);a+=Math.pow(2,i),o-=u}return(p?-1:1)*a*Math.pow(2,o-i)},n.write=function(e,t,n,i,r,o){var a,s,l,u=8*o-r-1,c=(1<<u)-1,f=c>>1,d=23===r?Math.pow(2,-24)-Math.pow(2,-77):0,p=i?0:o-1,h=i?1:-1,g=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=c):(a=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-a))<1&&(a--,l*=2),2<=(t+=1<=a+f?d/l:d*Math.pow(2,1-f))*l&&(a++,l/=2),c<=a+f?(s=0,a=c):1<=a+f?(s=(t*l-1)*Math.pow(2,r),a+=f):(s=t*Math.pow(2,f-1)*Math.pow(2,r),a=0));8<=r;e[n+p]=255&s,p+=h,s/=256,r-=8);for(a=a<<r|s,u+=r;0<u;e[n+p]=255&a,p+=h,a/=256,u-=8);e[n+p-h]|=128*g}},{}],41:[function(e,t,n){var i=[].indexOf;t.exports=function(e,t){if(i)return e.indexOf(t);for(var n=0;n<e.length;++n)if(e[n]===t)return n;return-1}},{}],42:[function(e,t,n){t.exports=function(e){var t=i.call(e);return"[object Function]"===t||"function"==typeof e&&"[object RegExp]"!==t||"undefined"!=typeof window&&(e===window.setTimeout||e===window.alert||e===window.confirm||e===window.prompt)};var i=Object.prototype.toString},{}],43:[function(e,t,n){var i={}.toString;t.exports=Array.isArray||function(e){return"[object Array]"==i.call(e)}},{}],44:[function(e,T,Z){(function(x){!function(e){var t="object"==typeof Z&&Z&&!Z.nodeType&&Z,n="object"==typeof T&&T&&!T.nodeType&&T,i="object"==typeof x&&x;i.global!==i&&i.window!==i&&i.self!==i||(e=i);var r,o,v=2147483647,y=36,w=1,b=26,a=38,s=700,I=72,M=128,C="-",l=/^xn--/,u=/[^\x20-\x7E]/,c=/[\x2E\u3002\uFF0E\uFF61]/g,f={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},d=y-w,A=Math.floor,S=String.fromCharCode;function E(e){throw new RangeError(f[e])}function p(e,t){for(var n=e.length,i=[];n--;)i[n]=t(e[n]);return i}function h(e,t){var n=e.split("@"),i="";return 1<n.length&&(i=n[0]+"@",e=n[1]),i+p((e=e.replace(c,".")).split("."),t).join(".")}function D(e){for(var t,n,i=[],r=0,o=e.length;r<o;)55296<=(t=e.charCodeAt(r++))&&t<=56319&&r<o?56320==(64512&(n=e.charCodeAt(r++)))?i.push(((1023&t)<<10)+(1023&n)+65536):(i.push(t),r--):i.push(t);return i}function N(e){return p(e,function(e){var t="";return 65535<e&&(t+=S((e-=65536)>>>10&1023|55296),e=56320|1023&e),t+=S(e)}).join("")}function j(e,t){return e+22+75*(e<26)-((0!=t)<<5)}function L(e,t,n){var i=0;for(e=n?A(e/s):e>>1,e+=A(e/t);d*b>>1<e;i+=y)e=A(e/d);return A(i+(d+1)*e/(e+a))}function g(e){var t,n,i,r,o,a,s,l,u,c,f,d=[],p=e.length,h=0,g=M,m=I;for((n=e.lastIndexOf(C))<0&&(n=0),i=0;i<n;++i)128<=e.charCodeAt(i)&&E("not-basic"),d.push(e.charCodeAt(i));for(r=0<n?n+1:0;r<p;){for(o=h,a=1,s=y;p<=r&&E("invalid-input"),f=e.charCodeAt(r++),(y<=(l=f-48<10?f-22:f-65<26?f-65:f-97<26?f-97:y)||l>A((v-h)/a))&&E("overflow"),h+=l*a,!(l<(u=s<=m?w:m+b<=s?b:s-m));s+=y)a>A(v/(c=y-u))&&E("overflow"),a*=c;m=L(h-o,t=d.length+1,0==o),A(h/t)>v-g&&E("overflow"),g+=A(h/t),h%=t,d.splice(h++,0,g)}return N(d)}function m(e){var t,n,i,r,o,a,s,l,u,c,f,d,p,h,g,m=[];for(d=(e=D(e)).length,t=M,o=I,a=n=0;a<d;++a)(f=e[a])<128&&m.push(S(f));for(i=r=m.length,r&&m.push(C);i<d;){for(s=v,a=0;a<d;++a)t<=(f=e[a])&&f<s&&(s=f);for(s-t>A((v-n)/(p=i+1))&&E("overflow"),n+=(s-t)*p,t=s,a=0;a<d;++a)if((f=e[a])<t&&++n>v&&E("overflow"),f==t){for(l=n,u=y;!(l<(c=u<=o?w:o+b<=u?b:u-o));u+=y)g=l-c,h=y-c,m.push(S(j(c+g%h,0))),l=A(g/h);m.push(S(j(l,0))),o=L(n,p,i==r),n=0,++i}++n,++t}return m.join("")}if(r={version:"1.4.1",ucs2:{decode:D,encode:N},decode:g,encode:m,toASCII:function(e){return h(e,function(e){return u.test(e)?"xn--"+m(e):e})},toUnicode:function(e){return h(e,function(e){return l.test(e)?g(e.slice(4).toLowerCase()):e})}},t&&n)if(T.exports==t)n.exports=r;else for(o in r)r.hasOwnProperty(o)&&(t[o]=r[o]);else e.punycode=r}(this)}).call(this,"undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{})},{}],45:[function(e,n,t){!function(e,t){void 0!==n&&n.exports?n.exports=t():this.$script=t()}(0,function(){var u,r,o=document,a=o.getElementsByTagName("head")[0],s=!1,c="push",l="readyState",f="onreadystatechange",d={},p={},h={},g={};function m(e,t){for(var n=0,i=e.length;n<i;++n)if(!t(e[n]))return s;return 1}function v(e,t){m(e,function(e){return!t(e)})}function y(e,t,n){e=e[c]?e:[e];var i=t&&t.call,r=i?t:n,o=i?e.join(""):t,a=e.length;function s(e){return e.call?e():d[e]}function l(){if(!--a)for(var e in d[o]=1,r&&r(),h)m(e.split("|"),s)&&!v(h[e],s)&&(h[e]=[])}return setTimeout(function(){v(e,function e(t,n){return null===t?l():(n||/^https?:\/\//.test(t)||!u||(t=-1===t.indexOf(".js")?u+t+".js":u+t),g[t]?(o&&(p[o]=1),2==g[t]?l():setTimeout(function(){e(t,!0)},0)):(g[t]=1,o&&(p[o]=1),void w(t,l)))})},0),y}function w(e,t){var n,i=o.createElement("script");i.onload=i.onerror=i[f]=function(){i[l]&&!/^c|loade/.test(i[l])||n||(i.onload=i[f]=null,n=1,g[e]=2,t())},i.async=1,i.src=r?e+(-1===e.indexOf("?")?"?":"&")+r:e,a.insertBefore(i,a.lastChild)}return y.get=w,y.order=function(n,i,r){!function e(t){t=n.shift(),n.length?y(t,e):y(t,i,r)}()},y.path=function(e){u=e},y.urlArgs=function(e){r=e},y.ready=function(e,t,n){e=e[c]?e:[e];var i,r=[];return!v(e,function(e){d[e]||r[c](e)})&&m(e,function(e){return d[e]})?t():(i=e.join("|"),h[i]=h[i]||[],h[i][c](t),n&&n(r)),y},y.done=function(e){y([null],e)},y})},{}]},{},[32])(32)});
