var config = {};
var timestamp = Date.parse(new Date());
$.ajax({
    url: '/config/config.json?ver=' + timestamp,
    type: 'GET',
    async: false,
    dataType: 'json',
    success: function (data) {
        config = data;
    },
    error: function (xhr, type, errorThrown) {
        layer.msg("向服务器请求数据失败");
    }
});

var IsAccount = false;
//是否微信浏览器
var IsWechatBrowser = isWeiXin();
var u = navigator.userAgent;
//android终端
var isAndroid = u.indexOf('Android') > -1 || u.indexOf('Adr') > -1;
//ios终端
var isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/);
var ClientType = 0;
if (isAndroid) ClientType = 5;
else if (isiOS) ClientType = 4;
else ClientType = 1;

var UserInfo = {};
UserInfo.ID = 0;

//获取代理状态
var AgentIDFromQueryString = getParameterByName("AgentID") == undefined ? 0 : getParameterByName("AgentID");
if (AgentIDFromQueryString > 0) {
    UserInfo.AgentID = AgentIDFromQueryString;
    SetCookie("AgentID", UserInfo.AgentID);
}
else UserInfo.AgentID = GetCookie("AgentID") == undefined ? 0 : GetCookie("AgentID");

UserInfo.Token = GetCookie("Token") == undefined ? "token" : GetCookie("Token");
UserInfo.LinkStr = UserInfo.AgentID > 0 ? "?AgentID=" + UserInfo.AgentID : "?math=237";

//Jquery Http请求
var Pub = {
    Ajax: function (url, data, callback) {
        var _this = this, Iasync = true;
        $.ajax({
            headers: {
                Token: UserInfo.Token
            },
            type: "post",
            dataType: "json",
            async: Iasync,
            url: url,
            data: data,
            beforeSend: function () {
            },
            error: function (db) {
                layer.msg(db.responseJSON.message);
            },
            success: function (db) {
                callback(db);
            },
            complete: function () { }
        });
    },
    SyncAjax: function (url, data, callback) {
        var _this = this, Iasync = false;
        $.ajax({
            headers: {
                Token: UserInfo.Token
            },
            type: "post",
            dataType: "json",
            async: Iasync,
            url: url,
            data: data,
            beforeSend: function () {
            },
            error: function (db) {
                layer.msg(db.responseJSON.message);
            },
            success: function (db) {
                callback(db);
            },
            complete: function () { }
        });
    }
}

var initUser = GetUserInfo();

if (!IsWechatBrowser) {
    var domain = document.location.origin + document.location.pathname;
    if (window.parent.length > 0)
        top.location.href = domain + (UserInfo.AgentID > 0 ? "?AgentID=" + UserInfo.AgentID : "");
}

function IsPC() {
    var userAgentInfo = navigator.userAgent;
    var Agents = ["Android", "iPhone",
        "SymbianOS", "Windows Phone",
        "iPad", "iPod"];
    var flag = true;
    for (var v = 0; v < Agents.length; v++) {
        if (userAgentInfo.indexOf(Agents[v]) > 0) {
            flag = false;
            break;
        }
    }
    return flag;
}

//添加js脚本
function AddJs(src, fn) {
    var oScript = document.createElement("script");
    oScript.type = "text/javascript";
    oScript.charset = "utf-8";
    oScript.src = src;
    fn(oScript);
}
//设置cookie
function SetCookie(name, value)//两个参数，一个是cookie的名子，一个是值
{
    $.cookie(name, value, { expires: 1, path: '/' });
}
//获取cookie
function GetCookie(name)//取cookies函数        
{
    var value = $.cookie(name);
    return value;
}

//清除cookie  
function ClearCookie(name) {
    $.cookie(name, null, { expires: 1, path: '/' });
}

//判断是否为微信浏览器
function isWeiXin() {
    var ua = navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == "micromessenger") {
        return true;
    }
    else if (ua.match(/WeiBo/i) == "weibo") {
        return false;
    }
    else if (ua.match(/QQ/i) == "qq") {
        return false;
    } else {
        return false;
    }
}
//获取QueryString参数
function getParameterByName(paramName) {
    var args = new Object();
    var argsStr = location.search;
    if (argsStr.length > 0) {
        argsStr = argsStr.substring(1);
        var nameValueArr = argsStr.split("&");
        for (var i = 0; i < nameValueArr.length; i++) {
            var pos = nameValueArr[i].indexOf('=');
            if (pos == -1) continue;
            var argName = nameValueArr[i].substring(0, pos);
            var argVal = nameValueArr[i].substring(pos + 1);
            args[argName] = argVal;
        }
        return args[paramName];
    }
}
//获取QueryString参数
function getParameterFromUrlByName(url, paramName) {
    var args = new Object();
    var argsStr = url
    if (url.indexOf('?') >= 0) {
        argsStr = url.substring(url.indexOf('?'), url.length);
    }
    if (argsStr.length > 0) {
        argsStr = argsStr.substring(1);
        var nameValueArr = argsStr.split("&");
        for (var i = 0; i < nameValueArr.length; i++) {
            var pos = nameValueArr[i].indexOf('=');
            if (pos == -1) continue;
            var argName = nameValueArr[i].substring(0, pos);
            var argVal = nameValueArr[i].substring(pos + 1);
            args[argName] = argVal;
        }
        return args[paramName];
    }
}

/***************************************
设为主页、加入收藏和复制到剪贴板
****************************************/
function SetHomePage() {
    if (document.all) {
        document.body.style.behavior = 'url(#default#homepage)';
        document.body.setHomePage(location.href);
    }
    else if (window.sidebar) {
        if (window.netscape) {
            try {
                netscape.security.PrivilegeManager.enablePrivilege("UniversalXPConnect");
            }
            catch (e) {
                alert("该操作被浏览器拒绝，如果想启用该功能，请在地址栏内输入 about:config,然后将项 signed.applets.codebase_principal_support 值改为true");
            }
        }
        var prefs = Components.classes['@mozilla.org/preferences-service;1'].getService(Components.interfaces.nsIPrefBranch);
        prefs.setCharPref('browser.startup.homepage', location.href);
    }
}
/**************************************
加入浏览器收藏
****************************************/
function AddFavorite(title, url) {
    if (isAndroid) layer.alert('当前浏览器不支持，请手动收藏：' + Domain);
    if (isiOS) layer.alert('当前浏览器不支持，请手动收藏：' + Domain);

    var ua = navigator.userAgent.toLowerCase();
    if (ua.indexOf("360se") > -1) {
        layer.alert("当前浏览器不支持，请手动收藏！");
    }
    else if (ua.indexOf("msie 8") > -1) {
        window.external.AddToFavoritesBar(url, title); //IE8
    }
    else if (document.all) {
        try {
            window.external.addFavorite(url, title);

        } catch (e) {
            layer.alert('当前浏览器不支持,请手动收藏!');
        }
    }
    else if (window.sidebar) {
        window.sidebar.addPanel(title, url, "");
    }
    else {
        layer.alert('当前浏览器不支持,请手动收藏!');
    }
}
/**************************************
截取指定字符串长度
****************************************/
function SubString(string, length) {
    if (string.length >= length)
        return string.substr(0, length) + "...";
    else
        return string;
}
/**************************************
JSON时间格式转为yyyy-MM-dd
****************************************/
function ChangeDate(jsondate) {
    jsondate = jsondate.replace("/Date(", "").replace(")/", "");
    if (jsondate.indexOf("+") > 0) {
        jsondate = jsondate.substring(0, jsondate.indexOf("+"));
    } else if (jsondate.indexOf("-") > 0) {
        jsondate = jsondate.substring(0, jsondate.indexOf("-"));
    }
    var date = new Date(parseInt(jsondate, 10));
    var month = date.getMonth() + 1 < 10 ? "0" + (date.getMonth() + 1) : date.getMonth() + 1;
    var currentDate = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
    return date.getFullYear() + "-" + month + "-" + currentDate;
}
//获取浏览器版本
function GetBrowser() {
    var userAgent = window.navigator.userAgent.toLowerCase();
    var bro = $.browser;
    var name = "NIE";
    if (bro.msie) { name = "IE"; }
    return { Name: name, Version: bro.version };
}
//获取和设置当前日期
function GetDate(day) {
    var dt = new Date();
    if (typeof (day) != "undefined") {
        var setDay = dt.getDate() + day;
        if (setDay <= 0) {
            setDay = 1;
        }
        var maxday = new Date(dt.getYear(), dt.getMonth() + 1, 0).getDate();
        if (setDay > maxday) {
            setDay = maxday;
        }
        dt.setDate(setDay);
    }
    var month = dt.getMonth() + 1;
    var day = dt.getDate();
    if (month < 10) {
        month = "0" + month;
    }
    if (day < 10) {
        day = "0" + day;
    }
    return dt.getFullYear() + "-" + month + "-" + day;
}
//刷新Token
function ReflashToken() {
    //回调函数
    var callback = function (result) {
        if (result.code == 1) {
            UserInfo.Token = result.data;
        }
        else {
            layer.msg(result.message);
        }
    };
    //请求参数
    var data = {};
    data.token = UserInfo.Token;
    data.userID = UserInfo.ID;
    //发起请求
    Pub.Ajax(config.api+"/User/ReflashToken", data, callback);
}
//获取用户信息
function GetUserInfo() {
    //初始化
    if (UserInfo.Token == "token") {
        UserInfo.ID = 0;
        UserInfo.Point = 0;
        UserInfo.IsVip = 0;
        UserInfo.VipMinutes = 0;
        UserInfo.UserName = "";
        return;
    }
    //未获取用户信息
    var callback = function (result) {
        if (result.code == 1) {
            UserInfo.ID = result.data.UserID;
            UserInfo.Point = result.data.Point;
            UserInfo.Token = result.data.Token;
            UserInfo.IsVip = result.data.IsVip;
            UserInfo.VipMinutes = result.data.VipMinutes;
            UserInfo.UserName = result.data.UserName;
            //设置为登录状态
            IsAccount = true;
        }
        else {
            if (result.code == 8) {
                layer.confirm('您的账号在别处登陆！如果不是自己操作，请修改密码！', {
                    btn: ['是', '否'] //按钮
                }, function () {
                    location.href = "/login.html";
                }, function () {
                    layer.close(layer.index);
                });
            }
            //用户验证不通过清理缓存
            ClearCookie("Token");
            ClearCookie("UserID");
            ClearCookie("IsVip");
            ClearCookie("VipMinutes");
            ClearCookie("UserName");
            IsAccount = false;
        }
    };
    //请求参数
    var data = {};
    data.token = UserInfo.Token;
    //发起请求
    Pub.SyncAjax(config.api +"/User/GetDetail", data, callback);

}

//搜索
function search(obj) {
    location.href = "tag.aspx?AgentID=" + UserInfo.AgentID + "&tag=" + $(obj).prev().val();
}

function isEmail(str) {
    var reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
    return reg.test(str);
} 


function goAD(){
	if(config.ad_state==="open"){
		if(isiOS){
			window.open(config.ad_url_ios);
		}
		else if(isAndroid){
			window.open(config.ad_url_andriod);
		}
		else{
			window.open(config.ad_url_web);
		}
	}
}