var start = {};
start.freestart = 0;
start.hotstart = 0;
start.recommendstart = 0;

var length = {};
length.newlenth = 12;
length.freelenth = 4;
length.hotlength = 8;
length.recommendlength = 8;

var pageindex = 1;


var vm = new Vue({
    el: "#container",
    data: {
        bannerList: [],
        freeList: [],
        hotList: [],
        newList: [],
        comList: []
    },
    mounted: function mounted() {
        //this.GetBanner();
        this.GetFreeList();
        this.GetHotList();
        this.GetRecommendList();
        this.GetNewList();
        this.ShowDomain();
    },
    methods: {
        GetBanner: function GetBanner() {
            var _this = this;

            axios.post(config.api + '/Website/GetBannerList').then(function (res) {
                if (res.data.code === 1) {
                    _this.bannerList = res.data.data;
                }
            });
        },
        GetFreeList: function GetFreeList() {
            var _this = this;

            var param = {};
            param.ClientType = ClientType;
            param.length = length.freelenth;
            param.start = start.freestart;
            axios.post(config.api + '/Video/GetHomeFreeList', param, {
                headers: { "Token": UserInfo.Token }
            }).then(function (res) {
                if (res.data.code === 1) {
                    _this.freeList = res.data.data;
                    if (start.freestart + length.freelenth >= res.data.recordsTotal) start.freestart = 0 - length.freelenth;
                    //隐藏加载图标
                    $("#freeLoader").hide();
                }
            });
        },
        GetHotList: function GetHotList() {
            var _this = this;

            var param = {};
            param.ClientType = ClientType;
            param.length = length.hotlength;
            param.start = start.hotstart;
            axios.post(config.api + '/Video/GetHomeHotList', param, {
                headers: { "Token": UserInfo.Token }
            }).then(function (res) {
                if (res.data.code === 1) {
                    _this.hotList = res.data.data;
                    if (start.hotstart + length.hotlength >= res.data.recordsTotal) start.hotstart = 0 - length.hotlength;
                    //隐藏加载图标
                    $("#hotLoader").hide();
                }
            });
        },
        GetRecommendList: function GetRecommendList() {
            var _this = this;

            var param = {};
            param.ClientType = ClientType;
            param.length = length.recommendlength;
            param.start = start.recommendstart;
            axios.post(config.api + '/Video/GetHomeRecomendList', param, {
                headers: { "Token": UserInfo.Token }
            }).then(function (res) {
                if (res.data.code === 1) {
                    _this.comList = res.data.data;
                    if (start.recommendstart + length.recommendlength >= res.data.recordsTotal) start.recommendstart = 0 - length.recommendlength;
                    //隐藏加载图标
                    $("#comLoader").hide();
                }
            });
        },

        GetNewList: function GetNewList() {
            var _this = this;

            var param = {};
            param.length = length.newlenth;
            param.ClientType = ClientType;
            param.searchtext = "";
            var orderlist = [];
            var ordertext = {};
            ordertext.column = "AddTime";
            ordertext.dir = "desc";
            orderlist.push(ordertext);
            param.ordertext = orderlist;
            param.pageindex = pageindex;

            axios.post(config.api + '/Video/GetHomeNewList', param, {
                headers: { "Token": UserInfo.Token }
            }).then(function (res) {
                _this.newList = res.data.data;
                document.getElementById('newLoader').style.display = 'none';

                //加载完全部数据，隐藏 "看更多" 按钮
                if (res.data.recordsTotal === res.data.data.length) {
                    $("#loadMoreActorBtn").hide();
                }
            });
        },
        ShowDomain: function ShowDomain() {
            var isfirstlogin = GetCookie("IsFirstLogin");
            if (isfirstlogin == 1)
            {
                $("#qrbg").attr("src", "http://img.imooa.cn/img/domain_save.jpg");
                $("#agentcode").text(UserInfo.AgentID);
                layer.open({
                    type: 1,
                    title: false,
                    closeBtn: 1,
                    area: ['320px', '480px'],
                    skin: 'layui-layer-nobg', //没有背景色
                    shadeClose: true,
                    content: $('#Qrcodelayer'),
                    end: function () {
                        $("#Qrcodelayer").hide();
                        $("#output").html("");
                    }
                });
                ClearCookie("IsFirstLogin");
            }
        },
        nextFreePage: function nextFreePage() {
            start.freestart += length.freelenth;
            vm.GetFreeList();
        },
        nextHotPage: function nextHotPage() {
            start.hotstart += length.hotlength;
            vm.GetHotList();
        },
        nextRecommendPage: function nextRecommendPage() {
            start.recommendstart += length.recommendlength;
            vm.GetRecommendList();
        },
        nextNewPage: function nextNewPage() {
            pageindex++;
            length.newlenth += 12;
            vm.GetNewList();
        }
    }
});

function savePng() {
    if (client.isAndroid || client.isIOS) {
        layer.msg("手机上请截屏保存哦！");
        return;
    }
    var filename = "down.png";

    html2canvas($("#saveImg")).then(function (canvas) {
        var imgUri = canvas.toDataURL("image/png").replace("image/png", "image/octet-stream"); // 获取生成的图片的url

        var save_link = document.createElementNS('http://www.w3.org/1999/xhtml', 'a');
        save_link.href = imgUri;
        save_link.download = filename;

        var event = document.createEvent('MouseEvents');
        event.initMouseEvent('click', true, false, window, 0, 0, 0, 0, 0, false, false, false, false, 0, null);
        save_link.dispatchEvent(event);

    });
}