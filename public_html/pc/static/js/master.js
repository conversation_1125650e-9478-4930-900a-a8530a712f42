var vmMenu = new Vue({
    el: "#header"
});

//控制登录按钮
if (IsAccount) {
    if (UserInfo.IsVip != 1) {
        $("#userLevelIcon").find(".user_profile").html("<span class='sprite sprite-icon-internicon user_pic_wrapper'></span><span class='user_id intern'>体验会员</span>");
    }

    $("#userLoginIconM").hide();
    $("#userLevelIconM").show();
    $("#userLoginIcon").hide();
    $("#userLevelIcon").css("display", "");
} else {
    $("#userLoginIconM").show();
    $("#userLevelIconM").hide();
    $("#userLoginIcon").css("display", "");
    $("#userLevelIcon").hide();
}

var vmFooter = new Vue({
    el: "#footer"
});

function AddCollection(obj, videoid, ev) {
    var oEvent = ev || event;
    //阻止链接默认行为
    oEvent.preventDefault();

    if (UserInfo.ID <= 0) {
        layer.msg("请先登录");
        return;
    }

    var data = {};
    data.VideoID = videoid;
    data.UserID = UserInfo.ID;
    data.ClientType = ClientType;
    var callback = function callback(result) {
        if (result.code == 1) {
            if ($(obj).hasClass("fa-plus")) {
                $(obj).removeClass("fa");
                $(obj).removeClass("fa-plus");
                $(obj).addClass("icon-full-like fa fa-heart collect-video-page");
            } else {
                $(obj).removeClass("icon-full-like");
                $(obj).removeClass("fa");
                $(obj).removeClass("fa-heart");
                $(obj).removeClass("fa-heart collect-video-page");
                $(obj).addClass("fa fa-plus");
            }
        }
    };
    Pub.SyncAjax(config.api + "/User/AddCollection", data, callback);
}

function Logout() {
    UserInfo.Token = "token";
    UserInfo.ID = 0;
    UserInfo.Point = 0;
    UserInfo.IsVip = 0;
    UserInfo.VipMinutes = 0;
    UserInfo.UserName = "";
    ClearCookie("Token");
    ClearCookie("UserID");
    ClearCookie("IsVip");
    ClearCookie("VipMinutes");
    ClearCookie("UserName");
    IsAccount = false;
    location.href = "index.aspx";
}