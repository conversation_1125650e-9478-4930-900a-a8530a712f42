$(function () {
    if (IsWechatBrowser) {
        var winHeight = typeof window.innerHeight !== 'undefined' ? window.innerHeight : document.documentElement.clientHeight;
        var tip = document.getElementById('weixin-tip');
        if (isiOS) {
            document.getElementById('turn_img').src = 'http://img.imooa.cn/img/wechat-tip-ios.png';
        }
        tip.style.height = winHeight + 'px';
        tip.style.display = 'block';

        return;
    }
});

var videoID = getParameterByName("id") === undefined ? 0 : getParameterByName("id");
var player;
var Tags = {};
var recommendTag = "";

var vm = new Vue({
    el: "#container",
    data: {
        videoinfo: {},
        recomendList: [],
        goodsList: []
    },
    mounted: function () {
        this.GetInfo();
        this.GetGoods();
    },
    methods: {
        GetInfo: function () {
            //请求参数
            var param = {};
            param.videoID = videoID;
            param.userID = UserInfo.ID;
            param.ClientType = ClientType;
            axios.post(config.api+'/Video/GetInfo', param, {
                headers: { "Token": UserInfo.Token }
            }).then(function (res) {
                if (res.data.code === 1) {
                    var strs = res.data.data.Tags === null ? new Array() : res.data.data.Tags.split(","); //字符分割 
                    Tags = res.data.data.Tags = strs;
                    vm.videoinfo = res.data.data;

                    //加载推荐列表
                    vm.GetRecommend();
                    //加载免费资源
                    if (vm.videoinfo.IsVip === 0 && vm.videoinfo.IsNeedLogin === 0 && vm.videoinfo.Point === 0) {
                        $("#video-section").addClass("fullsrceen");
                        $("#videoPackageWrap").hide();
                        $("#permissCover").hide();
                        $("#iframeCover").hide();
                        $("#J_prismPlayer").show();
                        vm.GetFree(videoID);
                        return;
                    }
                    //加载打赏资源
                    else if (vm.videoinfo.IsVip === 0 && vm.videoinfo.IsNeedLogin === 0 && vm.videoinfo.Point > 0) {
                        $("#video-section").addClass("fullsrceen");
                        $("#needPoint").show();
                        return;
                    }
                    //加载vip资源
                    else {
                        if (UserInfo.ID > 0) {
                            //vip视频只有VIP能看
                            if (vm.videoinfo.IsVip === 1 && UserInfo.IsVip === 0) {
                                $("#iframeCover").show();
                                $("#needVip").show();
                                $("#videoPackageWrap").show();
                                return;
                            }
                            //权限正确
                            else {
                                $("#video-section").addClass("fullsrceen");
                                $("#videoPackageWrap").hide();
                                $("#permissCover").hide();
                                $("#iframeCover").hide();
                                $("#J_prismPlayer").show();
                                vm.GetVideo(videoID);
                                return;
                            }
                        }
                        //未登录
                        else {
                            $("#permissCover").show();
                            $("#videoPackageWrap").show();
                        }
                    }
                }
                else layer.msg(res.data.message);
            });
        },
        GetVideo: function () {
            var data = {};
            data.videoID = videoID;
            data.userID = UserInfo.ID;
            data.ClientType = ClientType;
            axios.post(config.api +'/Video/GetClient', data, {
                headers: { "Token": UserInfo.Token }
            }).then(function (res) {
                if (res.data.code === 1) {
                    //加载播放器
                    vm.SetPlayer(res.data.data.Url, res.data.data.CoverImgUrl);
                }
                //token验证失败
                else if (res.data.code === 3) {
                    layer.confirm('您尚未登录，是否前往登录？', {
                        btn: ['是', '否'] //按钮
                    }, function () {
                        location.href = "login.html";
                    }, function () {
                        history.go(-1);
                    });
                }
                //重复登陆
                else if (res.data.code === 8) {
                    layer.confirm('您的账号在别处登陆！如果不是自己操作，请修改密码！', {
                        btn: ['是', '否'] //按钮
                    }, function () {
                        location.href = "login.html";
                    }, function () {
                        location.href = "index.aspx";
                    });
                }
                //如果是token过期，先从服务器刷新token再去获取视频，也可以设置成需要手动重新登陆
                else if (res.data.code === 4) {
                    ReflashToken();
                    getVideo(videoID);
                }
                else {
                    layer.confirm(res.data.message + '，是否重新登陆？', {
                        btn: ['是', '否'] //按钮
                    }, function () {
                        location.href = "login.html";
                    }, function () {
                        location.href = "index.aspx";
                    });
                }
            });

        },
        GetFree: function () {
            var data = {};
            data.videoID = videoID;
            data.userID = UserInfo.ID;
            data.ClientType = ClientType;
            axios.post(config.api +'/Video/GetFree', data, {
                headers: { "Token": UserInfo.Token }
            }).then(function (res) {
                if (res.data.code === 1) {
                    //加载播放器
                    vm.SetPlayer( res.data.data.Url, res.data.data.CoverImgUrl);
                }
                else {
                    layer.msg(res.data.message);
                }
            });
        },
        GetRecommend: function () {
            var tags = Tags;
            var index = Math.floor((Math.random() * tags.length));
            recommendTag = tags[index];
            var data = {};
            data.searchtext = recommendTag;
            data.ClientType = ClientType;
            axios.post(config.api +'/Website/GetRecommendList', data, {
                headers: { "Token": UserInfo.Token }
            }).then(function (res) {
                if (res.data.code === 1) {
                    vm.recomendList = res.data.data.recomendList;
                    $("#comLoader").hide();
                }
                else layer.msg(res.data.message);
            });
        },
        GetGoods: function () {
            axios.post(config.api +'/Website/GetGoodsList').then(function (res) {
                if (res.data.code === 1) {
                    for (var i = 0; i < res.data.data.length; i++) {
                        var strs = res.data.data[i].Description === null ? new Array() : res.data.data[i].Description.split(","); //字符分割 
                        res.data.data[i].Description = strs;
                    }
                    vm.goodsList = res.data.data;
                }
            });
        },
        SetPlayer: function (videoUrl, coverUrl) {
            var index1 = videoUrl.lastIndexOf(".");
            var index2 = videoUrl.length;
            var suffix = videoUrl.substring(index1 + 1, index2);
            var type = "application/x-mpegurl";
            if (suffix === "mp4") type = "video/mp4";
            else if (suffix === "m3u8") type = "application/x-mpegurl";

            var player = flowplayer("#J_prismPlayer", {
                poster: coverUrl,
                share: false,
                autoplay: false,
                native_fullscreen: true,//ios设备使用原生播放器
                clip: {
                    hlsjs: { safari: true },
                    sources: [
                        { type: type, src: videoUrl }
                    ]
                }
            });
        }
    }
});

function AddSimgleCollection(obj, videoid) {
    if (UserInfo.ID <= 0) {
        layer.msg("请先登录");
        return;
    }

    var data = {};
    data.VideoID = videoid;
    data.UserID = UserInfo.ID;
    data.ClientType = ClientType;
    var callback = function (result) {
        if (result.code === 1) {
            var color = $(obj).find("i").css("color");
            if (color === "rgb(255, 255, 255)") {
                $(obj).find("i").css("color", "#FF8382");
                vm.videoinfo.CollectionCount += 1;
            }
            else {
                $(obj).find("i").css("color", "#FFFFFF");
                vm.videoinfo.CollectionCount -= 1;
            }
        }
    };
    Pub.SyncAjax(config.api +"/User/AddCollection", data, callback);

}