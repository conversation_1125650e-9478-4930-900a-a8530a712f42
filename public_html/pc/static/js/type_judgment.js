var client = {};
//是否为移动端
client.isMobile = /(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i.test(navigator.userAgent);
//移动端类型
client.isiPad = /iPad/i.test(navigator.userAgent);//ipad
client.isiPhone = /iPhone|iPod/i.test(navigator.userAgent);//iphone
client.isAndroid = /Android/i.test(navigator.userAgent);//android
client.isIOS = /\(i[^;]+;( U;)? CPU.+Mac OS X/i.test(navigator.userAgent)//IOS
client.isWechat = /MicroMessenger/i.test(navigator.userAgent);//wechat
client.isQQ = /QQ/i.test(navigator.userAgent);//QQ
client.isWeibo = /Weibo/i.test(navigator.userAgent);//weibo
//浏览器类型
client.isQQBro = /MQQBrowser/i.test(navigator.userAgent)//QQ浏览器
client.isUCBro = /UCBrowser/i.test(navigator.userAgent)//UC浏览器
client.isChrome = /Chrome\/([\d\.]+)/i.test(navigator.userAgent)//Chrome
client.isFireFox = /Firefox\/([\d\.]+)/i.test(navigator.userAgent)//FireFox
client.isIE = navigator.userAgent.indexOf("compatible") > -1 && navigator.userAgent.indexOf("MSIE") > -1 && !isOpera//IE
client.isSafari = /Safari/i.test(navigator.userAgent)//Safari
client.isOpera = /Opera/i.test(navigator.userAgent)//Opera
client.isMaxthon = navigator.userAgent.indexOf("Maxthon") > -1//遨游broswer