<?php
$nowTime = time();
$retainDay = 86400 * 7; //��������
$dir = '/update/65.52.171.4.log';
$lastMonth = date("Ym", strtotime("-2 month"));
$file_arr = array();
parseDirectory($dir);
deldir($dir, $lastMonth);
function parseDirectory($dir)
{
    global $nowTime;
    global $retainDay;
    if (is_dir($dir)) {//��
        if ($dh = @opendir($dir)) {//��ȡ
            while (($file = readdir($dh)) !== false) {
                if ($file != '.' && $file != '..') {
                    $newDir = $dir . '/' . $file;
                    if (is_dir($newDir)) {
                        parseDirectory($newDir);
                    } else {
                        $str = explode("-", $file);
                        if (intval($nowTime) - intval($str['0']) > $retainDay && intval(count($str)) >= 2) {
                            unlink("$newDir");
                        }
                    }
                }
            }
            //�ر�
            closedir($dh);
        }
    }
}

function deldir($path, $lastMonth)
{
    //�����Ŀ¼�����
    if (is_dir($path)) {
        //ɨ��һ���ļ����ڵ������ļ��к��ļ�����������
        $p = scandir($path);
        foreach ($p as $val) {
            //�ų�Ŀ¼�е�.��..
            if ($val != "." && $val != "..") {
                if ($lastMonth >= $val) {
                    //�����Ŀ¼��ݹ���Ŀ¼����������
                    if (is_dir($path . '/' . $val)) {
                        //��Ŀ¼�в���ɾ���ļ��к��ļ�
                        $p2 = scandir($path . '/' . $val);
                        foreach ($p2 as $v) {
                            if ($v != "." && $v != "..") {
                                unlink($path . '/' . $val . '/' . $v);
                            }
                        }
                        //Ŀ¼��պ�ɾ�����ļ���
                        var_dump(@rmdir($path . '/' . $val));
                    } else {
                        //������ļ�ֱ��ɾ��
                        unlink($path . '/' . $val);
                    }
                }

            }
        }
    }
}

?>
