layui.define(['element', 'form'], function(exports) {
    var $ = layui.jquery,element = layui.element, layer = layui.layer, form = layui.form;
    var lockscreen = function() {
        document.oncontextmenu=new Function("event.returnValue=false;");
        document.onselectstart=new Function("event.returnValue=false;");
        layer.open({
            title: false,
            type: 1,
            content: '<div class="lock-screen"><input type="password" id="unlockedPwd" class="layui-input" placeholder="请输入登录密码解锁..." autocomplete="off"><button id="unlocked" class="layui-btn">解锁</button><div id="unlockTips"></div></div>',
            closeBtn: 0,
            shade: 0.95,
            offset: '350px'
        });

        $('#unlocked').click(function() {
            var pwd = $('#unlockedPwd').val();
            if (pwd == '') {
                return false;
            }
            $.post(ADMIN_PATH+'/admin/publics/unlocked', {password:pwd}, function(res) {
                if (res.code == 1) {
                    window.sessionStorage.setItem("lockscreen", false);
                    layer.closeAll();
                } else {
                    $('#unlockTips').html(res.msg);
                    setTimeout(function(){
                        $('#unlockTips').html('');
                    }, 3000);
                }
            });
        });
    }



    /* 锁屏 */
    $('#lockScreen').click(function () {
        window.sessionStorage.setItem("lockscreen", true);
        lockscreen();
    });
    if(window.sessionStorage.getItem("lockscreen") == "true"){
        lockscreen();
    }
    
    /* 导航高亮标记 */
    $('.admin-nav-item').click(function() {
        window.localStorage.setItem("adminNavTag", $(this).attr('href'));
    });
    if (window.localStorage.getItem("adminNavTag")) {
        $('#switchNav a[href="'+window.localStorage.getItem("adminNavTag")+'"]').parent('dd').addClass('layui-this').parents('li').addClass('layui-nav-itemed').siblings('li').removeClass('layui-nav-itemed');
    }
    if (typeof(LAYUI_OFFSET) == 'undefined') {
        layer.config({offset:'60px'});  
    } else {
        layer.config({offset:LAYUI_OFFSET+'px'});  
    }
    /* 打开/关闭左侧导航 */
    $('#foldSwitch').click(function(){
        var that = $(this);
        if (!that.hasClass('close')) {
            that.addClass('close');
            $('#switchNav').animate({width:'52px'}, 100).addClass('close').hover(function() {
                if (that.hasClass('close')) {
                    $(".layui-nav-item .fa").css({"margin-right":'5px'});//$$DM
                    $(this).animate({width:'200px'}, 300);
                    $('#switchNav .fold-mark').removeClass('fold-mark');
                    $('a[href="'+window.localStorage.getItem("adminNavTag")+'"]').parent('dd').addClass('layui-this').parents('li').addClass('layui-nav-itemed').siblings('li').removeClass('layui-nav-itemed');
                }
            },function() {
                $(".layui-nav-item .fa").css({"margin-right":'25px'});//$$DM
                if (that.hasClass('close')) {
                    $(this).animate({width:'52px'}, 300);
                    $('#switchNav .layui-nav-item').addClass('fold-mark').removeClass('layui-nav-itemed');
                }
            });
            $(".layui-nav-item .fa").css({"margin-right":'25px'});//$$DM
            $('#switchBody,.footer').animate({left:'52px'}, 100);
            $('#switchNav .layui-nav-item').addClass('fold-mark').removeClass('layui-nav-itemed');
        } else {
            $(".layui-nav-item .fa").css({"margin-right":'5px'});//$$DM
            $('a[href="'+window.localStorage.getItem("adminNavTag")+'"]').parent('dd').addClass('layui-this').parents('li').addClass('layui-nav-itemed').siblings('li').removeClass('layui-nav-itemed');
            that.removeClass('close');
            $('#switchNav').animate({width:'200px'}, 100).removeClass('close');
            $('#switchBody,.footer').animate({left:'200px'}, 100);
            $('#switchNav .fold-mark').removeClass('fold-mark');
        }
    });

    /* 导航菜单切换 */
    $('.main-nav a').click(function () {
        var that = $(this), i = $('.main-nav a').index(this);
        $('.layui-nav-tree').hide().eq(i).show();
    });

    /* 操作提示 */
    $('.help-tips').click(function(){
        layer.tips($(this).attr('data-title'), this, {
            tips: [3, '#009688'],
            time: 5000
        });
        return false;
    });

    /* 全屏控制 */
    $('#fullscreen-btn').click(function(){
        var that = $(this);
        if (!that.hasClass('ai-quanping')) {
            $('#switchBody').css({'z-index':1000});
            $('#switchNav').css({'z-index':900});
            that.addClass('ai-quanping').removeClass('ai-quanping1').parents('.page-body').addClass('fullscreen');
            $('.page-tab-content').css({'min-height':($(window).height()-63)+'px'});
        } else {
            $('#switchBody').css({'z-index':998});
            $('#switchNav').css({'z-index':1000});
            that.addClass('ai-quanping1').removeClass('ai-quanping').parents('.page-body').removeClass('fullscreen');
            $('.page-tab-content').css({'min-height':'auto'});
        }
    });

    /* 全选 */
    form.on('checkbox(allChoose)', function(data) {
        var child = $(data.elem).parents('table').find('tbody input.checkbox-ids');
        child.each(function(index, item) {
            item.checked = data.elem.checked;
        });
        form.render('checkbox');
    });

    /* 删除快捷菜单 */
    $('.j-del-menu').click(function(){
        var that = $(this);
        layer.confirm('删除之后无法恢复，您确定要删除吗？', {title:false, closeBtn:0}, function(index){

            $.post(that.attr('data-href'), function(res) {
                layer.msg(res.msg);
                if (res.code == 1) {
                    that.parents('dd').animate({left:'-1000px'},function(){
                        $(this).remove();
                    });
                }
            });
            layer.close(index);
        });

    });

    /*iframe弹窗*/
    $('.j-iframe-poq').click(function(){
        var a=$("input[name='ids[]']");
        var v = [];
        for (var i = 0; i < a.length; i++) {
            if(a[i].checked){
                v.push(a[i].value);
            }
        }
        console.log(v.join(','));
       var that = $(this),
            _url = that.attr('href')+"?ids="+v.join(','),
            _title = that.attr('title'),
            _width = that.attr('width') ? that.attr('width') : 750,
            _height = that.attr('height') ? that.attr('height') : 560;
        if (!_url) {
            layer.msg('请设置href参数');
            return false;
        }
        layer.open({type:2, title:_title, content:_url, area: [_width+'px', _height+'px'],end:function(){
            // location.reload();
            return ;
        }});
        return false;
    });

    /* 监听状态设置开关 */
    form.on('switch(switchStatus)', function(data) {
        var that = $(this), status = 0;
        if (!that.attr('data-href')) {
            layer.msg('请设置data-href参数');
            return false;
        }
        if (this.checked) {
            status = 1;
        }
        $.get(that.attr('data-href'), {val:status}, function(res) {
            layer.msg(res.msg);
            if (res.code == 0) {
                that.trigger('click');
                form.render('checkbox');
            }
        });
    });

    /* 监听表单提交 */
    form.on('submit(formSubmit)', function(data) {
        var _form = '';
        if ($(this).attr('data-form')) {
            _form = $($(this).attr('data-form'));
        } else {
            _form = $(this).parents('form');
        }
        // CKEditor专用
        if (typeof(CKEDITOR) != 'undefined') {
            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].updateElement();
            }
        }
        layer.msg('数据提交中...',{time:500000});
        $.ajax({
            type: "POST",
            url: _form.attr('action'),
            data: _form.serialize(),
            success: function(res) {
                layer.msg(res.msg, {},function() {
                    if ((res.url).length > 0) {
                        location.href= res.url;
                    }
                });

            }
        });
        return false;
    });
    $('.j-check').click(function(){
        var that = $(this),
            _url = that.attr('href')
        if (!_url) {
            layer.msg('请设置href参数');
            return false;
        }
        layer.confirm('您确定审核通过此资源吗？如果不通过请点击“拒绝”，可填写拒绝理由！', {
            btn: ['通过','拒绝'] //按钮
        }, function(){
            layer.closeAll();
            layer.msg('数据提交中...',{time:500000});
            $.post(_url, {val:1}, function(res) {
                layer.msg(res.msg, {}, function(){
                    if (res.code != 0) {
                        location.reload();
                    }
                });
            });
        }, function(){
            layer.prompt({title: '驳回原因：', formType: 2,value:"1  内含广告，请删除广告后再提交审核。 \n2 没有验证信息，不能证明原创作品。", btn: ['提交','取消']}, function(text, index){
                layer.msg('数据提交中...',{time:500000});
                $.post(_url, {val:2,word:text}, function(res) {
                    layer.msg(res.msg, {}, function(){
                        if (res.code != 0) {
                            location.reload();
                        }
                    });
                });
                layer.close(index);
            });
        });

        return false;
    });
    /*iframe弹窗*/
    $('.j-iframe-pop').click(function(){
        var that = $(this), 
            _url = that.attr('href'), 
            _title = that.attr('title'), 
            _width = that.attr('width') ? that.attr('width') : 750, 
            _height = that.attr('height') ? that.attr('height') : 500;
        if (!_url) {
            layer.msg('请设置href参数');
            return false;
        }
        layer.open({type:2, title:_title, content:_url, area: [_width+'px', _height+'px']});
        return false;
    });

    /* 监听状态设置开关 */
    form.on('switch(switchStatus)', function(data) {
        var that = $(this), status = 0;
        if (!that.attr('data-href')) {
            layer.msg('请设置data-href参数');
            return false;
        }
        if (this.checked) {
            status = 1;
        }
        $.get(that.attr('data-href'), {val:status}, function(res) {
            layer.msg(res.msg);
            if (res.code == 0) {
                that.trigger('click');
                form.render('checkbox');
            }
        });
    });

    /* 监听表单提交 */
    form.on('submit(formSubmit)', function(data) {
        var _form = '';
        if ($(this).attr('data-form')) {
            _form = $($(this).attr('data-form'));
        } else {
            _form = $(this).parents('form');
        }
        // CKEditor专用
        if (typeof(CKEDITOR) != 'undefined') {
            for (instance in CKEDITOR.instances) {
                CKEDITOR.instances[instance].updateElement();
            }
        }
        layer.msg('数据提交中...',{time:500000});
        $.ajax({
            type: "POST",
            url: _form.attr('action'),
            data: _form.serialize(),
            success: function(res) {
                layer.msg(res.msg, {},function() {
					if(res.code == 1){
						var index = parent.layer.getFrameIndex(window.name);
						if(index){
							window.parent.location.reload();
							parent.layer.close(index);
						}
					}	
                    if ((res.url).length > 0) {
                        location.href= res.url;
                    }
                }); 
            }
        });
        return false;
    });

    /* TR数据行删除 */
    $('.j-tr-del').click(function() {
        var that = $(this),
            href = !that.attr('data-href') ? that.attr('href') : that.attr('data-href');
        layer.confirm('删除之后无法恢复，您确定要删除吗？', {title:false, closeBtn:0}, function(index){
            if (!href) {
                layer.msg('请设置data-href参数');
                return false;
            }
            $.get(href, function(res) {
                if (res.code == 0) {
                    layer.msg(res.msg);
                } else {
                    that.parents('tr').remove();
                }
            });
            layer.close(index);
        });
        return false;
    });
    /* TR数据行删除 */
    $('.j-dt-del').click(function() {
        var that = $(this),
            href = !that.attr('data-href') ? that.attr('href') : that.attr('data-href');
        layer.confirm('删除之后无法恢复，您确定要删除吗？', {title:false, closeBtn:0}, function(index){
            if (!href) {
                layer.msg('请设置data-href参数');
                return false;
            }
            $.get(href, function(res) {
                if (res.code == 0) {
                    layer.msg(res.msg);
                } else {
                    that.parents('dt').remove();
                }
            });
            layer.close(index);
        });
        return false;
    });

    /* ajax请求操作 */
    $(document).on('click', '.j-ajax', function() {
        var that = $(this), 
            href = !that.attr('data-href') ? that.attr('href') : that.attr('data-href'),
            refresh = !that.attr('refresh') ? 0 : 1;
        if (!href) {
            layer.msg('请设置data-href参数');
            return false;
        }

        if (!that.attr('confirm')) {
            layer.msg('数据提交中...', {time:500000});
            $.get(href, {}, function(res) {
                layer.msg(res.msg, {}, function() {
                    if (refresh == 1) {
                        if ((res.url).length > 0) {
                            location.href = res.url;
                        } else {
                            location.reload();
                        }
                    }
                });
            });
            layer.close();
        } else {
            layer.confirm(that.attr('confirm'), {title:false, closeBtn:0}, function(index){
                layer.msg('数据提交中...', {time:500000});
                $.get(href, {}, function(res) {
                    layer.msg(res.msg, {}, function() {
                        if (refresh == 1) {
                            if ((res.url).length > 0) {
                                location.href = res.url;
                            } else {
                                location.reload();
                            }
                        }
                    });
                });
                layer.close(index);
            });
        }
        return false;
    });

    /* 数据列表input编辑自动选中ids */
    $('.j-auto-checked').blur(function(){
        var that = $(this);
        if(that.attr('data-value') != that.val()) {
            that.parents('tr').find('input[name="ids[]"]').attr("checked", true);
        }else{
            that.parents('tr').find('input[name="ids[]"]').attr("checked", false);
        };
        form.render('checkbox');
    });

    /* 用ajax方式更新input*/
    $('.j-ajax-input').focusout(function(){
        var that = $(this), _val = that.val();
        if (_val == '') return false;
        if (that.attr('data-value') == _val) return false;
        if (!that.attr('data-href')) {
            layer.msg('请设置data-href参数');
            return false;
        }
        $.post(that.attr('data-href'), {val:_val}, function(res) {
            if (res.code == 1) {
                that.attr('data-value', _val);
            }
            layer.msg(res.msg);
        });
    });

    /* 小提示 */
    $('.tooltip').hover(function() {
        var that = $(this);
        that.find('i').show();
    }, function() {
        var that = $(this);
        that.find('i').hide();
    });

    /* 列表按钮组 */
    $('.j-page-btns').click(function(){
        var that = $(this),
            code = function(that) {
                var href = that.attr('href') ? that.attr('href') : that.attr('data-href');
                if (!href) {
                    layer.msg('请设置data-href参数');
                    return false;
                }
                if ($('.checkbox-ids:checked').length <= 0) {
                    layer.msg('请选择要操作的数据');
                    return false;
                }
                if (that.parents('form')[0]) {
                    var query = that.parents('form').serialize();
                } else {
                    var query = $('#pageListForm').serialize();
                }
                layer.msg('数据提交中...',{time:500000});
                $.post(href, query, function(res) {
                    layer.msg(res.msg, {}, function(){
                        if (res.code != 0) {
                            location.reload();
                        } 
                    });
                });
            };
        if (that.hasClass('confirm')) {
            var tips = that.attr('tips') ? that.attr('tips') : '您确定要执行此操作吗？';
            layer.confirm(tips, {title:false, closeBtn:0}, function(index){
                code(that);
                layer.close(index);
            });
        } else {
           code(that); 
        }
        return false;
    });
    exports('global', {});
});