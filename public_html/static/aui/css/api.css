.aui-fold-active {
	background: #f4f4f4;
}
.aui-fold-active .aui-fold-content {
	display:block;
}
.aui-fold-content {
    position: relative;
    display: none;
    margin: 12px -15px -12px;
    padding-left: 15px;
    background: #fff;
}
.aui-fold .aui-arrow-right {
	position: relative;
    display: block;
    overflow: hidden;
    margin: -12px -15px;
    padding: inherit;
    white-space: nowrap;
    text-overflow: ellipsis;
    color: inherit;
}
.aui-fold-ccontent-full {
	margin-left: -15px;
	margin-right: -15px;
	padding: 0;
	width: auto;
}
.aui-fold > .aui-arrow-right:after {
	content: "\e661";
}
.aui-fold-active > .aui-arrow-right:after{
	content: "\e6de"
}
.aui-fold-content .aui-list-view,
.aui-fold-content .aui-user-view {
	margin-bottom: 0;
}
.aui-fold-content .aui-list-view:after,
.aui-fold-content .aui-user-view:after,
.aui-fold-content .aui-input-row:first-child:after {
	border-top: 0;
}
.aui-fold-content .aui-list-view-cell:last-child:after,
.aui-fold-content .aui-user-view-cell:last-child:after,
.aui-fold-content .aui-input-row:last-child:after,
.aui-fold-content .aui-btn-row:last-child:after {
	border-bottom: 0;
}