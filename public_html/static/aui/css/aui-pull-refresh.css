/**
 * aui-pull-refresh.css 下拉刷新
 * verson 0.0.1
 * <AUTHOR> && Beck
 * http://www.auicss.com
 * @todo more things to abstract, e.g. Loading css etc.
 * Licensed under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
 */
.aui-refresh-content {
	position: relative;
	-webkit-overflow-scrolling: touch;
	-webkit-transform: translate3d(0, 0, 0);
	      	transform: translate3d(0, 0, 0);
}
.aui-refresh-load {
	width: 100%;
	height: 2.2rem;
	position: absolute;
	top: -2.2rem;
	left: 0;
	padding: 0.75rem 0;
}
.aui-refresh-pull-arrow {
	width: 1.2rem;
	height: 1.2rem;
	position: absolute;
	left: 50%;
	top: 50%;
	visibility: visible;
	margin-left: -0.6rem;
	margin-top: -0.6rem;
	background: no-repeat center;
	background-image: url('data:image/svg+xml;charset=utf-8,<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><path d="M99.225 3.528c8.34 0 15.104 6.763 15.104 15.105 0 8.34-6.766 15.105-15.105 15.105-8.342 0-15.105-6.763-15.105-15.105S90.883 3.528 99.225 3.528zM76.567 48.843h45.315c4.17 0 7.552 3.38 7.552 7.552v5.035c0 4.172-3.382 7.552-7.552 7.552H76.567c-4.172 0-7.553-3.38-7.553-7.552v-5.035c0-4.172 3.38-7.552 7.553-7.552zm-10.07 35.245h65.455c4.17 0 7.552 3.38 7.552 7.552v5.035c0 4.172-3.382 7.552-7.552 7.552H66.497c-4.172 0-7.553-3.38-7.553-7.552V91.64c0-4.172 3.38-7.552 7.553-7.552zm8.044 87.596c-20.397-23.16-45.805-52.352-45.805-52.352h140.98s-25.146 28.735-45.4 51.884c-33.454 38.234-18.118 36.4-49.774.468z" fill="#707070" class="transform-group"/></svg>');
	background-size: 1.2rem  1.2rem;
	z-index: 10;
	-webkit-transform: rotate(0deg) translate3d(0, 0, 0);
	      	transform: rotate(0deg) translate3d(0, 0, 0);
	-webkit-transition-duration: 300ms;
	      	transition-duration: 300ms;
}
.aui-refresh-content.aui-refresh-pull-down .aui-refresh-pull-arrow {
	-webkit-transform: rotate(0deg) translate3d(0,0,0);
          	transform: rotate(0deg) translate3d(0,0,0);
}
.aui-refresh-content.aui-refresh-pull-up .aui-refresh-pull-arrow {
	-webkit-transform: rotate(180deg) translate3d(0,0,0);
          	transform: rotate(180deg) translate3d(0,0,0);
}
.aui-refreshing .aui-refresh-pull-arrow{
	background-image: url('data:image/svg+xml;charset=utf-8,<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg"><path d="M99.917.167C45.52.167 1.28 43.642.03 97.737c1.153-47.204 37.045-85.08 81.153-85.08 44.834 0 81.184 39.147 81.184 87.426 0 10.346 8.387 18.738 18.733 18.738 10.342 0 18.733-8.39 18.733-18.737C199.833 44.9 155.1.167 99.917.167zm0 199.833c54.395 0 98.637-43.475 99.883-97.57-1.15 47.2-37.042 85.082-81.15 85.082-44.833 0-81.183-39.15-81.183-87.43 0-10.34-8.392-18.732-18.738-18.732C8.39 81.35 0 89.737 0 100.083 0 155.267 44.733 200 99.917 200z" fill="#707070" class="transform-group"/></svg>');
	-webkit-animation: rotate 1s 0s linear infinite;
        	animation: rotate 1s 0s linear infinite;
}