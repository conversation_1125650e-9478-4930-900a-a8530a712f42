/**
 * aui-slide.css 轮播样式
 * verson 0.0.2
 * <AUTHOR>
 * http://www.auicss.com
 * @todo more things to abstract, e.g. Loading css etc.
 * Licensed under the MIT license.
 * http://www.opensource.org/licenses/mit-license.php
 */
.aui-slide-wrap {
    display: block;
    position: relative;
    list-style: none;
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
    overflow: hidden;
}
.aui-slide-wrap .aui-slide-node {
    position: absolute;
    top: 0;
    left: 0;
    margin: 0;
    padding: 0;
    width: 100%;
    height: 100%;
    list-style: none;
    overflow: hidden;
    background: #ffffff;
}
.aui-slide-node-center {
	display: -moz-box;
    display: -webkit-box;
    display: box;
    -moz-box-pack: center;
    -webkit-box-pack: center;
    box-pack: center;
}
.aui-slide-node-middle {
	display: -moz-box;
    display: -webkit-box;
    display: box;
	-moz-box-align: center;
    -webkit-box-align: center;
    box-align: center;
}
.aui-slide-node img {
    width: 100%;
    height: 100%;
}
.aui-slide-page-wrap {
    width: 100%;
    height: 30px;
    line-height: 30px;
    position: absolute;
    padding: 0;
    left: 0;
    right: 0;
    bottom: 5px;
    z-index: 9999;
    text-align: center;
}
.aui-slide-page-dot {
    position: relative;
    display: inline-block;
    width: 6px;
    height: 6px;
    border-radius: 50%;
    list-style: none;
    margin: 5px 3px 0 3px;
    background: #c7c7c7;
    -webkit-transition: all 0.5s;
            transition: all 0.5s;
}
.aui-slide-page-line {
	position: absolute;
	bottom: -14px;
	height: 1px !important;
	position: relative;
    display: inline-block;
    background: #eeeeee;
    -webkit-transition: all 0.5s;
            transition: all 0.5s;
}
.aui-slide-page-active {
    background: #1abc9c;
    from { opacity: 0.3; }
    to { opacity: 1 }
}
.aui-slide-prev-btn,
.aui-slide-next-btn {
    display: inline-block;
    width: 50px;
    height: 30px;
    line-height: 30px;
    font-size: 1.2em;
    color: #1abc9c;
    background: rgba(249,249,249,0.6);
    position: relative;
}
.aui-slide-prev-btn {
    border-top-left-radius: 15px;
    border-bottom-left-radius: 15px;
}
.aui-slide-next-btn {
    border-top-right-radius: 15px;
    border-bottom-right-radius: 15px;
}
.aui-slide-prev-btn:active,
.aui-slide-next-btn:active {
    color: #f1c40f;
}