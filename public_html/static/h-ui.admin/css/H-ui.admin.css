@charset "utf-8";
/* -----------H-ui前端框架-------------
* H-ui.admin.css v2.5
* http://www.h-ui.net/
* Created & Modified by guojun<PERSON>
* Date modified 2016.05.31
*
* Copyright 2013-2016 北京颖杰联创科技有限公司 All rights reserved.
* Licensed under MIT license.
* http://opensource.org/licenses/MIT
*
*/
html{height:100%}
body{min-height: 100%;position: relative;font-size:14px;color:#333; background-color:#fff}
a{color:#333}a:hover,a:focus,.maincolor,.maincolor a{color:#06c}
.bg-1{ background-color:#f5fafe}
h4{line-height:30px}

/*页面框架*/
.Hui-header{position:absolute;top:0; right:0; left:0;height:44px;z-index:999; padding:0 15px}
.Hui-aside{position: absolute;top:44px;bottom:0;left:0;padding-top:10px;width:199px;z-index:99;overflow:auto; background-color:rgba(238,238,238,0.98);_background-color:rgb(238,238,238);border-right: 1px solid #e5e5e5}
.Hui-article-box{position: absolute;top:44px;right:0;bottom: 0;left:199px; overflow:hidden; z-index:1; background-color:#fff}
	.Hui-article{position: absolute;top:34px;bottom:0;left:0; right:0;overflow:auto;z-index:1}
.Hui-container{ padding:45px 0 0}
.Hui-aside,.Hui-article-box,.dislpayArrow{-moz-transition: all 0.2s ease 0s;-webkit-transition: all 0.2s ease 0s;-ms-transition: all 0.2s ease 0s;-o-transition: all 0.2s ease 0s;transition: all 0.2s ease 0s}
.big-page .Hui-article-box,.big-page .dislpayArrow,.big-page .breadcrumb{left:0px}
.big-page .Hui-aside{ left:-200px}
.page-container{ padding:20px}
@media (max-width: 767px) {
	.page-container{ padding:15px}
}
/*组件*/
/*logo*/

/*导航*/
#Hui-nav > ul > li{ font-weight:normal}
@media (max-width: 767px) {
	#Hui-nav{display: block; top: 0; left: 65px; right: 65px}
	#Hui-nav > ul { width: 100%}
	#Hui-nav > ul > li{ width: 24%;text-align:center;}
	#Hui-nav > ul > li a{ text-align: center; padding: 0}
}
#Hui-userbar{right:20px; position:absolute; top:0;}
#Hui-userbar > ul > li > a{padding:0 10px}
	
#Hui-msg .badge{ position:absolute; left:19px; top:4px; font-size:12px; font-weight:normal; padding:1px 5px}
/*左侧菜单*/
.Hui-aside .menu_dropdown ul { padding:0px}
.Hui-aside .menu_dropdown ul li { line-height:35px;overflow:hidden;zoom:1}
.Hui-aside .menu_dropdown ul li a { padding-left:15px;line-height:35px; display:block;font-weight: bold; margin:0;border-bottom: 1px solid #e5e5e5;}
.Hui-aside .menu_dropdown ul li a:hover { text-decoration: none;}
.Hui-aside .menu_dropdown dl{ margin-bottom:0}
.Hui-aside .menu_dropdown dl > dt {display:block;line-height:35px;padding-left:15px;cursor:pointer;position:relative;border-bottom: 1px solid #e5e5e5;font-weight:normal}
.Hui-aside .menu_dropdown dl > dt .menu_dropdown-arrow { position:absolute;overflow: hidden; top:0; right:15px;transition-duration:0.3s ;transition-property:all}
.Hui-aside .menu_dropdown dl > dd { display:none}
.Hui-aside .menu_dropdown dl > dt.selected .menu_dropdown-arrow {transform: rotate(180deg)}
.Hui-aside .menu_dropdown dl > dd.selected {display:block; margin-left:0px}
.Hui-aside .menu_dropdown dl > dd > ul{padding:3px 8px}
.Hui-aside .menu_dropdown dl > dd > ul > li { line-height:35px}
.Hui-aside .menu_dropdown dl > dd > ul > li > a{ padding-left:26px;font-weight:normal;border-bottom:none;}
.Hui-aside .menu_dropdown dl > dd > ul > li.current > a,
.Hui-aside .menu_dropdown dl > dd > ul > li.current > a:hover{background-color:rgba(255,255,255,0.2)}
.Hui-aside .menu_dropdown dl > dd > ul > li > a i{ font-weight: normal}

/*菜单收缩切换*/
.dislpayArrow{position: absolute;top: 0;bottom: 0;left:200px;width:0px; height:100%;z-index:10}
.dislpayArrow a{ position:absolute; display:block; width:17px; height:61px;top:50%; margin-top:-30px;outline:none}
.dislpayArrow a.open{ background-position:0 -61px}
.dislpayArrow a:hover{ text-decoration:none; background-position:right 0}
.dislpayArrow a.open:hover{background-position:right -61px}

/*选项卡导航*/
.Hui-tabNav-wp{position:relative; height:35px;overflow:hidden}
.Hui-tabNav,
.Hui-tabNav .acrossTab li
,.Hui-tabNav .acrossTab li em{background-image:url(../images/acrossTab/acrossTab-2.png)}
.Hui-tabNav{height:35px; padding-right:75px;overflow:hidden; position:relative;background-color:#efeef0; background-repeat: repeat-x; background-position: 0 -177px;}
.Hui-tabNav .acrossTab{ position:absolute; height:26px; line-height:26px; background:none; top:8px; left:0;padding-top:0}
.Hui-tabNav .acrossTab li{height:26px;line-height:26px;}
.Hui-tabNav .acrossTab li em{ right:-16px; height: 26px; width: 16px}
.loading {background:url(../images/loading.gif) no-repeat center; height:100px}
.show_iframe{ position:absolute; top:0; right:0; left:0; bottom:0;}
.show_iframe iframe {position: absolute;bottom: 0;height: 100%;width: 100%}
.Hui-tabNav-more {position: absolute;right:0px;width:70px;top:4px;display: none}

/*面包屑导航*/
.breadcrumb{background-color:#f5f5f5; padding:0 20px; position:relative; z-index:99}
@media (max-width: 767px) {
	.breadcrumb{ padding:0 15px;}
	.show_iframe{-webkit-overflow-scrolling: touch;overflow-y: scroll;}
}

/*横向手机 竖向平板*/
@media (max-width: 767px) {
	.Hui-aside{ display:none;float:none; width:100%; height:auto; margin-top:0;padding-top:0;bottom:auto}
	.big-page .Hui-aside{ left:0!important}
		.menu_dropdown dt{line-height:44px}
		.menu_dropdown li,.menu_dropdown dd li,.menu_dropdown dd li a{ line-height:44px}
  .Hui-tabNav{ position:fixed; z-index:998}
  .Hui-article{ top:44px}
  .Hui-article-box{ position:static; left:0}
  .Hui-article-box .pd-20{padding:15px}
		.Hui-article{left:0}
		.Hui-container .bk-gray{ border:none}
		.Hui-container .pd-10{ padding:0}
}

/*==============以下是业务相关的样式====================*/
/*权限*/
.permission-list{ border:solid 1px #eee;}
.permission-list > dt{ background-color:#efefef;padding:5px 10px}
.permission-list > dd{ padding:10px; padding-left:30px}
.permission-list > dd > dl{ border-bottom:solid 1px #eee; padding:5px 0}
.permission-list > dd > dl > dt{ display:inline-block;float:left;white-space:nowrap;width:100px}
.permission-list > dd > dl > dd{ margin-left:100px;}
.permission-list > dd > dl > dd > label{ padding-right:10px}

/*图片预览*/
.portfolio-area{ margin-right: -20px;}
.portfolio-area li{position: relative; float: left; margin-right: 20px; width:162px; height:162px;margin-top: 20px;}
.portfolio-area li.hover{ z-index:9}
.portfolio-area li .portfoliobox{ position: absolute; top: 0; left: 0; width: 152px; height: 152px;padding:5px;border: solid 1px #eee; background-color: #fff;}
.portfolio-area li .checkbox{position: absolute; top: 10px; right: 5px; cursor:pointer}
.portfolio-area li.hover .portfoliobox{ height:auto;padding-bottom:10px;box-shadow:0 1px 3px rgba(68, 68, 68,0.3);-moz-box-shadow:0 1px 3px rgba(68, 68, 68,0.3);-webkit-box-shadow:0 1px 3px rgba(68, 68, 68,0.3)}
.portfolio-area li .picbox{width: 150px; height: 150px;overflow: hidden;text-align: center;vertical-align:middle;display:table-cell; line-height:150px;}
.portfolio-area li .picbox img{max-width:150px; max-height:150px;vertical-align:middle;}
.portfolio-area li .textbox{ display: none; margin-top: 5px;}
.portfolio-area li.hover .textbox{ display: block;}
.portfolio-area li label{ display:block; cursor:pointer}