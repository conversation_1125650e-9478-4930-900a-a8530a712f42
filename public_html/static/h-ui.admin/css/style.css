@charset "utf-8";
/* 你自己的样式 */
.mbm, .mvm, .mam {
    margin-bottom: 10px !important;
}
.mtm, .mvm, .mam {
    margin-top: 10px !important;
}
.pagination {
    display: inline-block;
    float: right;
    margin: 20px 0;
    border-radius: 4px;
}

.pagination {
	list-style: none;
    display: inline-block;
    padding-left: 0;
    margin: 20px 0;
    border-radius: 0 !important;
} 
.pagination>li {
	list-style: none;
    display: inline;
	margin: 0;
    padding: 0;
    border: none;
    outline: 0;
    font-size: 100%;
    font: inherit;
    vertical-align: baseline;
}
.pagination li a {
  color: #dc6767;
}
.pagination>li>a, .pagination>li>span {
    position: relative;
    float: left;
    padding: 6px 12px;
    line-height: 1.42857143;
    text-decoration: none;
   color: #428bca;
  background-color: #ffffff;
    border: 1px solid #ddd;
    margin-left: -1px;
}
.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
  color: #2a6496;
  background-color: #eeeeee;
  border-color: #dddddd;
}
.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  z-index: 2;
  color: #ffffff;
  background-color: #428bca;
  border-color: #428bca;
  cursor: default;
}

.tooltip-inner {max-width: 800px;}
.green{color: green;}
.red{color: red;}