/*-----------H-ui前端框架-------------
* H-ui.min.js v3.1.9
* http://www.h-ui.net/
* Created & Modified by guojunhui
* Date modified 2017.11.13
*
* Copyright 2013-2017 北京颖杰联创科技有限公司 All rights reserved.
* Licensed under MIT license.
* http://opensource.org/licenses/MIT
*/
!function () { if (navigator.userAgent.match(/IEMobile\/10\.0/)) { var a = document.createElement("style"); a.appendChild(document.createTextNode("@-ms-viewport{width:auto!important}")); document.getElementsByTagName("head")[0].appendChild(a) } }(); function stopDefault(a) { if (a && a.preventDefault) { a.preventDefault() } else { window.event.returnValue = false } return false } !(function (a) { if (typeof define === "function" && define.amd) { define(["jquery"], a) } else { if (typeof exports === "object") { module.exports = a(require("jquery")) } else { a(jQuery) } } }(function (f) { var a = /\+/g; function d(i) { return b.raw ? i : encodeURIComponent(i) } function g(i) { return b.raw ? i : decodeURIComponent(i) } function h(i) { return d(b.json ? JSON.stringify(i) : String(i)) } function c(i) { if (i.indexOf('"') === 0) { i = i.slice(1, -1).replace(/\\"/g, '"').replace(/\\\\/g, "\\") } try { i = decodeURIComponent(i.replace(a, " ")); return b.json ? JSON.parse(i) : i } catch (j) { } } function e(j, i) { var k = b.raw ? j : c(j); return f.isFunction(i) ? i(k) : k } var b = f.cookie = function (q, p, v) { if (arguments.length > 1 && !f.isFunction(p)) { v = f.extend({}, b.defaults, v); if (typeof v.expires === "number") { var r = v.expires, u = v.expires = new Date(); u.setMilliseconds(u.getMilliseconds() + r * 86400000) } return (document.cookie = [d(q), "=", h(p), v.expires ? "; expires=" + v.expires.toUTCString() : "", v.path ? "; path=" + v.path : "", v.domain ? "; domain=" + v.domain : "", v.secure ? "; secure" : ""].join("")) } var w = q ? undefined : {}, s = document.cookie ? document.cookie.split("; ") : [], o = 0, m = s.length; for (; o < m; o++) { var n = s[o].split("="), j = g(n.shift()), k = n.join("="); if (q === j) { w = e(k, p); break } if (!q && (k = e(k)) !== undefined) { w[j] = k } } return w }; b.defaults = {}; f.removeCookie = function (j, i) { f.cookie(j, "", f.extend({}, i, { expires: -1 })); return !f.cookie(j) } })); (function (a) { if (typeof define === "function" && define.amd) { define(["jquery"], a) } else { a((typeof (jQuery) != "undefined") ? jQuery : window.Zepto) } }(function (f) { var c = {}; c.fileapi = f("<input type='file'/>").get(0).files !== undefined; c.formdata = window.FormData !== undefined; var e = !!f.fn.prop; f.fn.attr2 = function () { if (!e) { return this.attr.apply(this, arguments) } var g = this.prop.apply(this, arguments); if ((g && g.jquery) || typeof g === "string") { return g } return this.attr.apply(this, arguments) }; f.fn.ajaxSubmit = function (j) { if (!this.length) { d("ajaxSubmit: skipping submit process - no element selected"); return this } var i, C, m, o = this; if (typeof j == "function") { j = { success: j } } else { if (j === undefined) { j = {} } } i = j.type || this.attr2("method"); C = j.url || this.attr2("action"); m = (typeof C === "string") ? f.trim(C) : ""; m = m || window.location.href || ""; if (m) { m = (m.match(/^([^#]+)/) || [])[1] } j = f.extend(true, { url: m, success: f.ajaxSettings.success, type: i || f.ajaxSettings.type, iframeSrc: /^https/i.test(window.location.href || "") ? "javascript:false" : "about:blank" }, j); var u = {}; this.trigger("form-pre-serialize", [this, j, u]); if (u.veto) { d("ajaxSubmit: submit vetoed via form-pre-serialize trigger"); return this } if (j.beforeSerialize && j.beforeSerialize(this, j) === false) { d("ajaxSubmit: submit aborted via beforeSerialize callback"); return this } var n = j.traditional; if (n === undefined) { n = f.ajaxSettings.traditional } var s = []; var E, F = this.formToArray(j.semantic, s); if (j.data) { j.extraData = j.data; E = f.param(j.data, n) } if (j.beforeSubmit && j.beforeSubmit(F, this, j) === false) { d("ajaxSubmit: submit aborted via beforeSubmit callback"); return this } this.trigger("form-submit-validate", [F, this, j, u]); if (u.veto) { d("ajaxSubmit: submit vetoed via form-submit-validate trigger"); return this } var y = f.param(F, n); if (E) { y = (y ? (y + "&" + E) : E) } if (j.type.toUpperCase() == "GET") { j.url += (j.url.indexOf("?") >= 0 ? "&" : "?") + y; j.data = null } else { j.data = y } var H = []; if (j.resetForm) { H.push(function () { o.resetForm() }) } if (j.clearForm) { H.push(function () { o.clearForm(j.includeHidden) }) } if (!j.dataType && j.target) { var l = j.success || function () { }; H.push(function (q) { var k = j.replaceTarget ? "replaceWith" : "html"; f(j.target)[k](q).each(l, arguments) }) } else { if (j.success) { H.push(j.success) } } j.success = function (K, q, L) { var J = j.context || this; for (var I = 0, k = H.length; I < k; I++) { H[I].apply(J, [K, q, L || o, o]) } }; if (j.error) { var z = j.error; j.error = function (J, k, q) { var I = j.context || this; z.apply(I, [J, k, q, o]) } } if (j.complete) { var h = j.complete; j.complete = function (I, k) { var q = j.context || this; h.apply(q, [I, k, o]) } } var D = f("input[type=file]:enabled", this).filter(function () { return f(this).val() !== "" }); var p = D.length > 0; var B = "multipart/form-data"; var x = (o.attr("enctype") == B || o.attr("encoding") == B); var w = c.fileapi && c.formdata; d("fileAPI :" + w); var r = (p || x) && !w; var v; if (j.iframe !== false && (j.iframe || r)) { if (j.closeKeepAlive) { f.get(j.closeKeepAlive, function () { v = G(F) }) } else { v = G(F) } } else { if ((p || x) && w) { v = t(F) } else { v = f.ajax(j) } } o.removeData("jqxhr").data("jqxhr", v); for (var A = 0; A < s.length; A++) { s[A] = null } this.trigger("form-submit-notify", [this, j]); return this; function g(K) { var L = f.param(K, j.traditional).split("&"); var q = L.length; var k = []; var J, I; for (J = 0; J < q; J++) { L[J] = L[J].replace(/\+/g, " "); I = L[J].split("="); k.push([decodeURIComponent(I[0]), decodeURIComponent(I[1])]) } return k } function t(q) { var k = new FormData(); for (var I = 0; I < q.length; I++) { k.append(q[I].name, q[I].value) } if (j.extraData) { var L = g(j.extraData); for (I = 0; I < L.length; I++) { if (L[I]) { k.append(L[I][0], L[I][1]) } } } j.data = null; var K = f.extend(true, {}, f.ajaxSettings, j, { contentType: false, processData: false, cache: false, type: i || "POST" }); if (j.uploadProgress) { K.xhr = function () { var M = f.ajaxSettings.xhr(); if (M.upload) { M.upload.addEventListener("progress", function (Q) { var P = 0; var N = Q.loaded || Q.position; var O = Q.total; if (Q.lengthComputable) { P = Math.ceil(N / O * 100) } j.uploadProgress(Q, N, O, P) }, false) } return M } } K.data = null; var J = K.beforeSend; K.beforeSend = function (N, M) { if (j.formData) { M.data = j.formData } else { M.data = k } if (J) { J.call(this, N, M) } }; return f.ajax(K) } function G(af) { var L = o[0], K, ab, V, ad, Y, N, Q, O, P, Z, ac, T; var ai = f.Deferred(); ai.abort = function (aj) { O.abort(aj) }; if (af) { for (ab = 0; ab < s.length; ab++) { K = f(s[ab]); if (e) { K.prop("disabled", false) } else { K.removeAttr("disabled") } } } V = f.extend(true, {}, f.ajaxSettings, j); V.context = V.context || V; Y = "jqFormIO" + (new Date().getTime()); if (V.iframeTarget) { N = f(V.iframeTarget); Z = N.attr2("name"); if (!Z) { N.attr2("name", Y) } else { Y = Z } } else { N = f('<iframe name="' + Y + '" src="' + V.iframeSrc + '" />'); N.css({ position: "absolute", top: "-1000px", left: "-1000px" }) } Q = N[0]; O = { aborted: 0, responseText: null, responseXML: null, status: 0, statusText: "n/a", getAllResponseHeaders: function () { }, getResponseHeader: function () { }, setRequestHeader: function () { }, abort: function (aj) { var ak = (aj === "timeout" ? "timeout" : "aborted"); d("aborting upload... " + ak); this.aborted = 1; try { if (Q.contentWindow.document.execCommand) { Q.contentWindow.document.execCommand("Stop") } } catch (al) { } N.attr("src", V.iframeSrc); O.error = ak; if (V.error) { V.error.call(V.context, O, ak, aj) } if (ad) { f.event.trigger("ajaxError", [O, V, ak]) } if (V.complete) { V.complete.call(V.context, O, ak) } } }; ad = V.global; if (ad && 0 === f.active++) { f.event.trigger("ajaxStart") } if (ad) { f.event.trigger("ajaxSend", [O, V]) } if (V.beforeSend && V.beforeSend.call(V.context, O, V) === false) { if (V.global) { f.active-- } ai.reject(); return ai } if (O.aborted) { ai.reject(); return ai } P = L.clk; if (P) { Z = P.name; if (Z && !P.disabled) { V.extraData = V.extraData || {}; V.extraData[Z] = P.value; if (P.type == "image") { V.extraData[Z + ".x"] = L.clk_x; V.extraData[Z + ".y"] = L.clk_y } } } var U = 1; var R = 2; function S(al) { var ak = null; try { if (al.contentWindow) { ak = al.contentWindow.document } } catch (aj) { d("cannot get iframe.contentWindow document: " + aj) } if (ak) { return ak } try { ak = al.contentDocument ? al.contentDocument : al.document } catch (aj) { d("cannot get iframe.contentDocument: " + aj); ak = al.document } return ak } var J = f("meta[name=csrf-token]").attr("content"); var I = f("meta[name=csrf-param]").attr("content"); if (I && J) { V.extraData = V.extraData || {}; V.extraData[I] = J } function aa() { var ar = o.attr2("target"), an = o.attr2("action"), al = "multipart/form-data", ao = o.attr("enctype") || o.attr("encoding") || al; L.setAttribute("target", Y); if (!i || /post/i.test(i)) { L.setAttribute("method", "POST") } if (an != V.url) { L.setAttribute("action", V.url) } if (!V.skipEncodingOverride && (!i || /post/i.test(i))) { o.attr({ encoding: "multipart/form-data", enctype: "multipart/form-data" }) } if (V.timeout) { T = setTimeout(function () { ac = true; X(U) }, V.timeout) } function ap() { try { var at = S(Q).readyState; d("state = " + at); if (at && at.toLowerCase() == "uninitialized") { setTimeout(ap, 50) } } catch (au) { d("Server abort: ", au, " (", au.name, ")"); X(R); if (T) { clearTimeout(T) } T = undefined } } var aq = []; try { if (V.extraData) { for (var ak in V.extraData) { if (V.extraData.hasOwnProperty(ak)) { if (f.isPlainObject(V.extraData[ak]) && V.extraData[ak].hasOwnProperty("name") && V.extraData[ak].hasOwnProperty("value")) { aq.push(f('<input type="hidden" name="' + V.extraData[ak].name + '">').val(V.extraData[ak].value).appendTo(L)[0]) } else { aq.push(f('<input type="hidden" name="' + ak + '">').val(V.extraData[ak]).appendTo(L)[0]) } } } } if (!V.iframeTarget) { N.appendTo("body") } if (Q.attachEvent) { Q.attachEvent("onload", X) } else { Q.addEventListener("load", X, false) } setTimeout(ap, 15); try { L.submit() } catch (am) { var aj = document.createElement("form").submit; aj.apply(L) } } finally { L.setAttribute("action", an); L.setAttribute("enctype", ao); if (ar) { L.setAttribute("target", ar) } else { o.removeAttr("target") } f(aq).remove() } } if (V.forceSync) { aa() } else { setTimeout(aa, 10) } var ag, ah, ae = 50, M; function X(ap) { if (O.aborted || M) { return } ah = S(Q); if (!ah) { d("cannot access response document"); ap = R } if (ap === U && O) { O.abort("timeout"); ai.reject(O, "timeout"); return } else { if (ap == R && O) { O.abort("server abort"); ai.reject(O, "error", "server abort"); return } } if (!ah || ah.location.href == V.iframeSrc) { if (!ac) { return } } if (Q.detachEvent) { Q.detachEvent("onload", X) } else { Q.removeEventListener("load", X, false) } var an = "success", ar; try { if (ac) { throw "timeout" } var am = V.dataType == "xml" || ah.XMLDocument || f.isXMLDoc(ah); d("isXml=" + am); if (!am && window.opera && (ah.body === null || !ah.body.innerHTML)) { if (--ae) { d("requeing onLoad callback, DOM not available"); setTimeout(X, 250); return } } var at = ah.body ? ah.body : ah.documentElement; O.responseText = at ? at.innerHTML : null; O.responseXML = ah.XMLDocument ? ah.XMLDocument : ah; if (am) { V.dataType = "xml" } O.getResponseHeader = function (aw) { var av = { "content-type": V.dataType }; return av[aw.toLowerCase()] }; if (at) { O.status = Number(at.getAttribute("status")) || O.status; O.statusText = at.getAttribute("statusText") || O.statusText } var aj = (V.dataType || "").toLowerCase(); var aq = /(json|script|text)/.test(aj); if (aq || V.textarea) { var ao = ah.getElementsByTagName("textarea")[0]; if (ao) { O.responseText = ao.value; O.status = Number(ao.getAttribute("status")) || O.status; O.statusText = ao.getAttribute("statusText") || O.statusText } else { if (aq) { var ak = ah.getElementsByTagName("pre")[0]; var au = ah.getElementsByTagName("body")[0]; if (ak) { O.responseText = ak.textContent ? ak.textContent : ak.innerText } else { if (au) { O.responseText = au.textContent ? au.textContent : au.innerText } } } } } else { if (aj == "xml" && !O.responseXML && O.responseText) { O.responseXML = W(O.responseText) } } try { ag = k(O, aj, V) } catch (al) { an = "parsererror"; O.error = ar = (al || an) } } catch (al) { d("error caught: ", al); an = "error"; O.error = ar = (al || an) } if (O.aborted) { d("upload aborted"); an = null } if (O.status) { an = (O.status >= 200 && O.status < 300 || O.status === 304) ? "success" : "error" } if (an === "success") { if (V.success) { V.success.call(V.context, ag, "success", O) } ai.resolve(O.responseText, "success", O); if (ad) { f.event.trigger("ajaxSuccess", [O, V]) } } else { if (an) { if (ar === undefined) { ar = O.statusText } if (V.error) { V.error.call(V.context, O, an, ar) } ai.reject(O, "error", ar); if (ad) { f.event.trigger("ajaxError", [O, V, ar]) } } } if (ad) { f.event.trigger("ajaxComplete", [O, V]) } if (ad && !--f.active) { f.event.trigger("ajaxStop") } if (V.complete) { V.complete.call(V.context, O, an) } M = true; if (V.timeout) { clearTimeout(T) } setTimeout(function () { if (!V.iframeTarget) { N.remove() } else { N.attr("src", V.iframeSrc) } O.responseXML = null }, 100) } var W = f.parseXML || function (aj, ak) { if (window.ActiveXObject) { ak = new ActiveXObject("Microsoft.XMLDOM"); ak.async = "false"; ak.loadXML(aj) } else { ak = (new DOMParser()).parseFromString(aj, "text/xml") } return (ak && ak.documentElement && ak.documentElement.nodeName != "parsererror") ? ak : null }; var q = f.parseJSON || function (aj) { return window["eval"]("(" + aj + ")") }; var k = function (ao, am, al) { var ak = ao.getResponseHeader("content-type") || "", aj = am === "xml" || !am && ak.indexOf("xml") >= 0, an = aj ? ao.responseXML : ao.responseText; if (aj && an.documentElement.nodeName === "parsererror") { if (f.error) { f.error("parsererror") } } if (al && al.dataFilter) { an = al.dataFilter(an, am) } if (typeof an === "string") { if (am === "json" || !am && ak.indexOf("json") >= 0) { an = q(an) } else { if (am === "script" || !am && ak.indexOf("javascript") >= 0) { f.globalEval(an) } } } return an }; return ai } }; f.fn.ajaxForm = function (g) { g = g || {}; g.delegation = g.delegation && f.isFunction(f.fn.on); if (!g.delegation && this.length === 0) { var h = { s: this.selector, c: this.context }; if (!f.isReady && h.s) { d("DOM not ready, queuing ajaxForm"); f(function () { f(h.s, h.c).ajaxForm(g) }); return this } d("terminating; zero elements found by selector" + (f.isReady ? "" : " (DOM not ready)")); return this } if (g.delegation) { f(document).off("submit.form-plugin", this.selector, b).off("click.form-plugin", this.selector, a).on("submit.form-plugin", this.selector, g, b).on("click.form-plugin", this.selector, g, a); return this } return this.ajaxFormUnbind().on("submit.form-plugin", g, b).on("click.form-plugin", g, a) }; function b(h) { var g = h.data; if (!h.isDefaultPrevented()) { h.preventDefault(); f(h.target).ajaxSubmit(g) } } function a(k) { var j = k.target; var h = f(j); if (!(h.is("[type=submit],[type=image]"))) { var g = h.closest("[type=submit]"); if (g.length === 0) { return } j = g[0] } var i = this; i.clk = j; if (j.type == "image") { if (k.offsetX !== undefined) { i.clk_x = k.offsetX; i.clk_y = k.offsetY } else { if (typeof f.fn.offset == "function") { var l = h.offset(); i.clk_x = k.pageX - l.left; i.clk_y = k.pageY - l.top } else { i.clk_x = k.pageX - j.offsetLeft; i.clk_y = k.pageY - j.offsetTop } } } setTimeout(function () { i.clk = i.clk_x = i.clk_y = null }, 100) } f.fn.ajaxFormUnbind = function () { return this.unbind("submit.form-plugin click.form-plugin") }; f.fn.formToArray = function (x, g) { var w = []; if (this.length === 0) { return w } var l = this[0]; var z = this.attr("id"); var q = x ? l.getElementsByTagName("*") : l.elements; var A; if (q && !/MSIE [678]/.test(navigator.userAgent)) { q = f(q).get() } if (z) { A = f(':input[form="' + z + '"]').get(); if (A.length) { q = (q || []).concat(A) } } if (!q || !q.length) { return w } var r, p, o, y, m, t, k; for (r = 0, t = q.length; r < t; r++) { m = q[r]; o = m.name; if (!o || m.disabled) { continue } if (x && l.clk && m.type == "image") { if (l.clk == m) { w.push({ name: o, value: f(m).val(), type: m.type }); w.push({ name: o + ".x", value: l.clk_x }, { name: o + ".y", value: l.clk_y }) } continue } y = f.fieldValue(m, true); if (y && y.constructor == Array) { if (g) { g.push(m) } for (p = 0, k = y.length; p < k; p++) { w.push({ name: o, value: y[p] }) } } else { if (c.fileapi && m.type == "file") { if (g) { g.push(m) } var h = m.files; if (h.length) { for (p = 0; p < h.length; p++) { w.push({ name: o, value: h[p], type: m.type }) } } else { w.push({ name: o, value: "", type: m.type }) } } else { if (y !== null && typeof y != "undefined") { if (g) { g.push(m) } w.push({ name: o, value: y, type: m.type, required: m.required }) } } } } if (!x && l.clk) { var s = f(l.clk), u = s[0]; o = u.name; if (o && !u.disabled && u.type == "image") { w.push({ name: o, value: s.val() }); w.push({ name: o + ".x", value: l.clk_x }, { name: o + ".y", value: l.clk_y }) } } return w }; f.fn.formSerialize = function (g) { return f.param(this.formToArray(g)) }; f.fn.fieldSerialize = function (h) { var g = []; this.each(function () { var m = this.name; if (!m) { return } var k = f.fieldValue(this, h); if (k && k.constructor == Array) { for (var l = 0, j = k.length; l < j; l++) { g.push({ name: m, value: k[l] }) } } else { if (k !== null && typeof k != "undefined") { g.push({ name: this.name, value: k }) } } }); return f.param(g) }; f.fn.fieldValue = function (m) { for (var l = [], j = 0, g = this.length; j < g; j++) { var k = this[j]; var h = f.fieldValue(k, m); if (h === null || typeof h == "undefined" || (h.constructor == Array && !h.length)) { continue } if (h.constructor == Array) { f.merge(l, h) } else { l.push(h) } } return l }; f.fieldValue = function (g, o) { var j = g.name, u = g.type, w = g.tagName.toLowerCase(); if (o === undefined) { o = true } if (o && (!j || g.disabled || u == "reset" || u == "button" || (u == "checkbox" || u == "radio") && !g.checked || (u == "submit" || u == "image") && g.form && g.form.clk != g || w == "select" && g.selectedIndex == -1)) { return null } if (w == "select") { var p = g.selectedIndex; if (p < 0) { return null } var r = [], h = g.options; var l = (u == "select-one"); var q = (l ? p + 1 : h.length); for (var k = (l ? p : 0); k < q; k++) { var m = h[k]; if (m.selected) { var s = m.value; if (!s) { s = (m.attributes && m.attributes.value && !(m.attributes.value.specified)) ? m.text : m.value } if (l) { return s } r.push(s) } } return r } return f(g).val() }; f.fn.clearForm = function (g) { return this.each(function () { f("input,select,textarea", this).clearFields(g) }) }; f.fn.clearFields = f.fn.clearInputs = function (g) { var h = /^(?:color|date|datetime|email|month|number|password|range|search|tel|text|time|url|week)$/i; return this.each(function () { var j = this.type, i = this.tagName.toLowerCase(); if (h.test(j) || i == "textarea") { this.value = "" } else { if (j == "checkbox" || j == "radio") { this.checked = false } else { if (i == "select") { this.selectedIndex = -1 } else { if (j == "file") { if (/MSIE/.test(navigator.userAgent)) { f(this).replaceWith(f(this).clone(true)) } else { f(this).val("") } } else { if (g) { if ((g === true && /hidden/.test(j)) || (typeof g == "string" && f(this).is(g))) { this.value = "" } } } } } } }) }; f.fn.resetForm = function () { return this.each(function () { if (typeof this.reset == "function" || (typeof this.reset == "object" && !this.reset.nodeType)) { this.reset() } }) }; f.fn.enable = function (g) { if (g === undefined) { g = true } return this.each(function () { this.disabled = !g }) }; f.fn.selected = function (g) { if (g === undefined) { g = true } return this.each(function () { var h = this.type; if (h == "checkbox" || h == "radio") { this.checked = g } else { if (this.tagName.toLowerCase() == "option") { var i = f(this).parent("select"); if (g && i[0] && i[0].type == "select-one") { i.find("option").selected(false) } this.selected = g } } }) }; f.fn.ajaxSubmit.debug = false; function d() { if (!f.fn.ajaxSubmit.debug) { return } var g = "[jquery.form] " + Array.prototype.join.call(arguments, ""); if (window.console && window.console.log) { window.console.log(g) } else { if (window.opera && window.opera.postError) { window.opera.postError(g) } } } })); !(function (c, b, a, e) { var d = c(b); c.fn.lazyload = function (f) { var h = this; var i; var g = { threshold: 0, failure_limit: 0, event: "scroll", effect: "show", container: b, data_attribute: "original", skip_invisible: true, appear: null, load: null, placeholder: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsQAAA7EAZUrDhsAAAANSURBVBhXYzh8+PB/AAffA0nNPuCLAAAAAElFTkSuQmCC" }; function j() { var k = 0; h.each(function () { var l = c(this); if (g.skip_invisible && !l.is(":visible")) { return } if (c.abovethetop(this, g) || c.leftofbegin(this, g)) { } else { if (!c.belowthefold(this, g) && !c.rightoffold(this, g)) { l.trigger("appear"); k = 0 } else { if (++k > g.failure_limit) { return false } } } }) } if (f) { if (e !== f.failurelimit) { f.failure_limit = f.failurelimit; delete f.failurelimit } if (e !== f.effectspeed) { f.effect_speed = f.effectspeed; delete f.effectspeed } c.extend(g, f) } i = (g.container === e || g.container === b) ? d : c(g.container); if (0 === g.event.indexOf("scroll")) { i.on(g.event, function () { return j() }) } this.each(function () { var k = this; var l = c(k); k.loaded = false; if (l.attr("src") === e || l.attr("src") === false) { if (l.is("img")) { l.attr("src", g.placeholder) } } l.one("appear", function () { if (!this.loaded) { if (g.appear) { var m = h.length; g.appear.call(k, m, g) } c("<img />").on("load", function () { var o = l.attr("data-" + g.data_attribute); l.hide(); if (l.is("img")) { l.attr("src", o) } else { l.css("background-image", "url('" + o + "')") } l[g.effect](g.effect_speed); k.loaded = true; var n = c.grep(h, function (q) { return !q.loaded }); h = c(n); if (g.load) { var p = h.length; g.load.call(k, p, g) } }).attr("src", l.attr("data-" + g.data_attribute)) } }); if (0 !== g.event.indexOf("scroll")) { l.on(g.event, function () { if (!k.loaded) { l.trigger("appear") } }) } }); d.on("resize", function () { j() }); if ((/(?:iphone|ipod|ipad).*os 5/gi).test(navigator.appVersion)) { d.on("pageshow", function (k) { if (k.originalEvent && k.originalEvent.persisted) { h.each(function () { c(this).trigger("appear") }) } }) } c(a).ready(function () { j() }); return this }; c.belowthefold = function (g, h) { var f; if (h.container === e || h.container === b) { f = (b.innerHeight ? b.innerHeight : d.height()) + d.scrollTop() } else { f = c(h.container).offset().top + c(h.container).height() } return f <= c(g).offset().top - h.threshold }; c.rightoffold = function (g, h) { var f; if (h.container === e || h.container === b) { f = d.width() + d.scrollLeft() } else { f = c(h.container).offset().left + c(h.container).width() } return f <= c(g).offset().left - h.threshold }; c.abovethetop = function (g, h) { var f; if (h.container === e || h.container === b) { f = d.scrollTop() } else { f = c(h.container).offset().top } return f >= c(g).offset().top + h.threshold + c(g).height() }; c.leftofbegin = function (g, h) { var f; if (h.container === e || h.container === b) { f = d.scrollLeft() } else { f = c(h.container).offset().left } return f >= c(g).offset().left + h.threshold + c(g).width() }; c.inviewport = function (f, g) { return !c.rightoffold(f, g) && !c.leftofbegin(f, g) && !c.belowthefold(f, g) && !c.abovethetop(f, g) }; c.extend(c.expr[":"], { "below-the-fold": function (f) { return c.belowthefold(f, { threshold: 0 }) }, "above-the-top": function (f) { return !c.belowthefold(f, { threshold: 0 }) }, "right-of-screen": function (f) { return c.rightoffold(f, { threshold: 0 }) }, "left-of-screen": function (f) { return !c.rightoffold(f, { threshold: 0 }) }, "in-viewport": function (f) { return c.inviewport(f, { threshold: 0 }) }, "above-the-fold": function (f) { return !c.belowthefold(f, { threshold: 0 }) }, "right-of-fold": function (f) { return c.rightoffold(f, { threshold: 0 }) }, "left-of-fold": function (f) { return !c.rightoffold(f, { threshold: 0 }) } }) })(jQuery, window, document); (function (a, d, b) { var c = function (e, g) { var f = !!d.getComputedStyle; if (!f) { d.getComputedStyle = function (x) { this.el = x; this.getPropertyValue = function (z) { var y = /(\-([a-z]){1})/g; if (z === "float") { z = "styleFloat" } if (y.test(z)) { z = z.replace(y, function () { return arguments[2].toUpperCase() }) } return x.currentStyle[z] ? x.currentStyle[z] : null }; return this } } var q = function (A, y, z, x) { if ("addEventListener" in A) { try { A.addEventListener(y, z, x) } catch (B) { if (typeof z === "object" && z.handleEvent) { A.addEventListener(y, function (C) { z.handleEvent.call(z, C) }, x) } else { throw B } } } else { if ("attachEvent" in A) { if (typeof z === "object" && z.handleEvent) { A.attachEvent("on" + y, function () { z.handleEvent.call(z) }) } else { A.attachEvent("on" + y, z) } } } }, r = function (A, y, z, x) { if ("removeEventListener" in A) { try { A.removeEventListener(y, z, x) } catch (B) { if (typeof z === "object" && z.handleEvent) { A.removeEventListener(y, function (C) { z.handleEvent.call(z, C) }, x) } else { throw B } } } else { if ("detachEvent" in A) { if (typeof z === "object" && z.handleEvent) { A.detachEvent("on" + y, function () { z.handleEvent.call(z) }) } else { A.detachEvent("on" + y, z) } } } }, h = function (z) { if (z.children.length < 1) { throw new Error("The Nav container has no containing elements") } var y = []; for (var x = 0; x < z.children.length; x++) { if (z.children[x].nodeType === 1) { y.push(z.children[x]) } } return y }, s = function (z, x) { for (var y in x) { z.setAttribute(y, x[y]) } }, l = function (y, x) { if (y.className.indexOf(x) !== 0) { y.className += " " + x; y.className = y.className.replace(/(^\s*)|(\s*$)/g, "") } }, o = function (z, x) { var y = new RegExp("(\\s|^)" + x + "(\\s|$)"); z.className = z.className.replace(y, " ").replace(/(^\s*)|(\s*$)/g, "") }, t = function (A, z, y) { for (var x = 0; x < A.length; x++) { z.call(y, x, A[x]) } }; var j, n, i, w = a.createElement("style"), k = a.documentElement, v, p, u; var m = function (z, x) { var y; this.options = { animate: true, transition: 284, label: "Menu", insert: "before", customToggle: "", closeOnNavClick: false, openPos: "relative", navClass: "nav-collapse", navActiveClass: "js-nav-active", jsClass: "js", init: function () { }, open: function () { }, close: function () { } }; for (y in x) { this.options[y] = x[y] } l(k, this.options.jsClass); this.wrapperEl = z.replace("#", ""); if (a.getElementById(this.wrapperEl)) { this.wrapper = a.getElementById(this.wrapperEl) } else { if (a.querySelector(this.wrapperEl)) { this.wrapper = a.querySelector(this.wrapperEl) } else { throw new Error("The nav element you are trying to select doesn't exist") } } this.wrapper.inner = h(this.wrapper); n = this.options; j = this.wrapper; this._init(this) }; m.prototype = { destroy: function () { this._removeStyles(); o(j, "closed"); o(j, "opened"); o(j, n.navClass); o(j, n.navClass + "-" + this.index); o(k, n.navActiveClass); j.removeAttribute("style"); j.removeAttribute("aria-hidden"); r(d, "resize", this, false); r(d, "focus", this, false); r(a.body, "touchmove", this, false); r(i, "touchstart", this, false); r(i, "touchend", this, false); r(i, "mouseup", this, false); r(i, "keyup", this, false); r(i, "click", this, false); if (!n.customToggle) { i.parentNode.removeChild(i) } else { i.removeAttribute("aria-hidden") } }, toggle: function () { if (v === true) { if (!u) { this.open() } else { this.close() } } }, open: function () { if (!u) { o(j, "closed"); l(j, "opened"); l(k, n.navActiveClass); l(i, "active"); j.style.position = n.openPos; s(j, { "aria-hidden": "false" }); u = true; n.open() } }, close: function () { if (u) { l(j, "closed"); o(j, "opened"); o(k, n.navActiveClass); o(i, "active"); s(j, { "aria-hidden": "true" }); if (n.animate) { v = false; setTimeout(function () { j.style.position = "absolute"; v = true }, n.transition + 10) } else { j.style.position = "absolute" } u = false; n.close() } }, resize: function () { if (d.getComputedStyle(i, null).getPropertyValue("display") !== "none") { p = true; s(i, { "aria-hidden": "false" }); if (j.className.match(/(^|\s)closed(\s|$)/)) { s(j, { "aria-hidden": "true" }); j.style.position = "absolute" } this._createStyles(); this._calcHeight() } else { p = false; s(i, { "aria-hidden": "true" }); s(j, { "aria-hidden": "false" }); j.style.position = n.openPos; this._removeStyles() } }, handleEvent: function (y) { var x = y || d.event; switch (x.type) { case "touchstart": this._onTouchStart(x); break; case "touchmove": this._onTouchMove(x); break; case "touchend": case "mouseup": this._onTouchEnd(x); break; case "click": this._preventDefault(x); break; case "keyup": this._onKeyUp(x); break; case "focus": case "resize": this.resize(x); break } }, _init: function () { this.index = b++; l(j, n.navClass); l(j, n.navClass + "-" + this.index); l(j, "closed"); v = true; u = false; this._closeOnNavClick(); this._createToggle(); this._transitions(); this.resize(); var x = this; setTimeout(function () { x.resize() }, 20); q(d, "resize", this, false); q(d, "focus", this, false); q(a.body, "touchmove", this, false); q(i, "touchstart", this, false); q(i, "touchend", this, false); q(i, "mouseup", this, false); q(i, "keyup", this, false); q(i, "click", this, false); n.init() }, _createStyles: function () { if (!w.parentNode) { w.type = "text/css"; a.getElementsByTagName("head")[0].appendChild(w) } }, _removeStyles: function () { if (w.parentNode) { w.parentNode.removeChild(w) } }, _createToggle: function () { if (!n.customToggle) { var y = a.createElement("a"); y.innerHTML = n.label; s(y, { "href": "#", "class": "nav-toggle" }); if (n.insert === "after") { j.parentNode.insertBefore(y, j.nextSibling) } else { j.parentNode.insertBefore(y, j) } i = y } else { var x = n.customToggle.replace("#", ""); if (a.getElementById(x)) { i = a.getElementById(x) } else { if (a.querySelector(x)) { i = a.querySelector(x) } else { throw new Error("The custom nav toggle you are trying to select doesn't exist") } } } }, _closeOnNavClick: function () { if (n.closeOnNavClick) { var y = j.getElementsByTagName("a"), x = this; t(y, function (z, A) { q(y[z], "click", function () { if (p) { x.toggle() } }, false) }) } }, _preventDefault: function (x) { if (x.preventDefault) { if (x.stopImmediatePropagation) { x.stopImmediatePropagation() } x.preventDefault(); x.stopPropagation(); return false } else { x.returnValue = false } }, _onTouchStart: function (x) { if (!Event.prototype.stopImmediatePropagation) { this._preventDefault(x) } this.startX = x.touches[0].clientX; this.startY = x.touches[0].clientY; this.touchHasMoved = false; r(i, "mouseup", this, false) }, _onTouchMove: function (x) { if (Math.abs(x.touches[0].clientX - this.startX) > 10 || Math.abs(x.touches[0].clientY - this.startY) > 10) { this.touchHasMoved = true } }, _onTouchEnd: function (y) { this._preventDefault(y); if (!p) { return } if (!this.touchHasMoved) { if (y.type === "touchend") { this.toggle(); return } else { var x = y || d.event; if (!(x.which === 3 || x.button === 2)) { this.toggle() } } } }, _onKeyUp: function (y) { var x = y || d.event; if (x.keyCode === 13) { this.toggle() } }, _transitions: function () { if (n.animate) { var x = j.style, y = "max-height " + n.transition + "ms"; x.WebkitTransition = x.MozTransition = x.OTransition = x.transition = y } }, _calcHeight: function () { var z = 0; for (var y = 0; y < j.inner.length; y++) { z += j.inner[y].offsetHeight } var x = "." + n.jsClass + " ." + n.navClass + "-" + this.index + ".opened{max-height:" + z + "px !important} ." + n.jsClass + " ." + n.navClass + "-" + this.index + ".opened.dropdown-active {max-height:9999px !important}"; if (w.styleSheet) { w.styleSheet.cssText = x } else { w.innerHTML = x } x = "" } }; return new m(e, g) }; if (typeof module !== "undefined" && module.exports) { module.exports = c } else { d.responsiveNav = c } }(document, window, 0)); !function (h, j, e) { var a = "placeholder" in j.createElement("input"); var f = "placeholder" in j.createElement("textarea"); var k = e.fn; var d = e.valHooks; var b = e.propHooks; var m; var l; if (a && f) { l = k.placeholder = function () { return this }; l.input = l.textarea = true } else { l = k.placeholder = function () { var o = this; o.filter((a ? "textarea" : ":input") + "[placeholder]").not(".placeholder").on({ "focus.placeholder": c, "blur.placeholder": g }).data("placeholder-enabled", true).trigger("blur.placeholder"); return o }; l.input = a; l.textarea = f; m = { "get": function (p) { var o = e(p); var q = o.data("placeholder-password"); if (q) { return q[0].value } return o.data("placeholder-enabled") && o.hasClass("placeholder") ? "" : p.value }, "set": function (p, r) { var o = e(p); var q = o.data("placeholder-password"); if (q) { return q[0].value = r } if (!o.data("placeholder-enabled")) { return p.value = r } if (r == "") { p.value = r; if (p != n()) { g.call(p) } } else { if (o.hasClass("placeholder")) { c.call(p, true, r) || (p.value = r) } else { p.value = r } } return o } }; if (!a) { d.input = m; b.value = m } if (!f) { d.textarea = m; b.value = m } e(function () { e(j).delegate("form", "submit.placeholder", function () { var o = e(".placeholder", this).each(c); setTimeout(function () { o.each(g) }, 10) }) }); e(h).on("beforeunload.placeholder", function () { e(".placeholder").each(function () { this.value = "" }) }) } function i(p) { var o = {}; var q = /^jQuery\d+$/; e.each(p.attributes, function (s, r) { if (r.specified && !q.test(r.name)) { o[r.name] = r.value } }); return o } function c(p, q) { var o = this; var r = e(o); if (o.value == r.attr("placeholder") && r.hasClass("placeholder")) { if (r.data("placeholder-password")) { r = r.hide().next().show().attr("id", r.removeAttr("id").data("placeholder-id")); if (p === true) { return r[0].value = q } r.focus() } else { o.value = ""; r.removeClass("placeholder"); o == n() && o.select() } } } function g() { var s; var o = this; var r = e(o); var q = this.id; if (o.value == "") { if (o.type == "password") { if (!r.data("placeholder-textinput")) { try { s = r.clone().prop("type", "text") } catch (p) { s = e("<input>").prop(e.extend(i(this), { "type": "text" })) } s.removeAttr("name").data({ "placeholder-password": r, "placeholder-id": q }).on("focus.placeholder", c); r.data({ "placeholder-textinput": s, "placeholder-id": q }).before(s) } r = r.removeAttr("id").hide().prev().attr("id", q).show() } r.addClass("placeholder"); r[0].value = r.attr("placeholder") } else { r.removeClass("placeholder") } } function n() { try { return j.activeElement } catch (o) { } } }(this, document, jQuery); !function (c) { var b = "emailsuggest", d = { sugClass: "emailSug", domains: ["163.com", "126.com", "sohu.com", "139.com", "sina.com", "qq.com", "gmail.com"] }; function a(f, e) { this.$field = c(f); this.options = c.extend(true, {}, d, e); this._defaults = d; this._domains = this.options.domains; this.selectedIndex = 0; this.init() } a.prototype = { init: function () { this.addEvent() }, addEvent: function () { var e = this, f; this.$field.on("keyup.ema", function (g) { f = c.trim(this.value); if (f) { e.create(this, f); e.doSelect(g.keyCode) } else { e.hide() } }).on("blur.ema", function () { setTimeout(function () { e.hide() }, 200) }) }, create: function (i, q) { var l = this, k, m, o, n = [], j, h, p, g, r, f, e = 2; i = c(i); j = i.offset(); g = i.outerWidth(true) - e; r = i.outerHeight(true); h = j.left; p = j.top + r; f = "left: " + h + "px; top: " + p + "px; width: " + g + "px; border: 1px solid #e2e2e2; border-top: 0; display: none"; o = c('<div class="' + this.options.sugClass + '-wrapper" style="' + f + '" />'); n.push('<ul class="' + this.options.sugClass + '-list">'); k = this.filter(q, this._domains); m = k.length; c.each(k, function (t, u) { var s = l.options.sugClass + "-item"; if (l.selectedIndex > m - 1) { if (t === 0) { s += " active"; l.selectedIndex = 0 } } else { if (t === l.selectedIndex) { s += " active" } } n.push('<li class="' + s + '" data-index="' + t + '">' + q.replace(/@.*/, "") + "@" + u + "</li>") }); n.push("</ul>"); n = n.join(""); if (this.$suggest) { this.$suggest.empty(); this.$suggest.append(n) } else { o.append(n); c("body").append(o); this.$suggest = o; this.$suggest.on("mouseenter click", "." + this.options.sugClass + "-item", function (u) { var t, s; s = c(this); t = s.parent().children(); if (u.type === "mouseenter") { s.addClass("active").siblings().removeClass("active"); l.selectedIndex = c.inArray(this, t) } else { l.$field.val(t.eq(l.selectedIndex).text()); l.hide() } }) } this.show() }, doSelect: function (h) { var f = c("." + this.options.sugClass + "-item", this.$suggest), g = 0, e = f.length - 1; switch (h) { case 13: c("li.active", this.$suggest).trigger("click"); this.selectedIndex = 0; break; case 38: this.selectedIndex--; if (this.selectedIndex < g) { this.selectedIndex = e } f.removeClass("active").eq(this.selectedIndex).addClass("active"); break; case 40: this.selectedIndex++; if (this.selectedIndex > e) { this.selectedIndex = g } f.removeClass("active").eq(this.selectedIndex).addClass("active"); break; default: break } }, filter: function (g, e) { var i, h, f = []; i = g.indexOf("@"); if (i > -1) { h = g.substring(i + 1); c.each(e, function (j, k) { if (k.indexOf(h) > -1) { f.push(k) } }) } else { f = e } return f }, show: function () { if (this.$suggest) { this.$suggest.show() } }, hide: function () { if (this.$suggest) { this.$suggest.hide() } } }; c.fn[b] = function (e) { return this.each(function () { if (!c.data(this, b)) { c.data(this, b, new a(this, e)) } }) } }(window.jQuery); !function (a) { a.fn.Huispinner = function (c, e) { var d = { value: 1, minValue: 1, maxValue: 999, dis: 1, }; var c = a.extend(d, c); var b = { up: 38, down: 40 }; this.each(function () { var g = a(this); var k = '<div class="spinner">' + '<a class="subtract" href="javascript:void(0)"><i>-</i></a>' + '<input class="amount input-text" value="' + c.value + '" autocomplete="off">' + '<a class="add" href="javascript:void(0)"><i>+</i></a>' + "</div>"; g.append(k); var f = g.find(".input-text"), j = g.find(".subtract"), i = g.find(".add"), h = parseInt(f.val()); if (h <= c.minValue) { j.addClass("disabled"); i.removeClass("disabled") } if (h >= c.maxValue) { j.removeClass("disabled"); i.addClass("disabled") } f.on("blur", function () { var l = a(this).val(); l = l.replace(/[^\d]/g, ""); l = l.replace(/\b(0+)/gi, ""); if (l != "") { if (l > c.minValue && l < c.maxValue) { f.val(l); i.removeClass("disabled"); j.removeClass("disabled") } else { if (l <= c.minValue) { f.val(c.minValue); j.addClass("disabled"); i.removeClass("disabled") } else { f.val(c.maxValue); j.removeClass("disabled"); i.addClass("disabled") } } } else { a(this).val(c.minValue); j.addClass("disabled"); i.removeClass("disabled") } if (e) { e(f.val()) } }); f.on("keydown", function (m) { var l = m || window.event; if (l.keyCode === b.up) { j.trigger("click"); l.returnValue = false } if (l.keyCode === b.down) { i.trigger("click"); l.returnValue = false } }); j.on("click", function () { var l = parseInt(f.val()); f.val(l <= c.minValue ? c.minValue : l - c.dis); i.removeClass("disabled"); if (f.val() <= c.minValue) { f.val(c.minValue); j.addClass("disabled") } if (e) { e(f.val()) } }); i.on("click", function () { var l = parseInt(f.val()); f.val(l >= c.maxValue ? c.maxValue : l + c.dis); j.removeClass("disabled"); if (f.val() >= c.maxValue) { f.val(c.maxValue); i.addClass("disabled") } if (e) { e(f.val()) } }) }) } }(window.jQuery); !function (a) { a.Huiloading = { show: function (d) { if (a(".loading-wraper").length > 0) { a(".loading-wraper").remove() } if (d == null) { d = "" } var c = '<div class="loading-wraper"><div class="loading-content"><i class="iconpic iconpic-loading"></i> <span>' + d + "</span></div></div>"; a(document.body).append(c); var b = a(".loading-wraper .loading-content").width() + 40; a(".loading-wraper .loading-content").css({ "margin-left": -(b / 2) + "px", }) }, hide: function () { a(".loading-wraper").remove() } } }(window.jQuery); !function (a) { a.extend({ format: function (j, b, k) { j = j.toString(); var h = j.length; if (h > b) { var d = h % b, c = parseInt(h / b), g = [], f = j.substr(0, d); if (f != "") { g.push(f) } for (var e = 0; e < c; e++) { g.push(j.substr(d + e * b, b)) } j = g.join(k) } return j } }) }(window.jQuery); !function (a) { a.fn.togglePassword = function (c) { var d = a.extend(a.fn.togglePassword.defaults, c), b = a(this); a(d.el).on(d.ev, function () { "password" == a(b).attr("type") ? a(b).attr("type", "text") : a(b).attr("type", "password") }) }; a.fn.togglePassword.defaults = { ev: "click" } }(window.jQuery); !(function (c) { var g = "iCheck", e = g + "-helper", q = "checkbox", a = "radio", s = "checked", x = "un" + s, i = "disabled", h = "determinate", b = "in" + h, r = "update", t = "type", d = "click", w = "touchbegin.i touchend.i", p = "addClass", f = "removeClass", l = "trigger", z = "label", o = "cursor", n = /ipad|iphone|ipod|android|blackberry|windows phone|opera mini|silk/i.test(navigator.userAgent); c.fn[g] = function (N, E) { var J = 'input[type="' + q + '"], input[type="' + a + '"]', L = c(), B = function (O) { O.each(function () { var P = c(this); if (P.is(J)) { L = L.add(P) } else { L = L.add(P.find(J)) } }) }; if (/^(check|uncheck|toggle|indeterminate|determinate|disable|enable|update|destroy)$/i.test(N)) { N = N.toLowerCase(); B(this); return L.each(function () { var O = c(this); if (N == "destroy") { u(O, "ifDestroyed") } else { v(O, true, N) } if (c.isFunction(E)) { E() } }) } else { if (typeof N == "object" || !N) { var F = c.extend({ checkedClass: s, disabledClass: i, indeterminateClass: b, labelHover: true }, N), G = F.handle, I = F.hoverClass || "hover", M = F.focusClass || "focus", K = F.activeClass || "active", C = !!F.labelHover, H = F.labelHoverClass || "hover", D = ("" + F.increaseArea).replace("%", "") | 0; if (G == q || G == a) { J = 'input[type="' + G + '"]' } if (D < -50) { D = -50 } B(this); return L.each(function () { var Z = c(this); u(Z); var R = this, O = R.id, S = -D + "%", aa = 100 + (D * 2) + "%", T = { position: "absolute", top: S, left: S, display: "block", width: aa, height: aa, margin: 0, padding: 0, background: "#fff", border: 0, opacity: 0 }, U = n ? { position: "absolute", visibility: "hidden" } : D ? T : { position: "absolute", opacity: 0 }, V = R[t] == q ? F.checkboxClass || "i" + q : F.radioClass || "i" + a, X = c(z + '[for="' + O + '"]').add(Z.closest(z)), W = !!F.aria, Q = g + "-" + Math.random().toString(36).substr(2, 6), Y = '<div class="' + V + '" ' + (W ? 'role="' + R[t] + '" ' : ""), P; if (W) { X.each(function () { Y += 'aria-labelledby="'; if (this.id) { Y += this.id } else { this.id = Q; Y += Q } Y += '"' }) } Y = Z.wrap(Y + "/>")[l]("ifCreated").parent().append(F.insert); P = c('<ins class="' + e + '"/>').css(T).appendTo(Y); Z.data(g, { o: F, s: Z.attr("style") }).css(U); !!F.inheritClass && Y[p](R.className || ""); !!F.inheritID && O && Y.attr("id", g + "-" + O); Y.css("position") == "static" && Y.css("position", "relative"); v(Z, true, r); if (X.length) { X.on(d + ".i mouseover.i mouseout.i " + w, function (ad) { var ab = ad[t], ac = c(this); if (!R[i]) { if (ab == d) { if (c(ad.target).is("a")) { return } v(Z, false, true) } else { if (C) { if (/ut|nd/.test(ab)) { Y[f](I); ac[f](H) } else { Y[p](I); ac[p](H) } } } if (n) { ad.stopPropagation() } else { return false } } }) } Z.on(d + ".i focus.i blur.i keyup.i keydown.i keypress.i", function (ad) { var ac = ad[t], ab = ad.keyCode; if (ac == d) { return false } else { if (ac == "keydown" && ab == 32) { if (!(R[t] == a && R[s])) { if (R[s]) { y(Z, s) } else { k(Z, s) } } return false } else { if (ac == "keyup" && R[t] == a) { !R[s] && k(Z, s) } else { if (/us|ur/.test(ac)) { Y[ac == "blur" ? f : p](M) } } } } }); P.on(d + " mousedown mouseup mouseover mouseout " + w, function (ad) { var ac = ad[t], ab = /wn|up/.test(ac) ? K : I; if (!R[i]) { if (ac == d) { v(Z, false, true) } else { if (/wn|er|in/.test(ac)) { Y[p](ab) } else { Y[f](ab + " " + K) } if (X.length && C && ab == I) { X[/ut|nd/.test(ac) ? f : p](H) } } if (n) { ad.stopPropagation() } else { return false } } }) }) } else { return this } } }; function v(B, H, G) { var C = B[0], E = /er/.test(G) ? b : /bl/.test(G) ? i : s, F = G == r ? { checked: C[s], disabled: C[i], indeterminate: B.attr(b) == "true" || B.attr(h) == "false" } : C[E]; if (/^(ch|di|in)/.test(G) && !F) { k(B, E) } else { if (/^(un|en|de)/.test(G) && F) { y(B, E) } else { if (G == r) { for (var D in F) { if (F[D]) { k(B, D, true) } else { y(B, D, true) } } } else { if (!H || G == "toggle") { if (!H) { B[l]("ifClicked") } if (F) { if (C[t] !== a) { y(B, E) } } else { k(B, E) } } } } } } function k(K, D, B) { var G = K[0], M = K.parent(), L = D == s, C = D == b, H = D == i, N = C ? h : L ? x : "enabled", F = m(K, N + j(G[t])), J = m(K, D + j(G[t])); if (G[D] !== true) { if (!B && D == s && G[t] == a && G.name) { var E = K.closest("form"), I = 'input[name="' + G.name + '"]'; I = E.length ? E.find(I) : c(I); I.each(function () { if (this !== G && c(this).data(g)) { y(c(this), D) } }) } if (C) { G[D] = true; if (G[s]) { y(K, s, "force") } } else { if (!B) { G[D] = true } if (L && G[b]) { y(K, b, false) } } A(K, L, D, B) } if (G[i] && !!m(K, o, true)) { M.find("." + e).css(o, "default") } M[p](J || m(K, D) || ""); if (!!M.attr("role") && !C) { M.attr("aria-" + (H ? i : s), "true") } M[f](F || m(K, N) || "") } function y(I, D, B) { var F = I[0], K = I.parent(), J = D == s, C = D == b, G = D == i, L = C ? h : J ? x : "enabled", E = m(I, L + j(F[t])), H = m(I, D + j(F[t])); if (F[D] !== false) { if (C || !B || B == "force") { F[D] = false } A(I, J, L, B) } if (!F[i] && !!m(I, o, true)) { K.find("." + e).css(o, "pointer") } K[f](H || m(I, D) || ""); if (!!K.attr("role") && !C) { K.attr("aria-" + (G ? i : s), "false") } K[p](E || m(I, L) || "") } function u(B, C) { if (B.data(g)) { B.parent().html(B.attr("style", B.data(g).s || "")); if (C) { B[l](C) } B.off(".i").unwrap(); c(z + '[for="' + B[0].id + '"]').add(B.closest(z)).off(".i") } } function m(B, D, C) { if (B.data(g)) { return B.data(g).o[D + (C ? "" : "Class")] } } function j(B) { return B.charAt(0).toUpperCase() + B.slice(1) } function A(C, D, E, B) { if (!B) { if (D) { C[l]("ifToggled") } C[l]("ifChanged")[l]("if" + j(E)) } } })(window.jQuery || window.Zepto); !(function (b) { var a = { init: function (c) { return this.each(function () { var d = this, h = b(d).empty(); d.opt = b.extend(true, {}, b.fn.raty.defaults, c); h.data("settings", d.opt); d.opt.number = a.between(d.opt.number, 0, 20); if (d.opt.path.substring(d.opt.path.length - 1, d.opt.path.length) != "/") { d.opt.path += "/" } if (typeof d.opt.score == "function") { d.opt.score = d.opt.score.call(d) } if (d.opt.score) { d.opt.score = a.between(d.opt.score, 0, d.opt.number) } for (var e = 1; e <= d.opt.number; e++) { b("<img />", { src: d.opt.path + ((!d.opt.score || d.opt.score < e) ? d.opt.starOff : d.opt.starOn), alt: e, title: (e <= d.opt.hints.length && d.opt.hints[e - 1] !== null) ? d.opt.hints[e - 1] : e }).appendTo(d); if (d.opt.space) { h.append((e < d.opt.number) ? "" : "") } } d.stars = h.children('img:not(".raty-cancel")'); d.score = b("<input />", { type: "hidden", name: d.opt.scoreName }).appendTo(d); if (d.opt.score && d.opt.score > 0) { d.score.val(d.opt.score); a.roundStar.call(d, d.opt.score) } if (d.opt.iconRange) { a.fill.call(d, d.opt.score) } a.setTarget.call(d, d.opt.score, d.opt.targetKeep); var g = d.opt.space ? 4 : 0, f = d.opt.width || (d.opt.number * d.opt.size + d.opt.number * g); if (d.opt.cancel) { d.cancel = b("<img />", { src: d.opt.path + d.opt.cancelOff, alt: "x", title: d.opt.cancelHint, "class": "raty-cancel" }); if (d.opt.cancelPlace == "left") { } else { } f += (d.opt.size + g) } if (d.opt.readOnly) { a.fixHint.call(d); if (d.cancel) { d.cancel.hide() } } else { h.css("cursor", "pointer"); a.bindAction.call(d) } }) }, between: function (e, d, c) { return Math.min(Math.max(parseFloat(e), d), c) }, bindAction: function () { var c = this, e = b(c); e.mouseleave(function () { var f = c.score.val() || undefined; a.initialize.call(c, f); a.setTarget.call(c, f, c.opt.targetKeep); if (c.opt.mouseover) { c.opt.mouseover.call(c, f) } }); var d = c.opt.half ? "mousemove" : "mouseover"; if (c.opt.cancel) { c.cancel.mouseenter(function () { b(this).attr("src", c.opt.path + c.opt.cancelOn); c.stars.attr("src", c.opt.path + c.opt.starOff); a.setTarget.call(c, null, true); if (c.opt.mouseover) { c.opt.mouseover.call(c, null) } }).mouseleave(function () { b(this).attr("src", c.opt.path + c.opt.cancelOff); if (c.opt.mouseover) { c.opt.mouseover.call(c, c.score.val() || null) } }).click(function (f) { c.score.removeAttr("value"); if (c.opt.click) { c.opt.click.call(c, null, f) } }) } c.stars.bind(d, function (g) { var h = parseInt(this.alt, 10); if (c.opt.half) { var f = parseFloat((g.pageX - b(this).offset().left) / c.opt.size), i = (f > 0.5) ? 1 : 0.5; h = parseFloat(this.alt) - 1 + i; a.fill.call(c, h); if (c.opt.precision) { h = h - i + f } a.showHalf.call(c, h) } else { a.fill.call(c, h) } e.data("score", h); a.setTarget.call(c, h, true); if (c.opt.mouseover) { c.opt.mouseover.call(c, h, g) } }).click(function (f) { c.score.val((c.opt.half || c.opt.precision) ? e.data("score") : this.alt); if (c.opt.click) { c.opt.click.call(c, c.score.val(), f) } }) }, cancel: function (c) { return b(this).each(function () { var d = this, e = b(d); if (e.data("readonly") === true) { return this } if (c) { a.click.call(d, null) } else { a.score.call(d, null) } d.score.removeAttr("value") }) }, click: function (c) { return b(this).each(function () { if (b(this).data("readonly") === true) { return this } a.initialize.call(this, c); if (this.opt.click) { this.opt.click.call(this, c) } else { a.error.call(this, 'you must add the "click: function(score, evt) { }" callback.') } a.setTarget.call(this, c, true) }) }, error: function (c) { b(this).html(c); b.error(c) }, fill: function (k) { var c = this, h = c.stars.length, g = 0, d, j, f; for (var e = 1; e <= h; e++) { d = c.stars.eq(e - 1); if (c.opt.iconRange && c.opt.iconRange.length > g) { j = c.opt.iconRange[g]; if (c.opt.single) { f = (e == k) ? (j.on || c.opt.starOn) : (j.off || c.opt.starOff) } else { f = (e <= k) ? (j.on || c.opt.starOn) : (j.off || c.opt.starOff) } if (e <= j.range) { d.attr("src", c.opt.path + f) } if (e == j.range) { g++ } } else { if (c.opt.single) { f = (e == k) ? c.opt.starOn : c.opt.starOff } else { f = (e <= k) ? c.opt.starOn : c.opt.starOff } d.attr("src", c.opt.path + f) } } }, fixHint: function () { var c = b(this), e = parseInt(this.score.val(), 10), d = this.opt.noRatedMsg; if (!isNaN(e) && e > 0) { d = (e <= this.opt.hints.length && this.opt.hints[e - 1] !== null) ? this.opt.hints[e - 1] : e } c.data("readonly", true).css("cursor", "default").attr("title", d); this.score.attr("readonly", "readonly"); this.stars.attr("title", d) }, getScore: function () { var d = [], c; b(this).each(function () { c = this.score.val(); d.push(c ? parseFloat(c) : undefined) }); return (d.length > 1) ? d : d[0] }, readOnly: function (c) { return this.each(function () { var d = b(this); if (d.data("readonly") === c) { return this } if (this.cancel) { if (c) { this.cancel.hide() } else { this.cancel.show() } } if (c) { d.unbind(); d.children("img").unbind(); a.fixHint.call(this) } else { a.bindAction.call(this); a.unfixHint.call(this) } d.data("readonly", c) }) }, reload: function () { return a.set.call(this, {}) }, roundStar: function (e) { var d = (e - Math.floor(e)).toFixed(2); if (d > this.opt.round.down) { var c = this.opt.starOn; if (d < this.opt.round.up && this.opt.halfShow) { c = this.opt.starHalf } else { if (d < this.opt.round.full) { c = this.opt.starOff } } this.stars.eq(Math.ceil(e) - 1).attr("src", this.opt.path + c) } }, score: function () { return arguments.length ? a.setScore.apply(this, arguments) : a.getScore.call(this) }, set: function (c) { this.each(function () { var d = b(this), f = d.data("settings"), e = d.clone().removeAttr("style").insertBefore(d); d.remove(); e.raty(b.extend(f, c)) }); return b(this.selector) }, setScore: function (c) { return b(this).each(function () { if (b(this).data("readonly") === true) { return this } a.initialize.call(this, c); a.setTarget.call(this, c, true) }) }, setTarget: function (e, d) { if (this.opt.target) { var c = b(this.opt.target); if (c.length == 0) { a.error.call(this, "目标选择器无效或丢失!") } var f = e; if (!d || f === undefined) { f = this.opt.targetText } else { if (this.opt.targetType == "hint") { f = (f === null && this.opt.cancel) ? this.opt.cancelHint : this.opt.hints[Math.ceil(f - 1)] } else { f = this.opt.precision ? parseFloat(f).toFixed(1) : parseInt(f, 10) } } if (this.opt.targetFormat.indexOf("{score}") < 0) { a.error.call(this, '模版 "{score}" 找不到!') } if (e !== null) { f = this.opt.targetFormat.toString().replace("{score}", f) } if (c.is(":input")) { c.val(f) } else { c.html(f) } } }, showHalf: function (d) { var c = (d - Math.floor(d)).toFixed(1); if (c > 0 && c < 0.6) { this.stars.eq(Math.ceil(d) - 1).attr("src", this.opt.path + this.opt.starHalf) } }, initialize: function (c) { c = !c ? 0 : a.between(c, 0, this.opt.number); a.fill.call(this, c); if (c > 0) { if (this.opt.halfShow) { a.roundStar.call(this, c) } this.score.val(c) } }, unfixHint: function () { for (var c = 0; c < this.opt.number; c++) { this.stars.eq(c).attr("title", (c < this.opt.hints.length && this.opt.hints[c] !== null) ? this.opt.hints[c] : c) } b(this).data("readonly", false).css("cursor", "pointer").removeAttr("title"); this.score.attr("readonly", "readonly") } }; b.fn.raty = function (c) { if (a[c]) { return a[c].apply(this, Array.prototype.slice.call(arguments, 1)) } else { if (typeof c === "object" || !c) { return a.init.apply(this, arguments) } else { b.error("方法 " + c + " 不存在!") } } }; b.fn.raty.defaults = { cancel: false, cancelHint: "取消评级!", cancelOff: "cancel-off.png", cancelOn: "cancel-on.png", cancelPlace: "left", click: undefined, half: false, halfShow: true, hints: ["10", "20", "30", "40", "50", "60", "70", "80", "90", "100"], iconRange: undefined, mouseover: undefined, noRatedMsg: "没有额定", number: 10, path: "images/", precision: false, round: { down: 0.25, full: 0.6, up: 0.76 }, readOnly: false, score: undefined, scoreName: "score", single: false, size: 16, space: true, starHalf: "star-half.png", starOff: "star-off.png", starOn: "star-on.png", target: undefined, targetFormat: "{score}", targetKeep: false, targetText: "", targetType: "hint", width: undefined } })(jQuery); !(function (a) { a.fn.onePageNav = function (b) { var c = a.extend({}, a.fn.onePageNav.defaults, b), d = {}; d.sections = {}; d.bindNav = function (f, h, i) { var g = f.parent(), e = f.attr("href"), j = a(window); if (!g.hasClass(i.currentClass)) { if (i.begin) { i.begin() } d.adjustNav(h, g, i.currentClass); j.unbind(".onePageNav"); a.scrollTo(e, i.scrollSpeed, { easing: i.easing, offset: { top: -i.scrollOffset }, onAfter: function () { if (i.changeHash) { window.location.hash = e } j.bind("scroll.onePageNav", function () { d.scrollChange(h, i) }); if (i.end) { i.end() } } }) } }; d.adjustNav = function (g, e, f) { g.find("." + f).removeClass(f); e.addClass(f) }; d.getPositions = function (f, g) { var e = f.find("a"); if (g.filter !== "") { e = e.filter(g.filter) } e.each(function () { var j = a(this).attr("href"), i = a(j).offset(), h = i.top; d.sections[j.substr(1)] = Math.round(h) - g.scrollOffset }) }; d.getSection = function (i, g) { var e = "", h = Math.round(a(window).height() * g.scrollThreshold); for (var f in d.sections) { if ((d.sections[f] - h) < i) { e = f } } return e }; d.scrollChange = function (g, h) { d.getPositions(g, h); var f = a(window).scrollTop(), e = d.getSection(f, h); if (e !== "") { d.adjustNav(g, g.find("a[href=#" + e + "]").parent(), h.currentClass) } }; d.init = function (g, h) { var f = false, e = g.find("a"); if (h.filter !== "") { e = e.filter(h.filter) } e.bind("click", function (i) { d.bindNav(a(this), g, h); i.preventDefault() }); d.getPositions(g, h); a(window).bind("scroll.onePageNav", function () { f = true }); setInterval(function () { if (f) { f = false; d.scrollChange(g, h) } }, 250) }; return this.each(function () { var e = a(this), f = a.meta ? a.extend({}, c, e.data()) : c; d.init(e, f) }) }; a.fn.onePageNav.defaults = { currentClass: "current", changeHash: false, easing: "swing", filter: "", scrollSpeed: 750, scrollOffset: 0, scrollThreshold: 0.5, begin: false, end: false } })(jQuery); (function (e) { var b = "Hui.colorPicker"; var d = '<div class="colorpicker"><button type="button" class="btn dropdown-toggle" data-toggle="dropdown"><span class="cp-title"></span><i class="ic"></i></button><ul class="dropdown-menu clearfix"></ul></div>'; var a = { zh_cn: { errorTip: "不是有效的颜色值" }, zh_tw: { errorTip: "不是有效的顏色值" }, en: { errorTip: "Not a valid color value" } }; var c = function (g, f) { this.name = b; this.$ = e(g); this.getOptions(f); this.init() }; c.DEFAULTS = { colors: ["#00BCD4", "#388E3C", "#3280fc", "#3F51B5", "#9C27B0", "#795548", "#F57C00", "#F44336", "#E91E63"], pullMenuRight: true, wrapper: "btn-wrapper", tileSize: 30, lineCount: 5, optional: true, tooltip: "top", icon: "caret-down", }; c.prototype.init = function () { var f = this.options, h = this; this.$picker = e(d).addClass(f.wrapper); this.$picker.find(".cp-title").toggle(f.title !== undefined).text(f.title); this.$menu = this.$picker.find(".dropdown-menu").toggleClass("pull-right", f.pullMenuRight); this.$btn = this.$picker.find(".btn.dropdown-toggle"); this.$btn.find(".ic").addClass("icon-" + f.icon); if (f.btnTip) { this.$picker.attr("data-toggle", "tooltip").tooltip({ title: f.btnTip, placement: f.tooltip, container: "body" }) } this.$.attr("data-provide", null).after(this.$picker); this.colors = {}; e.each(this.options.colors, function (j, l) { if (e.zui.Color.isColor(l)) { var k = new e.zui.Color(l); h.colors[k.toCssStr()] = k } }); this.updateColors(); var h = this; this.$picker.on("click", ".cp-tile", function () { h.setValue(e(this).data("color")) }); var i = this.$; var g = function () { var k = i.val(); var j = e.zui.Color.isColor(k); i.parent().toggleClass("has-error", !j && !(f.optional && k === "")); if (j) { h.setValue(k, true) } else { if (f.optional && k === "") { i.tooltip("hide") } else { if (!i.is(":focus")) { i.tooltip("show", f.errorTip) } } } }; if (i.is("input:not([type=hidden])")) { if (f.tooltip) { i.attr("data-toggle", "tooltip").tooltip({ trigger: "manual", placement: f.tooltip, tipClass: "tooltip-danger", container: "body" }) } i.on("keyup paste input change", g) } else { i.appendTo(this.$picker) } g() }; c.prototype.addColor = function (f) { var h = f.toCssStr(), g = this.options; if (!this.colors[h]) { this.colors[h] = f } var i = e('<a href="###" class="cp-tile"></a>', { titile: f }).data("color", f).css({ "color": f.contrast().toCssStr(), "background": h, "border-color": f.luma() > 0.43 ? "#ccc" : "transparent" }).attr("data-color", h); this.$menu.append(e("<li/>").css({ width: g.tileSize, height: g.tileSize }).append(i)); if (g.optional) { this.$menu.find(".cp-tile.empty").parent().detach().appendTo(this.$menu) } }; c.prototype.updateColors = function (g) { var k = this.$picker, i = this.$menu.empty(), h = this.options, g = g || this.colors, j = this; var f = 0; e.each(g, function (m, n) { j.addColor(n); f++ }); if (h.optional) { var l = e('<li><a class="cp-tile empty" href="###"></a></li>').css({ width: h.tileSize, height: h.tileSize }); this.$menu.append(l); f++ } i.css("width", Math.min(f, h.lineCount) * h.tileSize + 6) }; c.prototype.setValue = function (f, i) { var g = this.options; this.$menu.find(".cp-tile.active").removeClass("active"); var h = ""; if (f) { var j = new e.zui.Color(f); h = j.toCssStr().toLowerCase(); this.$btn.css({ background: h, color: j.contrast().toCssStr(), borderColor: j.luma() > 0.43 ? "#ccc" : h }); if (!this.colors[h]) { this.addColor(j) } if (!i && this.$.val().toLowerCase() !== h) { this.$.val(h).trigger("change") } this.$menu.find(".cp-tile[data-color=" + h + "]").addClass("active"); this.$.tooltip("hide"); this.$.trigger("colorchange", j) } else { this.$btn.attr("style", null); if (!i && this.$.val() !== "") { this.$.val(h).trigger("change") } if (g.optional) { this.$.tooltip("hide") } this.$menu.find(".cp-tile.empty").addClass("active"); this.$.trigger("colorchange", null) } if (g.updateBorder) { e(g.updateBorder).css("border-color", h) } if (g.updateBackground) { e(g.updateBackground).css("background-color", h) } if (g.updateColor) { e(g.updateText).css("color", h) } if (g.updateText) { e(g.updateText).text(h) } }; c.prototype.getOptions = function (f) { var g = e.extend({}, c.DEFAULTS, this.$.data(), f); if (typeof g.colors === "string") { g.colors = g.colors.split(",") } var h = (g.lang || e.zui.clientLang()).toLowerCase(); if (!g.errorTip) { g.errorTip = a[h].errorTip } if (!e.fn.tooltip) { g.btnTip = false } this.options = g }; e.fn.colorPicker = function (f) { return this.each(function () { var i = e(this); var h = i.data(b); var g = typeof f == "object" && f; if (!h) { i.data(b, (h = new c(this, g))) } if (typeof f == "string") { h[f]() } }) }; e.fn.colorPicker.Constructor = c; e(function () { e('[data-provide="colorpicker"]').colorPicker() }) }(jQuery)); function HuiaddFavorite(b) { b.site = b.site || window.location.href; b.name = b.name || document.title; try { window.external.addFavorite(b.site, b.name) } catch (a) { try { window.sidebar.addPanel(name, site, "") } catch (a) { $.Huimodalalert("加入收藏失败，请使用Ctrl+D进行添加", 2000) } } } function Huisethome(c) { try { c.style.behavior = "url(#default#homepage)"; c.setHomePage(webSite) } catch (b) { if (window.netscape) { try { netscape.security.PrivilegeManager.enablePrivilege("UniversalXPConnect") } catch (b) { $.Huimodalalert("此操作被浏览器拒绝！\n请在浏览器地址栏输入\"about:config\"并回车\n然后将 [signed.applets.codebase_principal_support]的值设置为'true',双击即可。", 2000) } var a = Components.classes["@mozilla.org/preferences-service;1"].getService(Components.interfaces.nsIPrefBranch); a.setCharPref("browser.startup.homepage", url) } } } function displaynavbar(a) { if ($(a).hasClass("open")) { $(a).removeClass("open"); $("body").removeClass("big-page") } else { $(a).addClass("open"); $("body").addClass("big-page") } } !function (a) { a.fn.Huihover = function (b) { var c = { className: "hover", }; var b = a.extend(c, b); this.each(function () { var d = a(this); d.hover(function () { d.addClass(b.className) }, function () { d.removeClass(b.className) }) }) } }(window.jQuery); !function (a) { a.fn.Huifocusblur = function (b) { var c = { className: "focus", }; var b = a.extend(c, b); this.each(function () { var d = a(this); d.focus(function () { d.addClass(b.className).removeClass("inputError") }); d.blur(function () { d.removeClass(b.className) }) }) } }(window.jQuery); !function (a) { a.Huiselect = function (c, d) { var b = a(d); a(c + " cite").click(function () { var e = a(c + " ul"); e.slideToggle() }); a(c + " ul li a").click(function () { var e = a(this).text(); a(c + " cite").html(e); var f = a(this).attr("selectid"); b.val(f); a(c + " ul").hide() }); a(document).click(function () { a(c + " ul").hide() }) } }(window.jQuery); !function (a) { a.fn.Huitab = function (b, d) { var c = { tabBar: ".tabBar span", tabCon: ".tabCon", className: "current", tabEvent: "click", index: 0, }; var b = a.extend(c, b); this.each(function () { var e = a(this); e.find(b.tabBar).removeClass(b.className); e.find(b.tabBar).eq(b.index).addClass(b.className); e.find(b.tabCon).hide(); e.find(b.tabCon).eq(b.index).show(); e.find(b.tabBar).on(b.tabEvent, function () { e.find(b.tabBar).removeClass(b.className); a(this).addClass(b.className); var f = e.find(b.tabBar).index(this); e.find(b.tabCon).hide(); e.find(b.tabCon).eq(f).show(); if (d) { d() } }) }) } }(window.jQuery); !function (a) { a.fn.Huifold = function (b) { var c = { titCell: ".item .Huifold-header", mainCell: ".item .Huifold-body", type: 1, trigger: "click", className: "selected", speed: "first", }; var b = a.extend(c, b); this.each(function () { var d = a(this); d.find(b.titCell).on(b.trigger, function () { if (a(this).next().is(":visible")) { if (b.type == 2) { return false } else { a(this).next().slideUp(b.speed).end().removeClass(b.className); if (a(this).find("b")) { a(this).find("b").html("+") } } } else { if (b.type == 3) { a(this).next().slideDown(b.speed).end().addClass(b.className); if (a(this).find("b")) { a(this).find("b").html("-") } } else { d.find(b.mainCell).slideUp(b.speed); d.find(b.titCell).removeClass(b.className); if (d.find(b.titCell).find("b")) { d.find(b.titCell).find("b").html("+") } a(this).next().slideDown(b.speed).end().addClass(b.className); if (a(this).find("b")) { a(this).find("b").html("-") } } } }) }) } }(window.jQuery); !function (a) { a.fn.Huitags = function (c) { var d = { value: "Hui前端框架,H-ui,辉哥", maxlength: 20, number: 5, tagsDefault: ["Html", "CSS", "JS"], }; var c = a.extend(d, c); var b = { Enter: 13, Enter2: 108, Spacebar: 32 }; this.each(function () { var j = a(this); var n = '<div class="Huitags-wraper">' + '<div class="Huitags-editor cl"></div>' + '<div class="Huitags-input-wraper">' + '<input type="text" class="input-text Huitags-input" maxlength="' + c.maxlength + '" value="">' + "</div>" + '<div class="Huitags-list">' + '<div class="Huitags-notag" style="display:none">暂无常用标签</div>' + '<div class="Huitags-has"></div>' + "</div>" + '<input type="hidden" class="Huitags-val" name="" value="' + c.value + '">' + "</div>"; j.append(n); var h = j.find(".Huitags-wraper"); var k = j.find(".Huitags-editor"); var p = j.find(".Huitags-input"); var l = j.find(".Huitags-list"); var q = j.find(".Huitags-has"); var e = j.find(".Huitags-val"); if (c.tagsDefault) { var m = (c.tagsDefault).length; for (var g = 0; g < m; g++) { q.append("<span>" + c.tagsDefault[g] + "</span>") } q.find("span").on("click", function () { var i = a(this).text(); i = i.replace(/(^\s*)|(\s*$)/g, ""); k.append('<span class="Huitags-token">' + i + "</span>"); o(this); a(this).remove() }) } function o(t) { var u = ""; var s = j.find(".Huitags-token"); if (s.length < 1) { p.val(""); return false } for (var r = 0; r < s.length; r++) { u += s.eq(r).text() + "," } u = f(u, 1); u = u.join(); e.val(u) } function f(t, s) { t = t.replace(/(^\s*)|(\s*$)/g, ""); if (s == 1) { t = t.replace(/(\s)|(，)/g, ",") } else { t = t.replace(/(，)/g, ",") } t = t.replace(/^,|,$/g, ""); t = t.replace(/,+/g, ","); t = t.split(","); var u = [t[0]]; for (var r = 1; r < t.length; r++) { if (t.indexOf(t[r]) == r) { if (t[r] == "") { continue } u.push(t[r]) } } return u } p.on("keydown", function (t) { var i = t || window.event; if (i.keyCode == b.Enter || i.keyCode == b.Enter2 || i.keyCode == b.Spacebar) { var r = p.val().replace(/\s+/g, ""); var s = /^,|,$/gi; r = r.replace(s, ""); r = a.trim(r); if (r != "") { p.change() } else { return false } } }); p.on("change", function () { var w = p.val(); var u = e.val(); var r = u + "," + w; if (r != "") { var t = '<i class="Huitags-icon Hui-iconfont">&#xe64b;</i>'; var i = f(r, 1); if (i.length > 0) { for (var s = 0; s < i.length; s++) { t += '<span class="Huitags-token">' + i[s] + "</span>" } e.val(i); k.html(t); p.val("").blur() } } }); a(document).on("click", ".Huitags-token", function () { a(this).remove(); var s = ""; if (j.find(".Huitags-token").length < 1) { e.val(""); return false } else { for (var r = 0; r < j.find(".Huitags-token").length; r++) { s += j.find(".Huitags-token").eq(r).text() + "," } s = s.substring(0, s.length - 1); e.val(s) } }); p.change() }) } }(window.jQuery); !function (a) { a.Huitagsmixed = function (b) { a(b).each(function () { var c = 9; var e = 0; var d = parseInt(Math.random() * (c - e + 1) + e); a(this).addClass("tags" + d) }) } }(window.jQuery); !function (a) { a.fn.Huitextarealength = function (b) { var c = { minlength: 0, maxlength: 140, errorClass: "error", exceed: true, }; var b = a.extend(c, b); this.each(function () { var f = a(this); var e = f.val(); var d = e.length; var g = '<p class="textarea-numberbar"><em class="textarea-length">' + d + "</em>/" + b.maxlength + "</p>"; f.parent().append(g); f.on("keyup", function () { e = f.val(); d = e.length; if (d > b.maxlength) { if (b.exceed) { f.addClass(b.errorClass) } else { e = e.substring(0, b.maxlength); f.val(e); f.removeClass(b.errorClass) } } else { if (d < b.minlength) { f.addClass(b.errorClass) } else { f.removeClass(b.errorClass) } } f.parent().find(".textarea-length").text(e.length) }) }) } }(window.jQuery); !function (a) { a.fn.Huipreview = function (b) { var c = { className: "active", bigImgWidth: 300, }; var b = a.extend(c, b); this.each(function () { var d = a(this); var e; d.hover(function () { clearTimeout(e); e = setTimeout(function () { a("#preview-wraper").remove(); var f = d.find("img").attr("src"); var n = d.attr("data-src"); var l = d.attr("data-width"); var t = d.attr("data-height"); var q = a(window).width(); var o = q / 2; var r = d.parent().offset().top; var g = d.parent().offset().left; var p = d.parent().width(); var k = d.parent().height(); var m = (g + p / 2); var s = "auto", h = "auto"; if (m < o) { s = (p + g) + "px" } else { h = (q - g) + "px" } d.addClass(b.className); if (n == "") { return false } else { var j = '<div id="preview-wraper" style="position: absolute;z-index:999;width:' + b.bigImgWidth + "px;height:auto;top:" + r + "px;right:" + h + ";left:" + s + '">' + '<img src="' + f + '" width="' + b.bigImgWidth + '">' + "</div>"; a("body").append(j); var i = new Image(); i.src = n; i.onload = function () { a("#preview-wraper").find("img").attr("src", n).css("width", b.bigImgWidth) } } }, 500) }, function () { clearTimeout(e); d.removeClass(b.className); a("#preview-wraper").remove() }) }) } }(window.jQuery); !function (a) { a.Huimodalalert = function (c, b) { if (a(".modal-alert").length > 0) { a(".modal-alert").remove() } if (b == 0 || typeof (b) == "undefined") { a(document.body).append('<div id="modal-alert" class="modal modal-alert radius">' + '<div class="modal-alert-info">' + c + "</div>" + '<div class="modal-footer"> <button class="btn btn-primary radius" onClick="$.Huimodal_alert.hide()">确定</button></div>' + "</div>"); a("#modal-alert").fadeIn() } else { a(document.body).append('<div id="modal-alert" class="modal modal-alert radius">' + '<div class="modal-alert-info">' + c + "</div>" + "</div>"); a("#modal-alert").fadeIn(); setTimeout(a.Huimodalalert.hide, b) } }; a.Huimodalalert.hide = function () { a("#modal-alert").fadeOut("normal", function () { a("#modal-alert").remove() }) } }(window.jQuery); !function (a) { a.Huialert = function () { a(".Huialert i").Huihover(); a(document).on("click", ".Huialert i", function () { var b = a(this).parents(".Huialert"); b.fadeOut("normal", function () { b.remove() }) }) }; a.Huialert() }(window.jQuery); !function (a) { a.Huitotop = function (b) { if (!b) { b = 60 } var d = '<a href="javascript:void(0)" class="tools-right toTop Hui-iconfont" title="返回顶部" alt="返回顶部" style="display:none;bottom:' + b + 'px">&#xe684;</a>'; a(d).appendTo(a("body")).click(function () { a("html, body").animate({ scrollTop: 0 }, 120) }); var c = function () { var e = a(document).scrollTop(); var f = a(window).height(); if (e > 0) { a(".toTop").show() } else { a(".toTop").hide() } if (!window.XMLHttpRequest) { a(".toTop").css("top", e + f - 166) } }; a(window).on("scroll", c) } }(window.jQuery); !function (a) { a.Huimarquee = function (i, e, f) { var b; var j = false; var c = document.getElementById("marquee"); if (document.getElementById("holder").offsetHeight <= i) { return } var h = c.innerHTML.replace("holder", "holder2"); c.innerHTML += h; c.onmouseover = function () { j = true }; c.onmouseout = function () { j = false }; c.scrollTop = 0; var d = function () { b = setInterval(g, e); if (!j) { c.scrollTop += 2 } }; var g = function () { if (c.scrollTop % i != 0) { c.scrollTop += 2; if (c.scrollTop >= c.scrollHeight / 2) { c.scrollTop = 0 } } else { clearInterval(b); setTimeout(d, f) } }; setTimeout(d, f) } }(window.jQuery); $(function () { $(document).on("change", ".input-file", function () { var a = $(this).val(); $(this).parent().find(".upload-url").val(a).focus().blur() }) }); !function (d) { var b = function (f, e) { this.$element = d(f); this.options = d.extend({}, b.DEFAULTS, e); this.isLoading = false }; b.VERSION = "3.3.0"; b.DEFAULTS = { loadingText: "loading..." }; b.prototype.setState = function (g) { var i = "disabled"; var e = this.$element; var h = e.is("input") ? "val" : "html"; var f = e.data(); g = g + "Text"; if (f.resetText == null) { e.data("resetText", e[h]()) } setTimeout(d.proxy(function () { e[h](f[g] == null ? this.options[g] : f[g]); if (g == "loadingText") { this.isLoading = true; e.addClass(i).attr(i, i) } else { if (this.isLoading) { this.isLoading = false; e.removeClass(i).removeAttr(i) } } }, this), 0) }; b.prototype.toggle = function () { var f = true; var e = this.$element.closest('[data-toggle="buttons"]'); if (e.length) { var g = this.$element.find("input"); if (g.prop("type") == "radio") { if (g.prop("checked") && this.$element.hasClass("active")) { f = false } else { e.find(".active").removeClass("active") } } if (f) { g.prop("checked", !this.$element.hasClass("active")).trigger("change") } } else { this.$element.attr("aria-pressed", !this.$element.hasClass("active")) } if (f) { this.$element.toggleClass("active") } }; function c(e) { return this.each(function () { var h = d(this); var g = h.data("bs.button"); var f = typeof e == "object" && e; if (!g) { h.data("bs.button", (g = new b(this, f))) } if (e == "toggle") { g.toggle() } else { if (e) { g.setState(e) } } }) } var a = d.fn.button; d.fn.button = c; d.fn.button.Constructor = b; d.fn.button.noConflict = function () { d.fn.button = a; return this }; d(document).on("click.bs.button.data-api", '[data-toggle^="button"]', function (g) { var f = d(g.target); if (!f.hasClass("btn")) { } f = f.closest(".btn"); c.call(f, "toggle"); g.preventDefault() }).on("focus.bs.button.data-api blur.bs.button.data-api", '[data-toggle^="button"]', function (f) { d(f.target).closest(".btn").toggleClass("focus", f.type == "focus") }) }(jQuery); jQuery(function (a) { a(document).ready(function () { var e = []; var n = []; var h = []; var g = 0; var i = ""; var l = ""; var b = ""; var c = null; var m = 0; var k = 0; var d = 0; var f = 0; var j = 0; a(window).scroll(function (p) { var o = a(this).scrollTop(); if (o > g) { i = "down" } else { i = "up" } g = o }); a.fn.stickUp = function (q) { a(this).addClass("stuckMenu"); var p = 0; if (q != null) { for (var r in q.parts) { if (q.parts.hasOwnProperty(r)) { h[p] = q.parts[p]; p++ } } if (p == 0) { console.log("error:needs arguments") } l = q.itemClass; b = q.itemHover; if (q.topMargin != null) { if (q.topMargin == "auto") { f = parseInt(a(".stuckMenu").css("margin-top")) } else { if (isNaN(q.topMargin) && q.topMargin.search("px") > 0) { f = parseInt(q.topMargin.replace("px", "")) } else { if (!isNaN(parseInt(q.topMargin))) { f = parseInt(q.topMargin) } else { console.log("incorrect argument, ignored."); f = 0 } } } } else { f = 0 } c = a("." + l).size() } m = parseInt(a(this).height()); k = parseInt(a(this).css("margin-bottom")); d = parseInt(a(this).next().closest("div").css("margin-top")); j = parseInt(a(this).offset().top) }; a(document).on("scroll", function () { varscroll = parseInt(a(document).scrollTop()); if (c != null) { for (var o = 0; o < c; o++) { n[o] = a("#" + h[o] + "").offset().top; function p(q) { contentView = a("#" + h[q] + "").height() * 0.4; testView = n[q] - contentView; if (varscroll > testView) { a("." + l).removeClass(b); a("." + l + ":eq(" + q + ")").addClass(b) } else { if (varscroll < 50) { a("." + l).removeClass(b); a("." + l + ":eq(0)").addClass(b) } } } if (i == "down" && varscroll > n[o] - 50 && varscroll < n[o] + 50) { a("." + l).removeClass(b); a("." + l + ":eq(" + o + ")").addClass(b) } if (i == "up") { p(o) } } } if (j < varscroll + f) { a(".stuckMenu").addClass("isStuck"); a(".stuckMenu").next().closest("div").css({ "margin-top": m + k + d + "px" }, 10); a(".stuckMenu").css("position", "fixed"); a(".isStuck").css({ top: "0px" }, 10, function () { }) } if (varscroll + f < j) { a(".stuckMenu").removeClass("isStuck"); a(".stuckMenu").next().closest("div").css({ "margin-top": d + "px" }, 10); a(".stuckMenu").css("position", "relative") } }) }) }); !function (d) { var b = function (f, e) { this.options = e; this.$body = d(document.body); this.$element = d(f); this.$backdrop = this.isShown = null; this.scrollbarWidth = 0; if (this.options.remote) { this.$element.find(".modal-content").load(this.options.remote, d.proxy(function () { this.$element.trigger("loaded.bs.modal") }, this)) } }; b.VERSION = "3.3.0"; b.TRANSITION_DURATION = 300; b.BACKDROP_TRANSITION_DURATION = 150; b.DEFAULTS = { backdrop: true, keyboard: true, show: true, }; b.prototype.toggle = function (e) { return this.isShown ? this.hide() : this.show(e) }; b.prototype.show = function (h) { var f = this; var g = d.Event("show.bs.modal", { relatedTarget: h }); this.$element.trigger(g); if (this.isShown || g.isDefaultPrevented()) { return } this.isShown = true; this.checkScrollbar(); this.$body.addClass("modal-open"); this.setScrollbar(); this.escape(); this.$element.on("click.dismiss.bs.modal", '[data-dismiss="modal"]', d.proxy(this.hide, this)); this.backdrop(function () { var j = d.support.transition && f.$element.hasClass("fade"); if (!f.$element.parent().length) { f.$element.appendTo(f.$body) } f.$element.show().scrollTop(0); if (j) { f.$element[0].offsetWidth } f.$element.addClass("in").attr("aria-hidden", false); f.enforceFocus(); var i = d.Event("shown.bs.modal", { relatedTarget: h }); j ? f.$element.find(".modal-dialog").one("bsTransitionEnd", function () { f.$element.trigger("focus").trigger(i) }).emulateTransitionEnd(b.TRANSITION_DURATION) : f.$element.trigger("focus").trigger(i) }) }; b.prototype.hide = function (f) { if (f) { f.preventDefault() } f = d.Event("hide.bs.modal"); this.$element.trigger(f); if (!this.isShown || f.isDefaultPrevented()) { return } this.isShown = false; this.escape(); d(document).off("focusin.bs.modal"); this.$element.removeClass("in").attr("aria-hidden", true).off("click.dismiss.bs.modal"); d.support.transition && this.$element.hasClass("fade") ? this.$element.one("bsTransitionEnd", d.proxy(this.hideModal, this)).emulateTransitionEnd(b.TRANSITION_DURATION) : this.hideModal() }; b.prototype.enforceFocus = function () { d(document).off("focusin.bs.modal").on("focusin.bs.modal", d.proxy(function (f) { if (this.$element[0] !== f.target && !this.$element.has(f.target).length) { this.$element.trigger("focus") } }, this)) }; b.prototype.escape = function () { if (this.isShown && this.options.keyboard) { this.$element.on("keydown.dismiss.bs.modal", d.proxy(function (f) { f.which == 27 && this.hide() }, this)) } else { if (!this.isShown) { this.$element.off("keydown.dismiss.bs.modal") } } }; b.prototype.hideModal = function () { var e = this; this.$element.hide(); this.backdrop(function () { e.$body.removeClass("modal-open"); e.resetScrollbar(); e.$element.trigger("hidden.bs.modal") }) }; b.prototype.removeBackdrop = function () { this.$backdrop && this.$backdrop.remove(); this.$backdrop = null }; b.prototype.backdrop = function (i) { var h = this; var f = this.$element.hasClass("fade") ? "fade" : ""; if (this.isShown && this.options.backdrop) { var e = d.support.transition && f; this.$backdrop = d('<div class="modal-backdrop ' + f + '" />').prependTo(this.$element).on("click.dismiss.bs.modal", d.proxy(function (j) { if (j.target !== j.currentTarget) { return this.options.backdrop == "static" ? this.$element[0].focus.call(this.$element[0]) : this.hide.call(this) } }, this)); if (e) { this.$backdrop[0].offsetWidth } this.$backdrop.addClass("in"); if (!i) { return } e ? this.$backdrop.one("bsTransitionEnd", i).emulateTransitionEnd(b.BACKDROP_TRANSITION_DURATION) : i() } else { if (!this.isShown && this.$backdrop) { this.$backdrop.removeClass("in"); var g = function () { h.removeBackdrop(); i && i() }; d.support.transition && this.$element.hasClass("fade") ? this.$backdrop.one("bsTransitionEnd", g).emulateTransitionEnd(b.BACKDROP_TRANSITION_DURATION) : g() } else { if (i) { i() } } } }; b.prototype.checkScrollbar = function () { this.scrollbarWidth = this.measureScrollbar() }; b.prototype.setScrollbar = function () { var e = parseInt((this.$body.css("padding-right") || 0), 10); if (this.scrollbarWidth) { this.$body.css("padding-right", e + this.scrollbarWidth) } }; b.prototype.resetScrollbar = function () { this.$body.css("padding-right", "") }; b.prototype.measureScrollbar = function () { if (document.body.clientWidth >= window.innerWidth) { return 0 } var f = document.createElement("div"); f.className = "modal-scrollbar-measure"; this.$body.append(f); var e = f.offsetWidth - f.clientWidth; this.$body[0].removeChild(f); return e }; function c(e, f) { return this.each(function () { var i = d(this); var h = i.data("bs.modal"); var g = d.extend({}, b.DEFAULTS, i.data(), typeof e == "object" && e); if (!h) { i.data("bs.modal", (h = new b(this, g))) } if (typeof e == "string") { h[e](f) } else { if (g.show) { h.show(f) } } }) } var a = d.fn.modal; d.fn.modal = c; d.fn.modal.Constructor = b; d.fn.modal.noConflict = function () { d.fn.modal = a; return this }; d(document).on("click.bs.modal.data-api", '[data-toggle="modal"]', function (j) { var i = d(this); var g = i.attr("href"); var f = d(i.attr("data-target") || (g && g.replace(/.*(?=#[^\s]+$)/, ""))); var h = f.data("bs.modal") ? "toggle" : d.extend({ remote: !/#/.test(g) && g }, f.data(), i.data()); if (i.is("a")) { j.preventDefault() } f.one("show.bs.modal", function (e) { if (e.isDefaultPrevented()) { return } f.one("hidden.bs.modal", function () { i.is(":visible") && i.trigger("focus") }) }); c.call(f, h, this) }) }(window.jQuery); !function (h) { var e = ".dropdown-backdrop"; var b = '[data-toggle="dropdown"]'; var a = function (i) { h(i).on("click.bs.dropdown", this.toggle) }; a.VERSION = "3.3.5"; function f(k) { var i = k.attr("data-target"); if (!i) { i = k.attr("href"); i = i && /#[A-Za-z]/.test(i) && i.replace(/.*(?=#[^\s]*$)/, "") } var j = i && h(i); return j && j.length ? j : k.parent() } function d(i) { if (i && i.which === 3) { return h(e).remove() } h(b).each(function () { var l = h(this); var k = f(l); var j = { relatedTarget: this }; if (!k.hasClass("open")) { return } if (i && i.type == "click" && /input|textarea/i.test(i.target.tagName) && h.contains(k[0], i.target)) { return k.trigger(i = h.Event("hide.bs.dropdown", j)) } if (i.isDefaultPrevented()) { return } l.attr("aria-expanded", "false"); k.removeClass("open").trigger("hidden.bs.dropdown", j) }) } a.prototype.toggle = function (m) { var l = h(this); if (l.is(".disabled, :disabled")) { return } var k = f(l); var j = k.hasClass("open"); d(); if (!j) { if ("ontouchstart" in document.documentElement && !k.closest(".navbar-nav").length) { h(document.createElement("div")).addClass("dropdown-backdrop").insertAfter(h(this)).on("click", d) } var i = { relatedTarget: this }; k.trigger(m = h.Event("show.bs.dropdown", i)); if (m.isDefaultPrevented()) { return l.trigger("focus").attr("aria-expanded", "true") } k.toggleClass("open").trigger("shown.bs.dropdown", i) } return false }; a.prototype.keydown = function (m) { if (!/(38|40|27|32)/.test(m.which) || /input|textarea/i.test(m.target.tagName)) { return } var l = h(this); m.preventDefault(); m.stopPropagation(); if (l.is(".disabled, :disabled")) { return } var k = f(l); var j = k.hasClass("open"); if (!j && m.which != 27 || j && m.which == 27) { if (m.which == 27) { k.find(b).trigger("focus") } return; l.trigger("click") } var n = " li:not(.disabled):visible a"; var o = k.find(".dropdown-menu" + n); if (!o.length) { return } var i = o.index(m.target); if (m.which == 38 && i > 0) { i-- } if (m.which == 40 && i < o.length - 1) { i++ } if (!~i) { i = 0 } o.eq(i).trigger("focus") }; function g(i) { return this.each(function () { var k = h(this); var j = k.data("bs.dropdown"); if (!j) { k.data("bs.dropdown", (j = new a(this))) } if (typeof i == "string") { j[i].call(k) } }) } var c = h.fn.dropdown; h.fn.dropdown = g; h.fn.dropdown.Constructor = a; h.fn.dropdown.noConflict = function () { h.fn.dropdown = c; return this }; h(document).on("click.bs.dropdown.data-api", d).on("click.bs.dropdown.data-api", ".dropdown form", function (i) { i.stopPropagation() }).on("click.bs.dropdown.data-api", b, a.prototype.toggle).on("keydown.bs.dropdown.data-api", b, a.prototype.keydown).on("keydown.bs.dropdown.data-api", ".dropdown-menu", a.prototype.keydown) }(window.jQuery); $(function () { $(document).on("mouseenter", ".dropDown", function () { $(this).addClass("hover") }); $(document).on("mouseleave", ".dropDown", function () { $(this).removeClass("hover") }); $(document).on("mouseenter", ".dropDown_hover", function () { $(this).addClass("open") }); $(document).on("mouseleave", ".dropDown_hover", function () { $(this).removeClass("open") }); $(document).on("click", ".dropDown-menu li a", function () { $(".dropDown").removeClass("open") }); $(document).on("mouseenter", ".menu > li", function () { $(this).addClass("open") }); $(document).on("mouseleave", ".menu > li", function () { $(this).removeClass("open") }) }); !function (b) { function a() { var e = document.createElement("bootstrap"); var d = { WebkitTransition: "webkitTransitionEnd", MozTransition: "transitionend", OTransition: "oTransitionEnd otransitionend", transition: "transitionend" }; for (var c in d) { if (e.style[c] !== undefined) { return { end: d[c] } } } return false } b.fn.emulateTransitionEnd = function (e) { var d = false; var c = this; b(this).one("bsTransitionEnd", function () { d = true }); var f = function () { if (!d) { b(c).trigger(b.support.transition.end) } }; setTimeout(f, e); return this }; b(function () { b.support.transition = a(); if (!b.support.transition) { return } b.event.special.bsTransitionEnd = { bindType: b.support.transition.end, delegateType: b.support.transition.end, handle: function (c) { if (b(c.target).is(this)) { return c.handleObj.handler.apply(this, arguments) } } } }) }(window.jQuery); !function (d) { var c = function (f, e) { this.type = this.options = this.enabled = this.timeout = this.hoverState = this.$element = null; this.init("tooltip", f, e) }; c.VERSION = "3.3.0"; c.TRANSITION_DURATION = 150; c.DEFAULTS = { animation: true, placement: "top", selector: false, template: '<div class="tooltip" role="tooltip"><div class="tooltip-arrow"></div><div class="tooltip-inner"></div></div>', trigger: "hover focus", title: "", delay: 0, html: false, container: false, viewport: { selector: "body", padding: 0 } }; c.prototype.init = function (l, j, g) { this.enabled = true; this.type = l; this.$element = d(j); this.options = this.getOptions(g); this.$viewport = this.options.viewport && d(this.options.viewport.selector || this.options.viewport); var k = this.options.trigger.split(" "); for (var h = k.length; h--;) { var f = k[h]; if (f == "click") { this.$element.on("click." + this.type, this.options.selector, d.proxy(this.toggle, this)) } else { if (f != "manual") { var m = f == "hover" ? "mouseenter" : "focusin"; var e = f == "hover" ? "mouseleave" : "focusout"; this.$element.on(m + "." + this.type, this.options.selector, d.proxy(this.enter, this)); this.$element.on(e + "." + this.type, this.options.selector, d.proxy(this.leave, this)) } } } this.options.selector ? (this._options = d.extend({}, this.options, { trigger: "manual", selector: "" })) : this.fixTitle() }; c.prototype.getDefaults = function () { return c.DEFAULTS }; c.prototype.getOptions = function (e) { e = d.extend({}, this.getDefaults(), this.$element.data(), e); if (e.delay && typeof e.delay == "number") { e.delay = { show: e.delay, hide: e.delay } } return e }; c.prototype.getDelegateOptions = function () { var e = {}; var f = this.getDefaults(); this._options && d.each(this._options, function (g, h) { if (f[g] != h) { e[g] = h } }); return e }; c.prototype.enter = function (f) { var e = f instanceof this.constructor ? f : d(f.currentTarget).data("bs." + this.type); if (e && e.$tip && e.$tip.is(":visible")) { e.hoverState = "in"; return } if (!e) { e = new this.constructor(f.currentTarget, this.getDelegateOptions()); d(f.currentTarget).data("bs." + this.type, e) } clearTimeout(e.timeout); e.hoverState = "in"; if (!e.options.delay || !e.options.delay.show) { return e.show() } e.timeout = setTimeout(function () { if (e.hoverState == "in") { e.show() } }, e.options.delay.show) }; c.prototype.leave = function (f) { var e = f instanceof this.constructor ? f : d(f.currentTarget).data("bs." + this.type); if (!e) { e = new this.constructor(f.currentTarget, this.getDelegateOptions()); d(f.currentTarget).data("bs." + this.type, e) } clearTimeout(e.timeout); e.hoverState = "out"; if (!e.options.delay || !e.options.delay.hide) { return e.hide() } e.timeout = setTimeout(function () { if (e.hoverState == "out") { e.hide() } }, e.options.delay.hide) }; c.prototype.show = function () { var p = d.Event("show.bs." + this.type); if (this.hasContent() && this.enabled) { this.$element.trigger(p); var q = d.contains(this.$element[0].ownerDocument.documentElement, this.$element[0]); if (p.isDefaultPrevented() || !q) { return } var o = this; var m = this.tip(); var i = this.getUID(this.type); this.setContent(); m.attr("id", i); this.$element.attr("aria-describedby", i); if (this.options.animation) { m.addClass("fade") } var l = typeof this.options.placement == "function" ? this.options.placement.call(this, m[0], this.$element[0]) : this.options.placement; var t = /\s?auto?\s?/i; var u = t.test(l); if (u) { l = l.replace(t, "") || "top" } m.detach().css({ top: 0, left: 0, display: "block" }).addClass(l).data("bs." + this.type, this); this.options.container ? m.appendTo(this.options.container) : m.insertAfter(this.$element); var r = this.getPosition(); var f = m[0].offsetWidth; var n = m[0].offsetHeight; if (u) { var k = l; var s = this.options.container ? d(this.options.container) : this.$element.parent(); var h = this.getPosition(s); l = l == "bottom" && r.bottom + n > h.bottom ? "top" : l == "top" && r.top - n < h.top ? "bottom" : l == "right" && r.right + f > h.width ? "left" : l == "left" && r.left - f < h.left ? "right" : l; m.removeClass(k).addClass(l) } var j = this.getCalculatedOffset(l, r, f, n); this.applyPlacement(j, l); var g = function () { var e = o.hoverState; o.$element.trigger("shown.bs." + o.type); o.hoverState = null; if (e == "out") { o.leave(o) } }; d.support.transition && this.$tip.hasClass("fade") ? m.one("bsTransitionEnd", g).emulateTransitionEnd(c.TRANSITION_DURATION) : g() } }; c.prototype.applyPlacement = function (j, k) { var l = this.tip(); var g = l[0].offsetWidth; var q = l[0].offsetHeight; var f = parseInt(l.css("margin-top"), 10); var i = parseInt(l.css("margin-left"), 10); if (isNaN(f)) { f = 0 } if (isNaN(i)) { i = 0 } j.top = j.top + f; j.left = j.left + i; d.offset.setOffset(l[0], d.extend({ using: function (r) { l.css({ top: Math.round(r.top), left: Math.round(r.left) }) } }, j), 0); l.addClass("in"); var e = l[0].offsetWidth; var m = l[0].offsetHeight; if (k == "top" && m != q) { j.top = j.top + q - m } var p = this.getViewportAdjustedDelta(k, j, e, m); if (p.left) { j.left += p.left } else { j.top += p.top } var n = /top|bottom/.test(k); var h = n ? p.left * 2 - g + e : p.top * 2 - q + m; var o = n ? "offsetWidth" : "offsetHeight"; l.offset(j); this.replaceArrow(h, l[0][o], n) }; c.prototype.replaceArrow = function (g, e, f) { this.arrow().css(f ? "left" : "top", 50 * (1 - g / e) + "%").css(f ? "top" : "left", "") }; c.prototype.setContent = function () { var f = this.tip(); var e = this.getTitle(); f.find(".tooltip-inner")[this.options.html ? "html" : "text"](e); f.removeClass("fade in top bottom left right") }; c.prototype.hide = function (j) { var g = this; var i = this.tip(); var h = d.Event("hide.bs." + this.type); function f() { if (g.hoverState != "in") { i.detach() } g.$element.removeAttr("aria-describedby").trigger("hidden.bs." + g.type); j && j() } this.$element.trigger(h); if (h.isDefaultPrevented()) { return } i.removeClass("in"); d.support.transition && this.$tip.hasClass("fade") ? i.one("bsTransitionEnd", f).emulateTransitionEnd(c.TRANSITION_DURATION) : f(); this.hoverState = null; return this }; c.prototype.fixTitle = function () { var e = this.$element; if (e.attr("title") || typeof (e.attr("data-original-title")) != "string") { e.attr("data-original-title", e.attr("title") || "").attr("title", "") } }; c.prototype.hasContent = function () { return this.getTitle() }; c.prototype.getPosition = function (g) { g = g || this.$element; var i = g[0]; var f = i.tagName == "BODY"; var h = i.getBoundingClientRect(); if (h.width == null) { h = d.extend({}, h, { width: h.right - h.left, height: h.bottom - h.top }) } var k = f ? { top: 0, left: 0 } : g.offset(); var e = { scroll: f ? document.documentElement.scrollTop || document.body.scrollTop : g.scrollTop() }; var j = f ? { width: d(window).width(), height: d(window).height() } : null; return d.extend({}, h, e, j, k) }; c.prototype.getCalculatedOffset = function (e, h, f, g) { return e == "bottom" ? { top: h.top + h.height, left: h.left + h.width / 2 - f / 2 } : e == "top" ? { top: h.top - g, left: h.left + h.width / 2 - f / 2 } : e == "left" ? { top: h.top + h.height / 2 - g / 2, left: h.left - f } : { top: h.top + h.height / 2 - g / 2, left: h.left + h.width } }; c.prototype.getViewportAdjustedDelta = function (h, k, e, j) { var m = { top: 0, left: 0 }; if (!this.$viewport) { return m } var g = this.options.viewport && this.options.viewport.padding || 0; var l = this.getPosition(this.$viewport); if (/right|left/.test(h)) { var n = k.top - g - l.scroll; var i = k.top + g - l.scroll + j; if (n < l.top) { m.top = l.top - n } else { if (i > l.top + l.height) { m.top = l.top + l.height - i } } } else { var o = k.left - g; var f = k.left + g + e; if (o < l.left) { m.left = l.left - o } else { if (f > l.width) { m.left = l.left + l.width - f } } } return m }; c.prototype.getTitle = function () { var g; var e = this.$element; var f = this.options; g = e.attr("data-original-title") || (typeof f.title == "function" ? f.title.call(e[0]) : f.title); return g }; c.prototype.getUID = function (e) { do { e += ~~(Math.random() * 1000000) } while (document.getElementById(e)); return e }; c.prototype.tip = function () { return (this.$tip = this.$tip || d(this.options.template)) }; c.prototype.arrow = function () { return (this.$arrow = this.$arrow || this.tip().find(".tooltip-arrow")) }; c.prototype.enable = function () { this.enabled = true }; c.prototype.disable = function () { this.enabled = false }; c.prototype.toggleEnabled = function () { this.enabled = !this.enabled }; c.prototype.toggle = function (g) { var f = this; if (g) { f = d(g.currentTarget).data("bs." + this.type); if (!f) { f = new this.constructor(g.currentTarget, this.getDelegateOptions()); d(g.currentTarget).data("bs." + this.type, f) } } f.tip().hasClass("in") ? f.leave(f) : f.enter(f) }; c.prototype.destroy = function () { var e = this; clearTimeout(this.timeout); this.hide(function () { e.$element.off("." + e.type).removeData("bs." + e.type) }) }; function b(e) { return this.each(function () { var i = d(this); var h = i.data("bs.tooltip"); var g = typeof e == "object" && e; var f = g && g.selector; if (!h && e == "destroy") { return } if (f) { if (!h) { i.data("bs.tooltip", (h = {})) } if (!h[f]) { h[f] = new c(this, g) } } else { if (!h) { i.data("bs.tooltip", (h = new c(this, g))) } } if (typeof e == "string") { h[e]() } }) } var a = d.fn.tooltip; d.fn.tooltip = b; d.fn.tooltip.Constructor = c; d.fn.tooltip.noConflict = function () { d.fn.tooltip = a; return this } }(window.jQuery); $(function () { $("[data-toggle='tooltip']").tooltip() }); !function (d) { var c = function (f, e) { this.init("popover", f, e) }; if (!d.fn.tooltip) { throw new Error("Popover requires tooltip.js") } c.VERSION = "3.3.0"; c.DEFAULTS = d.extend({}, d.fn.tooltip.Constructor.DEFAULTS, { placement: "right", trigger: "click", content: "", template: '<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-title"></h3><div class="popover-content"></div></div>' }); c.prototype = d.extend({}, d.fn.tooltip.Constructor.prototype); c.prototype.constructor = c; c.prototype.getDefaults = function () { return c.DEFAULTS }; c.prototype.setContent = function () { var g = this.tip(); var f = this.getTitle(); var e = this.getContent(); g.find(".popover-title")[this.options.html ? "html" : "text"](f); g.find(".popover-content").children().detach().end()[this.options.html ? (typeof e == "string" ? "html" : "append") : "text"](e); g.removeClass("fade top bottom left right in"); if (!g.find(".popover-title").html()) { g.find(".popover-title").hide() } }; c.prototype.hasContent = function () { return this.getTitle() || this.getContent() }; c.prototype.getContent = function () { var e = this.$element; var f = this.options; return e.attr("data-content") || (typeof f.content == "function" ? f.content.call(e[0]) : f.content) }; c.prototype.arrow = function () { return (this.$arrow = this.$arrow || this.tip().find(".arrow")) }; c.prototype.tip = function () { if (!this.$tip) { this.$tip = d(this.options.template) } return this.$tip }; function b(e) { return this.each(function () { var i = d(this); var h = i.data("bs.popover"); var g = typeof e == "object" && e; var f = g && g.selector; if (!h && e == "destroy") { return } if (f) { if (!h) { i.data("bs.popover", (h = {})) } if (!h[f]) { h[f] = new c(this, g) } } else { if (!h) { i.data("bs.popover", (h = new c(this, g))) } } if (typeof e == "string") { h[e]() } }) } var a = d.fn.popover; d.fn.popover = b; d.fn.popover.Constructor = c; d.fn.popover.noConflict = function () { d.fn.popover = a; return this } }(window.jQuery); $(function () { $("[data-toggle='popover']").popover() }); !function (e) { var d = '[data-dismiss="alert"]'; var b = function (f) { e(f).on("click", d, this.close) }; b.VERSION = "3.3.0"; b.TRANSITION_DURATION = 150; b.prototype.close = function (j) { var i = e(this); var g = i.attr("data-target"); if (!g) { g = i.attr("href"); g = g && g.replace(/.*(?=#[^\s]*$)/, "") } var h = e(g); if (j) { j.preventDefault() } if (!h.length) { h = i.closest(".alert") } h.trigger(j = e.Event("close.bs.alert")); if (j.isDefaultPrevented()) { return } h.removeClass("in"); function f() { h.detach().trigger("closed.bs.alert").remove() } e.support.transition && h.hasClass("fade") ? h.one("bsTransitionEnd", f).emulateTransitionEnd(b.TRANSITION_DURATION) : f() }; function c(f) { return this.each(function () { var h = e(this); var g = h.data("bs.alert"); if (!g) { h.data("bs.alert", (g = new b(this))) } if (typeof f == "string") { g[f].call(h) } }) } var a = e.fn.alert; e.fn.alert = c; e.fn.alert.Constructor = b; e.fn.alert.noConflict = function () { e.fn.alert = a; return this }; e(document).on("click.bs.alert.data-api", d, b.prototype.close) }(window.jQuery); (function (a, b) { if (typeof define === "function" && define.amd) { define(["jquery"], b) } else { if (typeof module === "object" && module.exports) { var d; try { d = require("jquery") } catch (c) { d = null } module.exports = b(d) } else { a.Slider = b(a.jQuery) } } }(this, function (b) { var a; (function (e) { var f = Array.prototype.slice; function d() { } function c(h) { if (!h) { return } function j(k) { if (k.prototype.option) { return } k.prototype.option = function (l) { if (!h.isPlainObject(l)) { return } this.options = h.extend(true, this.options, l) } } var g = typeof console === "undefined" ? d : function (k) { console.error(k) }; function i(k, l) { h.fn[k] = function (p) { if (typeof p === "string") { var o = f.call(arguments, 1); for (var q = 0, n = this.length; q < n; q++) { var s = this[q]; var m = h.data(s, k); if (!m) { g("cannot call methods on " + k + " prior to initialization; " + "attempted to call '" + p + "'"); continue } if (!h.isFunction(m[p]) || p.charAt(0) === "_") { g("no such method '" + p + "' for " + k + " instance"); continue } var r = m[p].apply(m, o); if (r !== undefined && r !== m) { return r } } return this } else { var t = this.map(function () { var u = h.data(this, k); if (u) { u.option(p); u._init() } else { u = new l(this, p); h.data(this, k, u) } return h(this) }); if (!t || t.length > 1) { return t } else { return t[0] } } } } h.bridget = function (k, l) { j(l); i(k, l) }; return h.bridget } c(e) })(b); (function (e) { var f = { formatInvalidInputErrorMsg: function (g) { return "Invalid input value '" + g + "' passed in" }, callingContextNotSliderInstance: "Calling context element does not have instance of Slider bound to it. Check your code to make sure the JQuery object returned from the call to the slider() initializer is calling the method" }; a = function (h, g) { c.call(this, h, g); return this }; function c(k, m) { if (typeof k === "string") { this.element = document.querySelector(k) } else { if (k instanceof HTMLElement) { this.element = k } } var v = this.element.style.width; var j = false; var s = this.element.parentNode; var n; var h; var u; if (this.sliderElem) { j = true } else { this.sliderElem = document.createElement("div"); this.sliderElem.className = "slider"; var g = document.createElement("div"); g.className = "slider-track"; n = document.createElement("div"); n.className = "slider-selection"; h = document.createElement("div"); h.className = "slider-handle min-slider-handle"; u = document.createElement("div"); u.className = "slider-handle max-slider-handle"; g.appendChild(n); g.appendChild(h); g.appendChild(u); var p = function (B) { var C = document.createElement("div"); C.className = "tooltip-arrow"; var i = document.createElement("div"); i.className = "tooltip-inner"; B.appendChild(C); B.appendChild(i) }; var q = document.createElement("div"); q.className = "tooltip tooltip-main"; p(q); var l = document.createElement("div"); l.className = "tooltip tooltip-min"; p(l); var o = document.createElement("div"); o.className = "tooltip tooltip-max"; p(o); this.sliderElem.appendChild(g); this.sliderElem.appendChild(q); this.sliderElem.appendChild(l); this.sliderElem.appendChild(o); s.insertBefore(this.sliderElem, this.element); this.element.style.display = "none" } if (e) { this.$element = e(this.element); this.$sliderElem = e(this.sliderElem) } m = m ? m : {}; var t = Object.keys(this.defaultOptions); for (var x = 0; x < t.length; x++) { var r = t[x]; var A = m[r]; A = (typeof A !== "undefined") ? A : z(this.element, r); A = (A !== null) ? A : this.defaultOptions[r]; if (!this.options) { this.options = {} } this.options[r] = A } function z(C, E) { var B = "data-slider-" + E; var i = C.getAttribute(B); try { return JSON.parse(i) } catch (D) { return i } } this.eventToCallbackMap = {}; this.sliderElem.id = this.options.id; this.touchCapable = "ontouchstart" in window || (window.DocumentTouch && document instanceof window.DocumentTouch); this.tooltip = this.sliderElem.querySelector(".tooltip-main"); this.tooltipInner = this.tooltip.querySelector(".tooltip-inner"); this.tooltip_min = this.sliderElem.querySelector(".tooltip-min"); this.tooltipInner_min = this.tooltip_min.querySelector(".tooltip-inner"); this.tooltip_max = this.sliderElem.querySelector(".tooltip-max"); this.tooltipInner_max = this.tooltip_max.querySelector(".tooltip-inner"); if (j === true) { this._removeClass(this.sliderElem, "slider-horizontal"); this._removeClass(this.sliderElem, "slider-vertical"); this._removeClass(this.tooltip, "hide"); this._removeClass(this.tooltip_min, "hide"); this._removeClass(this.tooltip_max, "hide");["left", "top", "width", "height"].forEach(function (i) { this._removeProperty(this.trackSelection, i) }, this);[this.handle1, this.handle2].forEach(function (i) { this._removeProperty(i, "left"); this._removeProperty(i, "top") }, this);[this.tooltip, this.tooltip_min, this.tooltip_max].forEach(function (i) { this._removeProperty(i, "left"); this._removeProperty(i, "top"); this._removeProperty(i, "margin-left"); this._removeProperty(i, "margin-top"); this._removeClass(i, "right"); this._removeClass(i, "top") }, this) } if (this.options.orientation === "vertical") { this._addClass(this.sliderElem, "slider-vertical"); this.stylePos = "top"; this.mousePos = "pageY"; this.sizePos = "offsetHeight"; this._addClass(this.tooltip, "right"); this.tooltip.style.left = "100%"; this._addClass(this.tooltip_min, "right"); this.tooltip_min.style.left = "100%"; this._addClass(this.tooltip_max, "right"); this.tooltip_max.style.left = "100%" } else { this._addClass(this.sliderElem, "slider-horizontal"); this.sliderElem.style.width = v; this.options.orientation = "horizontal"; this.stylePos = "left"; this.mousePos = "pageX"; this.sizePos = "offsetWidth"; this._addClass(this.tooltip, "top"); this.tooltip.style.top = -this.tooltip.outerHeight - 14 + "px"; this._addClass(this.tooltip_min, "top"); this.tooltip_min.style.top = -this.tooltip_min.outerHeight - 14 + "px"; this._addClass(this.tooltip_max, "top"); this.tooltip_max.style.top = -this.tooltip_max.outerHeight - 14 + "px" } if (this.options.value instanceof Array) { this.options.range = true } else { if (this.options.range) { this.options.value = [this.options.value, this.options.max] } } this.trackSelection = n || this.trackSelection; if (this.options.selection === "none") { this._addClass(this.trackSelection, "hide") } this.handle1 = h || this.handle1; this.handle2 = u || this.handle2; if (j === true) { this._removeClass(this.handle1, "round triangle"); this._removeClass(this.handle2, "round triangle hide") } var y = ["round", "triangle", "custom"]; var w = y.indexOf(this.options.handle) !== -1; if (w) { this._addClass(this.handle1, this.options.handle); this._addClass(this.handle2, this.options.handle) } this.offset = this._offset(this.sliderElem); this.size = this.sliderElem[this.sizePos]; this.setValue(this.options.value); this.handle1Keydown = this._keydown.bind(this, 0); this.handle1.addEventListener("keydown", this.handle1Keydown, false); this.handle2Keydown = this._keydown.bind(this, 1); this.handle2.addEventListener("keydown", this.handle2Keydown, false); if (this.touchCapable) { this.mousedown = this._mousedown.bind(this); this.sliderElem.addEventListener("touchstart", this.mousedown, false) } else { this.mousedown = this._mousedown.bind(this); this.sliderElem.addEventListener("mousedown", this.mousedown, false) } if (this.options.tooltip === "hide") { this._addClass(this.tooltip, "hide"); this._addClass(this.tooltip_min, "hide"); this._addClass(this.tooltip_max, "hide") } else { if (this.options.tooltip === "always") { this._showTooltip(); this._alwaysShowTooltip = true } else { this.showTooltip = this._showTooltip.bind(this); this.hideTooltip = this._hideTooltip.bind(this); this.sliderElem.addEventListener("mouseenter", this.showTooltip, false); this.sliderElem.addEventListener("mouseleave", this.hideTooltip, false); this.handle1.addEventListener("focus", this.showTooltip, false); this.handle1.addEventListener("blur", this.hideTooltip, false); this.handle2.addEventListener("focus", this.showTooltip, false); this.handle2.addEventListener("blur", this.hideTooltip, false) } } if (this.options.enabled) { this.enable() } else { this.disable() } } a.prototype = { _init: function () { }, constructor: a, defaultOptions: { id: "", min: 0, max: 10, step: 1, precision: 0, orientation: "horizontal", value: 5, range: false, selection: "before", tooltip: "show", tooltip_split: false, handle: "round", reversed: false, enabled: true, formatter: function (g) { if (g instanceof Array) { return g[0] + " : " + g[1] } else { return g } }, natural_arrow_keys: false }, over: false, inDrag: false, getValue: function () { if (this.options.range) { return this.options.value } return this.options.value[0] }, setValue: function (k, i) { if (!k) { k = 0 } var h = this.getValue(); this.options.value = this._validateInputValue(k); var g = this._applyPrecision.bind(this); if (this.options.range) { this.options.value[0] = g(this.options.value[0]); this.options.value[1] = g(this.options.value[1]); this.options.value[0] = Math.max(this.options.min, Math.min(this.options.max, this.options.value[0])); this.options.value[1] = Math.max(this.options.min, Math.min(this.options.max, this.options.value[1])) } else { this.options.value = g(this.options.value); this.options.value = [Math.max(this.options.min, Math.min(this.options.max, this.options.value))]; this._addClass(this.handle2, "hide"); if (this.options.selection === "after") { this.options.value[1] = this.options.max } else { this.options.value[1] = this.options.min } } this.diff = this.options.max - this.options.min; if (this.diff > 0) { this.percentage = [(this.options.value[0] - this.options.min) * 100 / this.diff, (this.options.value[1] - this.options.min) * 100 / this.diff, this.options.step * 100 / this.diff] } else { this.percentage = [0, 0, 100] } this._layout(); var j = this.options.range ? this.options.value : this.options.value[0]; if (i === true) { this._trigger("slide", j) } if (h !== j) { this._trigger("change", { oldValue: h, newValue: j }) } this._setDataVal(j); return this }, destroy: function () { this._removeSliderEventHandlers(); this.sliderElem.parentNode.removeChild(this.sliderElem); this.element.style.display = ""; this._cleanUpEventCallbacksMap(); this.element.removeAttribute("data"); if (e) { this._unbindJQueryEventHandlers(); this.$element.removeData("slider") } }, disable: function () { this.options.enabled = false; this.handle1.removeAttribute("tabindex"); this.handle2.removeAttribute("tabindex"); this._addClass(this.sliderElem, "slider-disabled"); this._trigger("slideDisabled"); return this }, enable: function () { this.options.enabled = true; this.handle1.setAttribute("tabindex", 0); this.handle2.setAttribute("tabindex", 0); this._removeClass(this.sliderElem, "slider-disabled"); this._trigger("slideEnabled"); return this }, toggle: function () { if (this.options.enabled) { this.disable() } else { this.enable() } return this }, isEnabled: function () { return this.options.enabled }, on: function (g, h) { if (e) { this.$element.on(g, h); this.$sliderElem.on(g, h) } else { this._bindNonQueryEventHandler(g, h) } return this }, getAttribute: function (g) { if (g) { return this.options[g] } else { return this.options } }, setAttribute: function (g, h) { this.options[g] = h; return this }, refresh: function () { this._removeSliderEventHandlers(); c.call(this, this.element, this.options); if (e) { e.data(this.element, "slider", this) } return this }, relayout: function () { this._layout(); return this }, _removeSliderEventHandlers: function () { this.handle1.removeEventListener("keydown", this.handle1Keydown, false); this.handle1.removeEventListener("focus", this.showTooltip, false); this.handle1.removeEventListener("blur", this.hideTooltip, false); this.handle2.removeEventListener("keydown", this.handle2Keydown, false); this.handle2.removeEventListener("focus", this.handle2Keydown, false); this.handle2.removeEventListener("blur", this.handle2Keydown, false); this.sliderElem.removeEventListener("mouseenter", this.showTooltip, false); this.sliderElem.removeEventListener("mouseleave", this.hideTooltip, false); this.sliderElem.removeEventListener("touchstart", this.mousedown, false); this.sliderElem.removeEventListener("mousedown", this.mousedown, false) }, _bindNonQueryEventHandler: function (g, h) { if (this.eventToCallbackMap[g] === undefined) { this.eventToCallbackMap[g] = [] } this.eventToCallbackMap[g].push(h) }, _cleanUpEventCallbacksMap: function () { var j = Object.keys(this.eventToCallbackMap); for (var h = 0; h < j.length; h++) { var g = j[h]; this.eventToCallbackMap[g] = null } }, _showTooltip: function () { if (this.options.tooltip_split === false) { this._addClass(this.tooltip, "in") } else { this._addClass(this.tooltip_min, "in"); this._addClass(this.tooltip_max, "in") } this.over = true }, _hideTooltip: function () { if (this.inDrag === false && this.alwaysShowTooltip !== true) { this._removeClass(this.tooltip, "in"); this._removeClass(this.tooltip_min, "in"); this._removeClass(this.tooltip_max, "in") } this.over = false }, _layout: function () { var l; if (this.options.reversed) { l = [100 - this.percentage[0], this.percentage[1]] } else { l = [this.percentage[0], this.percentage[1]] } this.handle1.style[this.stylePos] = l[0] + "%"; this.handle2.style[this.stylePos] = l[1] + "%"; if (this.options.orientation === "vertical") { this.trackSelection.style.top = Math.min(l[0], l[1]) + "%"; this.trackSelection.style.height = Math.abs(l[0] - l[1]) + "%" } else { this.trackSelection.style.left = Math.min(l[0], l[1]) + "%"; this.trackSelection.style.width = Math.abs(l[0] - l[1]) + "%"; var k = this.tooltip_min.getBoundingClientRect(); var h = this.tooltip_max.getBoundingClientRect(); if (k.right > h.left) { this._removeClass(this.tooltip_max, "top"); this._addClass(this.tooltip_max, "bottom"); this.tooltip_max.style.top = 18 + "px" } else { this._removeClass(this.tooltip_max, "bottom"); this._addClass(this.tooltip_max, "top"); this.tooltip_max.style.top = this.tooltip_min.style.top } } var j; if (this.options.range) { j = this.options.formatter(this.options.value); this._setText(this.tooltipInner, j); this.tooltip.style[this.stylePos] = (l[1] + l[0]) / 2 + "%"; if (this.options.orientation === "vertical") { this._css(this.tooltip, "margin-top", -this.tooltip.offsetHeight / 2 + "px") } else { this._css(this.tooltip, "margin-left", -this.tooltip.offsetWidth / 2 + "px") } if (this.options.orientation === "vertical") { this._css(this.tooltip, "margin-top", -this.tooltip.offsetHeight / 2 + "px") } else { this._css(this.tooltip, "margin-left", -this.tooltip.offsetWidth / 2 + "px") } var g = this.options.formatter(this.options.value[0]); this._setText(this.tooltipInner_min, g); var i = this.options.formatter(this.options.value[1]); this._setText(this.tooltipInner_max, i); this.tooltip_min.style[this.stylePos] = l[0] + "%"; if (this.options.orientation === "vertical") { this._css(this.tooltip_min, "margin-top", -this.tooltip_min.offsetHeight / 2 + "px") } else { this._css(this.tooltip_min, "margin-left", -this.tooltip_min.offsetWidth / 2 + "px") } this.tooltip_max.style[this.stylePos] = l[1] + "%"; if (this.options.orientation === "vertical") { this._css(this.tooltip_max, "margin-top", -this.tooltip_max.offsetHeight / 2 + "px") } else { this._css(this.tooltip_max, "margin-left", -this.tooltip_max.offsetWidth / 2 + "px") } } else { j = this.options.formatter(this.options.value[0]); this._setText(this.tooltipInner, j); this.tooltip.style[this.stylePos] = l[0] + "%"; if (this.options.orientation === "vertical") { this._css(this.tooltip, "margin-top", -this.tooltip.offsetHeight / 2 + "px") } else { this._css(this.tooltip, "margin-left", -this.tooltip.offsetWidth / 2 + "px") } } }, _removeProperty: function (g, h) { if (g.style.removeProperty) { g.style.removeProperty(h) } else { g.style.removeAttribute(h) } }, _mousedown: function (h) { if (!this.options.enabled) { return false } this._triggerFocusOnHandle(); this.offset = this._offset(this.sliderElem); this.size = this.sliderElem[this.sizePos]; var g = this._getPercentage(h); if (this.options.range) { var k = Math.abs(this.percentage[0] - g); var j = Math.abs(this.percentage[1] - g); this.dragged = (k < j) ? 0 : 1 } else { this.dragged = 0 } this.percentage[this.dragged] = this.options.reversed ? 100 - g : g; this._layout(); if (this.touchCapable) { document.removeEventListener("touchmove", this.mousemove, false); document.removeEventListener("touchend", this.mouseup, false) } if (this.mousemove) { document.removeEventListener("mousemove", this.mousemove, false) } if (this.mouseup) { document.removeEventListener("mouseup", this.mouseup, false) } this.mousemove = this._mousemove.bind(this); this.mouseup = this._mouseup.bind(this); if (this.touchCapable) { document.addEventListener("touchmove", this.mousemove, false); document.addEventListener("touchend", this.mouseup, false) } document.addEventListener("mousemove", this.mousemove, false); document.addEventListener("mouseup", this.mouseup, false); this.inDrag = true; var i = this._calculateValue(); this._trigger("slideStart", i); this._setDataVal(i); this.setValue(i); this._pauseEvent(h); return true }, _triggerFocusOnHandle: function (g) { if (g === 0) { this.handle1.focus() } if (g === 1) { this.handle2.focus() } }, _keydown: function (l, k) { if (!this.options.enabled) { return false } var j; switch (k.keyCode) { case 37: case 40: j = -1; break; case 39: case 38: j = 1; break }if (!j) { return } if (this.options.natural_arrow_keys) { var g = (this.options.orientation === "vertical" && !this.options.reversed); var i = (this.options.orientation === "horizontal" && this.options.reversed); if (g || i) { j = j * -1 } } var n = j * this.percentage[2]; var h = this.percentage[l] + n; if (h > 100) { h = 100 } else { if (h < 0) { h = 0 } } this.dragged = l; this._adjustPercentageForRangeSliders(h); this.percentage[this.dragged] = h; this._layout(); var m = this._calculateValue(); this._trigger("slideStart", m); this._setDataVal(m); this.setValue(m, true); this._trigger("slideStop", m); this._setDataVal(m); this._pauseEvent(k); return false }, _pauseEvent: function (g) { if (g.stopPropagation) { g.stopPropagation() } if (g.preventDefault) { g.preventDefault() } g.cancelBubble = true; g.returnValue = false }, _mousemove: function (h) { if (!this.options.enabled) { return false } var g = this._getPercentage(h); this._adjustPercentageForRangeSliders(g); this.percentage[this.dragged] = this.options.reversed ? 100 - g : g; this._layout(); var i = this._calculateValue(); this.setValue(i, true); return false }, _adjustPercentageForRangeSliders: function (g) { if (this.options.range) { if (this.dragged === 0 && this.percentage[1] < g) { this.percentage[0] = this.percentage[1]; this.dragged = 1 } else { if (this.dragged === 1 && this.percentage[0] > g) { this.percentage[1] = this.percentage[0]; this.dragged = 0 } } } }, _mouseup: function () { if (!this.options.enabled) { return false } if (this.touchCapable) { document.removeEventListener("touchmove", this.mousemove, false); document.removeEventListener("touchend", this.mouseup, false) } document.removeEventListener("mousemove", this.mousemove, false); document.removeEventListener("mouseup", this.mouseup, false); this.inDrag = false; if (this.over === false) { this._hideTooltip() } var g = this._calculateValue(); this._layout(); this._trigger("slideStop", g); this._setDataVal(g); return false }, _calculateValue: function () { var g; if (this.options.range) { g = [this.options.min, this.options.max]; if (this.percentage[0] !== 0) { g[0] = (Math.max(this.options.min, this.options.min + Math.round((this.diff * this.percentage[0] / 100) / this.options.step) * this.options.step)); g[0] = this._applyPrecision(g[0]) } if (this.percentage[1] !== 100) { g[1] = (Math.min(this.options.max, this.options.min + Math.round((this.diff * this.percentage[1] / 100) / this.options.step) * this.options.step)); g[1] = this._applyPrecision(g[1]) } } else { g = (this.options.min + Math.round((this.diff * this.percentage[0] / 100) / this.options.step) * this.options.step); if (g < this.options.min) { g = this.options.min } else { if (g > this.options.max) { g = this.options.max } } g = parseFloat(g); g = this._applyPrecision(g) } return g }, _applyPrecision: function (h) { var g = this.options.precision || this._getNumDigitsAfterDecimalPlace(this.options.step); return this._applyToFixedAndParseFloat(h, g) }, _getNumDigitsAfterDecimalPlace: function (h) { var g = ("" + h).match(/(?:\.(\d+))?(?:[eE]([+-]?\d+))?$/); if (!g) { return 0 } return Math.max(0, (g[1] ? g[1].length : 0) - (g[2] ? +g[2] : 0)) }, _applyToFixedAndParseFloat: function (h, g) { var i = h.toFixed(g); return parseFloat(i) }, _getPercentage: function (h) { if (this.touchCapable && (h.type === "touchstart" || h.type === "touchmove")) { h = h.touches[0] } var g = (h[this.mousePos] - this.offset[this.stylePos]) * 100 / this.size; g = Math.round(g / this.percentage[2]) * this.percentage[2]; return Math.max(0, Math.min(100, g)) }, _validateInputValue: function (g) { if (typeof g === "number") { return g } else { if (g instanceof Array) { this._validateArray(g); return g } else { throw new Error(f.formatInvalidInputErrorMsg(g)) } } }, _validateArray: function (j) { for (var h = 0; h < j.length; h++) { var g = j[h]; if (typeof g !== "number") { throw new Error(f.formatInvalidInputErrorMsg(g)) } } }, _setDataVal: function (h) { var g = "value: '" + h + "'"; this.element.setAttribute("data", g); this.element.setAttribute("value", h) }, _trigger: function (g, l) { l = (l || l === 0) ? l : undefined; var k = this.eventToCallbackMap[g]; if (k && k.length) { for (var j = 0; j < k.length; j++) { var h = k[j]; h(l) } } if (e) { this._triggerJQueryEvent(g, l) } }, _triggerJQueryEvent: function (g, i) { var h = { type: g, value: i }; this.$element.trigger(h); this.$sliderElem.trigger(h) }, _unbindJQueryEventHandlers: function () { this.$element.off(); this.$sliderElem.off() }, _setText: function (g, h) { if (typeof g.innerText !== "undefined") { g.innerText = h } else { if (typeof g.textContent !== "undefined") { g.textContent = h } } }, _removeClass: function (j, l) { var h = l.split(" "); var n = j.className; for (var g = 0; g < h.length; g++) { var m = h[g]; var k = new RegExp("(?:\\s|^)" + m + "(?:\\s|$)"); n = n.replace(k, " ") } j.className = n.trim() }, _addClass: function (j, l) { var h = l.split(" "); var o = j.className; for (var g = 0; g < h.length; g++) { var n = h[g]; var k = new RegExp("(?:\\s|^)" + n + "(?:\\s|$)"); var m = k.test(o); if (!m) { o += " " + n } } j.className = o.trim() }, _offset: function (i) { var g = 0; var h = 0; if (i.offsetParent) { do { g += i.offsetLeft; h += i.offsetTop } while (i = i.offsetParent) } return { left: g, top: h } }, _css: function (j, g, i) { if (e) { e.style(j, g, i) } else { var h = g.replace(/^-ms-/, "ms-").replace(/-([\da-z])/gi, function (k, l) { return l.toUpperCase() }); j.style[h] = i } } }; if (e) { var d = e.fn.slider ? "bootstrapSlider" : "slider"; e.bridget(d, a) } })(b); return a })); !(function (a) { if (typeof define === "function" && define.amd) { define(["jquery"], a) } else { if (typeof exports === "object") { a(require("jquery")) } else { a(jQuery) } } }(function (f, c) { if (!("indexOf" in Array.prototype)) { Array.prototype.indexOf = function (k, j) { if (j === c) { j = 0 } if (j < 0) { j += this.length } if (j < 0) { j = 0 } for (var l = this.length; j < l; j++) { if (j in this && this[j] === k) { return j } } return -1 } } function e(l) { var k = f(l); var j = k.add(k.parents()); var m = false; j.each(function () { if (f(this).css("position") === "fixed") { m = true; return false } }); return m } function h() { return new Date(Date.UTC.apply(Date, arguments)) } function d() { var j = new Date(); return h(j.getUTCFullYear(), j.getUTCMonth(), j.getUTCDate(), j.getUTCHours(), j.getUTCMinutes(), j.getUTCSeconds(), 0) } var i = function (l, k) { var n = this; this.element = f(l); this.container = k.container || "body"; this.language = k.language || this.element.data("date-language") || "en"; this.language = this.language in a ? this.language : this.language.split("-")[0]; this.language = this.language in a ? this.language : "en"; this.isRTL = a[this.language].rtl || false; this.formatType = k.formatType || this.element.data("format-type") || "standard"; this.format = g.parseFormat(k.format || this.element.data("date-format") || a[this.language].format || g.getDefaultFormat(this.formatType, "input"), this.formatType); this.isInline = false; this.isVisible = false; this.isInput = this.element.is("input"); this.fontAwesome = k.fontAwesome || this.element.data("font-awesome") || false; this.bootcssVer = k.bootcssVer || (this.isInput ? (this.element.is(".form-control") ? 3 : 2) : (this.bootcssVer = this.element.is(".input-group") ? 3 : 2)); this.component = this.element.is(".date") ? (this.bootcssVer == 3 ? this.element.find(".input-group-addon .glyphicon-th, .input-group-addon .glyphicon-time, .input-group-addon .glyphicon-remove, .input-group-addon .glyphicon-calendar, .input-group-addon .fa-calendar, .input-group-addon .fa-clock-o").parent() : this.element.find(".add-on .icon-th, .add-on .icon-time, .add-on .icon-calendar, .add-on .fa-calendar, .add-on .fa-clock-o").parent()) : false; this.componentReset = this.element.is(".date") ? (this.bootcssVer == 3 ? this.element.find(".input-group-addon .glyphicon-remove, .input-group-addon .fa-times").parent() : this.element.find(".add-on .icon-remove, .add-on .fa-times").parent()) : false; this.hasInput = this.component && this.element.find("input").length; if (this.component && this.component.length === 0) { this.component = false } this.linkField = k.linkField || this.element.data("link-field") || false; this.linkFormat = g.parseFormat(k.linkFormat || this.element.data("link-format") || g.getDefaultFormat(this.formatType, "link"), this.formatType); this.minuteStep = k.minuteStep || this.element.data("minute-step") || 5; this.pickerPosition = k.pickerPosition || this.element.data("picker-position") || "bottom-right"; this.showMeridian = k.showMeridian || this.element.data("show-meridian") || false; this.initialDate = k.initialDate || new Date(); this.zIndex = k.zIndex || this.element.data("z-index") || c; this.title = typeof k.title === "undefined" ? false : k.title; this.defaultTimeZone = (new Date).toString().split("(")[1].slice(0, -1); this.timezone = k.timezone || this.defaultTimeZone; this.icons = { leftArrow: this.fontAwesome ? "fa-arrow-left" : (this.bootcssVer === 3 ? "glyphicon-arrow-left" : "icon-arrow-left"), rightArrow: this.fontAwesome ? "fa-arrow-right" : (this.bootcssVer === 3 ? "glyphicon-arrow-right" : "icon-arrow-right") }; this.icontype = this.fontAwesome ? "fa" : "glyphicon"; this._attachEvents(); this.clickedOutside = function (o) { if (f(o.target).closest(".datetimepicker").length === 0) { n.hide() } }; this.formatViewType = "datetime"; if ("formatViewType" in k) { this.formatViewType = k.formatViewType } else { if ("formatViewType" in this.element.data()) { this.formatViewType = this.element.data("formatViewType") } } this.minView = 0; if ("minView" in k) { this.minView = k.minView } else { if ("minView" in this.element.data()) { this.minView = this.element.data("min-view") } } this.minView = g.convertViewMode(this.minView); this.maxView = g.modes.length - 1; if ("maxView" in k) { this.maxView = k.maxView } else { if ("maxView" in this.element.data()) { this.maxView = this.element.data("max-view") } } this.maxView = g.convertViewMode(this.maxView); this.wheelViewModeNavigation = false; if ("wheelViewModeNavigation" in k) { this.wheelViewModeNavigation = k.wheelViewModeNavigation } else { if ("wheelViewModeNavigation" in this.element.data()) { this.wheelViewModeNavigation = this.element.data("view-mode-wheel-navigation") } } this.wheelViewModeNavigationInverseDirection = false; if ("wheelViewModeNavigationInverseDirection" in k) { this.wheelViewModeNavigationInverseDirection = k.wheelViewModeNavigationInverseDirection } else { if ("wheelViewModeNavigationInverseDirection" in this.element.data()) { this.wheelViewModeNavigationInverseDirection = this.element.data("view-mode-wheel-navigation-inverse-dir") } } this.wheelViewModeNavigationDelay = 100; if ("wheelViewModeNavigationDelay" in k) { this.wheelViewModeNavigationDelay = k.wheelViewModeNavigationDelay } else { if ("wheelViewModeNavigationDelay" in this.element.data()) { this.wheelViewModeNavigationDelay = this.element.data("view-mode-wheel-navigation-delay") } } this.startViewMode = 2; if ("startView" in k) { this.startViewMode = k.startView } else { if ("startView" in this.element.data()) { this.startViewMode = this.element.data("start-view") } } this.startViewMode = g.convertViewMode(this.startViewMode); this.viewMode = this.startViewMode; this.viewSelect = this.minView; if ("viewSelect" in k) { this.viewSelect = k.viewSelect } else { if ("viewSelect" in this.element.data()) { this.viewSelect = this.element.data("view-select") } } this.viewSelect = g.convertViewMode(this.viewSelect); this.forceParse = true; if ("forceParse" in k) { this.forceParse = k.forceParse } else { if ("dateForceParse" in this.element.data()) { this.forceParse = this.element.data("date-force-parse") } } var m = this.bootcssVer === 3 ? g.templateV3 : g.template; while (m.indexOf("{iconType}") !== -1) { m = m.replace("{iconType}", this.icontype) } while (m.indexOf("{leftArrow}") !== -1) { m = m.replace("{leftArrow}", this.icons.leftArrow) } while (m.indexOf("{rightArrow}") !== -1) { m = m.replace("{rightArrow}", this.icons.rightArrow) } this.picker = f(m).appendTo(this.isInline ? this.element : this.container).on({ click: f.proxy(this.click, this), mousedown: f.proxy(this.mousedown, this) }); if (this.wheelViewModeNavigation) { if (f.fn.mousewheel) { this.picker.on({ mousewheel: f.proxy(this.mousewheel, this) }) } else { console.log("Mouse Wheel event is not supported. Please include the jQuery Mouse Wheel plugin before enabling this option") } } if (this.isInline) { this.picker.addClass("datetimepicker-inline") } else { this.picker.addClass("datetimepicker-dropdown-" + this.pickerPosition + " dropdown-menu") } if (this.isRTL) { this.picker.addClass("datetimepicker-rtl"); var j = this.bootcssVer === 3 ? ".prev span, .next span" : ".prev i, .next i"; this.picker.find(j).toggleClass(this.icons.leftArrow + " " + this.icons.rightArrow) } f(document).on("mousedown", this.clickedOutside); this.autoclose = false; if ("autoclose" in k) { this.autoclose = k.autoclose } else { if ("dateAutoclose" in this.element.data()) { this.autoclose = this.element.data("date-autoclose") } } this.keyboardNavigation = true; if ("keyboardNavigation" in k) { this.keyboardNavigation = k.keyboardNavigation } else { if ("dateKeyboardNavigation" in this.element.data()) { this.keyboardNavigation = this.element.data("date-keyboard-navigation") } } this.todayBtn = (k.todayBtn || this.element.data("date-today-btn") || false); this.clearBtn = (k.clearBtn || this.element.data("date-clear-btn") || false); this.todayHighlight = (k.todayHighlight || this.element.data("date-today-highlight") || false); this.weekStart = ((k.weekStart || this.element.data("date-weekstart") || a[this.language].weekStart || 0) % 7); this.weekEnd = ((this.weekStart + 6) % 7); this.startDate = -Infinity; this.endDate = Infinity; this.datesDisabled = []; this.daysOfWeekDisabled = []; this.setStartDate(k.startDate || this.element.data("date-startdate")); this.setEndDate(k.endDate || this.element.data("date-enddate")); this.setDatesDisabled(k.datesDisabled || this.element.data("date-dates-disabled")); this.setDaysOfWeekDisabled(k.daysOfWeekDisabled || this.element.data("date-days-of-week-disabled")); this.setMinutesDisabled(k.minutesDisabled || this.element.data("date-minute-disabled")); this.setHoursDisabled(k.hoursDisabled || this.element.data("date-hour-disabled")); this.fillDow(); this.fillMonths(); this.update(); this.showMode(); if (this.isInline) { this.show() } }; i.prototype = { constructor: i, _events: [], _attachEvents: function () { this._detachEvents(); if (this.isInput) { this._events = [[this.element, { focus: f.proxy(this.show, this), keyup: f.proxy(this.update, this), keydown: f.proxy(this.keydown, this) }]] } else { if (this.component && this.hasInput) { this._events = [[this.element.find("input"), { focus: f.proxy(this.show, this), keyup: f.proxy(this.update, this), keydown: f.proxy(this.keydown, this) }], [this.component, { click: f.proxy(this.show, this) }]]; if (this.componentReset) { this._events.push([this.componentReset, { click: f.proxy(this.reset, this) }]) } } else { if (this.element.is("div")) { this.isInline = true } else { this._events = [[this.element, { click: f.proxy(this.show, this) }]] } } } for (var j = 0, k, l; j < this._events.length; j++) { k = this._events[j][0]; l = this._events[j][1]; k.on(l) } }, _detachEvents: function () { for (var j = 0, k, l; j < this._events.length; j++) { k = this._events[j][0]; l = this._events[j][1]; k.off(l) } this._events = [] }, show: function (j) { this.picker.show(); this.height = this.component ? this.component.outerHeight() : this.element.outerHeight(); if (this.forceParse) { this.update() } this.place(); f(window).on("resize", f.proxy(this.place, this)); if (j) { j.stopPropagation(); j.preventDefault() } this.isVisible = true; this.element.trigger({ type: "show", date: this.date }) }, hide: function (j) { if (!this.isVisible) { return } if (this.isInline) { return } this.picker.hide(); f(window).off("resize", this.place); this.viewMode = this.startViewMode; this.showMode(); if (!this.isInput) { f(document).off("mousedown", this.hide) } if (this.forceParse && (this.isInput && this.element.val() || this.hasInput && this.element.find("input").val())) { this.setValue() } this.isVisible = false; this.element.trigger({ type: "hide", date: this.date }) }, remove: function () { this._detachEvents(); f(document).off("mousedown", this.clickedOutside); this.picker.remove(); delete this.picker; delete this.element.data().datetimepicker }, getDate: function () { var j = this.getUTCDate(); return new Date(j.getTime() + (j.getTimezoneOffset() * 60000)) }, getUTCDate: function () { return this.date }, getInitialDate: function () { return this.initialDate }, setInitialDate: function (j) { this.initialDate = j }, setDate: function (j) { this.setUTCDate(new Date(j.getTime() - (j.getTimezoneOffset() * 60000))) }, setUTCDate: function (j) { if (j >= this.startDate && j <= this.endDate) { this.date = j; this.setValue(); this.viewDate = this.date; this.fill() } else { this.element.trigger({ type: "outOfRange", date: j, startDate: this.startDate, endDate: this.endDate }) } }, setFormat: function (k) { this.format = g.parseFormat(k, this.formatType); var j; if (this.isInput) { j = this.element } else { if (this.component) { j = this.element.find("input") } } if (j && j.val()) { this.setValue() } }, setValue: function () { var j = this.getFormattedDate(); if (!this.isInput) { if (this.component) { this.element.find("input").val(j) } this.element.data("date", j) } else { this.element.val(j) } if (this.linkField) { f("#" + this.linkField).val(this.getFormattedDate(this.linkFormat)) } }, getFormattedDate: function (j) { if (j == c) { j = this.format } return g.formatDate(this.date, j, this.language, this.formatType, this.timezone) }, setStartDate: function (j) { this.startDate = j || -Infinity; if (this.startDate !== -Infinity) { this.startDate = g.parseDate(this.startDate, this.format, this.language, this.formatType, this.timezone) } this.update(); this.updateNavArrows() }, setEndDate: function (j) { this.endDate = j || Infinity; if (this.endDate !== Infinity) { this.endDate = g.parseDate(this.endDate, this.format, this.language, this.formatType, this.timezone) } this.update(); this.updateNavArrows() }, setDatesDisabled: function (j) { this.datesDisabled = j || []; if (!f.isArray(this.datesDisabled)) { this.datesDisabled = this.datesDisabled.split(/,\s*/) } this.datesDisabled = f.map(this.datesDisabled, function (k) { return g.parseDate(k, this.format, this.language, this.formatType, this.timezone).toDateString() }); this.update(); this.updateNavArrows() }, setTitle: function (j, k) { return this.picker.find(j).find("th:eq(1)").text(this.title === false ? k : this.title) }, setDaysOfWeekDisabled: function (j) { this.daysOfWeekDisabled = j || []; if (!f.isArray(this.daysOfWeekDisabled)) { this.daysOfWeekDisabled = this.daysOfWeekDisabled.split(/,\s*/) } this.daysOfWeekDisabled = f.map(this.daysOfWeekDisabled, function (k) { return parseInt(k, 10) }); this.update(); this.updateNavArrows() }, setMinutesDisabled: function (j) { this.minutesDisabled = j || []; if (!f.isArray(this.minutesDisabled)) { this.minutesDisabled = this.minutesDisabled.split(/,\s*/) } this.minutesDisabled = f.map(this.minutesDisabled, function (k) { return parseInt(k, 10) }); this.update(); this.updateNavArrows() }, setHoursDisabled: function (j) { this.hoursDisabled = j || []; if (!f.isArray(this.hoursDisabled)) { this.hoursDisabled = this.hoursDisabled.split(/,\s*/) } this.hoursDisabled = f.map(this.hoursDisabled, function (k) { return parseInt(k, 10) }); this.update(); this.updateNavArrows() }, place: function () { if (this.isInline) { return } if (!this.zIndex) { var k = 0; f("div").each(function () { var p = parseInt(f(this).css("zIndex"), 10); if (p > k) { k = p } }); this.zIndex = k + 10 } var o, n, m, l; if (this.container instanceof f) { l = this.container.offset() } else { l = f(this.container).offset() } if (this.component) { o = this.component.offset(); m = o.left; if (this.pickerPosition == "bottom-left" || this.pickerPosition == "top-left") { m += this.component.outerWidth() - this.picker.outerWidth() } } else { o = this.element.offset(); m = o.left; if (this.pickerPosition == "bottom-left" || this.pickerPosition == "top-left") { m += this.element.outerWidth() - this.picker.outerWidth() } } var j = document.body.clientWidth || window.innerWidth; if (m + 220 > j) { m = j - 220 } if (this.pickerPosition == "top-left" || this.pickerPosition == "top-right") { n = o.top - this.picker.outerHeight() } else { n = o.top + this.height } n = n - l.top; m = m - l.left; this.picker.css({ top: n, left: m, zIndex: this.zIndex }) }, update: function () { var j, k = false; if (arguments && arguments.length && (typeof arguments[0] === "string" || arguments[0] instanceof Date)) { j = arguments[0]; k = true } else { j = (this.isInput ? this.element.val() : this.element.find("input").val()) || this.element.data("date") || this.initialDate; if (typeof j == "string" || j instanceof String) { j = j.replace(/^\s+|\s+$/g, "") } } if (!j) { j = new Date(); k = false } this.date = g.parseDate(j, this.format, this.language, this.formatType, this.timezone); if (k) { this.setValue() } if (this.date < this.startDate) { this.viewDate = new Date(this.startDate) } else { if (this.date > this.endDate) { this.viewDate = new Date(this.endDate) } else { this.viewDate = new Date(this.date) } } this.fill() }, fillDow: function () { var j = this.weekStart, k = "<tr>"; while (j < this.weekStart + 7) { k += '<th class="dow">' + a[this.language].daysMin[(j++) % 7] + "</th>" } k += "</tr>"; this.picker.find(".datetimepicker-days thead").append(k) }, fillMonths: function () { var k = "", j = 0; while (j < 12) { k += '<span class="month">' + a[this.language].monthsShort[j++] + "</span>" } this.picker.find(".datetimepicker-months td").html(k) }, fill: function () { if (this.date == null || this.viewDate == null) { return } var H = new Date(this.viewDate), u = H.getUTCFullYear(), I = H.getUTCMonth(), n = H.getUTCDate(), D = H.getUTCHours(), y = H.getUTCMinutes(), z = this.startDate !== -Infinity ? this.startDate.getUTCFullYear() : -Infinity, E = this.startDate !== -Infinity ? this.startDate.getUTCMonth() : -Infinity, q = this.endDate !== Infinity ? this.endDate.getUTCFullYear() : Infinity, A = this.endDate !== Infinity ? this.endDate.getUTCMonth() + 1 : Infinity, r = (new h(this.date.getUTCFullYear(), this.date.getUTCMonth(), this.date.getUTCDate())).valueOf(), G = new Date(); this.setTitle(".datetimepicker-days", a[this.language].months[I] + " " + u); if (this.formatViewType == "time") { var k = this.getFormattedDate(); this.setTitle(".datetimepicker-hours", k); this.setTitle(".datetimepicker-minutes", k) } else { this.setTitle(".datetimepicker-hours", n + " " + a[this.language].months[I] + " " + u); this.setTitle(".datetimepicker-minutes", n + " " + a[this.language].months[I] + " " + u) } this.picker.find("tfoot th.today").text(a[this.language].today || a["en"].today).toggle(this.todayBtn !== false); this.picker.find("tfoot th.clear").text(a[this.language].clear || a["en"].clear).toggle(this.clearBtn !== false); this.updateNavArrows(); this.fillMonths(); var K = h(u, I - 1, 28, 0, 0, 0, 0), C = g.getDaysInMonth(K.getUTCFullYear(), K.getUTCMonth()); K.setUTCDate(C); K.setUTCDate(C - (K.getUTCDay() - this.weekStart + 7) % 7); var j = new Date(K); j.setUTCDate(j.getUTCDate() + 42); j = j.valueOf(); var s = []; var v; while (K.valueOf() < j) { if (K.getUTCDay() == this.weekStart) { s.push("<tr>") } v = ""; if (K.getUTCFullYear() < u || (K.getUTCFullYear() == u && K.getUTCMonth() < I)) { v += " old" } else { if (K.getUTCFullYear() > u || (K.getUTCFullYear() == u && K.getUTCMonth() > I)) { v += " new" } } if (this.todayHighlight && K.getUTCFullYear() == G.getFullYear() && K.getUTCMonth() == G.getMonth() && K.getUTCDate() == G.getDate()) { v += " today" } if (K.valueOf() == r) { v += " active" } if ((K.valueOf() + 86400000) <= this.startDate || K.valueOf() > this.endDate || f.inArray(K.getUTCDay(), this.daysOfWeekDisabled) !== -1 || f.inArray(K.toDateString(), this.datesDisabled) !== -1) { v += " disabled" } s.push('<td class="day' + v + '">' + K.getUTCDate() + "</td>"); if (K.getUTCDay() == this.weekEnd) { s.push("</tr>") } K.setUTCDate(K.getUTCDate() + 1) } this.picker.find(".datetimepicker-days tbody").empty().append(s.join("")); s = []; var w = "", F = "", t = ""; var l = this.hoursDisabled || []; for (var B = 0; B < 24; B++) { if (l.indexOf(B) !== -1) { continue } var x = h(u, I, n, B); v = ""; if ((x.valueOf() + 3600000) <= this.startDate || x.valueOf() > this.endDate) { v += " disabled" } else { if (D == B) { v += " active" } } if (this.showMeridian && a[this.language].meridiem.length == 2) { F = (B < 12 ? a[this.language].meridiem[0] : a[this.language].meridiem[1]); if (F != t) { if (t != "") { s.push("</fieldset>") } s.push('<fieldset class="hour"><legend>' + F.toUpperCase() + "</legend>") } t = F; w = (B % 12 ? B % 12 : 12); s.push('<span class="hour' + v + " hour_" + (B < 12 ? "am" : "pm") + '">' + w + "</span>"); if (B == 23) { s.push("</fieldset>") } } else { w = B + ":00"; s.push('<span class="hour' + v + '">' + w + "</span>") } } this.picker.find(".datetimepicker-hours td").html(s.join("")); s = []; w = "", F = "", t = ""; var m = this.minutesDisabled || []; for (var B = 0; B < 60; B += this.minuteStep) { if (m.indexOf(B) !== -1) { continue } var x = h(u, I, n, D, B, 0); v = ""; if (x.valueOf() < this.startDate || x.valueOf() > this.endDate) { v += " disabled" } else { if (Math.floor(y / this.minuteStep) == Math.floor(B / this.minuteStep)) { v += " active" } } if (this.showMeridian && a[this.language].meridiem.length == 2) { F = (D < 12 ? a[this.language].meridiem[0] : a[this.language].meridiem[1]); if (F != t) { if (t != "") { s.push("</fieldset>") } s.push('<fieldset class="minute"><legend>' + F.toUpperCase() + "</legend>") } t = F; w = (D % 12 ? D % 12 : 12); s.push('<span class="minute' + v + '">' + w + ":" + (B < 10 ? "0" + B : B) + "</span>"); if (B == 59) { s.push("</fieldset>") } } else { w = B + ":00"; s.push('<span class="minute' + v + '">' + D + ":" + (B < 10 ? "0" + B : B) + "</span>") } } this.picker.find(".datetimepicker-minutes td").html(s.join("")); var L = this.date.getUTCFullYear(); var p = this.setTitle(".datetimepicker-months", u).end().find("span").removeClass("active"); if (L == u) { var o = p.length - 12; p.eq(this.date.getUTCMonth() + o).addClass("active") } if (u < z || u > q) { p.addClass("disabled") } if (u == z) { p.slice(0, E).addClass("disabled") } if (u == q) { p.slice(A).addClass("disabled") } s = ""; u = parseInt(u / 10, 10) * 10; var J = this.setTitle(".datetimepicker-years", u + "-" + (u + 9)).end().find("td"); u -= 1; for (var B = -1; B < 11; B++) { s += '<span class="year' + (B == -1 || B == 10 ? " old" : "") + (L == u ? " active" : "") + (u < z || u > q ? " disabled" : "") + '">' + u + "</span>"; u += 1 } J.html(s); this.place() }, updateNavArrows: function () { var n = new Date(this.viewDate), l = n.getUTCFullYear(), m = n.getUTCMonth(), k = n.getUTCDate(), j = n.getUTCHours(); switch (this.viewMode) { case 0: if (this.startDate !== -Infinity && l <= this.startDate.getUTCFullYear() && m <= this.startDate.getUTCMonth() && k <= this.startDate.getUTCDate() && j <= this.startDate.getUTCHours()) { this.picker.find(".prev").css({ visibility: "hidden" }) } else { this.picker.find(".prev").css({ visibility: "visible" }) } if (this.endDate !== Infinity && l >= this.endDate.getUTCFullYear() && m >= this.endDate.getUTCMonth() && k >= this.endDate.getUTCDate() && j >= this.endDate.getUTCHours()) { this.picker.find(".next").css({ visibility: "hidden" }) } else { this.picker.find(".next").css({ visibility: "visible" }) } break; case 1: if (this.startDate !== -Infinity && l <= this.startDate.getUTCFullYear() && m <= this.startDate.getUTCMonth() && k <= this.startDate.getUTCDate()) { this.picker.find(".prev").css({ visibility: "hidden" }) } else { this.picker.find(".prev").css({ visibility: "visible" }) } if (this.endDate !== Infinity && l >= this.endDate.getUTCFullYear() && m >= this.endDate.getUTCMonth() && k >= this.endDate.getUTCDate()) { this.picker.find(".next").css({ visibility: "hidden" }) } else { this.picker.find(".next").css({ visibility: "visible" }) } break; case 2: if (this.startDate !== -Infinity && l <= this.startDate.getUTCFullYear() && m <= this.startDate.getUTCMonth()) { this.picker.find(".prev").css({ visibility: "hidden" }) } else { this.picker.find(".prev").css({ visibility: "visible" }) } if (this.endDate !== Infinity && l >= this.endDate.getUTCFullYear() && m >= this.endDate.getUTCMonth()) { this.picker.find(".next").css({ visibility: "hidden" }) } else { this.picker.find(".next").css({ visibility: "visible" }) } break; case 3: case 4: if (this.startDate !== -Infinity && l <= this.startDate.getUTCFullYear()) { this.picker.find(".prev").css({ visibility: "hidden" }) } else { this.picker.find(".prev").css({ visibility: "visible" }) } if (this.endDate !== Infinity && l >= this.endDate.getUTCFullYear()) { this.picker.find(".next").css({ visibility: "hidden" }) } else { this.picker.find(".next").css({ visibility: "visible" }) } break } }, mousewheel: function (k) { k.preventDefault(); k.stopPropagation(); if (this.wheelPause) { return } this.wheelPause = true; var j = k.originalEvent; var m = j.wheelDelta; var l = m > 0 ? 1 : (m === 0) ? 0 : -1; if (this.wheelViewModeNavigationInverseDirection) { l = -l } this.showMode(l); setTimeout(f.proxy(function () { this.wheelPause = false }, this), this.wheelViewModeNavigationDelay) }, click: function (n) { n.stopPropagation(); n.preventDefault(); var o = f(n.target).closest("span, td, th, legend"); if (o.is("." + this.icontype)) { o = f(o).parent().closest("span, td, th, legend") } if (o.length == 1) { if (o.is(".disabled")) { this.element.trigger({ type: "outOfRange", date: this.viewDate, startDate: this.startDate, endDate: this.endDate }); return } switch (o[0].nodeName.toLowerCase()) { case "th": switch (o[0].className) { case "switch": this.showMode(1); break; case "prev": case "next": var j = g.modes[this.viewMode].navStep * (o[0].className == "prev" ? -1 : 1); switch (this.viewMode) { case 0: this.viewDate = this.moveHour(this.viewDate, j); break; case 1: this.viewDate = this.moveDate(this.viewDate, j); break; case 2: this.viewDate = this.moveMonth(this.viewDate, j); break; case 3: case 4: this.viewDate = this.moveYear(this.viewDate, j); break }this.fill(); this.element.trigger({ type: o[0].className + ":" + this.convertViewModeText(this.viewMode), date: this.viewDate, startDate: this.startDate, endDate: this.endDate }); break; case "clear": this.reset(); if (this.autoclose) { this.hide() } break; case "today": var k = new Date(); k = h(k.getFullYear(), k.getMonth(), k.getDate(), k.getHours(), k.getMinutes(), k.getSeconds(), 0); if (k < this.startDate) { k = this.startDate } else { if (k > this.endDate) { k = this.endDate } } this.viewMode = this.startViewMode; this.showMode(0); this._setDate(k); this.fill(); if (this.autoclose) { this.hide() } break }break; case "span": if (!o.is(".disabled")) { var q = this.viewDate.getUTCFullYear(), p = this.viewDate.getUTCMonth(), r = this.viewDate.getUTCDate(), s = this.viewDate.getUTCHours(), l = this.viewDate.getUTCMinutes(), t = this.viewDate.getUTCSeconds(); if (o.is(".month")) { this.viewDate.setUTCDate(1); p = o.parent().find("span").index(o); r = this.viewDate.getUTCDate(); this.viewDate.setUTCMonth(p); this.element.trigger({ type: "changeMonth", date: this.viewDate }); if (this.viewSelect >= 3) { this._setDate(h(q, p, r, s, l, t, 0)) } } else { if (o.is(".year")) { this.viewDate.setUTCDate(1); q = parseInt(o.text(), 10) || 0; this.viewDate.setUTCFullYear(q); this.element.trigger({ type: "changeYear", date: this.viewDate }); if (this.viewSelect >= 4) { this._setDate(h(q, p, r, s, l, t, 0)) } } else { if (o.is(".hour")) { s = parseInt(o.text(), 10) || 0; if (o.hasClass("hour_am") || o.hasClass("hour_pm")) { if (s == 12 && o.hasClass("hour_am")) { s = 0 } else { if (s != 12 && o.hasClass("hour_pm")) { s += 12 } } } this.viewDate.setUTCHours(s); this.element.trigger({ type: "changeHour", date: this.viewDate }); if (this.viewSelect >= 1) { this._setDate(h(q, p, r, s, l, t, 0)) } } else { if (o.is(".minute")) { l = parseInt(o.text().substr(o.text().indexOf(":") + 1), 10) || 0; this.viewDate.setUTCMinutes(l); this.element.trigger({ type: "changeMinute", date: this.viewDate }); if (this.viewSelect >= 0) { this._setDate(h(q, p, r, s, l, t, 0)) } } } } } if (this.viewMode != 0) { var m = this.viewMode; this.showMode(-1); this.fill(); if (m == this.viewMode && this.autoclose) { this.hide() } } else { this.fill(); if (this.autoclose) { this.hide() } } } break; case "td": if (o.is(".day") && !o.is(".disabled")) { var r = parseInt(o.text(), 10) || 1; var q = this.viewDate.getUTCFullYear(), p = this.viewDate.getUTCMonth(), s = this.viewDate.getUTCHours(), l = this.viewDate.getUTCMinutes(), t = this.viewDate.getUTCSeconds(); if (o.is(".old")) { if (p === 0) { p = 11; q -= 1 } else { p -= 1 } } else { if (o.is(".new")) { if (p == 11) { p = 0; q += 1 } else { p += 1 } } } this.viewDate.setUTCFullYear(q); this.viewDate.setUTCMonth(p, r); this.element.trigger({ type: "changeDay", date: this.viewDate }); if (this.viewSelect >= 2) { this._setDate(h(q, p, r, s, l, t, 0)) } } var m = this.viewMode; this.showMode(-1); this.fill(); if (m == this.viewMode && this.autoclose) { this.hide() } break } } }, _setDate: function (j, l) { if (!l || l == "date") { this.date = j } if (!l || l == "view") { this.viewDate = j } this.fill(); this.setValue(); var k; if (this.isInput) { k = this.element } else { if (this.component) { k = this.element.find("input") } } if (k) { k.change(); if (this.autoclose && (!l || l == "date")) { } } this.element.trigger({ type: "changeDate", date: this.getDate() }); if (j == null) { this.date = this.viewDate } }, moveMinute: function (k, j) { if (!j) { return k } var l = new Date(k.valueOf()); l.setUTCMinutes(l.getUTCMinutes() + (j * this.minuteStep)); return l }, moveHour: function (k, j) { if (!j) { return k } var l = new Date(k.valueOf()); l.setUTCHours(l.getUTCHours() + j); return l }, moveDate: function (k, j) { if (!j) { return k } var l = new Date(k.valueOf()); l.setUTCDate(l.getUTCDate() + j); return l }, moveMonth: function (j, k) { if (!k) { return j } var n = new Date(j.valueOf()), r = n.getUTCDate(), o = n.getUTCMonth(), m = Math.abs(k), q, p; k = k > 0 ? 1 : -1; if (m == 1) { p = k == -1 ? function () { return n.getUTCMonth() == o } : function () { return n.getUTCMonth() != q }; q = o + k; n.setUTCMonth(q); if (q < 0 || q > 11) { q = (q + 12) % 12 } } else { for (var l = 0; l < m; l++) { n = this.moveMonth(n, k) } q = n.getUTCMonth(); n.setUTCDate(r); p = function () { return q != n.getUTCMonth() } } while (p()) { n.setUTCDate(--r); n.setUTCMonth(q) } return n }, moveYear: function (k, j) { return this.moveMonth(k, j * 12) }, dateWithinRange: function (j) { return j >= this.startDate && j <= this.endDate }, keydown: function (n) { if (this.picker.is(":not(:visible)")) { if (n.keyCode == 27) { this.show() } return } var p = false, k, q, o, r, j; switch (n.keyCode) { case 27: this.hide(); n.preventDefault(); break; case 37: case 39: if (!this.keyboardNavigation) { break } k = n.keyCode == 37 ? -1 : 1; viewMode = this.viewMode; if (n.ctrlKey) { viewMode += 2 } else { if (n.shiftKey) { viewMode += 1 } } if (viewMode == 4) { r = this.moveYear(this.date, k); j = this.moveYear(this.viewDate, k) } else { if (viewMode == 3) { r = this.moveMonth(this.date, k); j = this.moveMonth(this.viewDate, k) } else { if (viewMode == 2) { r = this.moveDate(this.date, k); j = this.moveDate(this.viewDate, k) } else { if (viewMode == 1) { r = this.moveHour(this.date, k); j = this.moveHour(this.viewDate, k) } else { if (viewMode == 0) { r = this.moveMinute(this.date, k); j = this.moveMinute(this.viewDate, k) } } } } } if (this.dateWithinRange(r)) { this.date = r; this.viewDate = j; this.setValue(); this.update(); n.preventDefault(); p = true } break; case 38: case 40: if (!this.keyboardNavigation) { break } k = n.keyCode == 38 ? -1 : 1; viewMode = this.viewMode; if (n.ctrlKey) { viewMode += 2 } else { if (n.shiftKey) { viewMode += 1 } } if (viewMode == 4) { r = this.moveYear(this.date, k); j = this.moveYear(this.viewDate, k) } else { if (viewMode == 3) { r = this.moveMonth(this.date, k); j = this.moveMonth(this.viewDate, k) } else { if (viewMode == 2) { r = this.moveDate(this.date, k * 7); j = this.moveDate(this.viewDate, k * 7) } else { if (viewMode == 1) { if (this.showMeridian) { r = this.moveHour(this.date, k * 6); j = this.moveHour(this.viewDate, k * 6) } else { r = this.moveHour(this.date, k * 4); j = this.moveHour(this.viewDate, k * 4) } } else { if (viewMode == 0) { r = this.moveMinute(this.date, k * 4); j = this.moveMinute(this.viewDate, k * 4) } } } } } if (this.dateWithinRange(r)) { this.date = r; this.viewDate = j; this.setValue(); this.update(); n.preventDefault(); p = true } break; case 13: if (this.viewMode != 0) { var m = this.viewMode; this.showMode(-1); this.fill(); if (m == this.viewMode && this.autoclose) { this.hide() } } else { this.fill(); if (this.autoclose) { this.hide() } } n.preventDefault(); break; case 9: this.hide(); break }if (p) { var l; if (this.isInput) { l = this.element } else { if (this.component) { l = this.element.find("input") } } if (l) { l.change() } this.element.trigger({ type: "changeDate", date: this.getDate() }) } }, showMode: function (j) { if (j) { var k = Math.max(0, Math.min(g.modes.length - 1, this.viewMode + j)); if (k >= this.minView && k <= this.maxView) { this.element.trigger({ type: "changeMode", date: this.viewDate, oldViewMode: this.viewMode, newViewMode: k }); this.viewMode = k } } this.picker.find(">div").hide().filter(".datetimepicker-" + g.modes[this.viewMode].clsName).css("display", "block"); this.updateNavArrows() }, reset: function (j) { this._setDate(null, "date") }, convertViewModeText: function (j) { switch (j) { case 4: return "decade"; case 3: return "year"; case 2: return "month"; case 1: return "day"; case 0: return "hour" } } }; var b = f.fn.datetimepicker; f.fn.datetimepicker = function (l) { var j = Array.apply(null, arguments); j.shift(); var k; this.each(function () { var o = f(this), n = o.data("datetimepicker"), m = typeof l == "object" && l; if (!n) { o.data("datetimepicker", (n = new i(this, f.extend({}, f.fn.datetimepicker.defaults, m)))) } if (typeof l == "string" && typeof n[l] == "function") { k = n[l].apply(n, j); if (k !== c) { return false } } }); if (k !== c) { return k } else { return this } }; f.fn.datetimepicker.defaults = {}; f.fn.datetimepicker.Constructor = i; var a = f.fn.datetimepicker.dates = { en: { days: ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六", "星期日"], daysShort: ["日", "一", "二", "三", "四", "五", "六", "日"], daysMin: ["日", "一", "二", "三", "四", "五", "六", "日"], months: ["一月", "二月", "三月", "四月", "五月", "六月", "七月", "八月", "九月", "十月", "十一月", "十二月"], monthsShort: ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"], meridiem: ["上午", "下午"], suffix: ["st", "nd", "rd", "th"], today: "今天", clear: "清空" } }; var g = { modes: [{ clsName: "minutes", navFnc: "Hours", navStep: 1 }, { clsName: "hours", navFnc: "Date", navStep: 1 }, { clsName: "days", navFnc: "Month", navStep: 1 }, { clsName: "months", navFnc: "FullYear", navStep: 1 }, { clsName: "years", navFnc: "FullYear", navStep: 10 }], isLeapYear: function (j) { return (((j % 4 === 0) && (j % 100 !== 0)) || (j % 400 === 0)) }, getDaysInMonth: function (j, k) { return [31, (g.isLeapYear(j) ? 29 : 28), 31, 30, 31, 30, 31, 31, 30, 31, 30, 31][k] }, getDefaultFormat: function (j, k) { if (j == "standard") { if (k == "input") { return "yyyy-mm-dd hh:ii" } else { return "yyyy-mm-dd hh:ii:ss" } } else { if (j == "php") { if (k == "input") { return "Y-m-d H:i" } else { return "Y-m-d H:i:s" } } else { throw new Error("Invalid format type.") } } }, validParts: function (j) { if (j == "standard") { return /t|hh?|HH?|p|P|z|Z|ii?|ss?|dd?|DD?|mm?|MM?|yy(?:yy)?/g } else { if (j == "php") { return /[dDjlNwzFmMnStyYaABgGhHis]/g } else { throw new Error("Invalid format type.") } } }, nonpunctuation: /[^ -\/:-@\[-`{-~\t\n\rTZ]+/g, parseFormat: function (m, k) { var j = m.replace(this.validParts(k), "\0").split("\0"), l = m.match(this.validParts(k)); if (!j || !j.length || !l || l.length == 0) { throw new Error("Invalid date format.") } return { separators: j, parts: l } }, parseDate: function (A, y, v, j, r) { if (A instanceof Date) { var u = new Date(A.valueOf() - A.getTimezoneOffset() * 60000); u.setMilliseconds(0); return u } if (/^\d{4}\-\d{1,2}\-\d{1,2}$/.test(A)) { y = this.parseFormat("yyyy-mm-dd", j) } if (/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}$/.test(A)) { y = this.parseFormat("yyyy-mm-dd hh:ii", j) } if (/^\d{4}\-\d{1,2}\-\d{1,2}[T ]\d{1,2}\:\d{1,2}\:\d{1,2}[Z]{0,1}$/.test(A)) { y = this.parseFormat("yyyy-mm-dd hh:ii:ss", j) } if (/^[-+]\d+[dmwy]([\s,]+[-+]\d+[dmwy])*$/.test(A)) { var l = /([-+]\d+)([dmwy])/, q = A.match(/([-+]\d+)([dmwy])/g), t, p; A = new Date(); for (var x = 0; x < q.length; x++) { t = l.exec(q[x]); p = parseInt(t[1]); switch (t[2]) { case "d": A.setUTCDate(A.getUTCDate() + p); break; case "m": A = i.prototype.moveMonth.call(i.prototype, A, p); break; case "w": A.setUTCDate(A.getUTCDate() + p * 7); break; case "y": A = i.prototype.moveYear.call(i.prototype, A, p); break } } return h(A.getUTCFullYear(), A.getUTCMonth(), A.getUTCDate(), A.getUTCHours(), A.getUTCMinutes(), A.getUTCSeconds(), 0) } var q = A && A.toString().match(this.nonpunctuation) || [], A = new Date(0, 0, 0, 0, 0, 0, 0), m = {}, z = ["hh", "h", "ii", "i", "ss", "s", "yyyy", "yy", "M", "MM", "m", "mm", "D", "DD", "d", "dd", "H", "HH", "p", "P", "z", "Z"], o = { hh: function (C, s) { return C.setUTCHours(s) }, h: function (C, s) { return C.setUTCHours(s) }, HH: function (C, s) { return C.setUTCHours(s == 12 ? 0 : s) }, H: function (C, s) { return C.setUTCHours(s == 12 ? 0 : s) }, ii: function (C, s) { return C.setUTCMinutes(s) }, i: function (C, s) { return C.setUTCMinutes(s) }, ss: function (C, s) { return C.setUTCSeconds(s) }, s: function (C, s) { return C.setUTCSeconds(s) }, yyyy: function (C, s) { return C.setUTCFullYear(s) }, yy: function (C, s) { return C.setUTCFullYear(2000 + s) }, m: function (C, s) { s -= 1; while (s < 0) { s += 12 } s %= 12; C.setUTCMonth(s); while (C.getUTCMonth() != s) { if (isNaN(C.getUTCMonth())) { return C } else { C.setUTCDate(C.getUTCDate() - 1) } } return C }, d: function (C, s) { return C.setUTCDate(s) }, p: function (C, s) { return C.setUTCHours(s == 1 ? C.getUTCHours() + 12 : C.getUTCHours()) }, z: function () { return r } }, B, k, t; o["M"] = o["MM"] = o["mm"] = o["m"]; o["dd"] = o["d"]; o["P"] = o["p"]; o["Z"] = o["z"]; A = h(A.getFullYear(), A.getMonth(), A.getDate(), A.getHours(), A.getMinutes(), A.getSeconds()); if (q.length == y.parts.length) { for (var x = 0, w = y.parts.length; x < w; x++) { B = parseInt(q[x], 10); t = y.parts[x]; if (isNaN(B)) { switch (t) { case "MM": k = f(a[v].months).filter(function () { var s = this.slice(0, q[x].length), C = q[x].slice(0, s.length); return s == C }); B = f.inArray(k[0], a[v].months) + 1; break; case "M": k = f(a[v].monthsShort).filter(function () { var s = this.slice(0, q[x].length), C = q[x].slice(0, s.length); return s.toLowerCase() == C.toLowerCase() }); B = f.inArray(k[0], a[v].monthsShort) + 1; break; case "p": case "P": B = f.inArray(q[x].toLowerCase(), a[v].meridiem); break; case "z": case "Z": r; break } } m[t] = B } for (var x = 0, n; x < z.length; x++) { n = z[x]; if (n in m && !isNaN(m[n])) { o[n](A, m[n]) } } } return A }, formatDate: function (l, q, m, p, o) { if (l == null) { return "" } var k; if (p == "standard") { k = { t: l.getTime(), yy: l.getUTCFullYear().toString().substring(2), yyyy: l.getUTCFullYear(), m: l.getUTCMonth() + 1, M: a[m].monthsShort[l.getUTCMonth()], MM: a[m].months[l.getUTCMonth()], d: l.getUTCDate(), D: a[m].daysShort[l.getUTCDay()], DD: a[m].days[l.getUTCDay()], p: (a[m].meridiem.length == 2 ? a[m].meridiem[l.getUTCHours() < 12 ? 0 : 1] : ""), h: l.getUTCHours(), i: l.getUTCMinutes(), s: l.getUTCSeconds(), z: o }; if (a[m].meridiem.length == 2) { k.H = (k.h % 12 == 0 ? 12 : k.h % 12) } else { k.H = k.h } k.HH = (k.H < 10 ? "0" : "") + k.H; k.P = k.p.toUpperCase(); k.Z = k.z; k.hh = (k.h < 10 ? "0" : "") + k.h; k.ii = (k.i < 10 ? "0" : "") + k.i; k.ss = (k.s < 10 ? "0" : "") + k.s; k.dd = (k.d < 10 ? "0" : "") + k.d; k.mm = (k.m < 10 ? "0" : "") + k.m } else { if (p == "php") { k = { y: l.getUTCFullYear().toString().substring(2), Y: l.getUTCFullYear(), F: a[m].months[l.getUTCMonth()], M: a[m].monthsShort[l.getUTCMonth()], n: l.getUTCMonth() + 1, t: g.getDaysInMonth(l.getUTCFullYear(), l.getUTCMonth()), j: l.getUTCDate(), l: a[m].days[l.getUTCDay()], D: a[m].daysShort[l.getUTCDay()], w: l.getUTCDay(), N: (l.getUTCDay() == 0 ? 7 : l.getUTCDay()), S: (l.getUTCDate() % 10 <= a[m].suffix.length ? a[m].suffix[l.getUTCDate() % 10 - 1] : ""), a: (a[m].meridiem.length == 2 ? a[m].meridiem[l.getUTCHours() < 12 ? 0 : 1] : ""), g: (l.getUTCHours() % 12 == 0 ? 12 : l.getUTCHours() % 12), G: l.getUTCHours(), i: l.getUTCMinutes(), s: l.getUTCSeconds() }; k.m = (k.n < 10 ? "0" : "") + k.n; k.d = (k.j < 10 ? "0" : "") + k.j; k.A = k.a.toString().toUpperCase(); k.h = (k.g < 10 ? "0" : "") + k.g; k.H = (k.G < 10 ? "0" : "") + k.G; k.i = (k.i < 10 ? "0" : "") + k.i; k.s = (k.s < 10 ? "0" : "") + k.s } else { throw new Error("Invalid format type.") } } var l = [], r = f.extend([], q.separators); for (var n = 0, j = q.parts.length; n < j; n++) { if (r.length) { l.push(r.shift()) } l.push(k[q.parts[n]]) } if (r.length) { l.push(r.shift()) } return l.join("") }, convertViewMode: function (j) { switch (j) { case 4: case "decade": j = 4; break; case 3: case "year": j = 3; break; case 2: case "month": j = 2; break; case 1: case "day": j = 1; break; case 0: case "hour": j = 0; break }return j }, headTemplate: "<thead>" + "<tr>" + '<th class="prev"><i class="{iconType} {leftArrow}"/></th>' + '<th colspan="5" class="switch"></th>' + '<th class="next"><i class="{iconType} {rightArrow}"/></th>' + "</tr>" + "</thead>", headTemplateV3: "<thead>" + "<tr>" + '<th class="prev"><span class="{iconType} {leftArrow}"></span> </th>' + '<th colspan="5" class="switch"></th>' + '<th class="next"><span class="{iconType} {rightArrow}"></span> </th>' + "</tr>" + "</thead>", contTemplate: '<tbody><tr><td colspan="7"></td></tr></tbody>', footTemplate: "<tfoot>" + '<tr><th colspan="7" class="today"></th></tr>' + '<tr><th colspan="7" class="clear"></th></tr>' + "</tfoot>" }; g.template = '<div class="datetimepicker">' + '<div class="datetimepicker-minutes">' + '<table class=" table-condensed">' + g.headTemplate + g.contTemplate + g.footTemplate + "</table>" + "</div>" + '<div class="datetimepicker-hours">' + '<table class=" table-condensed">' + g.headTemplate + g.contTemplate + g.footTemplate + "</table>" + "</div>" + '<div class="datetimepicker-days">' + '<table class=" table-condensed">' + g.headTemplate + "<tbody></tbody>" + g.footTemplate + "</table>" + "</div>" + '<div class="datetimepicker-months">' + '<table class="table-condensed">' + g.headTemplate + g.contTemplate + g.footTemplate + "</table>" + "</div>" + '<div class="datetimepicker-years">' + '<table class="table-condensed">' + g.headTemplate + g.contTemplate + g.footTemplate + "</table>" + "</div>" + "</div>"; g.templateV3 = '<div class="datetimepicker">' + '<div class="datetimepicker-minutes">' + '<table class=" table-condensed">' + g.headTemplateV3 + g.contTemplate + g.footTemplate + "</table>" + "</div>" + '<div class="datetimepicker-hours">' + '<table class=" table-condensed">' + g.headTemplateV3 + g.contTemplate + g.footTemplate + "</table>" + "</div>" + '<div class="datetimepicker-days">' + '<table class=" table-condensed">' + g.headTemplateV3 + "<tbody></tbody>" + g.footTemplate + "</table>" + "</div>" + '<div class="datetimepicker-months">' + '<table class="table-condensed">' + g.headTemplateV3 + g.contTemplate + g.footTemplate + "</table>" + "</div>" + '<div class="datetimepicker-years">' + '<table class="table-condensed">' + g.headTemplateV3 + g.contTemplate + g.footTemplate + "</table>" + "</div>" + "</div>"; f.fn.datetimepicker.DPGlobal = g; f.fn.datetimepicker.noConflict = function () { f.fn.datetimepicker = b; return this }; f(document).on("focus.datetimepicker.data-api click.datetimepicker.data-api", '[data-provide="datetimepicker"]', function (k) { var j = f(this); if (j.data("datetimepicker")) { return } k.preventDefault(); j.datetimepicker("show") }); f(function () { f('[data-provide="datetimepicker-inline"]').datetimepicker() }) })); !function (a) { a.fn["bootstrapSwitch"] = function (c) { var b = { init: function () { return this.each(function () { var p = a(this), m, f, l, o, h = "", g = p.attr("class"), i, d, j = "ON", e = "OFF", n = false; a.each(["size-MINI", "size-S", "size-L"], function (q, r) { if (g.indexOf(r) >= 0) { h = r } }); p.addClass("has-switch"); if (p.data("on") !== undefined) { i = "switch-" + p.data("on") } if (p.data("on-label") !== undefined) { j = p.data("on-label") } if (p.data("off-label") !== undefined) { e = p.data("off-label") } if (p.data("icon") !== undefined) { n = p.data("icon") } f = a("<span>").addClass("switch-left").addClass(h).addClass(i).html(j); i = ""; if (p.data("off") !== undefined) { i = "switch-" + p.data("off") } l = a("<span>").addClass("switch-right").addClass(h).addClass(i).html(e); o = a("<label>").html("&nbsp;").addClass(h).attr("for", p.find("input").attr("id")); if (n) { o.html('<i class="icon icon-' + n + '"></i>') } m = p.find(":checkbox").wrap(a("<div>")).parent().data("animated", false); if (p.data("animated") !== false) { m.addClass("switch-animate").data("animated", true) } m.append(f).append(o).append(l); p.find(">div").addClass(p.find("input").is(":checked") ? "switch-on" : "switch-off"); if (p.find("input").is(":disabled")) { a(this).addClass("deactivate") } var k = function (q) { q.siblings("label").trigger("mousedown").trigger("mouseup").trigger("click") }; p.on("keydown", function (q) { if (q.keyCode === 32) { q.stopImmediatePropagation(); q.preventDefault(); k(a(q.target).find("span:first")) } }); f.on("click", function (q) { k(a(this)) }); l.on("click", function (q) { k(a(this)) }); p.find("input").on("change", function (u) { var t = a(this), r = t.parent(), q = t.is(":checked"), s = r.is(".switch-off"); u.preventDefault(); r.css("left", ""); if (s === q) { if (q) { r.removeClass("switch-off").addClass("switch-on") } else { r.removeClass("switch-on").addClass("switch-off") } if (r.data("animated") !== false) { r.addClass("switch-animate") } r.parent().trigger("switch-change", { "el": t, "value": q }) } }); p.find("label").on("mousedown touchstart", function (r) { var q = a(this); d = false; r.preventDefault(); r.stopImmediatePropagation(); q.closest("div").removeClass("switch-animate"); if (q.closest(".has-switch").is(".deactivate")) { q.unbind("click") } else { q.on("mousemove touchmove", function (x) { var s = a(this).closest(".switch"), u = (x.pageX || x.originalEvent.targetTouches[0].pageX) - s.offset().left, v = (u / s.width()) * 100, w = 25, t = 75; d = true; if (v < w) { v = w } else { if (v > t) { v = t } } s.find(">div").css("left", (v - t) + "%") }); q.on("click touchend", function (u) { var t = a(this), s = a(u.target), v = s.siblings("input"); u.stopImmediatePropagation(); u.preventDefault(); t.unbind("mouseleave"); if (d) { v.prop("checked", !(parseInt(t.parent().css("left")) < -25)) } else { v.prop("checked", !v.is(":checked")) } d = false; v.trigger("change") }); q.on("mouseleave", function (t) { var s = a(this), u = s.siblings("input"); t.preventDefault(); t.stopImmediatePropagation(); s.unbind("mouseleave"); s.trigger("mouseup"); u.prop("checked", !(parseInt(s.parent().css("left")) < -25)).trigger("change") }); q.on("mouseup", function (s) { s.stopImmediatePropagation(); s.preventDefault(); a(this).unbind("mousemove") }) } }) }) }, toggleActivation: function () { a(this).toggleClass("deactivate") }, isActive: function () { return !a(this).hasClass("deactivate") }, setActive: function (d) { if (d) { a(this).removeClass("deactivate") } else { a(this).addClass("deactivate") } }, toggleState: function (d) { var e = a(this).find("input:checkbox"); e.prop("checked", !e.is(":checked")).trigger("change", d) }, setState: function (e, d) { a(this).find("input:checkbox").prop("checked", e).trigger("change", d) }, status: function () { return a(this).find("input:checkbox").is(":checked") }, destroy: function () { var d = a(this).find("div"), e; d.find(":not(input:checkbox)").remove(); e = d.children(); e.unwrap().unwrap(); e.unbind("change"); return e } }; if (b[c]) { return b[c].apply(this, Array.prototype.slice.call(arguments, 1)) } else { if (typeof c === "object" || !c) { return b.init.apply(this, arguments) } else { a.error("Method " + c + " does not exist!") } } } }(jQuery); $(function () { $(".switch")["bootstrapSwitch"]() });