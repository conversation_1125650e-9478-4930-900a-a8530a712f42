/**
 * @license Highcharts JS v5.0.6 (2016-12-07)
 * Plugin for displaying a message when there is no data visible in chart.
 *
 * (c) 2010-2016 Highsoft AS
 * Author: Oystein Moseng
 *
 * License: www.highcharts.com/license
 */
(function(factory) {
    if (typeof module === 'object' && module.exports) {
        module.exports = factory;
    } else {
        factory(Highcharts);
    }
}(function(Highcharts) {
    (function(H) {
        /**
         * Plugin for displaying a message when there is no data visible in chart.
         *
         * (c) 2010-2016 Highsoft AS
         * Author: Oystein Moseng
         *
         * License: www.highcharts.com/license
         */
        'use strict';

        var seriesTypes = H.seriesTypes,
            chartPrototype = H.Chart.prototype,
            defaultOptions = H.getOptions(),
            extend = H.extend,
            each = H.each;

        // Add language option
        extend(defaultOptions.lang, {
            noData: 'No data to display'
        });

        // Add default display options for message
        defaultOptions.noData = {
            position: {
                x: 0,
                y: 0,
                align: 'center',
                verticalAlign: 'middle'
            }
            // useHTML: false
        };



        /**
         * Define hasData functions for series. These return true if there are data points on this series within the plot area
         */
        function hasDataPie() {
            return !!this.points.length; /* != 0 */
        }

        each(['pie', 'gauge', 'waterfall', 'bubble', 'treemap'], function(type) {
            if (seriesTypes[type]) {
                seriesTypes[type].prototype.hasData = hasDataPie;
            }
        });

        H.Series.prototype.hasData = function() {
            return this.visible && this.dataMax !== undefined && this.dataMin !== undefined; // #3703
        };

        /**
         * Display a no-data message.
         *
         * @param {String} str An optional message to show in place of the default one 
         */
        chartPrototype.showNoData = function(str) {
            var chart = this,
                options = chart.options,
                text = str || options.lang.noData,
                noDataOptions = options.noData;

            if (!chart.noDataLabel) {
                chart.noDataLabel = chart.renderer
                    .label(
                        text,
                        0,
                        0,
                        null,
                        null,
                        null,
                        noDataOptions.useHTML,
                        null,
                        'no-data'
                    );



                chart.noDataLabel.add();

                chart.noDataLabel.align(extend(chart.noDataLabel.getBBox(), noDataOptions.position), false, 'plotBox');
            }
        };

        /**
         * Hide no-data message	
         */
        chartPrototype.hideNoData = function() {
            var chart = this;
            if (chart.noDataLabel) {
                chart.noDataLabel = chart.noDataLabel.destroy();
            }
        };

        /**
         * Returns true if there are data points within the plot area now
         */
        chartPrototype.hasData = function() {
            var chart = this,
                series = chart.series,
                i = series.length;

            while (i--) {
                if (series[i].hasData() && !series[i].options.isInternal) {
                    return true;
                }
            }

            return false;
        };

        /**
         * Show no-data message if there is no data in sight. Otherwise, hide it.
         */
        function handleNoData() {
            var chart = this;
            if (chart.hasData()) {
                chart.hideNoData();
            } else {
                chart.showNoData();
            }
        }

        /**
         * Add event listener to handle automatic display of no-data message
         */
        chartPrototype.callbacks.push(function(chart) {
            H.addEvent(chart, 'load', handleNoData);
            H.addEvent(chart, 'redraw', handleNoData);
        });

    }(Highcharts));
}));
