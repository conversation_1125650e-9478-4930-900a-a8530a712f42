$(function () {
    /*
    * 为了保持代码整洁和示例简单，这个文件
    * 只包含插件配置和回调。
    * 
    * UI functions ui_* 可设于: demo-ui.js
    */
    $('#drag-and-drop-zone').dmUploader({ //
        url: fileurl,
        // url: 'http://172.16.24.39',
     crossDomain: true,
        maxFileSize: 5120 * 1024 * 1024, // 3 Megs
        extFilter: ['mp4','m3u8'],
        onFileSizeError: function (file) {
            layer.msg(' 文件名: ' + file.name + '<br> 不能添加: 尺寸超出限制', function () { });
        },
        onFileExtError: function (file) {
            layer.msg(' 文件名: ' + file.name + '<br> 不能添加: 后缀不合法', function () { });
        },
        onDragEnter: function () {
            // 当拖动文件到DnD区域时发生
            this.addClass('active');
        },
        onDragLeave: function () {
            // 当拖出DnD区域时发生
            this.removeClass('active');
        },
        onInit: function () {
            // 插件可以使用了
            ui_add_log('Penguin initialized :)', 'info');
        },
        onComplete: function () {
            // 处理队列中的所有文件 (success or error)
            ui_add_log('所有未完成的传输完成');
        },
        onNewFile: function (id, file) {
            // 当使用文件选择器或DnD区域添加新文件时
            ui_add_log('添加新文件 #' + id);
            ui_multi_add_file(id, file);
        },
        onBeforeUpload: function (id) {
            // 开始上传一个文件
            $('#submit').removeClass('btn-primary').addClass('disabled').val('上传中...');
            $('#submit').attr('disabled', true);
            ui_add_log('开始上传 #' + id);
            ui_multi_update_file_status(id, '上传', '上传中...');
            ui_multi_update_file_progress(id, 0, '', true);
        },
        onUploadCanceled: function (id) {
            // 当用户直接取消文件时
            ui_multi_update_file_status(id, 'warning', '用户取消');
            ui_multi_update_file_progress(id, 0, 'warning', false);
        },
        onUploadProgress: function (id, percent) {
            // 更新文件的进展
            ui_multi_update_file_progress(id, percent);
        },
        onUploadSuccess: function (id, data) {
            // 文件上传成功
            if (data.status != 'error') {
                // var json = JSON.parse(data);
                console.log(data)
                var json = data;
                $('#url').val(json.path);
                $('#m3u8').val(json.m3u8path);
                $('#video_test').attr('src', json.path);
                ui_add_log('Server Response for file #' + id + ': ' + JSON.stringify(data));
                ui_add_log('Upload of file #' + id + ' COMPLETED', 'success');
                ui_multi_update_file_status(id, 'success', '上传完成');
                ui_multi_update_file_progress(id, 100, 'success', false);
            } else {
                ui_multi_update_file_status(id, 'error', "<span style=\"color: red;\">" + data.message + "</span>");
            }
            $('#submit').removeClass('disabled').addClass('btn-primary').val('提交');
            $('#submit').attr('disabled', false);
        },
        onUploadError: function (id, xhr, status, message) {
            ui_multi_update_file_status(id, 'danger', message);
            ui_multi_update_file_progress(id, 0, 'danger', false);
        },
        onFallbackMode: function () {
            // 当浏览器不支持这个插件时 :(
            layer.msg('浏览器不支持这个插件');
            ui_add_log('插件不能在这里使用，运行回调', 'danger');
        },
    });
});