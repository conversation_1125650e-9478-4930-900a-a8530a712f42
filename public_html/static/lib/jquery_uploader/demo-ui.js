/*
* 一些帮助函数可以与UI一起工作，并保持代码的整洁
*/

// 向调试区域添加一个条目
function ui_add_log(message, color) {
    var d = new Date();

    var dateString = (('0' + d.getHours())).slice(-2) + ':' +
        (('0' + d.getMinutes())).slice(-2) + ':' +
        (('0' + d.getSeconds())).slice(-2);

    color = (typeof color === 'undefined' ? 'muted' : color);

    var template = $('#debug-template').text();
    template = template.replace('%%date%%', dateString);
    template = template.replace('%%message%%', message);
    template = template.replace('%%color%%', color);

    $('#debug').find('li.empty').fadeOut(); // remove the 'no messages yet'
    $('#debug').prepend(template);
}

// 创建一个新文件并将其添加到列表中
function ui_multi_add_file(id, file) {
    var template = "<li class=\"media\">\
            <div class=\"media-body mb-1\" >\
                <p class=\"mb-2\">\
                    <strong>"+ file.name+"</strong> - 状态: <span class=\"text-muted\">等待...</span>\
                </p>\
                <div class=\"mb-2\">\
                    <div class=\"progress-bar progress-bar-striped progress-bar-animated bg-primary\" role=\"progressbar\" style=\"width: 0%\" aria-valuenow=\"0\" aria-valuemin=\"0\" aria-valuemax=\"100\"></div>\
                </div>\
                <hr class=\"mt-1 mb-1\" />\
            </div >\
        </li >";
    template = $(template);
    template.prop('id', 'uploaderFile' + id);
    template.data('file-id', id);

    $('#files').find('li.empty').fadeOut(); // remove the 'no files yet'
    $('#files').html(template);
}

// 更改列表中的状态消息
function ui_multi_update_file_status(id, status, message) {
    $('#uploaderFile' + id).find('span').html(message).prop('class', 'status text-' + status);
}

// 更新文件进度，取决于它可能使其动画化或更改颜色的参数。
function ui_multi_update_file_progress(id, percent, color, active) {
    color = (typeof color === 'undefined' ? false : color);
    active = (typeof active === 'undefined' ? true : active);

    var bar = $('#uploaderFile' + id).find('div.progress-bar');

    bar.width(percent + '%').attr('aria-valuenow', percent);
    bar.toggleClass('progress-bar-striped progress-bar-animated', active);

    if (percent === 0) {
        bar.html('');
    } else {
        bar.html(percent + '%');
    }

    if (color !== false) {
        bar.removeClass('bg-success bg-info bg-warning bg-danger');
        bar.addClass('bg-' + color);
    }
}