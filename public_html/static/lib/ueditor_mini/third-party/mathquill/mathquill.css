/*
 * LaTeX Math in pure HTML and CSS -- No images whatsoever
 * v0.xa
 * by <PERSON> and <PERSON>
 * Lesser GPL Licensed: http: //www.gnu.org/licenses/lgpl.html
 *
 * This file is automatically included by mathquill.js
 *
 */
@font-face {
  font-family: Symbola;
  src: url(font/Symbola.eot);
  src: local("Symbola Regular"), local("Symbola"), url(font/Symbola.ttf) format("truetype"), url(font/Symbola.otf) format("opentype"), url(font/Symbola.svg#Symbola) format("svg");
}
.mathquill-editable {
  display: -moz-inline-box;
  display: inline-block;
  white-space: pre-wrap;
}
.mathquill-editable .cursor {
  border-left: 1px solid black;
  margin-right: -1px;
  position: relative;
  z-index: 1;
  padding: 0;
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-editable .cursor.blink {
  visibility: hidden;
}
.mathquill-editable,
.mathquill-embedded-latex .mathquill-editable {
  border: 1px solid gray;
  padding: 2px;
}
.mathquill-embedded-latex .mathquill-editable {
  margin: 1px;
}
.mathquill-editable.hasCursor,
.mathquill-editable .hasCursor {
  -webkit-box-shadow: #68b4df 0 0 3px 2px;
  -moz-box-shadow: #68b4df 0 0 3px 2px;
  box-shadow: #68b4df 0 0 3px 2px;
}
.mathquill-editable .latex-command-input {
  color: inherit;
  font-family: "Courier New", monospace;
  border: 1px solid gray;
  padding-right: 1px;
  margin-right: 1px;
  margin-left: 2px;
}
.mathquill-editable .latex-command-input.empty {
  background: transparent;
}
.mathquill-editable .latex-command-input.hasCursor {
  border-color: ActiveBorder;
}
.mathquill-editable.empty:after,
.mathquill-textbox:after,
.mathquill-rendered-math .empty:after {
  visibility: hidden;
  content: 'c';
}
.mathquill-editable .cursor:only-child:after,
.mathquill-editable .textarea + .cursor:last-child:after {
  visibility: hidden;
  content: 'c';
}
.mathquill-textbox {
  overflow-x: auto;
  overflow-y: hidden;
}
.mathquill-rendered-math {
  font-variant: normal;
  font-weight: normal;
  font-style: normal;
  font-size: 115%;
  line-height: 1;
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-rendered-math .non-leaf,
.mathquill-rendered-math .scaled {
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-rendered-math var,
.mathquill-rendered-math .text,
.mathquill-rendered-math .nonSymbola {
  font-family: "Times New Roman", Symbola, serif;
  line-height: .9;
}
.mathquill-rendered-math * {
  font-size: inherit;
  line-height: inherit;
  margin: 0;
  padding: 0;
  border-color: black;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.mathquill-rendered-math .empty {
  background: #ccc;
}
.mathquill-rendered-math.empty {
  background: transparent;
}
.mathquill-rendered-math .text {
  font-size: 87%;
}
.mathquill-rendered-math .font {
  font: 1em "Times New Roman", Symbola, serif;
}
.mathquill-rendered-math .font * {
  font-family: inherit;
  font-style: inherit;
}
.mathquill-rendered-math b,
.mathquill-rendered-math b.font {
  font-weight: bolder;
}
.mathquill-rendered-math var,
.mathquill-rendered-math i,
.mathquill-rendered-math i.font {
  font-syle: italic;
}
.mathquill-rendered-math var.florin {
  margin: 0 -0.1em;
}
.mathquill-rendered-math big {
  font-size: 125%;
}
.mathquill-rendered-math .roman {
  font-style: normal;
}
.mathquill-rendered-math .sans-serif {
  font-family: sans-serif, Symbola, serif;
}
.mathquill-rendered-math .monospace {
  font-family: monospace, Symbola, serif;
}
.mathquill-rendered-math .overline {
  border-top: 1px solid black;
  margin-top: 1px;
}
.mathquill-rendered-math .underline {
  border-bottom: 1px solid black;
  margin-bottom: 1px;
}
.mathquill-rendered-math .binary-operator {
  padding: 0 0.2em;
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-rendered-math .unary-operator {
  padding-left: 0.2em;
}
.mathquill-rendered-math sup,
.mathquill-rendered-math sub {
  position: relative;
  font-size: 90%;
}
.mathquill-rendered-math sup .binary-operator,
.mathquill-rendered-math sub .binary-operator {
  padding: 0 .1em;
}
.mathquill-rendered-math sup .unary-operator,
.mathquill-rendered-math sub .unary-operator {
  padding-left: .1em;
}
.mathquill-rendered-math sup.limit,
.mathquill-rendered-math sub.limit,
.mathquill-rendered-math sup.nthroot,
.mathquill-rendered-math sub.nthroot {
  font-size: 80%;
}
.mathquill-rendered-math sup .fraction,
.mathquill-rendered-math sub .fraction {
  font-size: 70%;
  vertical-align: -0.4em;
}
.mathquill-rendered-math sup .numerator,
.mathquill-rendered-math sub .numerator {
  padding-bottom: 0;
}
.mathquill-rendered-math sup .denominator,
.mathquill-rendered-math sub .denominator {
  padding-top: 0;
}
.mathquill-rendered-math sup {
  vertical-align: .5em;
}
.mathquill-rendered-math sup.limit,
.mathquill-rendered-math sup.nthroot {
  vertical-align: 0.8em;
}
.mathquill-rendered-math sup.nthroot {
  margin-right: -0.6em;
  margin-left: .2em;
  min-width: .5em;
}
.mathquill-rendered-math sub {
  vertical-align: -0.4em;
}
.mathquill-rendered-math sub.limit {
  vertical-align: -0.6em;
}
.mathquill-rendered-math .paren {
  padding: 0 .1em;
  vertical-align: bottom;
  -webkit-transform-origin: bottom center;
  -moz-transform-origin: bottom center;
  -ms-transform-origin: bottom center;
  -o-transform-origin: bottom center;
  transform-origin: bottom center;
}
.mathquill-rendered-math .array {
  vertical-align: middle;
  text-align: center;
}
.mathquill-rendered-math .array > span {
  display: block;
}
.mathquill-rendered-math .non-italicized-function {
  font-family: Symbola, "Times New Roman", serif;
  line-height: .9;
  font-style: normal;
  padding-right: .2em;
}
.mathquill-rendered-math .fraction {
  font-size: 90%;
  text-align: center;
  vertical-align: -0.5em;
  padding: 0 .2em;
}
.mathquill-rendered-math .fraction,
.mathquill-rendered-math x:-moz-any-link {
  display: -moz-groupbox;
}
.mathquill-rendered-math .fraction,
.mathquill-rendered-math x:-moz-any-link,
.mathquill-rendered-math x:default {
  display: inline-block;
}
.mathquill-rendered-math .numerator,
.mathquill-rendered-math .denominator {
  display: block;
}
.mathquill-rendered-math .numerator {
  padding: 0 0.1em;
  margin-bottom: -0.1em;
}
.mathquill-rendered-math .denominator {
  border-top: 1px solid;
  float: right;
  width: 100%;
  padding: .1em .1em 0 .1em;
  margin-right: -0.1em;
  margin-left: -0.1em;
}
.mathquill-rendered-math .sqrt-prefix {
  padding-top: 0;
  position: relative;
  top: .1em;
  vertical-align: top;
  -webkit-transform-origin: top;
  -moz-transform-origin: top;
  -ms-transform-origin: top;
  -o-transform-origin: top;
  transform-origin: top;
}
.mathquill-rendered-math .sqrt-stem {
  border-top: 1px solid;
  margin-top: 1px;
  padding-left: .15em;
  padding-right: .2em;
  margin-right: .1em;
}
.mathquill-rendered-math .vector-prefix {
  display: block;
  text-align: center;
  line-height: .25em;
  margin-bottom: -0.1em;
  font-size: 0.75em;
}
.mathquill-rendered-math .vector-stem {
  display: block;
}
.mathquill-rendered-math,
.mathquill-rendered-math .mathquill-editable {
  cursor: text;
  font-family: Symbola, "Times New Roman", serif;
}
.mathquill-rendered-math .selection,
.mathquill-editable .selection,
.mathquill-rendered-math .selection .non-leaf,
.mathquill-editable .selection .non-leaf,
.mathquill-rendered-math .selection .scaled,
.mathquill-editable .selection .scaled {
  background: #B4D5FE !important;
  background: Highlight !important;
  color: HighlightText;
  border-color: HighlightText;
}
.mathquill-rendered-math .selection .matrixed,
.mathquill-editable .selection .matrixed {
  background: #39F !important;
}
.mathquill-rendered-math .selection .matrixed-container,
.mathquill-editable .selection .matrixed-container {
  filter: progid:DXImageTransform.Microsoft.Chroma(color='#3399FF') !important;
}
.mathquill-rendered-math .selection.blur,
.mathquill-editable .selection.blur,
.mathquill-rendered-math .selection.blur .non-leaf,
.mathquill-editable .selection.blur .non-leaf,
.mathquill-rendered-math .selection.blur .scaled,
.mathquill-editable .selection.blur .scaled,
.mathquill-rendered-math .selection.blur .matrixed,
.mathquill-editable .selection.blur .matrixed {
  background: #D4D4D4 !important;
  color: black;
  border-color: black;
}
.mathquill-rendered-math .selection.blur .matrixed-container,
.mathquill-editable .selection.blur .matrixed-container {
  filter: progid:DXImageTransform.Microsoft.Chroma(color='#D4D4D4') !important;
}
.mathquill-editable .textarea,
.mathquill-rendered-math .textarea {
  position: relative;
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
}
.mathquill-editable .textarea textarea,
.mathquill-rendered-math .textarea textarea,
.mathquill-editable .selectable,
.mathquill-rendered-math .selectable {
  -webkit-user-select: text;
  -moz-user-select: text;
  user-select: text;
  position: absolute;
  clip: rect(1em 1em 1em 1em);
}
.mathquill-rendered-math .matrixed {
  background: white;
  display: -moz-inline-box;
  display: inline-block;
}
.mathquill-rendered-math .matrixed-container {
  filter: progid:DXImageTransform.Microsoft.Chroma(color='white');
  margin-top: -0.1em;
}
