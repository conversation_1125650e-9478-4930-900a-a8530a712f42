(function(){function t(){}function e(t){var e=o.call(arguments,1);return function(){return t.apply(this,e)}}function n(t,e){if(!e)throw Error("prayer failed: "+t)}function i(t){n("a direction was passed",t===u||t===p)}function r(e,n,i,r){function s(){g=a;var t=c.selection?"$"+c.selection.latex()+"$":"";w.select(t)}function o(){l.detach()}var c,l,m,g,b,v,w,x=e.contents().detach();return i||e.addClass("mathquill-rendered-math"),n.jQ=e.attr(ce,n.id),n.revert=function(){e.empty().unbind(".mathquill").removeClass("mathquill-rendered-math mathquill-editable mathquill-textbox").append(x)},c=n.cursor=re(n),n.renderLatex(x.text()),l=n.textarea=f('<span class="textarea"><textarea></textarea></span>'),m=l.children(),n.selectionChanged=function(){g===a&&(g=setTimeout(s)),O(e[0])},e.bind("selectstart.mathquill",function(t){t.target!==m[0]&&t.preventDefault(),t.stopPropagation()}),v=c.blink,e.bind("mousedown.mathquill",function(n){function i(t){return c.seek(f(t.target),t.pageX,t.pageY),(c[u]!==b[u]||c.parent!==b.parent)&&c.selectFrom(b),!1}function s(t){return delete t.target,i(t)}function o(t){b=a,c.blink=v,c.selection||(r?c.show():l.detach()),e.unbind("mousemove",i),f(t.target.ownerDocument).unbind("mousemove",s).unbind("mouseup",o)}return setTimeout(function(){m.focus()}),c.blink=t,c.seek(f(n.target),n.pageX,n.pageY),b=d(c.parent,c[u],c[p]),r||e.prepend(l),e.mousemove(i),f(n.target.ownerDocument).mousemove(s).mouseup(o),!1}),r?(w=h(m,{container:e,key:function(t,e){c.parent.bubble("onKey",t,e)},text:function(t){c.parent.bubble("onText",t)},cut:function(t){c.selection&&setTimeout(function(){c.prepareEdit(),c.parent.bubble("redraw")}),t.stopPropagation()},paste:function(t){t="$"===t.slice(0,1)&&"$"===t.slice(-1)?t.slice(1,-1):"\\text{"+t+"}",c.writeLatex(t).show()}}),e.prepend(l),e.addClass("mathquill-editable"),i&&e.addClass("mathquill-textbox"),m.focus(function(t){c.parent||c.insAtRightEnd(n),c.parent.jQ.addClass("hasCursor"),c.selection?(c.selection.jQ.removeClass("blur"),setTimeout(n.selectionChanged)):c.show(),t.stopPropagation()}).blur(function(t){c.hide().parent.blur(),c.selection&&c.selection.jQ.addClass("blur"),t.stopPropagation()}),e.bind("focus.mathquill blur.mathquill",function(t){m.trigger(t)}).blur(),a):(w=h(m,{container:e}),e.bind("cut paste",!1).bind("copy",s).prepend('<span class="selectable">$'+n.latex()+"$</span>"),m.blur(function(){c.clearSelection(),setTimeout(o)}),a)}function s(t,e,n){return c(K,{ctrlSeq:t,htmlTemplate:"<"+e+" "+n+">&0</"+e+">"})}var a,o,c,h,l,u,p,f,d,m,g,b,v,w,x,j,k,q,y,Q,C,S,L,D,O,E,A,R,T,z,B,I,$,_,W,M,F,P,U,H,K,N,G,X,Z,Y,J,V,te,ee,ne,ie,re,se,ae=window.jQuery,oe="mathquill-command-id",ce="mathquill-block-id",he=Math.min;Math.max,o=[].slice,c=function(t,e,n){function i(t){return"object"==typeof t}function r(t){return"function"==typeof t}function s(){}function a(o,c){function h(){var t=new l;return r(t.init)&&t.init.apply(t,arguments),t}function l(){}var u,p,f;return c===n&&(c=o,o=Object),h.Bare=l,u=s[t]=o[t],p=l[t]=h[t]=new s,p.constructor=h,h.mixin=function(e){return l[t]=h[t]=a(h,e)[t],h},(h.open=function(t){if(f={},r(t)?f=t.call(h,p,u,h,o):i(t)&&(f=t),i(f))for(var n in f)e.call(f,n)&&(p[n]=f[n]);return r(p.init)||(p.init=o),h})(c)}return a}("prototype",{}.hasOwnProperty),h=function(){function e(t){var e,i=t.which||t.keyCode,r=n[i],s=[];return t.ctrlKey&&s.push("Ctrl"),t.originalEvent&&t.originalEvent.metaKey&&s.push("Meta"),t.altKey&&s.push("Alt"),t.shiftKey&&s.push("Shift"),e=r||String.fromCharCode(i),s.length||r?(s.push(e),s.join("-")):e}var n={8:"Backspace",9:"Tab",10:"Enter",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Esc",32:"Spacebar",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"Left",38:"Up",39:"Right",40:"Down",45:"Insert",46:"Del",144:"NumLock"};return function(n,i){function r(t){k=t,clearTimeout(j),j=setTimeout(t)}function s(e){k(),k=t,clearTimeout(j),w.val(e),e&&w[0].select()}function a(){var t=w[0];return"selectionStart"in t?t.selectionStart!==t.selectionEnd:!1}function o(t){var e=w.val();w.val(""),e&&t(e)}function c(){g(e(q),q)}function h(t){q=t,y=null,c()}function l(t){q&&y&&c(),y=t,r(u)}function u(){a()||o(m)}function p(){q=y=null}function f(){w.focus(),r(d)}function d(){o(b)}var m,g,b,v,w,x,j,k,q=null,y=null;return i||(i={}),m=i.text||t,g=i.key||t,b=i.paste||t,v=i.cut||t,w=ae(n),x=ae(i.container||w),k=t,x.bind("keydown keypress input keyup focusout paste",function(){k()}),x.bind({keydown:h,keypress:l,focusout:p,cut:v,paste:f}),{select:s}}}(),l=c(function(t,e,i){function r(t,e){throw t=t?"'"+t+"'":"EOF","Parse Error: "+e+" at "+t}var s,a,o;t.init=function(t){this._=t},t.parse=function(t){function e(t,e){return e}return this.skip(o)._(t,e,r)},t.or=function(t){n("or is passed a parser",t instanceof i);var e=this;return i(function(n,i,r){function s(){return t._(n,i,r)}return e._(n,i,s)})},t.then=function(t){var e=this;return i(function(r,s,a){function o(e,r){var o=t instanceof i?t:t(r);return n("a parser is returned",o instanceof i),o._(e,s,a)}return e._(r,o,a)})},t.many=function(){var t=this;return i(function(e,n){function i(t,n){return e=t,s.push(n),!0}function r(){return!1}for(var s=[];t._(e,i,r););return n(e,s)})},t.times=function(t,e){2>arguments.length&&(e=t);var n=this;return i(function(i,r,s){function a(t,e){return u.push(e),i=t,!0}function o(t,e){return h=e,i=t,!1}function c(){return!1}var h,l,u=[],p=!0;for(l=0;t>l;l+=1)if(p=n._(i,a,o),!p)return s(i,h);for(;e>l&&p;l+=1)p=n._(i,a,c);return r(i,u)})},t.result=function(t){return this.then(a(t))},t.atMost=function(t){return this.times(0,t)},t.atLeast=function(t){var e=this;return e.times(t).then(function(t){return e.many().map(function(e){return t.concat(e)})})},t.map=function(t){return this.then(function(e){return a(t(e))})},t.skip=function(t){return this.then(function(e){return t.result(e)})},this.string=function(t){var e=t.length,n="expected '"+t+"'";return i(function(i,r,s){var a=i.slice(0,e);return a===t?r(i.slice(e),a):s(i,n)})},s=this.regex=function(t){n("regexp parser is anchored","^"===(""+t).charAt(1));var e="expected "+t;return i(function(n,i,r){var s,a=t.exec(n);return a?(s=a[0],i(n.slice(s.length),s)):r(n,e)})},a=i.succeed=function(t){return i(function(e,n){return n(e,t)})},i.fail=function(t){return i(function(e,n,i){return i(e,t)})},i.letter=s(/^[a-z]/i),i.letters=s(/^[a-z]*/i),i.digit=s(/^[0-9]/),i.digits=s(/^[0-9]*/),i.whitespace=s(/^\s+/),i.optWhitespace=s(/^\s*/),i.any=i(function(t,e,n){return t?e(t.slice(1),t.charAt(0)):n(t,"expected any character")}),i.all=i(function(t,e){return e("",t)}),o=i.eof=i(function(t,e,n){return t?n(t,"expected EOF"):e(t,t)})}),u=-1,p=1,f=c(ae,function(t){t.insDirOf=function(t,e){return t===u?this.insertBefore(e.first()):this.insertAfter(e.last())},t.insAtDirEnd=function(t,e){return t===u?this.prependTo(e):this.appendTo(e)}}),d=c(function(t){t.parent=0,t[u]=0,t[p]=0,t.init=function(t,e,n){this.parent=t,this[u]=e,this[p]=n}}),m=c(function(t){t[u]=0,t[p]=0,t.parent=0,t.init=function(){this.ends={},this.ends[u]=0,this.ends[p]=0},t.children=function(){return g(this.ends[u],this.ends[p])},t.eachChild=function(t){return this.children().each(t)},t.foldChildren=function(t,e){return this.children().fold(t,e)},t.adopt=function(t,e,n){return g(this,this).adopt(t,e,n),this},t.disown=function(){return g(this,this).disown(),this}}),g=c(function(t){function e(t,e,i){n("a parent is always present",t),n("leftward is properly set up",function(){return e?e[p]===i&&e.parent===t:t.ends[u]===i}()),n("rightward is properly set up",function(){return i?i[u]===e&&i.parent===t:t.ends[p]===e}())}t.init=function(t,e){n("no half-empty fragments",!t==!e),this.ends={},t&&(n("left end node is passed to Fragment",t instanceof m),n("right end node is passed to Fragment",e instanceof m),n("leftEnd and rightEnd have the same parent",t.parent===e.parent),this.ends[u]=t,this.ends[p]=e)},t.adopt=function(t,n,i){var r,s,a;return e(t,n,i),r=this,r.disowned=!1,(s=r.ends[u])?(a=r.ends[p],n||(t.ends[u]=s),i?i[u]=a:t.ends[p]=a,r.ends[p][p]=i,r.each(function(e){e[u]=n,e.parent=t,n&&(n[p]=e),n=e}),r):this},t.disown=function(){var t,n,i=this,r=i.ends[u];return!r||i.disowned?i:(i.disowned=!0,t=i.ends[p],n=r.parent,e(n,r[u],r),e(n,t,t[p]),r[u]?r[u][p]=t[p]:n.ends[u]=t[p],t[p]?t[p][u]=r[u]:n.ends[p]=r[u],i)},t.each=function(t){var e=this,n=e.ends[u];if(!n)return e;for(;n!==e.ends[p][p]&&t.call(e,n)!==!1;n=n[p]);return e},t.fold=function(t,e){return this.each(function(n){t=e.call(this,t,n)}),t}}),b=function(){var t=0;return function(){return t+=1}}(),v=c(m,function(t,e){t.init=function(){e.init.call(this),this.id=b(),v[this.id]=this},t.toString=function(){return"[MathElement "+this.id+"]"},t.bubble=function(t){var e,n,i=o.call(arguments,1);for(e=this;e&&(n=e[t]&&e[t].apply(e,i),n!==!1);e=e.parent);return this},t.postOrder=function(t){var e,n=o.call(arguments,1);"string"==typeof t&&(e=t,t=function(t){e in t&&t[e].apply(t,n)}),function i(e){e.eachChild(i),t(e)}(this)},t.jQ=f(),t.jQadd=function(t){this.jQ=this.jQ.add(t)},this.jQize=function(t){var e=f(t);return e.find("*").andSelf().each(function(){var t=f(this),e=t.attr("mathquill-command-id"),n=t.attr("mathquill-block-id");e&&v[e].jQadd(t),n&&v[n].jQadd(t)}),e},t.finalizeInsert=function(){var t=this;t.postOrder("finalizeTree"),t.postOrder("blur"),t.postOrder("respace"),t[p].respace&&t[p].respace(),t[u].respace&&t[u].respace(),t.postOrder("redraw"),t.bubble("redraw")}}),w=c(v,function(e,i){e.init=function(t,e,n){var r=this;i.init.call(r),r.ctrlSeq||(r.ctrlSeq=t),e&&(r.htmlTemplate=e),n&&(r.textTemplate=n)},e.replaces=function(t){t.disown(),this.replacedFragment=t},e.isEmpty=function(){return this.foldChildren(!0,function(t,e){return t&&e.isEmpty()})},e.parser=function(){var t=ie.block,e=this;return t.times(e.numBlocks()).map(function(t){e.blocks=t;for(var n=0;t.length>n;n+=1)t[n].adopt(e,e.ends[p],0);return e})},e.createLeftOf=function(t){var e=this,n=e.replacedFragment;e.createBlocks(),v.jQize(e.html()),n&&(n.adopt(e.ends[u],0,0),n.jQ.appendTo(e.ends[u].jQ)),t.jQ.before(e.jQ),t[u]=e.adopt(t.parent,t[u],t[p]),e.finalizeInsert(t),e.placeCursor(t)},e.createBlocks=function(){var t,e,n=this,i=n.numBlocks(),r=n.blocks=Array(i);for(t=0;i>t;t+=1)e=r[t]=j(),e.adopt(n,n.ends[p],0)},e.respace=t,e.placeCursor=function(t){t.insAtRightEnd(this.foldChildren(this.ends[u],function(t,e){return t.isEmpty()?t:e}))},e.remove=function(){return this.disown(),this.jQ.remove(),this.postOrder(function(t){delete v[t.id]}),this},e.numBlocks=function(){var t=this.htmlTemplate.match(/&\d+/g);return t?t.length:0},e.html=function(){var t,e,i,r=this,s=r.blocks,a=" mathquill-command-id="+r.id,o=r.htmlTemplate.match(/<[^<>]+>|[^<>]+/g);for(n("no unmatched angle brackets",o.join("")===this.htmlTemplate),t=0,e=o[0];e;t+=1,e=o[t])if("/>"===e.slice(-2))o[t]=e.slice(0,-2)+a+"/>";else if("<"===e.charAt(0)){n("not an unmatched top-level close tag","/"!==e.charAt(1)),o[t]=e.slice(0,-1)+a+">",i=1;do t+=1,e=o[t],n("no missing close tags",e),"</"===e.slice(0,2)?i-=1:"<"===e.charAt(0)&&"/>"!==e.slice(-2)&&(i+=1);while(i>0)}return o.join("").replace(/>&(\d+)/g,function(t,e){return" mathquill-block-id="+s[e].id+">"+s[e].join("html")})},e.latex=function(){return this.foldChildren(this.ctrlSeq,function(t,e){return t+"{"+(e.latex()||" ")+"}"})},e.textTemplate=[""],e.text=function(){var t=this,e=0;return t.foldChildren(t.textTemplate[e],function(n,i){e+=1;var r=i.text();return n&&"("===t.textTemplate[e]&&"("===r[0]&&")"===r.slice(-1)?n+r.slice(1,-1)+t.textTemplate[e]:n+i.text()+(t.textTemplate[e]||"")})}}),x=c(w,function(e,n){e.init=function(t,e,i){i||(i=t&&t.length>1?t.slice(1):t),n.init.call(this,t,e,[i])},e.parser=function(){return l.succeed(this)},e.numBlocks=function(){return 0},e.replaces=function(t){t.remove()},e.createBlocks=t,e.latex=function(){return this.ctrlSeq},e.text=function(){return this.textTemplate},e.placeCursor=t,e.isEmpty=function(){return!0}}),j=c(v,function(t){t.join=function(t){return this.foldChildren("",function(e,n){return e+n[t]()})},t.latex=function(){return this.join("latex")},t.text=function(){return this.ends[u]===this.ends[p]?this.ends[u].text():"("+this.join("text")+")"},t.isEmpty=function(){return 0===this.ends[u]&&0===this.ends[p]},t.write=function(t,e,n){var i;i=e.match(/^[a-eg-zA-Z]$/)?X(e):(i=C[e]||S[e])?i(e):Z(e),n&&i.replaces(n),i.createLeftOf(t)},t.focus=function(){return this.jQ.addClass("hasCursor"),this.jQ.removeClass("empty"),this},t.blur=function(){return this.jQ.removeClass("hasCursor"),this.isEmpty()&&this.jQ.addClass("empty"),this}}),k=c(g,function(t,e){t.init=function(t,n){e.init.call(this,t,n||t),this.jQ=this.fold(f(),function(t,e){return e.jQ.add(t)})},t.latex=function(){return this.fold("",function(t,e){return t+e.latex()})},t.remove=function(){return this.jQ.remove(),this.each(function(t){t.postOrder(function(t){delete v[t.id]})}),this.disown()}}),q=c(j,function(t,e){t.latex=function(){return e.latex.call(this).replace(/(\\[a-z]+) (?![a-z])/gi,"$1")},t.text=function(){return this.foldChildren("",function(t,e){return t+e.text()})},t.renderLatex=function(t){var e=this.jQ;e.children().slice(1).remove(),this.ends[u]=this.ends[p]=0,delete this.cursor.selection,this.cursor.insAtRightEnd(this).writeLatex(t)},t.onKey=function(t,e){var n;switch(t){case"Ctrl-Shift-Backspace":case"Ctrl-Backspace":for(;this.cursor[u]||this.cursor.selection;)this.cursor.backspace();break;case"Shift-Backspace":case"Backspace":this.cursor.backspace();break;case"Esc":case"Tab":case"Spacebar":if(n=this.cursor.parent,n===this.cursor.root)return"Spacebar"===t&&e.preventDefault(),a;this.cursor.prepareMove(),n[p]?this.cursor.insAtLeftEnd(n[p]):this.cursor.insRightOf(n.parent);break;case"Shift-Tab":case"Shift-Esc":case"Shift-Spacebar":if(n=this.cursor.parent,n===this.cursor.root)return"Shift-Spacebar"===t&&e.preventDefault(),a;this.cursor.prepareMove(),n[u]?this.cursor.insAtRightEnd(n[u]):this.cursor.insLeftOf(n.parent);break;case"Enter":break;case"End":this.cursor.prepareMove().insAtRightEnd(this.cursor.parent);break;case"Ctrl-End":this.cursor.prepareMove().insAtRightEnd(this);break;case"Shift-End":for(;this.cursor[p];)this.cursor.selectRight();break;case"Ctrl-Shift-End":for(;this.cursor[p]||this.cursor.parent!==this;)this.cursor.selectRight();break;case"Home":this.cursor.prepareMove().insAtLeftEnd(this.cursor.parent);break;case"Ctrl-Home":this.cursor.prepareMove().insAtLeftEnd(this);break;case"Shift-Home":for(;this.cursor[u];)this.cursor.selectLeft();break;case"Ctrl-Shift-Home":for(;this.cursor[u]||this.cursor.parent!==this;)this.cursor.selectLeft();break;case"Left":this.cursor.moveLeft();break;case"Shift-Left":this.cursor.selectLeft();break;case"Ctrl-Left":break;case"Right":this.cursor.moveRight();break;case"Shift-Right":this.cursor.selectRight();break;case"Ctrl-Right":break;case"Up":this.cursor.moveUp();break;case"Down":this.cursor.moveDown();break;case"Shift-Up":if(this.cursor[u])for(;this.cursor[u];)this.cursor.selectLeft();else this.cursor.selectLeft();case"Shift-Down":if(this.cursor[p])for(;this.cursor[p];)this.cursor.selectRight();else this.cursor.selectRight();case"Ctrl-Up":break;case"Ctrl-Down":break;case"Ctrl-Shift-Del":case"Ctrl-Del":for(;this.cursor[p]||this.cursor.selection;)this.cursor.deleteForward();break;case"Shift-Del":case"Del":this.cursor.deleteForward();break;case"Meta-A":case"Ctrl-A":if(this!==this.cursor.root)return;for(this.cursor.prepareMove().insAtRightEnd(this);this.cursor[u];)this.cursor.selectLeft();break;default:return!1}return e.preventDefault(),!1},t.onText=function(t){return this.cursor.write(t),!1}}),y=c(w,function(t,e){t.init=function(t){e.init.call(this,"$"),this.cursor=t},t.htmlTemplate='<span class="mathquill-rendered-math">&0</span>',t.createBlocks=function(){this.ends[u]=this.ends[p]=q(),this.blocks=[this.ends[u]],this.ends[u].parent=this,this.ends[u].cursor=this.cursor,this.ends[u].write=function(t,e,n){"$"!==e?j.prototype.write.call(this,t,e,n):this.isEmpty()?(t.insRightOf(this.parent).backspace().show(),Z("\\$","$").createLeftOf(t)):t[p]?t[u]?j.prototype.write.call(this,t,e,n):t.insLeftOf(this.parent):t.insRightOf(this.parent)}},t.latex=function(){return"$"+this.ends[u].latex()+"$"}}),Q=c(j,function(t){t.renderLatex=function(t){var e,n,i,r,s,a,o,c,h,f,d,m=this,g=m.cursor;if(m.jQ.children().slice(1).remove(),m.ends[u]=m.ends[p]=0,delete g.selection,g.show().insAtRightEnd(m),e=l.regex,n=l.string,i=l.eof,r=l.all,s=n("$").then(ie).skip(n("$").or(i)).map(function(t){var e,n=y(g);return n.createBlocks(),e=n.ends[u],t.children().adopt(e,0,0),n}),a=n("\\$").result("$"),o=a.or(e(/^[^$]/)).map(Z),c=s.or(o).many(),h=c.skip(i).or(r.result(!1)).parse(t)){for(f=0;h.length>f;f+=1)h[f].adopt(m,m.ends[p],0);d=m.join("html"),v.jQize(d).appendTo(m.jQ),this.finalizeInsert()}},t.onKey=function(t){"Spacebar"!==t&&"Shift-Spacebar"!==t&&q.prototype.onKey.apply(this,arguments)},t.onText=q.prototype.onText,t.write=function(t,e,n){if(n&&n.remove(),"$"===e)y(t).createLeftOf(t);else{var i;"<"===e?i="&lt;":">"===e&&(i="&gt;"),Z(e,i).createLeftOf(t)}}}),C={},S={},O=t,E=document.createElement("div"),A=E.style,R={transform:1,WebkitTransform:1,MozTransform:1,OTransform:1,msTransform:1};for(T in R)if(T in A){D=T;break}D?L=function(t,e,n){t.css(D,"scale("+e+","+n+")")}:"filter"in A?(O=function(t){t.className=t.className},L=function(t,e,n){function i(){t.css("marginRight",(r.width()-1)*(e-1)/e+"px")}var r,s;e/=1+(n-1)/2,t.css("fontSize",n+"em"),t.hasClass("matrixed-container")||t.addClass("matrixed-container").wrapInner('<span class="matrixed"></span>'),r=t.children().css("filter","progid:DXImageTransform.Microsoft.Matrix(M11="+e+",SizingMethod='auto expand')"),i(),s=setInterval(i),f(window).load(function(){clearTimeout(s),i()})}):L=function(t,e,n){t.css("fontSize",n+"em")},z=c(w,function(t,e){t.init=function(t,n,i){e.init.call(this,t,"<"+n+" "+i+">&0</"+n+">")}}),S.mathrm=e(z,"\\mathrm","span",'class="roman font"'),S.mathit=e(z,"\\mathit","i",'class="font"'),S.mathbf=e(z,"\\mathbf","b",'class="font"'),S.mathsf=e(z,"\\mathsf","span",'class="sans-serif font"'),S.mathtt=e(z,"\\mathtt","span",'class="monospace font"'),S.underline=e(z,"\\underline","span",'class="non-leaf underline"'),S.overline=S.bar=e(z,"\\overline","span",'class="non-leaf overline"'),B=c(w,function(t,e){t.init=function(t,n,i){e.init.call(this,t,"<"+n+' class="non-leaf">&0</'+n+">",[i])},t.finalizeTree=function(){function t(t){var e=this.parent,n=t;do{if(n[p])return t.insLeftOf(e),!1;n=n.parent.parent}while(n!==e);return t.insRightOf(e),!1}n("SupSub is only _ and ^","^"===this.ctrlSeq||"_"===this.ctrlSeq),"_"===this.ctrlSeq?(this.down=this.ends[u],this.ends[u].up=t):(this.up=this.ends[u],this.ends[u].down=t)},t.latex=function(){var t=this.ends[u].latex();return 1===t.length?this.ctrlSeq+t:this.ctrlSeq+"{"+(t||" ")+"}"},t.redraw=function(){this[u]&&this[u].respace(),this[u]instanceof B||(this.respace(),!this[p]||this[p]instanceof B||this[p].respace())},t.respace=function(){if("\\int "===this[u].ctrlSeq||this[u]instanceof B&&this[u].ctrlSeq!=this.ctrlSeq&&this[u][u]&&"\\int "===this[u][u].ctrlSeq?this.limit||(this.limit=!0,this.jQ.addClass("limit")):this.limit&&(this.limit=!1,this.jQ.removeClass("limit")),this.respaced=this[u]instanceof B&&this[u].ctrlSeq!=this.ctrlSeq&&!this[u].respaced,this.respaced){var t=+this.jQ.css("fontSize").slice(0,-2),e=this[u].jQ.outerWidth(),n=this.jQ.outerWidth();this.jQ.css({left:(this.limit&&"_"===this.ctrlSeq?-.25:0)-e/t+"em",marginRight:.1-he(n,e)/t+"em"})}else this.limit&&"_"===this.ctrlSeq?this.jQ.css({left:"-.25em",marginRight:""}):this.jQ.css({left:"",marginRight:""});return this[p]instanceof B&&this[p].respace(),this}}),S.subscript=S._=e(B,"_","sub","_"),S.superscript=S.supscript=S["^"]=e(B,"^","sup","**"),I=S.frac=S.dfrac=S.cfrac=S.fraction=c(w,function(t){t.ctrlSeq="\\frac",t.htmlTemplate='<span class="fraction non-leaf"><span class="numerator">&0</span><span class="denominator">&1</span><span style="display:inline-block;width:0">&nbsp;</span></span>',t.textTemplate=["(","/",")"],t.finalizeTree=function(){this.up=this.ends[p].up=this.ends[u],this.down=this.ends[u].down=this.ends[p]}}),$=S.over=C["/"]=c(I,function(t,e){t.createLeftOf=function(t){if(!this.replacedFragment){for(var n=t[u];n&&!(n instanceof V||n instanceof K||n instanceof ee||",;:".split("").indexOf(n.ctrlSeq)>-1);)n=n[u];n instanceof ee&&n[p]instanceof B&&(n=n[p],n[p]instanceof B&&n[p].ctrlSeq!=n.ctrlSeq&&(n=n[p])),n!==t[u]&&(this.replaces(k(n[p]||t.parent.ends[u],t[u])),t[u]=n)}e.createLeftOf.call(this,t)}}),_=S.sqrt=S["√"]=c(w,function(t,e){t.ctrlSeq="\\sqrt",t.htmlTemplate='<span class="non-leaf"><span class="scaled sqrt-prefix">&radic;</span><span class="non-leaf sqrt-stem">&0</span></span>',t.textTemplate=["sqrt(",")"],t.parser=function(){return ie.optBlock.then(function(t){return ie.block.map(function(e){var n=W();return n.blocks=[t,e],t.adopt(n,0,0),e.adopt(n,t,0),n})}).or(e.parser.call(this))},t.redraw=function(){var t=this.ends[p].jQ;L(t.prev(),1,t.innerHeight()/+t.css("fontSize").slice(0,-2)-.1)}}),S.vec=c(w,function(t){t.ctrlSeq="\\vec",t.htmlTemplate='<span class="non-leaf"><span class="vector-prefix">&rarr;</span><span class="vector-stem">&0</span></span>',t.textTemplate=["vec(",")"]}),W=S.nthroot=c(_,function(t){t.htmlTemplate='<sup class="nthroot non-leaf">&0</sup><span class="scaled"><span class="sqrt-prefix scaled">&radic;</span><span class="sqrt-stem non-leaf">&1</span></span>',t.textTemplate=["sqrt[","](",")"],t.latex=function(){return"\\sqrt["+this.ends[u].latex()+"]{"+this.ends[p].latex()+"}"}}),M=c(w,function(t,e){t.init=function(t,n,i,r){e.init.call(this,"\\left"+i,'<span class="non-leaf"><span class="scaled paren">'+t+"</span>"+'<span class="non-leaf">&0</span>'+'<span class="scaled paren">'+n+"</span>"+"</span>",[t,n]),this.end="\\right"+r},t.jQadd=function(){e.jQadd.apply(this,arguments);var t=this.jQ;this.bracketjQs=t.children(":first").add(t.children(":last"))},t.latex=function(){return this.ctrlSeq+this.ends[u].latex()+this.end},t.redraw=function(){var t=this.ends[u].jQ,e=t.outerHeight()/+t.css("fontSize").slice(0,-2);L(this.bracketjQs,he(1+.2*(e-1),1.2),1.05*e)}}),S.left=c(w,function(t){t.parser=function(){var t=l.regex,e=l.string,n=l.succeed,i=l.optWhitespace;return i.then(t(/^(?:[([|]|\\\{)/)).then(function(r){"\\"===r.charAt(0)&&(r=r.slice(1));var s=C[r]();return ie.map(function(t){s.blocks=[t],t.adopt(s,0,0)}).then(e("\\right")).skip(i).then(t(/^(?:[\])|]|\\\})/)).then(function(t){return t.slice(-1)!==s.end.slice(-1)?l.fail("open doesn't match close"):n(s)})})}}),S.right=c(w,function(t){t.parser=function(){return l.fail("unmatched \\right")}}),S.lbrace=C["{"]=e(M,"{","}","\\{","\\}"),S.langle=S.lang=e(M,"&lang;","&rang;","\\langle ","\\rangle "),F=c(M,function(t,e){t.createLeftOf=function(t){t[p]||!t.parent.parent||t.parent.parent.end!==this.end||this.replacedFragment?e.createLeftOf.call(this,t):t.insRightOf(t.parent.parent)},t.placeCursor=function(t){this.ends[u].blur(),t.insRightOf(this)}}),S.rbrace=C["}"]=e(F,"{","}","\\{","\\}"),S.rangle=S.rang=e(F,"&lang;","&rang;","\\langle ","\\rangle "),P=function(t,e){t.init=function(t,n){e.init.call(this,t,n,t,n)}},U=c(M,P),S.lparen=C["("]=e(U,"(",")"),S.lbrack=S.lbracket=C["["]=e(U,"[","]"),H=c(F,P),S.rparen=C[")"]=e(H,"(",")"),S.rbrack=S.rbracket=C["]"]=e(H,"[","]"),S.lpipe=S.rpipe=C["|"]=c(U,function(t,e){t.init=function(){e.init.call(this,"|","|")},t.createLeftOf=F.prototype.createLeftOf}),K=C.$=S.text=S.textnormal=S.textrm=S.textup=S.textmd=c(w,function(t,e){t.ctrlSeq="\\text",t.htmlTemplate='<span class="text">&0</span>',t.replaces=function(t){t instanceof k?this.replacedText=t.remove().jQ.text():"string"==typeof t&&(this.replacedText=t)},t.textTemplate=['"','"'],t.parser=function(){var t=this,e=l.string,n=l.regex,i=l.optWhitespace;return i.then(e("{")).then(n(/^[^}]*/)).skip(e("}")).map(function(e){var n,i,r;for(t.createBlocks(),n=t.ends[u],i=0;e.length>i;i+=1)r=Z(e.charAt(i)),r.adopt(n,n.ends[p],0);return t})},t.createBlocks=function(){this.ends[u]=this.ends[p]=N(),this.blocks=[this.ends[u]],this.ends[u].parent=this},t.finalizeInsert=function(){this.ends[u].blur=function(){return delete this.blur,this},e.finalizeInsert.call(this)},t.createLeftOf=function(t){if(e.createLeftOf.call(this,this.cursor=t),this.replacedText)for(var n=0;this.replacedText.length>n;n+=1)this.ends[u].write(t,this.replacedText.charAt(n))}}),N=c(j,function(t,e){t.onKey=function(t){return"Spacebar"===t||"Shift-Spacebar"===t?!1:a},t.deleteOutOf=function(t,e){this.isEmpty()&&e.insRightOf(this.parent)},t.write=function(t,e,n){var i,r;return n&&n.remove(),"$"!==e?("<"===e?i="&lt;":">"===e&&(i="&gt;"),Z(e,i).createLeftOf(t)):this.isEmpty()?(t.insRightOf(this.parent).backspace(),Z("\\$","$").createLeftOf(t)):t[p]?t[u]?(r=K(),r.replaces(k(t[p],this.ends[p])),t.insRightOf(this.parent),r.adopt=function(){delete this.adopt,this.adopt.apply(this,arguments),this[u]=0},r.createLeftOf(t),r[u]=this.parent,t.insLeftOf(r)):t.insLeftOf(this.parent):t.insRightOf(this.parent),!1},t.blur=function(){if(this.jQ.removeClass("hasCursor"),this.isEmpty()){var t=this.parent,e=t.cursor;e.parent===this?this.jQ.addClass("empty"):(e.hide(),t.remove(),e[p]===t?e[p]=t[p]:e[u]===t&&(e[u]=t[u]),e.show().parent.bubble("redraw"))}return this},t.focus=function(){var t,n,i,r;return e.focus.call(this),t=this.parent,t[p].ctrlSeq===t.ctrlSeq?(n=this,i=t.cursor,r=t[p].ends[u],r.eachChild(function(t){t.parent=n,t.jQ.appendTo(n.jQ)}),this.ends[p]?this.ends[p][p]=r.ends[u]:this.ends[u]=r.ends[u],r.ends[u][u]=this.ends[p],this.ends[p]=r.ends[p],r.parent.remove(),i[u]?i.insRightOf(i[u]):i.insAtLeftEnd(this),i.parent.bubble("redraw")):t[u].ctrlSeq===t.ctrlSeq&&(i=t.cursor,i[u]?t[u].ends[u].focus():i.insAtRightEnd(t[u].ends[u])),this}}),S.em=S.italic=S.italics=S.emph=S.textit=S.textsl=s("\\textit","i",'class="text"'),S.strong=S.bold=S.textbf=s("\\textbf","b",'class="text"'),S.sf=S.textsf=s("\\textsf","span",'class="sans-serif text"'),S.tt=S.texttt=s("\\texttt","span",'class="monospace text"'),S.textsc=s("\\textsc","span",'style="font-variant:small-caps" class="text"'),S.uppercase=s("\\uppercase","span",'style="text-transform:uppercase" class="text"'),S.lowercase=s("\\lowercase","span",'style="text-transform:lowercase" class="text"'),C["\\"]=c(w,function(t,e){t.ctrlSeq="\\",t.replaces=function(t){this._replacedFragment=t.disown(),this.isEmpty=function(){return!1}},t.htmlTemplate='<span class="latex-command-input non-leaf">\\<span>&0</span></span>',t.textTemplate=["\\"],t.createBlocks=function(){e.createBlocks.call(this),this.ends[u].focus=function(){return this.parent.jQ.addClass("hasCursor"),this.isEmpty()&&this.parent.jQ.removeClass("empty"),this},this.ends[u].blur=function(){return this.parent.jQ.removeClass("hasCursor"),this.isEmpty()&&this.parent.jQ.addClass("empty"),this}},t.createLeftOf=function(t){if(e.createLeftOf.call(this,t),this.cursor=t.insAtRightEnd(this.ends[u]),this._replacedFragment){var n=this.jQ[0];this.jQ=this._replacedFragment.jQ.addClass("blur").bind("mousedown mousemove",function(t){return f(t.target=n).trigger(t),!1}).insertBefore(this.jQ).add(this.jQ)}this.ends[u].write=function(t,e,n){n&&n.remove(),e.match(/[a-z]/i)?Z(e).createLeftOf(t):(this.parent.renderCommand(),"\\"===e&&this.isEmpty()||this.parent.parent.write(t,e))}},t.latex=function(){return"\\"+this.ends[u].latex()+" "},t.onKey=function(t,e){return"Tab"===t||"Enter"===t||"Spacebar"===t?(this.renderCommand(),e.preventDefault(),!1):a},t.renderCommand=function(){this.jQ=this.jQ.last(),this.remove(),this[p]?this.cursor.insLeftOf(this[p]):this.cursor.insAtRightEnd(this.parent);var t=this.ends[u].latex();t||(t="backslash"),this.cursor.insertCmd(t,this._replacedFragment)}}),G=S.binom=S.binomial=c(w,function(t){t.ctrlSeq="\\binom",t.htmlTemplate='<span class="paren scaled">(</span><span class="non-leaf"><span class="array non-leaf"><span>&0</span><span>&1</span></span></span><span class="paren scaled">)</span>',t.textTemplate=["choose(",",",")"],t.redraw=function(){var t=this.jQ.eq(1),e=t.outerHeight()/+t.css("fontSize").slice(0,-2),n=this.jQ.filter(".paren");L(n,he(1+.2*(e-1),1.2),1.05*e)}}),S.choose=c(G,function(t){t.createLeftOf=$.prototype.createLeftOf}),S.vector=c(w,function(t,e){t.ctrlSeq="\\vector",t.htmlTemplate='<span class="array"><span>&0</span></span>',t.latex=function(){return"\\begin{matrix}"+this.foldChildren([],function(t,e){return t.push(e.latex()),t}).join("\\\\")+"\\end{matrix}"},t.text=function(){return"["+this.foldChildren([],function(t,e){return t.push(e.text()),t}).join()+"]"},t.createLeftOf=function(t){e.createLeftOf.call(this,this.cursor=t)},t.onKey=function(t,e){var n,i=this.cursor.parent;if(i.parent===this){if("Enter"===t)return n=j(),n.parent=this,n.jQ=f("<span></span>").attr(ce,n.id).insertAfter(i.jQ),i[p]?i[p][u]=n:this.ends[p]=n,n[p]=i[p],i[p]=n,n[u]=i,this.bubble("redraw").cursor.insAtRightEnd(n),e.preventDefault(),!1;if("Tab"===t&&!i[p])return i.isEmpty()?i[u]?(this.cursor.insRightOf(this),delete i[u][p],this.ends[p]=i[u],i.jQ.remove(),this.bubble("redraw"),e.preventDefault(),!1):a:(n=j(),n.parent=this,n.jQ=f("<span></span>").attr(ce,n.id).appendTo(this.jQ),this.ends[p]=n,i[p]=n,n[u]=i,this.bubble("redraw").cursor.insAtRightEnd(n),e.preventDefault(),!1);if(8===e.which){if(i.isEmpty())return i[u]?(this.cursor.insAtRightEnd(i[u]),i[u][p]=i[p]):(this.cursor.insLeftOf(this),this.ends[u]=i[p]),i[p]?i[p][u]=i[u]:this.ends[p]=i[u],i.jQ.remove(),this.isEmpty()?this.cursor.deleteForward():this.bubble("redraw"),e.preventDefault(),!1;if(!this.cursor[u])return e.preventDefault(),!1}}}}),S.editable=c(y,function(t,e){t.init=function(){w.prototype.init.call(this,"\\editable")},t.jQadd=function(){var t,n,i=this;e.jQadd.apply(i,arguments),t=i.ends[u].disown(),n=i.jQ.children().detach(),i.ends[u]=i.ends[p]=q(),i.blocks=[i.ends[u]],i.ends[u].parent=i,r(i.jQ,i.ends[u],!1,!0),i.cursor=i.ends[u].cursor,t.children().adopt(i.ends[u],0,0),n.appendTo(i.ends[u].jQ),i.ends[u].cursor.insAtRightEnd(i.ends[u])},t.latex=function(){return this.ends[u].latex()},t.text=function(){return this.ends[u].text()}}),S.f=e(x,"f",'<var class="florin">&fnof;</var><span style="display:inline-block;width:0">&nbsp;</span>'),X=c(x,function(t,e){t.init=function(t,n){e.init.call(this,t,"<var>"+(n||t)+"</var>")},t.text=function(){var t=this.ctrlSeq;return!this[u]||this[u]instanceof X||this[u]instanceof V||(t="*"+t),!this[p]||this[p]instanceof V||"^"===this[p].ctrlSeq||(t+="*"),t}}),Z=c(x,function(t,e){t.init=function(t,n){e.init.call(this,t,"<span>"+(n||t)+"</span>")}}),C[" "]=e(Z,"\\:"," "),S.prime=C["'"]=e(Z,"'","&prime;"),Y=c(x,function(t,e){t.init=function(t,n){e.init.call(this,t,'<span class="nonSymbola">'+(n||t)+"</span>")}}),S["@"]=Y,S["&"]=e(Y,"\\&","&amp;"),S["%"]=e(Y,"\\%","%"),S.alpha=S.beta=S.gamma=S.delta=S.zeta=S.eta=S.theta=S.iota=S.kappa=S.mu=S.nu=S.xi=S.rho=S.sigma=S.tau=S.chi=S.psi=S.omega=c(X,function(t,e){t.init=function(t){e.init.call(this,"\\"+t+" ","&"+t+";")}}),S.phi=e(X,"\\phi ","&#981;"),S.phiv=S.varphi=e(X,"\\varphi ","&phi;"),S.epsilon=e(X,"\\epsilon ","&#1013;"),S.epsiv=S.varepsilon=e(X,"\\varepsilon ","&epsilon;"),S.piv=S.varpi=e(X,"\\varpi ","&piv;"),S.sigmaf=S.sigmav=S.varsigma=e(X,"\\varsigma ","&sigmaf;"),S.thetav=S.vartheta=S.thetasym=e(X,"\\vartheta ","&thetasym;"),S.upsilon=S.upsi=e(X,"\\upsilon ","&upsilon;"),S.gammad=S.Gammad=S.digamma=e(X,"\\digamma ","&#989;"),S.kappav=S.varkappa=e(X,"\\varkappa ","&#1008;"),S.rhov=S.varrho=e(X,"\\varrho ","&#1009;"),S.pi=S["π"]=e(Y,"\\pi ","&pi;"),S.lambda=e(Y,"\\lambda ","&lambda;"),S.Upsilon=S.Upsi=S.upsih=S.Upsih=e(x,"\\Upsilon ",'<var style="font-family: serif">&upsih;</var>'),S.Gamma=S.Delta=S.Theta=S.Lambda=S.Xi=S.Pi=S.Sigma=S.Phi=S.Psi=S.Omega=S.forall=c(Z,function(t,e){t.init=function(t){e.init.call(this,"\\"+t+" ","&"+t+";")}}),J=c(w,function(t){t.init=function(t){this.latex=t},t.createLeftOf=function(t){t.writeLatex(this.latex)},t.parser=function(){var t=ie.parse(this.latex).children();return l.succeed(t)}}),S["¹"]=e(J,"^1"),S["²"]=e(J,"^2"),S["³"]=e(J,"^3"),S["¼"]=e(J,"\\frac14"),S["½"]=e(J,"\\frac12"),S["¾"]=e(J,"\\frac34"),V=c(x,function(t,e){t.init=function(t,n,i){e.init.call(this,t,'<span class="binary-operator">'+n+"</span>",i)
}}),te=c(V,function(t){t.init=Z.prototype.init,t.respace=function(){return this.jQ[0].className=this[u]?this[u]instanceof V&&this[p]&&!(this[p]instanceof V)?"unary-operator":"binary-operator":"",this}}),S["+"]=e(te,"+","+"),S["–"]=S["-"]=e(te,"-","&minus;"),S["±"]=S.pm=S.plusmn=S.plusminus=e(te,"\\pm ","&plusmn;"),S.mp=S.mnplus=S.minusplus=e(te,"\\mp ","&#8723;"),C["*"]=S.sdot=S.cdot=e(V,"\\cdot ","&middot;"),S["="]=e(V,"=","="),S["<"]=e(V,"<","&lt;"),S[">"]=e(V,">","&gt;"),S.notin=S.sim=S.cong=S.equiv=S.oplus=S.otimes=c(V,function(t,e){t.init=function(t){e.init.call(this,"\\"+t+" ","&"+t+";")}}),S.times=e(V,"\\times ","&times;","[x]"),S["÷"]=S.div=S.divide=S.divides=e(V,"\\div ","&divide;","[/]"),S["≠"]=S.ne=S.neq=e(V,"\\ne ","&ne;"),S.ast=S.star=S.loast=S.lowast=e(V,"\\ast ","&lowast;"),S.therefor=S.therefore=e(V,"\\therefore ","&there4;"),S.cuz=S.because=e(V,"\\because ","&#8757;"),S.prop=S.propto=e(V,"\\propto ","&prop;"),S["≈"]=S.asymp=S.approx=e(V,"\\approx ","&asymp;"),S.lt=e(V,"<","&lt;"),S.gt=e(V,">","&gt;"),S["≤"]=S.le=S.leq=e(V,"\\le ","&le;"),S["≥"]=S.ge=S.geq=e(V,"\\ge ","&ge;"),S.isin=S["in"]=e(V,"\\in ","&isin;"),S.ni=S.contains=e(V,"\\ni ","&ni;"),S.notni=S.niton=S.notcontains=S.doesnotcontain=e(V,"\\not\\ni ","&#8716;"),S.sub=S.subset=e(V,"\\subset ","&sub;"),S.sup=S.supset=S.superset=e(V,"\\supset ","&sup;"),S.nsub=S.notsub=S.nsubset=S.notsubset=e(V,"\\not\\subset ","&#8836;"),S.nsup=S.notsup=S.nsupset=S.notsupset=S.nsuperset=S.notsuperset=e(V,"\\not\\supset ","&#8837;"),S.sube=S.subeq=S.subsete=S.subseteq=e(V,"\\subseteq ","&sube;"),S.supe=S.supeq=S.supsete=S.supseteq=S.supersete=S.superseteq=e(V,"\\supseteq ","&supe;"),S.nsube=S.nsubeq=S.notsube=S.notsubeq=S.nsubsete=S.nsubseteq=S.notsubsete=S.notsubseteq=e(V,"\\not\\subseteq ","&#8840;"),S.nsupe=S.nsupeq=S.notsupe=S.notsupeq=S.nsupsete=S.nsupseteq=S.notsupsete=S.notsupseteq=S.nsupersete=S.nsuperseteq=S.notsupersete=S.notsuperseteq=e(V,"\\not\\supseteq ","&#8841;"),ee=c(x,function(t,e){t.init=function(t,n){e.init.call(this,t,"<big>"+n+"</big>")}}),S["∑"]=S.sum=S.summation=e(ee,"\\sum ","&sum;"),S["∏"]=S.prod=S.product=e(ee,"\\prod ","&prod;"),S.coprod=S.coproduct=e(ee,"\\coprod ","&#8720;"),S["∫"]=S["int"]=S.integral=e(ee,"\\int ","&int;"),S.N=S.naturals=S.Naturals=e(Z,"\\mathbb{N}","&#8469;"),S.P=S.primes=S.Primes=S.projective=S.Projective=S.probability=S.Probability=e(Z,"\\mathbb{P}","&#8473;"),S.Z=S.integers=S.Integers=e(Z,"\\mathbb{Z}","&#8484;"),S.Q=S.rationals=S.Rationals=e(Z,"\\mathbb{Q}","&#8474;"),S.R=S.reals=S.Reals=e(Z,"\\mathbb{R}","&#8477;"),S.C=S.complex=S.Complex=S.complexes=S.Complexes=S.complexplane=S.Complexplane=S.ComplexPlane=e(Z,"\\mathbb{C}","&#8450;"),S.H=S.Hamiltonian=S.quaternions=S.Quaternions=e(Z,"\\mathbb{H}","&#8461;"),S.quad=S.emsp=e(Z,"\\quad ","    "),S.qquad=e(Z,"\\qquad ","        "),S.diamond=e(Z,"\\diamond ","&#9671;"),S.bigtriangleup=e(Z,"\\bigtriangleup ","&#9651;"),S.ominus=e(Z,"\\ominus ","&#8854;"),S.uplus=e(Z,"\\uplus ","&#8846;"),S.bigtriangledown=e(Z,"\\bigtriangledown ","&#9661;"),S.sqcap=e(Z,"\\sqcap ","&#8851;"),S.triangleleft=e(Z,"\\triangleleft ","&#8882;"),S.sqcup=e(Z,"\\sqcup ","&#8852;"),S.triangleright=e(Z,"\\triangleright ","&#8883;"),S.odot=e(Z,"\\odot ","&#8857;"),S.bigcirc=e(Z,"\\bigcirc ","&#9711;"),S.dagger=e(Z,"\\dagger ","&#0134;"),S.ddagger=e(Z,"\\ddagger ","&#135;"),S.wr=e(Z,"\\wr ","&#8768;"),S.amalg=e(Z,"\\amalg ","&#8720;"),S.models=e(Z,"\\models ","&#8872;"),S.prec=e(Z,"\\prec ","&#8826;"),S.succ=e(Z,"\\succ ","&#8827;"),S.preceq=e(Z,"\\preceq ","&#8828;"),S.succeq=e(Z,"\\succeq ","&#8829;"),S.simeq=e(Z,"\\simeq ","&#8771;"),S.mid=e(Z,"\\mid ","&#8739;"),S.ll=e(Z,"\\ll ","&#8810;"),S.gg=e(Z,"\\gg ","&#8811;"),S.parallel=e(Z,"\\parallel ","&#8741;"),S.bowtie=e(Z,"\\bowtie ","&#8904;"),S.sqsubset=e(Z,"\\sqsubset ","&#8847;"),S.sqsupset=e(Z,"\\sqsupset ","&#8848;"),S.smile=e(Z,"\\smile ","&#8995;"),S.sqsubseteq=e(Z,"\\sqsubseteq ","&#8849;"),S.sqsupseteq=e(Z,"\\sqsupseteq ","&#8850;"),S.doteq=e(Z,"\\doteq ","&#8784;"),S.frown=e(Z,"\\frown ","&#8994;"),S.vdash=e(Z,"\\vdash ","&#8870;"),S.dashv=e(Z,"\\dashv ","&#8867;"),S.longleftarrow=e(Z,"\\longleftarrow ","&#8592;"),S.longrightarrow=e(Z,"\\longrightarrow ","&#8594;"),S.Longleftarrow=e(Z,"\\Longleftarrow ","&#8656;"),S.Longrightarrow=e(Z,"\\Longrightarrow ","&#8658;"),S.longleftrightarrow=e(Z,"\\longleftrightarrow ","&#8596;"),S.updownarrow=e(Z,"\\updownarrow ","&#8597;"),S.Longleftrightarrow=e(Z,"\\Longleftrightarrow ","&#8660;"),S.Updownarrow=e(Z,"\\Updownarrow ","&#8661;"),S.mapsto=e(Z,"\\mapsto ","&#8614;"),S.nearrow=e(Z,"\\nearrow ","&#8599;"),S.hookleftarrow=e(Z,"\\hookleftarrow ","&#8617;"),S.hookrightarrow=e(Z,"\\hookrightarrow ","&#8618;"),S.searrow=e(Z,"\\searrow ","&#8600;"),S.leftharpoonup=e(Z,"\\leftharpoonup ","&#8636;"),S.rightharpoonup=e(Z,"\\rightharpoonup ","&#8640;"),S.swarrow=e(Z,"\\swarrow ","&#8601;"),S.leftharpoondown=e(Z,"\\leftharpoondown ","&#8637;"),S.rightharpoondown=e(Z,"\\rightharpoondown ","&#8641;"),S.nwarrow=e(Z,"\\nwarrow ","&#8598;"),S.ldots=e(Z,"\\ldots ","&#8230;"),S.cdots=e(Z,"\\cdots ","&#8943;"),S.vdots=e(Z,"\\vdots ","&#8942;"),S.ddots=e(Z,"\\ddots ","&#8944;"),S.surd=e(Z,"\\surd ","&#8730;"),S.triangle=e(Z,"\\triangle ","&#9653;"),S.ell=e(Z,"\\ell ","&#8467;"),S.top=e(Z,"\\top ","&#8868;"),S.flat=e(Z,"\\flat ","&#9837;"),S.natural=e(Z,"\\natural ","&#9838;"),S.sharp=e(Z,"\\sharp ","&#9839;"),S.wp=e(Z,"\\wp ","&#8472;"),S.bot=e(Z,"\\bot ","&#8869;"),S.clubsuit=e(Z,"\\clubsuit ","&#9827;"),S.diamondsuit=e(Z,"\\diamondsuit ","&#9826;"),S.heartsuit=e(Z,"\\heartsuit ","&#9825;"),S.spadesuit=e(Z,"\\spadesuit ","&#9824;"),S.oint=e(Z,"\\oint ","&#8750;"),S.bigcap=e(Z,"\\bigcap ","&#8745;"),S.bigcup=e(Z,"\\bigcup ","&#8746;"),S.bigsqcup=e(Z,"\\bigsqcup ","&#8852;"),S.bigvee=e(Z,"\\bigvee ","&#8744;"),S.bigwedge=e(Z,"\\bigwedge ","&#8743;"),S.bigodot=e(Z,"\\bigodot ","&#8857;"),S.bigotimes=e(Z,"\\bigotimes ","&#8855;"),S.bigoplus=e(Z,"\\bigoplus ","&#8853;"),S.biguplus=e(Z,"\\biguplus ","&#8846;"),S.lfloor=e(Z,"\\lfloor ","&#8970;"),S.rfloor=e(Z,"\\rfloor ","&#8971;"),S.lceil=e(Z,"\\lceil ","&#8968;"),S.rceil=e(Z,"\\rceil ","&#8969;"),S.slash=e(Z,"\\slash ","&#47;"),S.opencurlybrace=e(Z,"\\opencurlybrace ","&#123;"),S.closecurlybrace=e(Z,"\\closecurlybrace ","&#125;"),S.caret=e(Z,"\\caret ","^"),S.underscore=e(Z,"\\underscore ","_"),S.backslash=e(Z,"\\backslash ","\\"),S.vert=e(Z,"|"),S.perp=S.perpendicular=e(Z,"\\perp ","&perp;"),S.nabla=S.del=e(Z,"\\nabla ","&nabla;"),S.hbar=e(Z,"\\hbar ","&#8463;"),S.AA=S.Angstrom=S.angstrom=e(Z,"\\text\\AA ","&#8491;"),S.ring=S.circ=S.circle=e(Z,"\\circ ","&#8728;"),S.bull=S.bullet=e(Z,"\\bullet ","&bull;"),S.setminus=S.smallsetminus=e(Z,"\\setminus ","&#8726;"),S.not=S["¬"]=S.neg=e(Z,"\\neg ","&not;"),S["…"]=S.dots=S.ellip=S.hellip=S.ellipsis=S.hellipsis=e(Z,"\\dots ","&hellip;"),S.converges=S.darr=S.dnarr=S.dnarrow=S.downarrow=e(Z,"\\downarrow ","&darr;"),S.dArr=S.dnArr=S.dnArrow=S.Downarrow=e(Z,"\\Downarrow ","&dArr;"),S.diverges=S.uarr=S.uparrow=e(Z,"\\uparrow ","&uarr;"),S.uArr=S.Uparrow=e(Z,"\\Uparrow ","&uArr;"),S.to=e(V,"\\to ","&rarr;"),S.rarr=S.rightarrow=e(Z,"\\rightarrow ","&rarr;"),S.implies=e(V,"\\Rightarrow ","&rArr;"),S.rArr=S.Rightarrow=e(Z,"\\Rightarrow ","&rArr;"),S.gets=e(V,"\\gets ","&larr;"),S.larr=S.leftarrow=e(Z,"\\leftarrow ","&larr;"),S.impliedby=e(V,"\\Leftarrow ","&lArr;"),S.lArr=S.Leftarrow=e(Z,"\\Leftarrow ","&lArr;"),S.harr=S.lrarr=S.leftrightarrow=e(Z,"\\leftrightarrow ","&harr;"),S.iff=e(V,"\\Leftrightarrow ","&hArr;"),S.hArr=S.lrArr=S.Leftrightarrow=e(Z,"\\Leftrightarrow ","&hArr;"),S.Re=S.Real=S.real=e(Z,"\\Re ","&real;"),S.Im=S.imag=S.image=S.imagin=S.imaginary=S.Imaginary=e(Z,"\\Im ","&image;"),S.part=S.partial=e(Z,"\\partial ","&part;"),S.inf=S.infin=S.infty=S.infinity=e(Z,"\\infty ","&infin;"),S.alef=S.alefsym=S.aleph=S.alephsym=e(Z,"\\aleph ","&alefsym;"),S.xist=S.xists=S.exist=S.exists=e(Z,"\\exists ","&exist;"),S.and=S.land=S.wedge=e(Z,"\\wedge ","&and;"),S.or=S.lor=S.vee=e(Z,"\\vee ","&or;"),S.o=S.O=S.empty=S.emptyset=S.oslash=S.Oslash=S.nothing=S.varnothing=e(V,"\\varnothing ","&empty;"),S.cup=S.union=e(V,"\\cup ","&cup;"),S.cap=S.intersect=S.intersection=e(V,"\\cap ","&cap;"),S.deg=S.degree=e(Z,"^\\circ ","&deg;"),S.ang=S.angle=e(Z,"\\angle ","&ang;"),ne=c(x,function(t,e){t.init=function(t){e.init.call(this,"\\"+t+" ","<span>"+t+"</span>")},t.respace=function(){this.jQ[0].className=this[p]instanceof B||this[p]instanceof M?"":"non-italicized-function"}}),S.ln=S.lg=S.log=S.span=S.proj=S.det=S.dim=S.min=S.max=S.mod=S.lcm=S.gcd=S.gcf=S.hcf=S.lim=ne,function(){var t,e=["sin","cos","tan","sec","cosec","csc","cotan","cot"];for(t in e)S[e[t]]=S[e[t]+"h"]=S["a"+e[t]]=S["arc"+e[t]]=S["a"+e[t]+"h"]=S["arc"+e[t]+"h"]=ne}(),ie=function(){function t(t){var e=j();return t.adopt(e,0,0),e}function e(t){var e,n=t[0]||j();for(e=1;t.length>e;e+=1)t[e].children().adopt(n,n.ends[p],0);return n}var n=l.string,i=l.regex,r=l.letter,s=l.any,a=l.optWhitespace,o=l.succeed,c=l.fail,h=r.map(X),u=i(/^[^${}\\_^]/).map(Z),f=i(/^[^\\a-eg-zA-Z]/).or(n("\\").then(i(/^[a-z]+/i).or(i(/^\s+/).result(" ")).or(s))).then(function(t){var e=S[t];return e?e(t).parser():c("unknown command: \\"+t)}),d=f.or(h).or(u),m=n("{").then(function(){return b}).skip(n("}")),g=a.then(m.or(d.map(t))),b=g.many().map(e).skip(a),v=n("[").then(g.then(function(t){return"]"!==t.join("latex")?o(t):c()}).many().map(e).skip(a)).skip(n("]")),w=b;return w.block=g,w.optBlock=v,w}(),re=c(d,function(t){function e(t,e){var i,r,s,a;if(t[p][e])t.insAtLeftEnd(t[p][e]);else if(t[u][e])t.insAtRightEnd(t[u][e]);else{i=t.parent;do{if(r=i[e],r&&("function"==typeof r&&(r=i[e](t)),r===!1||r instanceof j)){t.upDownCache[i.id]=d(t.parent,t[u],t[p]),r instanceof j&&(s=t.upDownCache[r.id],s?s[p]?t.insLeftOf(s[p]):t.insAtRightEnd(s.parent):(a=n(t).left,t.insAtRightEnd(r),t.seekHoriz(a,r)));break}i=i.parent.parent}while(i)}return t.clearSelection().show()}function n(t){var e=t.jQ.removeClass("cursor").offset();return t.jQ.addClass("cursor"),e}function r(t){t.upDownCache={}}t.init=function(t){this.parent=this.root=t;var e=this.jQ=this._jQ=f('<span class="cursor">&zwj;</span>');this.blink=function(){e.toggleClass("blink")},this.upDownCache={}},t.show=function(){return this.jQ=this._jQ.removeClass("blink"),"intervalId"in this?clearInterval(this.intervalId):(this[p]?this.selection&&this.selection.ends[u][u]===this[u]?this.jQ.insertBefore(this.selection.jQ):this.jQ.insertBefore(this[p].jQ.first()):this.jQ.appendTo(this.parent.jQ),this.parent.focus()),this.intervalId=setInterval(this.blink,500),this},t.hide=function(){return"intervalId"in this&&clearInterval(this.intervalId),delete this.intervalId,this.jQ.detach(),this.jQ=f(),this},t.withDirInsertAt=function(t,e,n,i){var r=this.parent;this.parent=e,this[t]=n,this[-t]=i,r.blur()},t.insDirOf=function(t,e){return i(t),this.withDirInsertAt(t,e.parent,e[t],e),this.parent.jQ.addClass("hasCursor"),this.jQ.insDirOf(t,e.jQ),this},t.insLeftOf=function(t){return this.insDirOf(u,t)},t.insRightOf=function(t){return this.insDirOf(p,t)},t.insAtDirEnd=function(t,e){return i(t),this.withDirInsertAt(t,e,0,e.ends[t]),t===u&&e.textarea?this.jQ.insDirOf(-t,e.textarea):this.jQ.insAtDirEnd(t,e.jQ),e.focus(),this},t.insAtLeftEnd=function(t){return this.insAtDirEnd(u,t)},t.insAtRightEnd=function(t){return this.insAtDirEnd(p,t)},t.hopDir=function(t){return i(t),this.jQ.insDirOf(t,this[t].jQ),this[-t]=this[t],this[t]=this[t][t],this},t.hopLeft=function(){return this.hopDir(u)},t.hopRight=function(){return this.hopDir(p)},t.moveDirWithin=function(t,e){if(i(t),this[t])this[t].ends[-t]?this.insAtDirEnd(-t,this[t].ends[-t]):this.hopDir(t);else{if(this.parent===e)return;this.parent[t]?this.insAtDirEnd(-t,this.parent[t]):this.insDirOf(t,this.parent.parent)}},t.moveLeftWithin=function(t){return this.moveDirWithin(u,t)},t.moveRightWithin=function(t){return this.moveDirWithin(p,t)},t.moveDir=function(t){return i(t),r(this),this.selection?this.insDirOf(t,this.selection.ends[t]).clearSelection():this.moveDirWithin(t,this.root),this.show()},t.moveLeft=function(){return this.moveDir(u)},t.moveRight=function(){return this.moveDir(p)},t.moveUp=function(){return e(this,"up")},t.moveDown=function(){return e(this,"down")},t.seek=function(t,e){r(this);var n,i,s=this.clearSelection().show();return t.hasClass("empty")?(s.insAtLeftEnd(v[t.attr(ce)]),s):(n=v[t.attr(oe)],n instanceof x?(t.outerWidth()>2*(e-t.offset().left)?s.insLeftOf(n):s.insRightOf(n),s):(n||(i=v[t.attr(ce)],i||(t=t.parent(),n=v[t.attr(oe)],n||(i=v[t.attr(ce)],i||(i=s.root)))),n?s.insRightOf(n):s.insAtRightEnd(i),s.seekHoriz(e,s.root)))},t.seekHoriz=function(t,e){var i,r=this,s=n(r).left-t;do r.moveLeftWithin(e),i=s,s=n(r).left-t;while(s>0&&(r[u]||r.parent!==e));return-s>i&&r.moveRightWithin(e),r},t.writeLatex=function(t){var e,n,i,s=this;return r(s),s.show().deleteSelection(),e=l.all,n=l.eof,i=ie.skip(n).or(e.result(!1)).parse(t),i&&(i.children().adopt(s.parent,s[u],s[p]),v.jQize(i.join("html")).insertBefore(s.jQ),s[u]=i.ends[p],i.finalizeInsert(),s.parent.bubble("redraw")),this.hide()},t.write=function(t){var e=this.prepareWrite();return this.insertCh(t,e)},t.insertCh=function(t,e){return this.parent.write(this,t,e),this},t.insertCmd=function(t,e){var n=S[t];return n?(n=n(t),e&&n.replaces(e),n.createLeftOf(this)):(n=K(),n.replaces(t),n.ends[u].focus=function(){return delete this.focus,this},n.createLeftOf(this),this.insRightOf(n),e&&e.remove()),this},t.unwrapGramp=function(){var t=this.parent.parent,e=t.parent,n=t[p],i=t[u];if(t.disown().eachChild(function(r){r.isEmpty()||(r.children().adopt(e,i,n).each(function(e){e.jQ.insertBefore(t.jQ.first())}),i=r.ends[p])}),!this[p])if(this[u])this[p]=this[u][p];else for(;!this[p];){if(this.parent=this.parent[p],!this.parent){this[p]=t[p],this.parent=e;break}this[p]=this.parent.ends[u]}this[p]?this.insLeftOf(this[p]):this.insAtRightEnd(e),t.jQ.remove(),t[u]&&t[u].respace(),t[p]&&t[p].respace()},t.deleteDir=function(t){if(i(t),r(this),this.show(),this.deleteSelection());else if(this[t])this[t].isEmpty()?this[t]=this[t].remove()[t]:this.selectDir(t);else if(this.parent!==this.root){if(this.parent.parent.isEmpty())return this.insDirOf(-t,this.parent.parent).deleteDir(t);this.unwrapGramp()}return this[u]&&this[u].respace(),this[p]&&this[p].respace(),this.parent.bubble("redraw"),this},t.backspace=function(){return this.deleteDir(u)},t.deleteForward=function(){return this.deleteDir(p)},t.selectFrom=function(t){var e,n,i,r,s,a,o=this,c=t;t:for(;;){for(e=this;e!==o.parent.parent;e=e.parent.parent)if(e.parent===c.parent){i=e,r=c;break t}for(n=t;n!==c.parent.parent;n=n.parent.parent)if(o.parent===n.parent){i=o,r=n;break t}o.parent.parent&&(o=o.parent.parent),c.parent.parent&&(c=c.parent.parent)}if(i[p]!==r){for(a=i;a;a=a[p])if(a===r[u]){s=!0;break}s||(s=r,r=i,i=s)}this.hide().selection=se(i[u][p]||i.parent.ends[u],r[p][u]||r.parent.ends[p]),this.insRightOf(r[p][u]||r.parent.ends[p]),this.root.selectionChanged()},t.selectDir=function(t){if(i(t),r(this),this.selection)if(this.selection.ends[t]===this[-t])this[t]?this.hopDir(t).selection.extendDir(t):this.parent!==this.root&&this.insDirOf(t,this.parent.parent).selection.levelUp();else{if(this.hopDir(t),this.selection.ends[t]===this.selection.ends[-t])return this.clearSelection().show(),a;this.selection.retractDir(t)}else{if(this[t])this.hopDir(t);else{if(this.parent===this.root)return;this.insDirOf(t,this.parent.parent)}this.hide().selection=se(this[-t])}this.root.selectionChanged()},t.selectLeft=function(){return this.selectDir(u)},t.selectRight=function(){return this.selectDir(p)},t.prepareMove=function(){return r(this),this.show().clearSelection()},t.prepareEdit=function(){return r(this),this.show().deleteSelection()},t.prepareWrite=function(){return r(this),this.show().replaceSelection()},t.clearSelection=function(){return this.selection&&(this.selection.clear(),delete this.selection,this.root.selectionChanged()),this},t.deleteSelection=function(){return this.selection?(this[u]=this.selection.ends[u][u],this[p]=this.selection.ends[p][p],this.selection.remove(),this.root.selectionChanged(),delete this.selection):!1},t.replaceSelection=function(){var t=this.selection;return t&&(this[u]=t.ends[u][u],this[p]=t.ends[p][p],delete this.selection),t}}),se=c(k,function(t,e){t.init=function(){var t=this;e.init.apply(t,arguments),t.jQwrap(t.jQ)},t.jQwrap=function(t){this.jQ=t.wrapAll('<span class="selection"></span>').parent()},t.adopt=function(){return this.jQ.replaceWith(this.jQ=this.jQ.children()),e.adopt.apply(this,arguments)},t.clear=function(){return this.jQ.replaceWith(this.jQ.children()),this},t.levelUp=function(){var t=this,e=t.ends[u]=t.ends[p]=t.ends[p].parent.parent;return t.clear().jQwrap(e.jQ),t},t.extendDir=function(t){return i(t),this.ends[t]=this.ends[t][t],this.ends[t].jQ.insAtDirEnd(t,this.jQ),this},t.extendLeft=function(){return this.extendDir(u)},t.extendRight=function(){return this.extendDir(p)},t.retractDir=function(t){i(t),this.ends[-t].jQ.insDirOf(-t,this.jQ),this.ends[-t]=this.ends[-t][t]},t.retractRight=function(){return this.retractDir(p)},t.retractLeft=function(){return this.retractDir(u)}}),ae.fn.mathquill=function(t,e){var n,i,s,a,o;switch(t){case"redraw":return this.each(function(){var t=f(this).attr(ce),e=t&&v[t];e&&function n(t){t.eachChild(n),t.redraw&&t.redraw()}(e)});case"revert":return this.each(function(){var t=f(this).attr(ce),e=t&&v[t];e&&e.revert&&e.revert()});case"latex":return arguments.length>1?this.each(function(){var t=f(this).attr(ce),n=t&&v[t];n&&n.renderLatex(e)}):(n=f(this).attr(ce),i=n&&v[n],i&&i.latex());case"text":return n=f(this).attr(ce),i=n&&v[n],i&&i.text();case"html":return this.html().replace(/ ?hasCursor|hasCursor /,"").replace(/ class=(""|(?= |>))/g,"").replace(/<span class="?cursor( blink)?"?><\/span>/i,"").replace(/<span class="?textarea"?><textarea><\/textarea><\/span>/i,"");case"write":if(arguments.length>1)return this.each(function(){var t=f(this).attr(ce),n=t&&v[t],i=n&&n.cursor;i&&i.writeLatex(e).parent.blur()});case"cmd":if(arguments.length>1)return this.each(function(){var t,n=f(this).attr(ce),i=n&&v[n],r=i&&i.cursor;r&&(t=r.prepareWrite(),/^\\[a-z]+$/i.test(e)?r.insertCmd(e.slice(1),t):r.insertCh(e,t),r.hide().parent.blur())});default:return s="textbox"===t,a=s||"editable"===t,o=s?Q:q,this.each(function(){r(f(this),o(),s,a)})}},ae(function(){ae(".mathquill-editable:not(.mathquill-rendered-math)").mathquill("editable"),ae(".mathquill-textbox:not(.mathquill-rendered-math)").mathquill("textbox"),ae(".mathquill-embedded-latex").mathquill()})})();