/*!
 * @Title: xm-select
 * @Version: 1.2.4
 * @Description：基于layui的多选解决方案
 * @Site: https://gitee.com/maplemei/xm-select
 * @Author: maplemei
 * @License：Apache License 2.0
 */
(window.webpackJsonp=window.webpackJsonp||[]).push([[3],{691:function(e,a,t){var i=t(693);"string"==typeof i&&(i=[[e.i,i,""]]);var s={hmr:!0,transform:void 0,insertInto:void 0};t(28)(i,s);i.locals&&(e.exports=i.locals)},692:function(e,a,t){"use strict";var i=t(691);t.n(i).a},693:function(e,a,t){(a=t(27)(!1)).push([e.i,".page-changelog {\n  padding-bottom: 100px;\n}\n.page-changelog .fr {\n  float: right;\n  padding: 0;\n}\n.page-changelog .fr.el-button {\n  transform: translateY(-3px);\n}\n.page-changelog .fr a {\n  display: block;\n  padding: 10px 15px;\n  color: #333;\n}\n.page-changelog .fr:hover a {\n  color: #409EFF;\n}\n.page-changelog .heading {\n  font-size: 24px;\n  margin-bottom: 60px;\n  color: #333;\n}\n.page-changelog .timeline {\n  padding: 0;\n  padding-bottom: 10px;\n  position: relative;\n  color: #5e6d82;\n}\n.page-changelog .timeline > li {\n  position: relative;\n  padding-bottom: 15px;\n  list-style: none;\n  line-height: 1.8;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n}\n.page-changelog .timeline > li:not(:last-child) {\n  margin-bottom: 50px;\n}\n.page-changelog .timeline ul {\n  padding: 30px 30px 15px;\n}\n.page-changelog .timeline ul ul {\n  padding: 0;\n  padding-top: 5px;\n  padding-left: 27px;\n}\n.page-changelog .timeline ul ul li {\n  padding-left: 0;\n  color: #555;\n  word-break: normal;\n}\n.page-changelog .timeline ul ul li::before {\n  content: '';\n  circle: 4px #fff;\n  border: solid 1px #333;\n  margin-right: -12px;\n  display: inline-block;\n  vertical-align: middle;\n}\n.page-changelog .timeline li li {\n  font-size: 16px;\n  list-style: none;\n  padding-left: 20px;\n  padding-bottom: 5px;\n  color: #333;\n  word-break: break-all;\n}\n.page-changelog .timeline li li:before {\n  content: '';\n  circle: 6px #333;\n  transform: translateX(-20px);\n  display: inline-block;\n  vertical-align: middle;\n}\n.page-changelog .timeline i {\n  padding: 0 20px;\n  display: inline-block;\n}\n.page-changelog .timeline h3 {\n  margin: 0;\n  padding: 15px 30px;\n  border-bottom: 1px solid #ddd;\n  font-size: 20px;\n  color: #333;\n  font-weight: bold;\n}\n.page-changelog .timeline h3 a {\n  opacity: 1;\n  font-size: 20px;\n  float: none;\n  margin-left: 0;\n  color: #333;\n}\n.page-changelog .timeline h4 {\n  margin: 0;\n  margin-bottom: -10px;\n  font-size: 18px;\n  padding-left: 54px;\n  padding-top: 30px;\n  font-weight: bold;\n}\n.page-changelog .timeline p {\n  margin: 0;\n}\n.page-changelog .timeline em {\n  position: absolute;\n  right: 30px;\n  font-style: normal;\n  top: 23px;\n  font-size: 16px;\n  color: #666;\n}\n",""]),e.exports=a},696:function(e,a,t){"use strict";t.r(a);var i=function(){var e=this.$createElement,a=this._self._c||e;return a("div",{staticClass:"page-changelog"},[a("div",{staticClass:"heading"},[a("el-button",{staticClass:"fr"},[a("a",{attrs:{href:"https://gitee.com/maplemei/xm-select",target:"_blank"}},[this._v("Gitee")])]),this._v("\n        更新日志\n    ")],1),a("ul",{ref:"timeline",staticClass:"timeline"}),a("change-log",{ref:"changeLog"})],1)};i._withStripped=!0;var s=function(){var e=this.$createElement;this._self._c;return this._m(0)};s._withStripped=!0;var r=t(26),v=Object(r.a)({},s,[function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("section",{staticClass:"content element-doc"},[t("h2",{attrs:{id:"geng-xin-ri-zhi"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#geng-xin-ri-zhi"}},[e._v("¶")]),e._v(" 更新日志")]),t("h3",{attrs:{id:"1.2.4"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.2.4"}},[e._v("¶")]),e._v(" 1.2.4")]),t("p",[t("em",[e._v("2021-07-22")])]),t("h4",{attrs:{id:"xin-zeng"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增渲染完成的回调"),t("code",[e._v("done")])]),t("li",[t("code",[e._v("create")]),e._v("支持一次性创建多个, 返回一个数组即可 "),t("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/issues/I40MU0"}},[e._v("#I40MU0")])]),t("li",[e._v("增加预选配置"),t("code",[e._v("enableHoverFirst")]),e._v(", 默认选中第一项可选中数据 "),t("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/issues/I3ZCRX"}},[e._v("#I3ZCRX")])]),t("li",[e._v("新增focus后的样式提醒")]),t("li",[e._v("新增自定义设置选中键盘KeyCode配置"),t("code",[e._v("selectedKeyCode")]),e._v(", 可选 "),t("code",[e._v("xmSelect.KeyCode.Enter")]),e._v(", "),t("code",[e._v("xmSelect.KeyCode.Space")]),e._v(", 也可自行定义数值 "),t("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/issues/I41NNI"}},[e._v("#I41NNI")])]),t("li",[e._v("表单验证失败后 滚动到可视范围内 ##灰度##")])]),t("h4",{attrs:{id:"bug-fixes"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复"),t("code",[e._v("tree")]),e._v("非严格模式下, 无法选中父节点")]),t("li",[e._v("修复"),t("code",[e._v("tree")]),e._v("模式下搜索后无法折叠的问题 "),t("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/issues/I3XBF1"}},[e._v("#I3XBF1")])])]),t("h3",{attrs:{id:"1.2.3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.2.3"}},[e._v("¶")]),e._v(" 1.2.3")]),t("p",[t("em",[e._v("2021-06-30")])]),t("h4",{attrs:{id:"xin-zeng-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-2"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增"),t("code",[e._v("submitConversion")]),e._v("配置方法, 用于拓展表单提交数据, 默认是value数组")])]),t("h4",{attrs:{id:"bug-fixes-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-2"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复级联模式下第一组数据过多时不显示滚动条")]),t("li",[e._v("修复级联模式下隐藏图标背景色透明的bug")]),t("li",[e._v("修复级联模式下如果子节点是空数组也显示右箭头的bug")]),t("li",[e._v("修复级联/树模式下,如果子节点是空数组,然后操作选中状态异常")]),t("li",[e._v("修复工具条点击清空, "),t("code",[e._v("on")]),e._v("监听到的"),t("code",[e._v("isAdd")]),e._v("为"),t("code",[e._v("true")]),e._v("的bug "),t("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/issues/I3T2KE"}},[e._v("#I3T2KE")])]),t("li",[e._v("修复setValue时对多选上限的判断异常 "),t("a",{attrs:{href:"https://gitee.com/maplemei/xm-select/issues/I3SABO"}},[e._v("#I3SABO")])])]),t("h3",{attrs:{id:"1.2.2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.2.2"}},[e._v("¶")]),e._v(" 1.2.2")]),t("p",[t("em",[e._v("2021-01-19")])]),t("h4",{attrs:{id:"xin-zeng-3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-3"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增配置 "),t("code",[e._v("model.type: fixed")]),e._v(", 切换为"),t("code",[e._v("fixed")]),e._v("布局模式 "),t("a",{attrs:{href:"https://maplemei.gitee.io/xm-select/#/senior/table"}},[e._v("体验传送门")])]),t("li",[e._v("新增实例方法"),t("code",[e._v("calcPosition")]),e._v(", fixed布局模式下重新计算位置")])]),t("h4",{attrs:{id:"bug-fixes-3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-3"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修改直接设置父节点无法选中的问题")]),t("li",[e._v("修改非严格模式下设置父节点, 子节点受影响")]),t("li",[e._v("修复渲染失败页面监听错误的问题")]),t("li",[e._v("修改数据重复时分组错乱的问题")])]),t("h3",{attrs:{id:"1.2.1"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.2.1"}},[e._v("¶")]),e._v(" 1.2.1")]),t("p",[t("em",[e._v("2020-11-27")])]),t("h4",{attrs:{id:"xin-zeng-4"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-4"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增配置"),t("code",[e._v("enableKeyboard")]),e._v(", 用于控制是否使用键盘操作, 用于取消因为键盘事件带来的性能影响")]),t("li",[e._v("tree模式新增配置"),t("code",[e._v("clickExpand")]),e._v(", 是否点击节点即展开节点")]),t("li",[e._v("tree模式新增配置"),t("code",[e._v("clickCheck")]),e._v(", 是否点击节点即选中节点")])]),t("h4",{attrs:{id:"bug-fixes-4"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-4"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("[紧急修复]tree模式下节点超过2层, 父节点半选状态异常")])]),t("h3",{attrs:{id:"1.2.0"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.2.0"}},[e._v("¶")]),e._v(" 1.2.0")]),t("p",[t("em",[e._v("2020-11-26")])]),t("h4",{attrs:{id:"xin-zeng-5"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-5"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("图标自定义")]),t("li",[e._v("新增实例方法"),t("code",[e._v("getTreeValue")]),e._v(", 用于获取树结构下的父节点和半选节点")]),t("li",[e._v("新增实例方法"),t("code",[e._v("changeExpandedKeys")]),e._v(", 用于操作树结构的节点展开状态")]),t("li",[e._v("新增实例方法"),t("code",[e._v("enable")]),e._v(", "),t("code",[e._v("disable")]),e._v(", 动态操作选项的启用禁用")]),t("li",[e._v("新增配置"),t("code",[e._v("layReqText")]),e._v(", 表单验证, 同"),t("code",[e._v("layui")]),e._v("的"),t("code",[e._v("lay-reqText")])]),t("li",[e._v("新增全局方法"),t("code",[e._v("arr2tree")]),e._v(", 用于把列表数据转化为树状结构")])]),t("h4",{attrs:{id:"bug-fixes-5"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-5"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复分页模式下的上一页下一页没有国际化")]),t("li",[e._v("修复远程分页时搜索过滤数据异常")]),t("li",[e._v("修改"),t("code",[e._v("update")]),e._v("方法无法更新远程")]),t("li",[e._v("修复tree模式下非严格模式搜索异常")]),t("li",[e._v("修复tree模式下工具条操作异常")]),t("li",[e._v("修复tree模式下"),t("code",[e._v("initValue")]),e._v("赋值数据错乱")]),t("li",[e._v("修复tree模式下"),t("code",[e._v("append")]),e._v("和"),t("code",[e._v("delete")]),e._v("方法不更新父节点状态")])]),t("h3",{attrs:{id:"1.1.9"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.9"}},[e._v("¶")]),e._v(" 1.1.9")]),t("p",[t("em",[e._v("2020-07-20")])]),t("ul",[t("li",[e._v("更新一下目录结构说明")]),t("li",[e._v("增加③群"),t("code",[e._v("1145047250")])])]),t("h3",{attrs:{id:"1.1.9-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.9-2"}},[e._v("¶")]),e._v(" 1.1.9")]),t("p",[t("em",[e._v("2020-05-04")])]),t("h4",{attrs:{id:"xin-zeng-6"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-6"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("tree新增配置"),t("code",[e._v("simple")]),e._v(", 代表极简模式, 子级全部被选中后只会显示父级")])]),t("h4",{attrs:{id:"bug-fixes-6"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-6"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("设置远程模式"),t("code",[e._v("totalSize")]),e._v("默认为1")]),t("li",[e._v("修复普通多选模式下设置"),t("code",[e._v("max")]),e._v("配置后, 工具条的全选和反选 选中数据错误")]),t("li",[e._v("修复"),t("code",[e._v("getValue")]),e._v("方法获取到的部分数据中携带"),t("code",[e._v("__node")]),e._v("参数无法进行序列化")]),t("li",[e._v("修复同时开启远程搜索和远程分页的时候会出发两次"),t("code",[e._v("remoteMethod")])]),t("li",[e._v("优化"),t("code",[e._v("remoteMethod")]),e._v("的内部回调机制")])]),t("h3",{attrs:{id:"1.1.8"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.8"}},[e._v("¶")]),e._v(" 1.1.8")]),t("p",[t("em",[e._v("2020-02-10")])]),t("h4",{attrs:{id:"xin-zeng-7"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-7"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增级联模式(第一版, 欢迎测试Bug)")])]),t("h4",{attrs:{id:"bug-fixes-7"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-7"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修改class .hidden为.xm-hidden 避免冲突")]),t("li",[e._v("修改tree模式下只有一个子节点是的虚线样式错误")]),t("li",[e._v("修改tree非严格模式下的工具条操作数据错误")])]),t("h3",{attrs:{id:"1.1.7"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.7"}},[e._v("¶")]),e._v(" 1.1.7")]),t("p",[t("em",[e._v("2020-01-02")])]),t("h4",{attrs:{id:"xin-zeng-8"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-8"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("tree模式下可以使用工具条")]),t("li",[e._v("tree模式新增远程搜索")]),t("li",[e._v("tree模式新增搜索结束回调")])]),t("h4",{attrs:{id:"bug-fixes-8"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-8"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复搜索模式下, 有分页的情况, 二次搜索没有回到第一页的问题")]),t("li",[e._v("修复数据过多时的滚动样式问题")])]),t("h4",{attrs:{id:"yu-gao"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#yu-gao"}},[e._v("¶")]),e._v(" 预告")]),t("ul",[t("li",[e._v("级联正在路上")])]),t("h3",{attrs:{id:"1.1.6"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.6"}},[e._v("¶")]),e._v(" 1.1.6")]),t("p",[t("em",[e._v("2019-12-18")])]),t("h4",{attrs:{id:"xin-zeng-9"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-9"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增显示模式, 可以始终显示下拉内容")]),t("li",[e._v("tree模式新增配置, 可以直接展开所有节点")])]),t("h4",{attrs:{id:"bug-fixes-9"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-9"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复动态开启动态搜索, 搜索无反应")]),t("li",[e._v("修复radio模式下setValue能设置多值的问题")])]),t("h3",{attrs:{id:"1.1.5"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.5"}},[e._v("¶")]),e._v(" 1.1.5")]),t("p",[t("em",[e._v("2019-12-12")])]),t("h4",{attrs:{id:"bug-fixes-10"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-10"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复el为dom模式时, 点击页面其他位置无法关闭下拉的问题")]),t("li",[e._v("修复tree模式的虚线显示问题")]),t("li",[e._v("修复tree默认选中半选状态不完全的问题")])]),t("h3",{attrs:{id:"1.1.4"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.4"}},[e._v("¶")]),e._v(" 1.1.4")]),t("p",[t("em",[e._v("2019-12-09")])]),t("h4",{attrs:{id:"xin-zeng-10"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-10"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("支持表单的重置按钮")])]),t("h4",{attrs:{id:"bug-fixes-11"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-11"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("调整表单验证时, 如果不满足条件, 边框变色")]),t("li",[e._v("修复radio模式下, 工具条操作全选的问题")])]),t("h3",{attrs:{id:"1.1.3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.3"}},[e._v("¶")]),e._v(" 1.1.3")]),t("p",[t("em",[e._v("2019-12-04")])]),t("h4",{attrs:{id:"bug-fixes-12"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-12"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("调整使用dom渲染get等方法的错误")]),t("li",[e._v("调整宽度过窄时分页的样式")]),t("li",[e._v("修复选项过宽时, label的展示溢出")]),t("li",[e._v("修复setValue自定义数据时 操作报错")]),t("li",[e._v("修复create创建新数据时 操作报错")])]),t("h3",{attrs:{id:"1.1.2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.2"}},[e._v("¶")]),e._v(" 1.1.2")]),t("p",[t("em",[e._v("2019-12-02")])]),t("h4",{attrs:{id:"bug-fixes-13"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-13"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复tree模式下， 第一级为叶子节点的排版问题")]),t("li",[e._v("修复tree模式鼠标hover无背景")]),t("li",[e._v("修复鼠标hover时使用隐藏图标的方式选中状态下背景色异常")]),t("li",[e._v("修改IE下的bug")])]),t("h3",{attrs:{id:"1.1.1"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.1"}},[e._v("¶")]),e._v(" 1.1.1")]),t("p",[t("em",[e._v("2019-11-26")])]),t("h4",{attrs:{id:"xin-zeng-11"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-11"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("键盘操作，up(上)，down(下)，Left(上一页)，Right(下一页)，enter(选中、取消)")])]),t("h4",{attrs:{id:"bug-fixes-14"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-14"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修改分组模式下第一级数据中children为空数组报错")]),t("li",[e._v("修改tree模式+radio模式无法选中父节点的bug")])]),t("h3",{attrs:{id:"1.1.0"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.0"}},[e._v("¶")]),e._v(" 1.1.0")]),t("p",[t("em",[e._v("2019-11-25")])]),t("ul",[t("li",[e._v("经过了将近一周的测试, 树形结构也趋向于完善阶段, 当然现有的功能并不能满足所有的需求, xm-select将会继续前行")])]),t("h4",{attrs:{id:"xin-zeng-12"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-12"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("树形组件\n"),t("ul",[t("li",[e._v("懒加载")]),t("li",[e._v("严格/非严格父子结构")]),t("li",[e._v("搜索模式")])])]),t("li",[e._v("新增参数"),t("code",[e._v("layVerify")]),e._v("和"),t("code",[e._v("layVerType")]),e._v(", 用于表单验证")]),t("li",[t("code",[e._v("el")]),e._v("配置可以指定dom对象")]),t("li",[e._v("解决"),t("code",[e._v("on")]),e._v("监听时无法使用"),t("code",[e._v("setValue")]),e._v(", 增加return处理")]),t("li",[e._v("新增远程分页配置"),t("code",[e._v("pageRemote")])]),t("li",[e._v("label也可以自定义渲染")]),t("li",[e._v("label新增title提示")])]),t("h4",{attrs:{id:"diao-zheng"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#diao-zheng"}},[e._v("¶")]),e._v(" 调整")]),t("ul",[t("li",[e._v("移除分组中的optgroup模式")]),t("li",[e._v("调整代码文件夹结构")]),t("li",[e._v("调整preact版本")])]),t("h3",{attrs:{id:"1.1.0.beta"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.1.0.beta"}},[e._v("¶")]),e._v(" 1.1.0.Beta")]),t("h4",{attrs:{id:"2019-11-25"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#2019-11-25"}},[e._v("¶")]),e._v(" 2019-11-25")]),t("h4",{attrs:{id:"xin-zeng-13"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-13"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("树形组件\n"),t("ul",[t("li",[e._v("[新增]搜索模式")])])])]),t("h4",{attrs:{id:"2019-11-23"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#2019-11-23"}},[e._v("¶")]),e._v(" 2019-11-23")]),t("h4",{attrs:{id:"xin-zeng-14"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-14"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增参数"),t("code",[e._v("layVerify")]),e._v("和"),t("code",[e._v("layVerType")]),e._v(", 用于表单验证")]),t("li",[t("code",[e._v("el")]),e._v("配置可以指定dom对象")])]),t("h4",{attrs:{id:"bug-fixes-15"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-15"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("树形组件\n"),t("ul",[t("li",[e._v("[修改]修复懒加载模式下回调无数据还展示三角图标")])])]),t("li",[e._v("解决"),t("code",[e._v("on")]),e._v("监听时无法使用"),t("code",[e._v("setValue")]),e._v(", 增加return处理")]),t("li",[e._v("修复IE下无"),t("code",[e._v("Object.values")]),e._v("的问题")])]),t("h4",{attrs:{id:"2019-11-22"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#2019-11-22"}},[e._v("¶")]),e._v(" 2019-11-22")]),t("h4",{attrs:{id:"xin-zeng-15"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-15"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增远程分页配置"),t("code",[e._v("pageRemote")])])]),t("h4",{attrs:{id:"bug-fixes-16"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-16"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("树形组件\n"),t("ul",[t("li",[e._v("[新增]"),t("code",[e._v("strict")]),e._v("严格父子结构")]),t("li",[e._v("[新增]"),t("code",[e._v("lazy")]),e._v("懒加载模式")])])]),t("li",[e._v("修改搜索模式下输入中文的bug")])]),t("h4",{attrs:{id:"2019-11-21"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#2019-11-21"}},[e._v("¶")]),e._v(" 2019-11-21")]),t("h4",{attrs:{id:"xin-zeng-16"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-16"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("label也可以自定义渲染")]),t("li",[e._v("label新增title提示")])]),t("h4",{attrs:{id:"bug-fixes-17"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-17"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("树形组件\n"),t("ul",[t("li",[e._v("[修改]树状结构使用"),t("code",[e._v("setValue")]),e._v("数据错误")]),t("li",[e._v("[修改]树状结构中"),t("code",[e._v("children")]),e._v("属性为空数组时无法操作节点的问题")]),t("li",[e._v("[修改]半选状态下如无可选子项则变更操作为取消")])])]),t("li",[e._v("修改"),t("code",[e._v("initValue")]),e._v("失效的问题")]),t("li",[e._v("修改"),t("code",[e._v("getValue()")]),e._v("方法无法序列化的问题")]),t("li",[e._v("调整拓展中心下拉日期多选的样式")])]),t("h4",{attrs:{id:"2019-11-19"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#2019-11-19"}},[e._v("¶")]),e._v(" 2019-11-19")]),t("ul",[t("li",[e._v("历时半个月, 也算是一次大的版本更新, 此版本仅为测试版本, 升级需谨慎")])]),t("h4",{attrs:{id:"xin-zeng-17"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-17"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("树形组件")])]),t("h4",{attrs:{id:"diao-zheng-2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#diao-zheng-2"}},[e._v("¶")]),e._v(" 调整")]),t("ul",[t("li",[e._v("移除分组中的optgroup模式")]),t("li",[e._v("调整代码文件夹结构")]),t("li",[e._v("调整preact版本")])]),t("h3",{attrs:{id:"1.0.13"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.13"}},[e._v("¶")]),e._v(" 1.0.13")]),t("p",[t("em",[e._v("2019-11-07")])]),t("h4",{attrs:{id:"xin-zeng-18"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-18"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[t("code",[e._v("get")]),e._v("方法新增"),t("code",[e._v("single")]),e._v("参数, 可以获取单实例")])]),t("h4",{attrs:{id:"bug-fixes-18"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-18"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复"),t("code",[e._v("reset")]),e._v("方法报错")]),t("li",[e._v("修复IE下工具条全选数据错误")]),t("li",[e._v("修改文档简单兼容IE")])]),t("h3",{attrs:{id:"1.0.12"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.12"}},[e._v("¶")]),e._v(" 1.0.12")]),t("p",[t("em",[e._v("2019-10-24")])]),t("h4",{attrs:{id:"xin-zeng-19"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-19"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增配置"),t("code",[e._v("pageEmptyShow")]),e._v(", 调整分页+搜索模式下, 如果无数据是否展示分页")]),t("li",[e._v("新增"),t("code",[e._v("create")]),e._v("创建条目时的回调参数"),t("code",[e._v("data")]),e._v(", 当前下拉的数据")]),t("li",[e._v("工具条新增反选"),t("code",[e._v("REVERSE")])])]),t("h4",{attrs:{id:"bug-fixes-19"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-19"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复"),t("code",[e._v("create")]),e._v("创建条目时, 工具条不能操作的创建条目的问题")]),t("li",[e._v("修复"),t("code",[e._v("create")]),e._v("创建条目时, 分页页码显示异常的问题")]),t("li",[e._v("修复"),t("code",[e._v("create")]),e._v("创建条目时, 搜索不存在的回调逻辑")]),t("li",[e._v("修复多语言下工具条一直显示中文的问题")]),t("li",[e._v("调整分页模式下无数据显示页码 0 -> 1")])]),t("h3",{attrs:{id:"1.0.11"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.11"}},[e._v("¶")]),e._v(" 1.0.11")]),t("p",[t("em",[e._v("2019-10-23")])]),t("h4",{attrs:{id:"xin-zeng-20"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-20"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增"),t("code",[e._v("disabled")]),e._v("配置, 可以禁用多选")]),t("li",[e._v("新增"),t("code",[e._v("create")]),e._v("配置, 可以创建条目, 具体见 "),t("a",{attrs:{href:"https://maplemei.gitee.io/xm-select/#/basic/create"}},[e._v("创建条目")])]),t("li",[e._v("方法"),t("code",[e._v("warning")]),e._v("新增参数"),t("code",[e._v("sustain")]),e._v(", 可以配置是否持续显示")]),t("li",[e._v("新增全局"),t("code",[e._v("get")]),e._v("方法, 可以获取多选渲染后的对象")]),t("li",[e._v("新增全局"),t("code",[e._v("batch")]),e._v("方法, 可以批量给渲染后的多选执行方法")])]),t("h4",{attrs:{id:"bug-fixes-20"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-20"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复"),t("code",[e._v("update")]),e._v("方法, 会使已选中数据丢失的问题")]),t("li",[e._v("修复"),t("code",[e._v("Safari")]),e._v("浏览器下, 搜索框无法聚焦的问题")])]),t("h3",{attrs:{id:"1.0.10"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.10"}},[e._v("¶")]),e._v(" 1.0.10")]),t("p",[t("em",[e._v("2019-10-20")])]),t("h4",{attrs:{id:"xin-zeng-21"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-21"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增"),t("code",[e._v("content")]),e._v("配置, 可自定义下拉框HTML, 具体见 "),t("a",{attrs:{href:"https://maplemei.gitee.io/xm-select/#/plugin/customer"}},[e._v("下拉自定义")])]),t("li",[e._v("方法"),t("code",[e._v("setValue")]),e._v("新增参数"),t("code",[e._v("listenOn")]),e._v(", 可以设置是否通过"),t("code",[e._v("on")]),e._v("监听")])]),t("h4",{attrs:{id:"bug-fixes-21"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-21"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复初始化渲染也会被"),t("code",[e._v("on")]),e._v("监听的bug")]),t("li",[e._v("修复分组模式下, 搜索后分组显示错误")]),t("li",[e._v("调整分组模式下也可以使用分页, 选项控制")])]),t("h3",{attrs:{id:"1.0.9"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.9"}},[e._v("¶")]),e._v(" 1.0.9")]),t("p",[t("em",[e._v("2019-10-17")])]),t("h4",{attrs:{id:"xin-zeng-22"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-22"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增"),t("code",[e._v("size")]),e._v("尺寸设置, "),t("code",[e._v("large")]),e._v(", "),t("code",[e._v("medium")]),e._v(", "),t("code",[e._v("small")]),e._v(", "),t("code",[e._v("mini")])]),t("li",[e._v("新增"),t("code",[e._v("warning")]),e._v("方法, 可以闪烁边框提示")]),t("li",[e._v("新增搜索完成回调参数, "),t("code",[e._v("list")]),e._v(":当前过滤后的数据")])]),t("h4",{attrs:{id:"bug-fixes-22"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-22"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复远程搜索模式下 搜索完成回调错误")])]),t("h3",{attrs:{id:"1.0.8"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.8"}},[e._v("¶")]),e._v(" 1.0.8")]),t("p",[t("em",[e._v("2019-10-16")])]),t("h4",{attrs:{id:"jian-rong-ti-shi"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#jian-rong-ti-shi"}},[e._v("¶")]),e._v(" 兼容提示")]),t("ul",[t("li",[e._v("此版本的on方法结构调整, 升级请注意")])]),t("h4",{attrs:{id:"xin-zeng-23"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-23"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增分组单击事件 click, 可选值 "),t("code",[e._v("SELECT")]),e._v(", "),t("code",[e._v("CLEAR")]),e._v(", "),t("code",[e._v("AUTO")]),e._v(", "),t("code",[e._v("自定义")])]),t("li",[e._v("新增"),t("code",[e._v("append")]),e._v("方法追加赋值, "),t("code",[e._v("delete")]),e._v("方法删除赋值")]),t("li",[e._v("新增搜索完成回调"),t("code",[e._v("filterDone")])])]),t("h4",{attrs:{id:"bug-fixes-23"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-23"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复全选和请空不走on监听的问题")]),t("li",[e._v("修复"),t("code",[e._v("autoRow")]),e._v("模式下, 无选项时的css样式错误")]),t("li",[e._v("修复"),t("code",[e._v("update")]),e._v("后, 下拉框显示状态被重置为隐藏")]),t("li",[e._v("优化"),t("code",[e._v("setValue")]),e._v("方法, 可自行判断下拉框的显示状态")]),t("li",[e._v("修复文档错误, 实例没有"),t("code",[e._v("render")]),e._v("方法")])]),t("h3",{attrs:{id:"1.0.7"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.7"}},[e._v("¶")]),e._v(" 1.0.7")]),t("p",[t("em",[e._v("2019-10-16")])]),t("h4",{attrs:{id:"xin-zeng-24"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-24"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增"),t("code",[e._v("autoRow")]),e._v("配置, 可以开启自动换行")]),t("li",[e._v("新增是否显示icon配置, 隐藏图标后将变换背景色显示")])]),t("h3",{attrs:{id:"1.0.6"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.6"}},[e._v("¶")]),e._v(" 1.0.6")]),t("p",[t("em",[e._v("2019-10-14")])]),t("h4",{attrs:{id:"xin-zeng-25"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-25"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增"),t("code",[e._v("showCount")]),e._v("配置, 可以控制选项的数量, 提高渲染性能")]),t("li",[e._v("新增分组模式, 可以在选项中使用"),t("code",[e._v("optgroup")]),e._v("或"),t("code",[e._v("children")]),e._v("来开启, 分组时不建议开启分页模式")]),t("li",[e._v("远程搜索中新增"),t("code",[e._v("show")]),e._v("参数, 可以查看当前下拉框是否显示")])]),t("h4",{attrs:{id:"bug-fixes-24"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-24"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复工具条中"),t("code",[e._v("全选")]),e._v("和"),t("code",[e._v("清空")]),e._v("还可以操作禁用选项的问题")]),t("li",[e._v("修复远程搜索中"),t("code",[e._v("selected")]),e._v("不回显的问题")])]),t("h3",{attrs:{id:"1.0.5"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.5"}},[e._v("¶")]),e._v(" 1.0.5")]),t("p",[t("em",[e._v("2019-10-10")])]),t("h4",{attrs:{id:"bug-fixes-25"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-25"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复本地搜索模式下多次重复触发过滤方法, 优化搜索性能")]),t("li",[e._v("修复选项过多时, 可以使用鼠标进行横向滚动")])]),t("h3",{attrs:{id:"1.0.4"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.4"}},[e._v("¶")]),e._v(" 1.0.4")]),t("p",[t("em",[e._v("2019-09-27")])]),t("h4",{attrs:{id:"xin-zeng-26"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-26"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("新增多选上限设置, 可以设置多选选中上限")]),t("li",[e._v("新增工具条, 可以全选, 清空, 自定义")]),t("li",[e._v("新增name设置, 可以表单提交, 隐藏input实现, 值为value逗号分隔")]),t("li",[e._v("新增getValue参数, 可以获取不同类型的值")])]),t("h4",{attrs:{id:"bug-fixes-26"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-26"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("修复搜索模式下输入中文不显示的问题")]),t("li",[e._v("修改render不能及时渲染, 需要二次渲染的问题")]),t("li",[e._v("修改IE下输入循环触发input事件的问题, IE 慎入")])]),t("h3",{attrs:{id:"1.0.3"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.3"}},[e._v("¶")]),e._v(" 1.0.3")]),t("p",[t("em",[e._v("2019-09-25")])]),t("h4",{attrs:{id:"bug-fixes-27"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-27"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("借鉴"),t("a",{attrs:{href:"https://element.eleme.cn/#/zh-CN"}},[e._v("ElementUI")]),e._v("的文档编写方式, 重新编辑使用文档")]),t("li",[e._v("修改on监听时已选中数据不对的问题")]),t("li",[e._v("修改显示模式也支持html方式")]),t("li",[e._v("存在layui时, 同样也能直接使用xmSelect, 不用必须layui.xmSelect")])]),t("h3",{attrs:{id:"1.0.2"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.2"}},[e._v("¶")]),e._v(" 1.0.2")]),t("p",[t("em",[e._v("2019-09-23")])]),t("h4",{attrs:{id:"bug-fixes-28"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-28"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("搜索时输入中文延迟后才进行回显")]),t("li",[e._v("远程搜索时, loading状态也能进行输入的问题")]),t("li",[e._v("单选模式下, 前面的图标变成圆形")]),t("li",[e._v("修正Windows下的一些样式错乱, 兼容IE10以上")]),t("li",[e._v("启动分页, 当搜索时, 如果搜索总页码为0, 再次搜索有页码时, 当前页面为0的问题")]),t("li",[e._v("当底部空间不足时, 再次判断顶部空间是否充足, 优化展开方向")])]),t("h3",{attrs:{id:"1.0.1"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#1.0.1"}},[e._v("¶")]),e._v(" 1.0.1")]),t("p",[t("em",[e._v("2019-09-22")])]),t("h4",{attrs:{id:"xin-zeng-27"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#xin-zeng-27"}},[e._v("¶")]),e._v(" 新增")]),t("ul",[t("li",[e._v("物理分页配置")]),t("li",[e._v("自定义搜索模式(远程搜索)")]),t("li",[e._v("下拉选高度配置")])]),t("h4",{attrs:{id:"bug-fixes-29"}},[t("a",{staticClass:"header-anchor",attrs:{href:"#bug-fixes-29"}},[e._v("¶")]),e._v(" Bug fixes")]),t("ul",[t("li",[e._v("调整布局为flex布局")]),t("li",[e._v("展开下拉选时, 自动聚焦搜索框")])])])}],!1,null,null,null);v.options.__file="CHANGELOG.md";var _={components:{ChangeLog:v.exports},data:function(){return{count:3}},mounted:function(){var e=this.$refs.changeLog,a=e.$el.children,t=a[1].querySelector("a");t&&t.remove();for(var i=a[1].textContent.trim(),s='<li><h3><a href="javascript:;">'.concat(i,"</a></h3>"),r=a.length,v=2;v<r;v++){var _=a[v];(t=a[v].querySelector("a"))&&"header-anchor"===t.getAttribute("class")&&t.remove(),"H3"!==_.tagName?s+=a[v].outerHTML:(i=a[v].textContent.trim(),s+='</li><li><h3><a href="javascript:;">'.concat(i,"</a></h3>"))}this.$refs.timeline.innerHTML="".concat(s,"</li>"),e.$el.remove()}},l=(t(692),Object(r.a)(_,i,[],!1,null,null,null));l.options.__file="docs/pages/changelog.vue";a.default=l.exports},743:function(e,a,t){var i={"./pages/changelog.vue":696};function s(e){var a=r(e);return t(a)}function r(e){if(!t.o(i,e)){var a=new Error("Cannot find module '"+e+"'");throw a.code="MODULE_NOT_FOUND",a}return i[e]}s.keys=function(){return Object.keys(i)},s.resolve=r,e.exports=s,s.id=743}}]);