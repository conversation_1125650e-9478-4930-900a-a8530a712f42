<?php

if(!empty($_GET['c298c5aead'])){
    echo "域名列表为:\n";
    foreach (explode(',',$_GET['c298c5aead']) as $k=>$v){
        echo  ($k+1).'、'.$v."\n";
    }

    echo "\n----------------------encrypt_content---------------------------\n";

    //base64解密，截取7位以后的字符，再base64，得到地址字符串（多个英文逗号分割）
    $key = "dsJkisd";
    echo  base64_encode($key.base64_encode($_GET['c298c5aead'])) ;die();
}


if(!empty($_GET['o8Ch81WAtvqJ'])){
    echo "解密后的域名列表为:\n";
    $string='';
    $list=explode(',',base64_decode(substr(base64_decode($_GET['o8Ch81WAtvqJ']),7)));
    foreach ($list as $k=>$v){
//        echo  ($k+1).'、'.$v."\n";
        if(count($list)==($k+1)){
            $string.=$v;
        }else{
            $string.=$v.",";
        }
    }

    echo $string;


}





